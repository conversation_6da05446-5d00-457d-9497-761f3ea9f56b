<?php

use Illuminate\Support\Facades\Route;

Route::match(['get', 'post'], '/open/pushGoodsToWms', 'OpenController@pushGoodsToWms'); //推送wms
Route::match(['get', 'post'], '/open/updateZy', 'OpenController@updateZy'); //搜索自营数据
Route::match(['get', 'post'], '/open/searchZy', 'OpenController@searchZy'); //搜索自营数据
Route::match(['get', 'post'], '/open/searchLy', 'OpenController@searchLy'); //搜索自营数据
Route::match(['get', 'post'], '/open/updateBatchSkuStock', 'OpenController@updateBatchSkuStock'); //对外联营更新库存
Route::match(['get', 'post'], '/open/lockLyStock', 'OpenController@lockLyStock'); //对外联营锁库接口
Route::match(['get', 'post'], '/open/getLyStock', 'OpenController@getLyStock'); //获取联营可购买数量

Route::match(['get', 'post'], '/open/getClassification', 'OpenController@getClassification'); //获取所有分类
Route::match(['get', 'post'], '/open/getStandardBrandList', 'OpenController@getStandardBrandList'); //获取所有标准品牌(缓存)
Route::match(['get', 'post'], '/open/standardBrandList', 'OpenController@standardBrandList'); //获取所有标准品牌
Route::match(['get', 'post'], '/open/getClassList', 'OpenController@getClassList'); //获取分类列表
Route::match(['get', 'post'], '/open/getStandardBrandInfo', 'OpenController@getStandardBrandInfo'); //获取某个标准品牌
Route::match(['get', 'post'], '/open/searchAbilitySku', 'OpenController@searchAbilitySku'); //搜索强履sku
Route::match(['get', 'post'], '/open/searchAbSku', 'OpenController@searchAbSku'); //动态搜索sku
Route::match(['get', 'post'], '/open/searchZySku', 'OpenController@searchZySku'); //动态搜索sku


Route::match(['get', 'post'], '/open/getSemourData', 'OpenController@getSemourData'); //深茂获取相关数据
Route::match(['get', 'post'], '/open/exportSpuList', 'OpenController@exportSpuList'); //导出SPU
Route::match(['get', 'post'], '/open/exportSpuAttr', 'OpenController@exportSpuAttr'); //导出SPU参数
Route::match(['get', 'post'], '/open/exportSkuList', 'OpenController@exportSkuList'); //导出sku列表
Route::match(['get', 'post'], '/open/exportSpuUniqueList', 'OpenController@exportSpuUniqueList'); //导出sku列表(去重导出)
Route::match(['get', 'post'], '/open/Sku/getSkuSeriesBySkuId', 'OpenController@getSkuSeriesBySkuId'); //导出sku列表(去重导出)
Route::match(['get', 'post'], '/open/exportShopSkuPushLog', 'OpenController@exportShopSkuPushLog'); //导出sku推送日志列表
Route::match(['get', 'post'], '/open/exportShopSpuPushLog', 'OpenController@exportShopSpuPushLog'); //导出spu推送日志列表
Route::match(['get', 'post'], '/open/exportShopSkuList', 'OpenController@exportShopSkuList'); //导出店铺SKU列表

Route::match(['get', 'post'], '/open/getSpuBySpuNameAndStandardBrandId', 'OpenController@getSpuBySpuNameAndStandardBrandId'); //根据型号名称+标准品牌id获取spu信息
Route::match(['get', 'post'], '/open/curl', 'OpenController@curl'); //curl
Route::match(['get', 'post'], '/open/getStandardBrand', 'OpenController@getStandardBrand'); //获取标品信息
Route::match(['get', 'post'], '/open/getStandardBrandAndRelatedBrands', 'OpenController@getStandardBrandAndRelatedBrands'); //获取标品信息以及相关品牌
Route::match(['get', 'post'], '/open/getSpuClassBySpuName', 'OpenController@getSpuClassBySpuName'); //根据型号名称获取spu信息
Route::match(['get', 'post'], '/open/getSpuListBySpuName', 'OpenController@getSpuListBySpuName'); //根据型号信息获取spu信息

