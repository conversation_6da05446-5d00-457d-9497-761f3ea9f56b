<?php

use App\Http\Models\SupplierModel;
use App\Http\Services\DataService;
use App\Console\Commands\ExportData;
use Illuminate\Support\Facades\Route;
use App\Http\Services\AttrPregService;
use App\Http\Services\SkuLabelService;
use App\Http\Models\BigData\ShopInfoModel;
use App\Http\Services\AbilityLevelRuleService;
use PhpOffice\PhpSpreadsheet\Chart\DataSeries;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', "IndexController@index"); //主菜单

Route::get('/test', function () {
  return  DataService::exportOperationLog();
    // DataService::initStandardBrandMapping();
});

Route::prefix('web')->group(function () {
    Route::get('/home', "HomeController@home"); //首页

    //品牌管理
    Route::get('/brand/brandList', 'BrandController@brandList'); //品牌列表
    Route::get('/brand/saveBrand', 'BrandController@saveBrand'); //保存品牌
    Route::get('/brand/mappingStandardBrand', 'BrandController@mappingStandardBrand'); //映射标准品牌

    //标准品牌管理
    Route::get('/standardBrand/standardBrandList', 'StandardBrandController@standardBrandList'); //标准品牌列表
    Route::get('/standardBrand/saveStandardBrand', 'StandardBrandController@saveStandardBrand'); //保存标准品牌
    Route::get('/standardBrand/addStandardBrandSpu', 'StandardBrandController@addStandardBrandSpu'); //新增标准品牌SPU

    //标准品牌映射管理
    Route::get('/standardBrandMapping/standardBrandMappingList', 'StandardBrandMappingController@standardBrandMappingList'); //标准品牌映射列表
    Route::get('/standardBrandMapping/addStandardBrandMapping', 'StandardBrandMappingController@addStandardBrandMapping'); //新增标准品牌映射
    Route::get('/standardBrandMapping/manageStandardBrandMapping', 'StandardBrandMappingController@manageStandardBrandMapping'); //管理标准品牌映射

    //供应链品牌和映射管理
    Route::get('/scmBrand/scmBrandList', 'ScmBrandController@scmBrandList'); //供应链品牌列表
    Route::get('/scmBrandMapping/scmBrandMapping', 'ScmBrandController@scmBrandMapping'); //供应链品牌映射
    Route::get('/scmBrandMapping/scmBrandMappingList', 'ScmBrandMappingController@scmBrandMappingList'); //供应链品牌映射列表
    Route::get('/scmBrandMapping/addScmBrandMapping', 'ScmBrandMappingController@addScmBrandMapping'); //新增供应链品牌映射
    Route::get('/scmBrandMapping/manageScmBrandMapping', 'ScmBrandMappingController@manageScmBrandMapping'); //管理供应链品牌映射

    //sku管理
    Route::get('/sku/skuList', 'SkuController@skuList'); //SKU列表
    Route::get('/sku/saveSku', 'SkuController@saveSku'); //新增编辑SKU
    Route::get('/sku/chooseSkuClass', 'SkuController@chooseSkuClass'); //选择分类页面
    Route::get('/sku/batchUpdateSkuStatus', 'SkuController@batchUpdateSkuStatus'); //批量上下架SKU
    Route::get('/sku/skuLabelList', 'SkuController@skuLabelList'); //sku分区列表
    Route::get('/sku/saveSkuLabel', 'SkuController@saveSkuLabel'); //保存sku分区
    Route::get('/sku/updateSku', 'SkuController@updateSku'); //批量修改SKU信息

    //spu管理
    Route::get('/spu/spuList', 'SpuController@spuList'); //SPU列表
    Route::get('/spu/saveSpu', 'SpuController@saveSpu'); //保存SPU
    Route::get('/spu/updateSpuClass', 'SpuController@updateSpuClass'); //修改SPU分类信息
    Route::get('/spu/updateSpuAttr', 'SpuController@updateSpuAttr'); //修改SPU参数
    Route::get('/spu/updateSpuPDF', 'SpuController@updateSpuPDF'); //修改SPU的PDF

    //分类管理
    Route::get('/class/classList', 'ClassController@classList'); //分类列表
    Route::get('/class/saveClass', 'ClassController@saveClass'); //保存分类
    Route::get('/class/export', 'ClassController@exportClass'); //导出分类

    //分类属性管理
    Route::get('/classAttr/classAttrList', 'ClassAttrController@classAttrList'); //分类属性列表
    Route::get('/classAttr/saveClassAttr', 'ClassAttrController@saveClassAttr'); //保存分类属性

    //分类属性值管理
    Route::get('/classAttrValue/classAttrValueList', 'ClassAttrValueController@classAttrValueList'); //分类属性值列表
    Route::get('/classAttrValue/saveClassAttrValue', 'ClassAttrValueController@saveClassAttrValue'); //保存分类属性值

    //分类属性单位管理
    Route::get('/classAttrUnit/classAttrUnitList', 'ClassAttrUnitController@classAttrUnitList'); //分类属性单位列表
    Route::get('/classAttrUnit/saveClassAttrUnit', 'ClassAttrUnitController@saveClassAttrUnit'); //保存分类属性单位

    //分类属性单位转换管理
    Route::get('/classAttrUnitConvert/classAttrUnitConvertList', 'ClassAttrUnitConvertController@classAttrUnitConvertList'); //获取分类属性单位换算列表
    Route::get('/classAttrUnitConvert/saveClassAttrUnitConvert', 'ClassAttrUnitConvertController@saveClassAttrUnitConvert'); //保存分类属性单位换算

    //供应商管理
    Route::get('/supplier/supplierList', 'SupplierController@supplierList'); //供应商列表
    Route::get('/supplier/saveSupplier', 'SupplierController@saveSupplier'); //保存供应商
    Route::get('/supplier/saveSuppExtendFee', 'SupplierController@saveSuppExtendFee'); //保存供应商附加费
    Route::get('/supplier/supplierRatioList', 'SupplierController@supplierRatioList'); //供应商系数列表
    Route::get('/supplier/saveSupplierCoefficient', 'SupplierController@saveSupplierCoefficient'); //保存供应商系数
    Route::get('/supplier/saveSupplierRatio', 'SupplierController@saveSupplierRatio'); //保存供应商系数

    //模板管理
    Route::get('/template/templateList', 'TemplateController@templateList'); //供应商列表
    Route::get('/template/downloadTemplate', 'TemplateController@downloadTemplate'); //供应商列表

    //国产替代料
    Route::get('/alikeSpu/alikeSpuList', 'AlikeSpuController@alikeSpuList'); //替代SPU列表
    Route::get('/alikeSpu/uploadAlikeSpu', 'AlikeSpuController@uploadAlikeSpu'); //上传替代SPU
    Route::get('/alikeSpu/alikeSpuItems', 'AlikeSpuController@alikeSpuItems'); //替代SPU明细列表
    Route::get('/alikeSpu/alikeSpuUploadLog', 'AlikeSpuController@alikeSpuUploadLog'); //替代SPU上传记录列表

    //样品
    Route::get('/sample/sampleList', 'SampleController@sampleList'); //样品列表
    Route::get('/sample/uploadSample', 'SampleController@uploadSample'); //上传样品
    Route::get('/sample/saveSample', 'SampleController@saveSample'); //上传样品
    Route::get('/sampleClass/sampleClassList', 'SampleClassController@sampleClassList'); //样品分类列表
    Route::get('/sampleClass/saveSampleClass', 'SampleClassController@saveSampleClass'); //保存样品分类
    Route::get('/sample/sampleUploadLog', 'SampleController@sampleUploadLog'); //样品上传记录列表

    //搜索屏蔽
    Route::get('/searchForbid/searchForbidList', 'SearchForbidController@searchForbidList'); //搜索屏蔽列表
    Route::get('/searchForbid/saveSearchForbid', 'SearchForbidController@saveSearchForbid'); //新增保存搜索屏蔽

    //代理品牌
    Route::get('/agentBrand/agentBrandList', 'AgentBrandController@agentBrandList'); //代理品牌列表
    Route::get('/agentBrand/saveAgentBrand', 'AgentBrandController@saveAgentBrand'); //保存代理品牌
    Route::get('/agentBrand/saveAgentBrandSpu', 'AgentBrandController@saveAgentBrandSpu'); //保存代理品牌产品

    //商品系列
    Route::get('/skuSeries/skuSeriesList', 'SkuSeriesController@skuSeriesList'); //商品系列列表
    Route::get('/skuSeries/addSkuSeries', 'SkuSeriesController@addSkuSeries'); //新增商品系列
    Route::get('/skuSeries/saveSkuSeries', 'SkuSeriesController@saveSkuSeries'); //编辑保存商品系列
    Route::get('/skuSeries/batchUpdateItemSeries', 'SkuSeriesController@batchUpdateItemSeries'); //编辑保存商品系列

    //第三方商品
    Route::get('/shopInfo/shopInfoList', 'ShopInfoController@shopInfoList'); //商店列表
    Route::get('/shopInfo/saveShopInfo', 'ShopInfoController@saveShopInfo'); //保存商店信息
    Route::get('/shopInfo/shopConfigList', 'ShopInfoController@shopConfigList'); //商店配置列表
    Route::get('/shopInfo/saveShopConfig', 'ShopInfoController@saveShopConfig'); //保存商店配置
    Route::get('/shopSku/shopSkuList', 'ShopSkuController@shopSkuList'); //商品列表
    Route::get('/shopSku/fetchSkuInfo', 'ShopSkuController@fetchSkuInfo'); //拉取商品信息

    //外部分类管理
    Route::get('/shopClass/shopClassMappingList', 'ShopClassController@shopClassMappingList'); //外部分类映射列表
    Route::get('/shopClass/shopClassMapping', 'ShopClassController@shopClassMapping'); //外部分类映射
    Route::get('/shopClass/shopClassList', 'ShopClassController@shopClassList'); //获取分类列表

    //外部品牌管理
    Route::get('/shopBrand/shopBrandMappingList', 'ShopBrandController@shopBrandMappingList'); //外部品牌映射列表
    Route::get('/shopBrand/shopBrandMapping', 'ShopBrandController@shopBrandMapping'); //外部品牌映射
    Route::get('/shopBrand/saveShopBrand', 'ShopBrandController@saveShopBrand'); //保存外部平台品牌
    Route::get('/shopBrand/shopBrandList', 'ShopBrandController@shopBrandList'); //外部品牌列表

    //外部参数管理
    Route::get('/shopAttr/shopAttrMappingList', 'ShopAttrController@shopAttrMappingList'); //外部参数映射列表
    Route::get('/shopAttr/shopAttrMapping', 'ShopAttrController@shopAttrMapping'); //外部参数映射
    Route::get('/shopAttr/saveShopAttrMappingItem', 'ShopAttrController@saveShopAttrMappingItem'); //编辑修改单条映射详情
    Route::get('/shopAttr/shopAttrList', 'ShopAttrController@shopAttrList'); //外部参数列表

    //数据分发规则管理
    Route::get('/shopDistributeRule/shopDistributeRuleList', 'ShopDistributeRuleController@shopDistributeRuleList'); //数据分发规则列表
    Route::get('/shopDistributeRule/saveShopDistributeRule', 'ShopDistributeRuleController@saveShopDistributeRule'); //保存数据分发规则

    //SPU分发规则管理
    Route::get('/shopDistributeRuleSpu/shopDistributeRuleSpuList', 'ShopDistributeRuleSpuController@shopDistributeRuleSpuList'); //SPU分发规则列表
    Route::get('/shopDistributeRuleSpu/saveShopDistributeRuleSpu', 'ShopDistributeRuleSpuController@saveShopDistributeRuleSpu'); //保存SPU分发规则

    //分发价格设置
    Route::get('/distributePrice/distributePriceList', 'DistributePriceController@distributePriceList'); //分发价格列表
    Route::get('/distributePrice/saveDistributePrice', 'DistributePriceController@saveDistributePrice'); //保存分发价格

    //分类管理
    Route::get('/classManagement/classManagementList', 'ClassManagementController@classManagementList'); //分类管理列表
    Route::get('/classManagement/saveClassManagement', 'ClassManagementController@saveClassManagement'); //保存分类管理

    //客户价格分发设置
    Route::get('/distributeUserPrice/distributeUserPriceList', 'DistributeUserPriceController@distributeUserPriceList'); //客户价格分发列表
    Route::get('/distributeUserPrice/saveDistributeUserPrice', 'DistributeUserPriceController@saveDistributeUserPrice'); //保存客户价格分发

    //履约能力规则
    Route::get('/abilityLevelRule/abilityLevelRuleList', 'AbilityLevelRuleController@abilityLevelRuleList'); //履约能力规则列表
    Route::get('/abilityLevelRule/saveAbilityLevelRule', 'AbilityLevelRuleController@saveAbilityLevelRule'); //保存履约能力规则


    //SKU分发规则管理
    Route::get('/shopDistributeSku/shopDistributeSkuList', 'ShopDistributeSkuController@shopDistributeSkuList'); //SKU分发规则列表
    Route::get('/shopDistributeSku/saveShopDistributeSku', 'ShopDistributeSkuController@saveShopDistributeSku'); //保存SKU分发规则
    Route::get('/shopDistributeSku/importShopDistributeSku', 'ShopDistributeSkuController@importShopDistributeSku'); //导入SKU分发规则

    //数据推送选品管理
    Route::get('/shopDistributeSku/batchSaveShopDistributeSku', 'ShopDistributeSkuController@batchSaveShopDistributeSku'); //批量设置选品

    //外部单位映射管理
    Route::get('/shopUnit/shopUnitMappingList', 'ShopUnitController@shopUnitMappingList'); //外部单位映射则列表
    Route::get('/shopUnit/saveShopUnitMapping', 'ShopUnitController@saveShopUnitMapping'); //保存外部单位映射则
    Route::get('/shopUnit/notMappingShopUnitList', 'ShopUnitController@notMappingShopUnitList'); //未映射单位列表

    //封装管理
    Route::get('/encap/encapList', 'EncapController@encapList'); //封装列表
    Route::get('/encap/saveEncap', 'EncapController@saveEncap'); //保存封装
    Route::get('/encap/mappingStandardEncap', 'EncapController@mappingStandardEncap'); //映射标准封装

    //标准封装管理
    Route::get('/standardEncap/standardEncapList', 'StandardEncapController@standardEncapList'); //标准封装列表
    Route::get('/standardEncap/saveStandardEncap', 'StandardEncapController@saveStandardEncap'); //保存标准封装

    //外部sku日志
    Route::get('/shopSkuLog/shopSkuPushLogList', 'ShopSkuLogController@shopSkuLogList'); //外部sku推送日志列表

    //外部spu日志
    Route::get('/shopSpuLog/shopSpuPushLogList', 'ShopSpuLogController@shopSpuLogList'); //外部spu推送日志列表

    //标准封装映射管理
    Route::get('/standardEncapMapping/standardEncapMappingList', 'StandardEncapMappingController@standardEncapMappingList'); //标准封装映射列表
    Route::get('/standardEncapMapping/addStandardEncapMapping', 'StandardEncapMappingController@addStandardEncapMapping'); //新增标准封装映射
    Route::get('/standardEncapMapping/manageStandardEncapMapping', 'StandardEncapMappingController@manageStandardEncapMapping'); //管理标准封装映射

    //训练数据管理
    Route::get('/trainingData/trainingDataList', 'TrainingDataController@trainingDataList'); //训练数据列表
    Route::get('/trainingData/saveTrainingData', 'TrainingDataController@saveTrainingData'); //保存训练数据

    //spu日志管理
    Route::get('/spuLog/spuLogList', 'SpuLogController@spuLogList'); //spu日志列表

    //任务日志管理
    Route::get('/taskLog/taskLogList', 'TaskLogController@taskLogList'); //任务日志列表

    //操作日志管理
    Route::get('/operationLog/operationLogList', 'OperationLogController@operationLogList'); //操作日志列表

});
