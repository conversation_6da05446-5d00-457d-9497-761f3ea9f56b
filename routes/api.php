<?php

use Illuminate\Support\Facades\Route;

//接口相关路由
Route::namespace('Api')->group(function () {
    Route::match(['get', 'post'], '/common/editorUpload', 'CommonApiController@editorUpload'); //富文本附件上传
    Route::match(['get', 'post'], '/common/checkStandardBrandNameList', 'CommonApiController@checkStandardBrandNameList'); //校验标准品牌
    Route::match(['get', 'post'], '/common/getCanalListForXmSelect', 'CommonApiController@getCanalListForXmSelect'); //给xm-select的供应商列表

    //品牌管理
    Route::match(['get', 'post'], '/brand/getBrandList', 'BrandApiController@getBrandList'); //获取品牌列表
    Route::match(['get', 'post'], '/brand/disableBrand', 'BrandApiController@disableBrand'); //禁用品牌
    Route::match(['get', 'post'], '/brand/changeBrandStatus', 'BrandApiController@changeBrandStatus'); //修改品牌状态
    Route::match(['get', 'post'], '/brand/saveBrand', 'BrandApiController@saveBrand'); //保存品牌
    Route::match(['get', 'post'], '/brand/searchBrand', 'BrandApiController@searchBrand'); //搜索品牌
    Route::match(['get', 'post'], '/brand/getBrandListForSelect', 'BrandApiController@getBrandListForSelect'); //筛选框品牌
    Route::match(['get', 'post'], '/brand/exportBrands', 'BrandApiController@exportBrands'); //导出品牌
    Route::match(['get', 'post'], '/brand/updateIgnoreHandle', 'BrandApiController@updateIgnoreHandle'); //修改是否处理

    //标准品牌管理
    Route::match(['get', 'post'], '/standardBrand/getStandardBrandList', 'StandardBrandApiController@getStandardBrandList'); //获取标准品牌列表
    Route::match(['get', 'post'], '/standardBrand/disableStandardBrand', 'StandardBrandApiController@disableStandardBrand'); //禁用标准品牌
    Route::match(['get', 'post'], '/standardBrand/enableStandardBrand', 'StandardBrandApiController@enableStandardBrand'); //启用标准品牌
    Route::match(['get', 'post'], '/standardBrand/getMainSpuList', 'StandardBrandApiController@getMainSpuList'); //获取标准品牌主要SPU
    Route::match(['get', 'post'], '/standardBrand/saveStandardBrand', 'StandardBrandApiController@saveStandardBrand'); //保存标准品牌
    Route::match(['get', 'post'], '/standardBrand/saveStandardBrandSpu', 'StandardBrandApiController@saveStandardBrandSpu'); //保存标准品牌SPU
    Route::match(['get', 'post'], '/standardBrand/deleteStandardBrandSpu', 'StandardBrandApiController@deleteStandardBrandSpu'); //删除标准品牌SPU
    Route::match(['get', 'post'], '/standardBrand/importStandardBrandSpu', 'StandardBrandApiController@importStandardBrandSpu'); //导入标准品牌SPU
    Route::match(['get', 'post'], '/standardBrand/exportStandardBrand', 'StandardBrandApiController@exportStandardBrand'); //导出标准品牌
    Route::match(['get', 'post'], '/standardBrand/searchStandardBrand', 'StandardBrandApiController@searchStandardBrand'); //搜索标准品牌
    Route::match(['get', 'post'], '/standardBrand/getStandardBrandById', 'StandardBrandApiController@getStandardBrandById'); //获取单个标品的信息

    //标准品牌映射管理
    Route::match(['get', 'post'], '/standardBrandMapping/getStandardBrandMappingList', 'StandardBrandMappingApiController@getStandardBrandMappingList'); //获取标准品牌映射列表
    Route::match(['get', 'post'], '/standardBrandMapping/findNotMappingBrand', 'StandardBrandMappingApiController@findNotMappingBrand'); //获取没有映射的标准品牌
    Route::match(['get', 'post'], '/standardBrandMapping/addStandardBrandMapping', 'StandardBrandMappingApiController@addStandardBrandMapping'); //新增标准品牌映射
    Route::match(['get', 'post'], '/standardBrandMapping/deleteStandardBrandMapping', 'StandardBrandMappingApiController@deleteStandardBrandMapping'); //删除标准品牌映射
    Route::match(['get', 'post'], '/standardBrandMapping/exportStandardBrandMapping', 'StandardBrandMappingApiController@exportStandardBrandMapping'); //导出标准品牌映射
    Route::match(['get', 'post'], '/standardBrandMapping/resendQueue', 'StandardBrandMappingApiController@resendQueue'); //导出标准品牌映射

    //供应链品牌和映射管理
    Route::match(['get', 'post'], '/scmBrand/getScmBrandList', 'ScmBrandApiController@getScmBrandList'); //获取供应链品牌列表
    Route::match(['get', 'post'], '/scmBrandMapping/getMappingBrand', 'ScmBrandMappingApiController@getMappingBrand'); //获取已经映射的品牌
    Route::match(['get', 'post'], '/scmBrandMapping/findNotMappingBrand', 'ScmBrandMappingApiController@findNotMappingBrand'); //获取没有映射的品牌
    Route::match(['get', 'post'], '/scmBrandMapping/addScmBrandMapping', 'ScmBrandMappingApiController@addScmBrandMapping'); //新增供应链品牌映射
    Route::match(['get', 'post'], '/scmBrandMapping/deleteScmBrandMapping', 'ScmBrandMappingApiController@deleteScmBrandMapping'); //删除供应链品牌映射
    Route::match(['get', 'post'], '/scmBrandMapping/getScmBrandMappingList', 'ScmBrandMappingApiController@getScmBrandMappingList'); //获取供应链品牌映射列表
    Route::match(['get', 'post'], '/scmBrand/exportScmBrand', 'ScmBrandApiController@exportScmBrand'); //导出供应链品牌

    //sku管理
    Route::match(['get', 'post'], '/sku/getSkuList', 'SkuApiController@getSkuList'); //获取SKU列表
    Route::match(['get', 'post'], '/sku/getChooseSkuClass', 'SkuApiController@getChooseSkuClass'); //获取选择SKU分类
    Route::match(['get', 'post'], '/sku/batchUpdateSkuStatus', 'SkuApiController@batchUpdateSkuStatus'); //上下架SKU
    Route::match(['get', 'post'], '/sku/exportSku', 'SkuApiController@exportSku'); //导出sku
    Route::match(['get', 'post'], '/sku/saveSku', 'SkuApiController@saveSku'); //新增编辑SKU
    Route::match(['get', 'post'], '/sku/updateSkuStock', 'SkuApiController@updateSkuStock'); //修改SKU库存
    Route::match(['get', 'post'], '/sku/getSkuLabelList', 'SkuApiController@getSkuLabelList'); //sku分区列表
    Route::match(['get', 'post'], '/sku/saveSkuLabel', 'SkuApiController@saveSkuLabel'); //保存sku分区
    Route::match(['get', 'post'], '/sku/updateSku', 'SkuApiController@updateSku'); //批量修改SKU信息
    Route::match(['get', 'post'], '/sku/downloadSkuUpdateResult', 'SkuApiController@downloadSkuUpdateResult'); //下载SPU更新分类结果
    Route::match(['get', 'post'], '/shopDistributeSku/batchSaveShopDistributeSkuByFilter', 'ShopDistributeSkuApiController@batchSaveShopDistributeSkuByFilter'); //批量保存店铺分配SKU

    //spu管理
    Route::match(['get', 'post'], '/spu/getSpuInfo', 'SpuApiController@getSpuInfo'); //获取SPU信息
    Route::match(['get', 'post'], '/spu/getSpuList', 'SpuApiController@getSpuList'); //获取SPU列表
    Route::match(['get', 'post'], '/spu/saveSpu', 'SpuApiController@saveSpu'); //保存SPU
    Route::match(['get', 'post'], '/spu/offShelf', 'SpuApiController@offShelf'); //上下架SPU
    Route::match(['get', 'post'], '/spu/updateSpuClass', 'SpuApiController@updateSpuClass'); //更新SPU分类信息
    Route::match(['get', 'post'], '/spu/downloadSpuUpdateClassResult', 'SpuApiController@downloadSpuUpdateClassResult'); //下载SPU更新分类结果
    Route::match(['get', 'post'], '/spu/updateSpuAttr', 'SpuApiController@updateSpuAttr'); //更新SPU参数
    Route::match(['get', 'post'], '/spu/downloadSpuUpdateAttrResult', 'SpuApiController@downloadSpuUpdateAttrResult'); //下载SPU更新参数结果
    Route::match(['get', 'post'], '/spu/exportSpu', 'SpuApiController@exportSpu'); //导出SPU
    Route::match(['get', 'post'], '/spu/exportSpuAttr', 'SpuApiController@exportSpuAttr'); //导出SPU参数
    Route::match(['get', 'post'], '/spu/updateSpuPDF', 'SpuApiController@updateSpuPDF'); //更新SPU的PDF
    Route::match(['get', 'post'], '/spu/downloadSpuUpdatePDFResult', 'SpuApiController@downloadSpuUpdatePDFResult'); //下载SPU更新PDF结果
    Route::match(['get', 'post'], '/spu/deleteSpu', 'SpuApiController@deleteSpu'); //删除spu
    Route::match(['get', 'post'], '/spu/getSpuListForXmSelect', 'SpuApiController@getSpuListForXmSelect'); //获取SPU列表给筛选

    //分类管理
    Route::match(['get', 'post'], '/class/getClassList', 'ClassApiController@getClassList'); //获取分类列表
    Route::match(['get', 'post'], '/class/getClassSubList', 'ClassApiController@getClassSubList'); //获取子分类列表
    Route::match(['get', 'post'], '/class/saveClass', 'ClassApiController@saveClass'); //保存分类
    Route::match(['get', 'post'], '/class/updateClassSort', 'ClassApiController@updateClassSort'); //更新分类排序
    Route::match(['get', 'post'], '/class/updateShowName', 'ClassApiController@updateShowName'); //更新分类展示名称
    Route::match(['get', 'post'], '/class/hideClass', 'ClassApiController@hideClass'); //隐藏分类
    Route::match(['get', 'post'], '/class/downloadAttrImportTemplate', 'ClassApiController@downloadAttrImportTemplate'); //下载分类导入模板
    Route::match(['get', 'post'], '/class/setMroType', 'ClassApiController@setMroType'); //设置mro分类
    Route::match(['get', 'post'], '/class/updateClassType', 'ClassApiController@updateClassType'); //修改分类类型
    Route::match(['get', 'post'], '/class/export', 'ClassApiController@exportClass'); //导出分类

    //分类属性管理
    Route::match(['get', 'post'], '/classAttr/classAttrList', 'ClassAttrApiController@classAttrList'); //获取分类属性列表
    Route::match(['get', 'post'], '/classAttr/getClassAttrList', 'ClassAttrApiController@getClassAttrList'); //获取分类属性列表(保存页面)
    Route::match(['get', 'post'], '/classAttr/saveClassAttr', 'ClassAttrApiController@saveClassAttr'); //保存分类属性
    Route::match(['get', 'post'], '/classAttr/updateShowName', 'ClassAttrApiController@updateShowName'); //更新分类属性展示名称
    Route::match(['get', 'post'], '/classAttr/updateIsMain', 'ClassAttrApiController@updateIsMain'); //更新是否为主要属性
    Route::match(['get', 'post'], '/classAttr/hideClassAttr', 'ClassAttrApiController@hideClassAttr'); //隐藏分类属性
    Route::match(['get', 'post'], '/classAttr/getClassAttrListByClassId', 'ClassAttrApiController@getClassAttrListByClassId'); //隐藏分类属性
    Route::match(['get', 'post'], '/classAttr/extractAttr', 'ClassAttrApiController@extractAttr'); //校验并且提取参数

    //分类属性值管理
    Route::match(['get', 'post'], '/classAttrValue/getClassAttrValueList', 'ClassAttrValueApiController@getClassAttrValueList'); //获取分类属性值列表
    Route::match(['get', 'post'], '/classAttrValue/saveClassAttrValue', 'ClassAttrValueApiController@saveClassAttrValue'); //保存分类属性值
    Route::match(['get', 'post'], '/classAttrValue/hideClassAttrValue', 'ClassAttrValueApiController@hideClassAttrValue'); //隐藏分类属性值

    //分类属性单位管理
    Route::match(['get', 'post'], '/classAttrUnit/classAttrUnitList', 'ClassAttrUnitApiController@classAttrUnitList'); //获取分类属性单位列表
    Route::match(['get', 'post'], '/classAttrUnit/getClassAttrUnitList', 'ClassAttrUnitApiController@getClassAttrUnitList'); //获取分类属性单位列表(保存页面)
    Route::match(['get', 'post'], '/classAttrUnit/saveClassAttrUnit', 'ClassAttrUnitApiController@saveClassAttrUnit'); //保存分类属性单位
    Route::match(['get', 'post'], '/classAttrUnit/hideClassAttrUnit', 'ClassAttrUnitApiController@hideClassAttrUnit'); //隐藏分类属性单位
    Route::match(['get', 'post'], '/classAttrUnit/saveClassAttrValueUnitConversion', 'ClassAttrUnitApiController@saveClassAttrValueUnitConversion'); //保存分类属性单位换算
    Route::match(['get', 'post'], '/classAttrUnit/hideClassAttrUnitConversion', 'ClassAttrUnitApiController@hideClassAttrUnitConversion'); //隐藏分类属性单位换算
    Route::match(['get', 'post'], '/classAttrUnitConvert/getClassAttrUnitConvertList', 'ClassAttrUnitConvertApiController@getClassAttrUnitConvertList'); //获取分类属性单位换算列表
    Route::match(['get', 'post'], '/classAttrUnitConvert/saveClassAttrUnitConvert', 'ClassAttrUnitConvertApiController@saveClassAttrUnitConvert'); //保存分类属性单位换算
    Route::match(['get', 'post'], '/classAttrUnitConvert/changeClassAttrUnitConvertStatus', 'ClassAttrUnitConvertApiController@changeClassAttrUnitConvertStatus'); //修改分类属性单位换算状态

    //供应商管理
    Route::match(['get', 'post'], '/supplier/getSupplierList', 'SupplierApiController@getSupplierList'); //获取供应商列表
    Route::match(['get', 'post'], '/supplier/saveSupplier', 'SupplierApiController@saveSupplier'); //保存编辑供应商
    Route::match(['get', 'post'], '/supplier/saveSupplierSeo', 'SupplierApiController@saveSupplierSeo'); //保存编辑供应商Seo
    Route::match(['get', 'post'], '/supplier/saveSuppExtendFee', 'SupplierApiController@saveSuppExtendFee'); //修改供应商附加费
    Route::match(['get', 'post'], '/supplier/getSupplierRatioList', 'SupplierApiController@getSupplierRatioList'); //获取供应商系数列表
    Route::match(['get', 'post'], '/supplier/saveSupplierRatio', 'SupplierApiController@saveSupplierRatio'); //修改供应商系数
    Route::match(['get', 'post'], '/supplier/saveSupplierRatioStatus', 'SupplierApiController@saveSupplierRatioStatus'); //修改供应商系数状态(禁用启用)
    Route::match(['get', 'post'], '/supplier/disableSupplierRatio', 'SupplierApiController@disableSupplierRatio'); //禁用供应商系数
    Route::match(['get', 'post'], '/supplier/deleteSupplierRatio', 'SupplierApiController@deleteSupplierRatio'); //删除供应商系数

    //国产替代料
    Route::match(['get', 'post'], '/alikeSpu/getAlikeSpuList', 'AlikeSpuApiController@getAlikeSpuList'); //替代spu列表
    Route::match(['get', 'post'], '/alikeSpu/uploadAlikeSpu', 'AlikeSpuApiController@uploadAlikeSpu'); //上次替代spu
    Route::match(['get', 'post'], '/alikeSpu/uploadLog', 'AlikeSpuApiController@uploadLog'); //替代spu上次日志
    Route::match(['get', 'post'], '/alikeSpu/deleteAlikeSpu', 'AlikeSpuApiController@deleteAlikeSpu'); //删除替代spu
    Route::match(['get', 'post'], '/alikeSpu/getAlikeSpuItems', 'AlikeSpuApiController@getAlikeSpuItems'); //替代spu明细列表
    Route::match(['get', 'post'], '/alikeSpu/deleteAlikeSpuItem', 'AlikeSpuApiController@deleteAlikeSpuItem'); //删除替代明细
    Route::match(['get', 'post'], '/alikeSpu/reloadData', 'AlikeSpuApiController@reloadData'); //重新跑替代spu数据
    Route::match(['get', 'post'], '/alikeSpu/export', 'AlikeSpuApiController@export'); //导出替代SPU数据
    Route::match(['get', 'post'], '/alikeSpu/getAlikeSpuUploadLogList', 'AlikeSpuApiController@getAlikeSpuUploadLogList'); //获取上传记录
    Route::match(['get', 'post'], '/alikeSpu/deleteSpuSku', 'AlikeSpuApiController@deleteSpuSku'); //删除被替代商品特定的sku
    Route::match(['get', 'post'], '/alikeSpu/deleteAlikeSpuSku', 'AlikeSpuApiController@deleteAlikeSpuSku'); //删除国产替代商品特定的sku
    Route::match(['get', 'post'], '/alikeSpu/downloadUploadResult', 'AlikeSpuApiController@downloadUploadResult'); //下载替代SPU上传结果

    //样品管理
    Route::match(['get', 'post'], '/sample/getSampleList', 'SampleApiController@getSampleList'); //获取样品列表
    Route::match(['get', 'post'], '/sample/uploadSample', 'SampleApiController@uploadSample'); //上传样品
    Route::match(['get', 'post'], '/sample/saveSample', 'SampleApiController@saveSample'); //上传样品
    Route::match(['get', 'post'], '/sample/deleteSample', 'SampleApiController@deleteSample'); //删除样品
    Route::match(['get', 'post'], '/sample/getSampleUploadLogList', 'SampleApiController@getSampleUploadLogList'); //样品上传日志
    Route::match(['get', 'post'], '/sample/downloadUploadResult', 'SampleApiController@downloadUploadResult'); //样品上传日志


    //样品分类管理
    Route::match(['get', 'post'], '/sampleClass/getSampleClassList', 'SampleClassApiController@getSampleClassList'); //获取样品分类列表
    Route::match(['get', 'post'], '/sampleClass/saveSampleClass', 'SampleClassApiController@saveSampleClass'); //保存样品分类
    Route::match(['get', 'post'], '/sampleClass/deleteSampleClass', 'SampleClassApiController@deleteSampleClass'); //删除样品分类

    //任务日志接口
    Route::match(['get', 'post'], '/taskLog/getTaskLogList', 'TaskLogApiController@getTaskLogList'); //获取任务日志列表

    //导出相关
    Route::match(['get', 'post'], '/spu/exportSpuList', 'SpuApiController@exportSpuList'); //spu列表导出
    Route::match(['get', 'post'], '/spu/exportSpuAttr', 'SpuApiController@exportSpuAttr'); //spu属性导出
    Route::match(['get', 'post'], '/spu/exportSpuUniqueList', 'SpuApiController@exportSpuUniqueList'); //spu去重列表导出
    Route::match(['get', 'post'], '/spu/getSpuBySpuNameAndStandardBrandId', 'SpuApiController@getSpuBySpuNameAndStandardBrandId'); //获取spu信息
    Route::match(['get', 'post'], '/sku/exportSkuList', 'SkuApiController@exportSkuList'); //sku导出

    //搜索屏蔽
    Route::match(['get', 'post'], '/searchForbid/getSearchForbidList', 'SearchForbidApiController@getSearchForbidList'); //获取搜索屏蔽列表
    Route::match(['get', 'post'], '/searchForbid/saveSearchForbid', 'SearchForbidApiController@saveSearchForbid'); //新增修改搜索屏蔽
    Route::match(['get', 'post'], '/searchForbid/deleteSearchForbid', 'SearchForbidApiController@deleteSearchForbid'); //删除搜索屏蔽

    //代理品牌
    Route::match(['get', 'post'], '/agentBrand/getAgentBrandList', 'AgentBrandApiController@getAgentBrandList'); //获取代理品牌列表
    Route::match(['get', 'post'], '/agentBrand/saveAgentBrand', 'AgentBrandApiController@saveAgentBrand'); //获取代理品牌列表
    Route::match(['get', 'post'], '/agentBrand/changeAgentBrandStatus', 'AgentBrandApiController@changeAgentBrandStatus'); //修改代理品牌状态
    Route::match(['get', 'post'], '/agentBrand/getAgentBrandSpuList', 'AgentBrandApiController@getAgentBrandSpuList'); //获取代理品牌产品列表
    Route::match(['get', 'post'], '/agentBrand/saveAgentBrandSpu', 'AgentBrandApiController@saveAgentBrandSpu'); //保存代理品牌产品
    Route::match(['get', 'post'], '/agentBrand/importAgentBrandSpu', 'AgentBrandApiController@importAgentBrandSpu'); //导入代理品牌产品
    Route::match(['get', 'post'], '/agentBrand/deleteAgentBrandSpu', 'AgentBrandApiController@deleteAgentBrandSpu'); //删除代理品牌产品
    Route::match(['get', 'post'], '/agentBrand/exportAgentBrand', 'AgentBrandApiController@exportAgentBrand'); //导出代理品牌

    //第三方商品管理
    Route::match(['get', 'post'], '/shopInfo/getShopInfoList', 'ShopInfoApiController@getShopInfoList'); //获取商店列表
    Route::match(['get', 'post'], '/shopInfo/saveShopInfo', 'ShopInfoApiController@saveShopInfo'); //获取商店列表
    Route::match(['get', 'post'], '/shopInfo/getShopInfoListByPlatform', 'ShopInfoApiController@getShopInfoListByPlatform'); //根据平台获取商店列表
    Route::match(['get', 'post'], '/shopInfo/saveShopConfig', 'ShopInfoApiController@saveShopConfig'); //保存商店配置
    Route::match(['get', 'post'], '/shopInfo/getShopConfigList', 'ShopInfoApiController@getShopConfigList'); //获取商店配置列表

    Route::match(['get', 'post'], '/shopSku/getShopSkuList', 'ShopSkuApiController@getShopSkuList'); //获取商品列表
    Route::match(['get', 'post'], '/shopSku/updateShopSkuStock', 'ShopSkuApiController@updateShopSkuStock'); //更新商品库存
    Route::match(['get', 'post'], '/shopSku/exportShopSku', 'ShopSkuApiController@exportShopSku'); //导出商品库存
    Route::match(['get', 'post'], '/shopSku/importShopSkuStock', 'ShopSkuApiController@importShopSkuStock'); //更新商品库存
    Route::match(['get', 'post'], '/shopSku/fetchShopSku', 'ShopSkuApiController@fetchShopSku'); //拉取商店所有sku
    Route::match(['get', 'post'], '/shopSku/fetchSkuInfo', 'ShopSkuApiController@fetchSkuInfo'); //拉取商店sku
    Route::match(['get', 'post'], '/shopSku/changeCzFlag', 'ShopSkuApiController@changeCzFlag'); //打厂直标
    Route::match(['get', 'post'], '/shopSku/batchUpdateSkuRate', 'ShopSkuApiController@batchUpdateSkuRate'); //导入价格系数

    //外部分类管理
    Route::match(['get', 'post'], '/shopClass/getShopClassMappingList', 'ShopClassApiController@getShopClassMappingList'); //外部分类映射列表
    Route::match(['get', 'post'], '/shopClass/getShopClassByPlatform', 'ShopClassApiController@getShopClassByPlatform'); //根据平台获取分类
    Route::match(['get', 'post'], '/shopClass/saveShopClassMapping', 'ShopClassApiController@saveShopClassMapping'); //保存平台分类映射
    Route::match(['get', 'post'], '/shopClass/deleteShopClassMapping', 'ShopClassApiController@deleteShopClassMapping'); //删除平台分类映射
    Route::match(['get', 'post'], '/shopClass/getShopClassList', 'ShopClassApiController@getShopClassList'); //获取分类列表
    //外部品牌管理
    Route::match(['get', 'post'], '/shopBrand/getShopBrandMappingList', 'ShopBrandApiController@getShopBrandMappingList'); //外部品牌映射列表
    Route::match(['get', 'post'], '/shopBrand/getShopBrandByPlatform', 'ShopBrandApiController@getShopBrandByPlatform'); //根据平台获取品牌
    Route::match(['get', 'post'], '/shopBrand/saveShopBrandMapping', 'ShopBrandApiController@saveShopBrandMapping'); //保存平台品牌映射
    Route::match(['get', 'post'], '/shopBrand/deleteShopBrandMapping', 'ShopBrandApiController@deleteShopBrandMapping'); //删除平台品牌映射
    Route::match(['get', 'post'], '/shopBrand/saveShopBrand', 'ShopBrandApiController@saveShopBrand'); //新增平台品牌
    Route::match(['get', 'post'], '/shopBrand/getShopBrandList', 'ShopBrandApiController@getShopBrandList'); //获取平台品牌列表
    Route::match(['get', 'post'], '/shopBrand/importShopBrandMapping', 'ShopBrandApiController@importShopBrandMapping'); //导入平台品牌映射

    //数据分发规则管理
    Route::match(['get', 'post'], '/shopDistributeRule/getShopDistributeRuleList', 'ShopDistributeRuleApiController@getShopDistributeRuleList'); //数据分发规则列表
    Route::match(['get', 'post'], '/shopDistributeRule/saveShopDistributeRule', 'ShopDistributeRuleApiController@saveShopDistributeRule'); //保存数据分发规则
    Route::match(['get', 'post'], '/shopDistributeRule/updateDistributeRuleStatus', 'ShopDistributeRuleApiController@updateDistributeRuleStatus'); //禁用/启用分发规则
    Route::match(['get', 'post'], '/shopDistributeRule/deleteShopDistributeRule', 'ShopDistributeRuleApiController@deleteShopDistributeRule'); //删除数据分发规则

    //SPU分发规则管理
    Route::match(['get', 'post'], '/shopDistributeRuleSpu/getShopDistributeRuleSpuList', 'ShopDistributeRuleSpuApiController@getShopDistributeRuleSpuList'); //SPU分发规则列表
    Route::match(['get', 'post'], '/shopDistributeRuleSpu/saveShopDistributeRuleSpu', 'ShopDistributeRuleSpuApiController@saveShopDistributeRuleSpu'); //保存SPU分发规则
    Route::match(['get', 'post'], '/shopDistributeRuleSpu/updateDistributeRuleSpuStatus', 'ShopDistributeRuleSpuApiController@updateDistributeRuleSpuStatus'); //禁用/启用SPU分发规则
    Route::match(['get', 'post'], '/shopDistributeRuleSpu/deleteShopDistributeRuleSpu', 'ShopDistributeRuleSpuApiController@deleteShopDistributeRuleSpu'); //删除SPU分发规则

    //分发价格设置
    Route::match(['get', 'post'], '/distributePrice/getDistributePriceList', 'DistributePriceApiController@getDistributePriceList'); //分发价格列表
    Route::match(['get', 'post'], '/distributePrice/saveDistributePrice', 'DistributePriceApiController@saveDistributePrice'); //保存分发价格
    Route::match(['get', 'post'], '/distributePrice/updateStatus', 'DistributePriceApiController@updateStatus'); //修改状态
    Route::match(['get', 'post'], '/distributePrice/deleteDistributePrice', 'DistributePriceApiController@deleteDistributePrice'); //删除分发价格

    //客户价格分发设置
    Route::match(['get', 'post'], '/distributeUserPrice/getDistributeUserPriceList', 'DistributeUserPriceApiController@getDistributeUserPriceList'); //客户价格分发列表
    Route::match(['get', 'post'], '/distributeUserPrice/saveDistributeUserPrice', 'DistributeUserPriceApiController@saveDistributeUserPrice'); //保存客户价格分发
    Route::match(['get', 'post'], '/distributeUserPrice/updateStatus', 'DistributeUserPriceApiController@updateStatus'); //修改状态
    Route::match(['get', 'post'], '/distributeUserPrice/deleteDistributeUserPrice', 'DistributeUserPriceApiController@deleteDistributeUserPrice'); //删除客户价格分发

    //履约能力规则
    Route::match(['get', 'post'], '/abilityLevelRule/getAbilityLevelRuleList', 'AbilityLevelRuleApiController@getAbilityLevelRuleList'); //履约能力规则列表

    //分类管理
    Route::match(['get', 'post'], '/classManagement/getClassManagementList', 'ClassManagementApiController@getClassManagementList'); //获取分类管理列表
    Route::match(['get', 'post'], '/classManagement/saveClassManagement', 'ClassManagementApiController@saveClassManagement'); //保存分类管理
    Route::match(['get', 'post'], '/classManagement/updateStatus', 'ClassManagementApiController@updateStatus'); //更新状态
    Route::match(['get', 'post'], '/classManagement/deleteClassManagement', 'ClassManagementApiController@deleteClassManagement'); //删除分类管理
    Route::match(['get', 'post'], '/abilityLevelRule/saveAbilityLevelRule', 'AbilityLevelRuleApiController@saveAbilityLevelRule'); //保存履约能力规则
    Route::match(['get', 'post'], '/abilityLevelRule/updateStatus', 'AbilityLevelRuleApiController@updateStatus'); //修改状态
    Route::match(['get', 'post'], '/abilityLevelRule/deleteAbilityLevelRule', 'AbilityLevelRuleApiController@deleteAbilityLevelRule'); //删除履约能力规则
    Route::match(['get', 'post'], '/abilityLevelRule/checkSupplierCodeExists', 'AbilityLevelRuleApiController@checkSupplierCodeExists'); //检查供应商编码是否存在
    //数据推送选品管理
    Route::match(['get', 'post'], '/shopDistributeSku/getShopDistributeSkuList', 'ShopDistributeSkuApiController@getShopDistributeSkuList'); //选品列表
    Route::match(['get', 'post'], '/shopDistributeSku/batchSaveShopDistributeSku', 'ShopDistributeSkuApiController@batchSaveShopDistributeSku'); //批量设置选品
    Route::match(['get', 'post'], '/shopDistributeSku/deleteShopDistributeSku', 'ShopDistributeSkuApiController@deleteShopDistributeSku'); //删除选品
    Route::match(['get', 'post'], '/shopDistributeSku/exportShopDistributeSku', 'ShopDistributeSkuApiController@exportShopDistributeSku'); //导出选品
    Route::match(['get', 'post'], '/shopDistributeSku/importShopDistributeSku', 'ShopDistributeSkuApiController@importShopDistributeSku'); //导入选品
    Route::match(['get', 'post'], '/shopDistributeSku/updateShopDistributeSku', 'ShopDistributeSkuApiController@updateShopDistributeSku'); //更新选品
    //外部单位映射管理
    Route::match(['get', 'post'], '/shopUnit/getShopUnitMappingList', 'ShopUnitApiController@getShopUnitMappingList'); //外部单位映射列表
    Route::match(['get', 'post'], '/shopUnit/getShopUnitListByPlatform', 'ShopUnitApiController@getShopUnitListByPlatform'); //根据平台获取单位列表
    Route::match(['get', 'post'], '/shopUnit/saveShopUnitMapping', 'ShopUnitApiController@saveShopUnitMapping'); //保存外部单位映射
    Route::match(['get', 'post'], '/shopUnit/deleteShopUnitMapping', 'ShopUnitApiController@deleteShopUnitMapping'); //删除外部单位映射
    Route::match(['get', 'post'], '/shopUnit/getNotMappingShopUnitList', 'ShopUnitApiController@getNotMappingShopUnitList'); //获取未映射单位列表

    //外部参数管理
    Route::match(['get', 'post'], '/shopAttr/getShopAttrMappingList', 'ShopAttrApiController@getShopAttrMappingList'); //外部参数映射列表
    Route::match(['get', 'post'], '/shopAttr/getShopAttrByPlatform', 'ShopAttrApiController@getShopAttrByPlatform'); //根据平台获取参数
    Route::match(['get', 'post'], '/shopAttr/saveShopAttrMapping', 'ShopAttrApiController@saveShopAttrMapping'); //保存平台参数映射
    Route::match(['get', 'post'], '/shopAttr/saveShopAttrMappingItem', 'ShopAttrApiController@saveShopAttrMappingItem'); //保存平台参数映射
    Route::match(['get', 'post'], '/shopAttr/deleteShopAttrMapping', 'ShopAttrApiController@deleteShopAttrMapping'); //删除平台参数映射
    Route::match(['get', 'post'], '/shopAttr/getAttrsByShopClassId', 'ShopAttrApiController@getAttrsByShopClassId'); //根据分类获取所有参数(猎芯自己的和第三方)
    Route::match(['get', 'post'], '/shopAttr/checkShopAttrChangeByClassMappingIds', 'ShopAttrApiController@checkShopAttrChangeByClassMappingIds'); //判断分类映射下是否有被修改的参数
    Route::match(['get', 'post'], '/shopAttr/getShopAttrList', 'ShopAttrApiController@getShopAttrList'); //获取平台参数列表

    //外部sku日志管理
    Route::match(['get', 'post'], '/shopSkuLog/getShopSkuPushLogList', 'ShopSkuLogApiController@getShopSkuPushLogList'); //获取外部sku推送日志列表
    Route::match(['get', 'post'], '/shopSkuLog/exportShopSkuPushLogList', 'ShopSkuLogApiController@exportShopSkuPushLogList'); //sku推送日志列表导出

    //外部spu日志管理
    Route::match(['get', 'post'], '/shopSpuLog/getShopSpuPushLogList', 'ShopSpuLogApiController@getShopSpuPushLogList'); //获取外部spu推送日志列表
    Route::match(['get', 'post'], '/shopSpuLog/exportShopSpuPushLogList', 'ShopSpuLogApiController@exportShopSpuPushLogList'); //spu推送日志列表导出

    //商品系列
    Route::match(['get', 'post'], '/skuSeries/getSkuSeriesList', 'SkuSeriesApiController@getSkuSeriesList'); //获取商品系列列表
    Route::match(['get', 'post'], '/skuSeries/checkSkuSeries', 'SkuSeriesApiController@checkSkuSeries'); //获取商品系列列表
    Route::match(['get', 'post'], '/skuSeries/saveSkuSeries', 'SkuSeriesApiController@saveSkuSeries'); //保存商品系列列表
    Route::match(['get', 'post'], '/skuSeries/changeSkuSeriesStatus', 'SkuSeriesApiController@changeSkuSeriesStatus'); //修改商品系列状态
    Route::match(['get', 'post'], '/skuSeries/checkSkuSeries', 'SkuSeriesApiController@checkSkuSeries'); //检测商品系列
    Route::match(['get', 'post'], '/skuSeries/getSkuSeriesItemList', 'SkuSeriesApiController@getSkuSeriesItemList'); //获取商品系列商品列表
    Route::match(['get', 'post'], '/skuSeries/checkCanBatchUpdateItemSeries', 'SkuSeriesApiController@checkCanBatchUpdateItemSeries'); //检测是否能批量修改商品系列
    Route::match(['get', 'post'], '/skuSeries/batchUpdateItemSeries', 'SkuSeriesApiController@batchUpdateItemSeries'); //批量修改商品的商品系列
    Route::match(['get', 'post'], '/skuSeries/batchDeleteSkuSeriesItem', 'SkuSeriesApiController@batchDeleteSkuSeriesItem'); //检测系列商品
    Route::match(['get', 'post'], '/skuSeries/updateSkuSeriesItem', 'SkuSeriesApiController@updateSkuSeriesItem'); //修改系列商品

    //封装管理
    Route::match(['get', 'post'], '/encap/getEncapList', 'EncapApiController@getEncapList'); //获取封装列表
    Route::match(['get', 'post'], '/encap/disableEncap', 'EncapApiController@disableEncap'); //禁用封装
    Route::match(['get', 'post'], '/encap/changeEncapStatus', 'EncapApiController@changeEncapStatus'); //修改封装状态
    Route::match(['get', 'post'], '/encap/saveEncap', 'EncapApiController@saveEncap'); //保存封装
    Route::match(['get', 'post'], '/encap/searchEncap', 'EncapApiController@searchEncap'); //搜索封装
    Route::match(['get', 'post'], '/encap/getEncapListForSelect', 'EncapApiController@getEncapListForSelect'); //筛选框封装
    Route::match(['get', 'post'], '/encap/exportEncaps', 'EncapApiController@exportEncaps'); //导出封装
    Route::match(['get', 'post'], '/encap/updateIgnoreHandle', 'EncapApiController@updateIgnoreHandle'); //修改是否处理

    //标准封装管理
    Route::match(['get', 'post'], '/standardEncap/getStandardEncapList', 'StandardEncapApiController@getStandardEncapList'); //获取标准封装列表
    Route::match(['get', 'post'], '/standardEncap/disableStandardEncap', 'StandardEncapApiController@disableStandardEncap'); //禁用标准封装
    Route::match(['get', 'post'], '/standardEncap/enableStandardEncap', 'StandardEncapApiController@enableStandardEncap'); //启用标准封装
    Route::match(['get', 'post'], '/standardEncap/getMainSpuList', 'StandardEncapApiController@getMainSpuList'); //获取标准封装主要SPU
    Route::match(['get', 'post'], '/standardEncap/saveStandardEncap', 'StandardEncapApiController@saveStandardEncap'); //保存标准封装
    Route::match(['get', 'post'], '/standardEncap/saveStandardEncapSpu', 'StandardEncapApiController@saveStandardEncapSpu'); //保存标准封装SPU
    Route::match(['get', 'post'], '/standardEncap/deleteStandardEncapSpu', 'StandardEncapApiController@deleteStandardEncapSpu'); //删除标准封装SPU
    Route::match(['get', 'post'], '/standardEncap/importStandardEncapSpu', 'StandardEncapApiController@importStandardEncapSpu'); //导入封装映射SPU
    Route::match(['get', 'post'], '/standardEncap/exportStandardEncap', 'StandardEncapApiController@exportStandardEncap'); //导出标准封装
    Route::match(['get', 'post'], '/standardEncap/searchStandardEncap', 'StandardEncapApiController@searchStandardEncap'); //搜索标准封装
    Route::match(['get', 'post'], '/standardEncap/getStandardEncapById', 'StandardEncapApiController@getStandardEncapById'); //获取单个标品的信息
    Route::match(['get', 'post'], '/standardEncap/getEncapByClassId', 'StandardEncapApiController@getEncapByClassId'); //根据分类id获取普通封装和标准封装

    //标准封装映射管理
    Route::match(['get', 'post'], '/standardEncapMapping/getStandardEncapMappingList', 'StandardEncapMappingApiController@getStandardEncapMappingList'); //获取标准封装映射列表
    Route::match(['get', 'post'], '/standardEncapMapping/findNotMappingEncap', 'StandardEncapMappingApiController@findNotMappingEncap'); //获取没有映射的标准封装
    Route::match(['get', 'post'], '/standardEncapMapping/addStandardEncapMapping', 'StandardEncapMappingApiController@addStandardEncapMapping'); //新增标准封装映射
    Route::match(['get', 'post'], '/standardEncapMapping/deleteStandardEncapMapping', 'StandardEncapMappingApiController@deleteStandardEncapMapping'); //删除标准封装映射
    Route::match(['get', 'post'], '/standardEncapMapping/importStandardEncapMapping', 'StandardEncapMappingApiController@importStandardEncapMapping'); //导入标准封装映射
    Route::match(['get', 'post'], '/standardEncapMapping/exportStandardEncapMapping', 'StandardEncapMappingApiController@exportStandardEncapMapping'); //导出标准封装映射

    //训练数据管理
    Route::match(['get', 'post'], '/trainingData/getTrainingDataList', 'TrainingDataApiController@getTrainingDataList'); //获取训练数据列表
    Route::match(['get', 'post'], '/trainingData/saveTrainingData', 'TrainingDataApiController@saveTrainingData'); //保存训练数据
    Route::match(['get', 'post'], '/trainingData/deleteTrainingData', 'TrainingDataApiController@deleteTrainingData'); //删除训练数据
    Route::match(['get', 'post'], '/trainingData/exportTrainingData', 'TrainingDataApiController@exportTrainingData'); //导出训练数据
    Route::match(['get', 'post'], '/trainingData/importBrandData', 'TrainingDataApiController@importBrandData'); //导入品牌数据
    Route::match(['get', 'post'], '/trainingData/importModelData', 'TrainingDataApiController@importModelData'); //导入型号数据
    Route::match(['get', 'post'], '/trainingData/importCategoryData', 'TrainingDataApiController@importCategoryData'); //导入分类数据
    Route::match(['get', 'post'], '/trainingData/getTrainingDataById', 'TrainingDataApiController@getTrainingDataById'); //获取单个训练数据信息

    //spu日志管理
    Route::match(['get', 'post'], '/spuLog/getSpuLogList', 'SpuLogApiController@getSpuLogList'); //spu日志列表

    //操作日志管理
    Route::match(['get', 'post'], '/operationLog/getOperationLogList', 'OperationLogApiController@getOperationLogList'); //操作日志列表
});
