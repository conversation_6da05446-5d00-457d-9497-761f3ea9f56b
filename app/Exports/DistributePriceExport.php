<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use App\Http\Services\DistributePriceService;

class DistributePriceExport implements FromArray, WithHeadings
{
    protected $params;

    public function __construct($params)
    {
        $this->params = $params;
    }

    /**
     * @return array
     */
    public function array(): array
    {
        $result = (new DistributePriceService())->getDistributePriceList($this->params);
        return $result['data'] ?? [];
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            '平台',
            '店铺分类',
            '二级分类',
            '图片',
            '是否保修',
            '保修期(月)',
            'JD标题属性ID'
        ];
    }
}
