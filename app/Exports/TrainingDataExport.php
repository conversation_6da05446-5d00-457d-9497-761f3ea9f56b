<?php

namespace App\Exports;

use App\Http\Models\TrainingDataModel;
use App\Http\Services\TrainingDataService;
use Maatwebsite\Excel\Concerns\FromArray;

class TrainingDataExport implements FromArray
{
    protected $map;

    public function __construct($map)
    {
        $this->map = $map;
    }

    public function array(): array
    {
        return $this->createData();
    }

    public function createData()
    {
        $model = new TrainingDataModel();
        $excelData = [];
        $header = [
            'ID',
            '内容',
            '类型',
            '创建时间',
            '创建人',
            '更新时间',
            '更新人'
        ];
        $query = $model->orderBy('id', 'desc');
        $query = (new TrainingDataService())->filter($query, $this->map);
        $result = $query->get()->toArray();
        foreach ($result as $item) {
            $excelData[] = [
                $item['id'],
                $item['content'],
                TrainingDataModel::TYPE_NAMES[$item['type']] ?? '',
                $item['create_time'] ? date('Y-m-d H:i:s', $item['create_time']) : '',
                $item['create_name'],
                $item['update_time'] ? date('Y-m-d H:i:s', $item['update_time']) : '',
                $item['update_name'],
            ];
        }
        array_unshift($excelData, $header);

        return $excelData;
    }
}
