<?php

namespace App\Exports;

use App\Http\Models\AlikeSpuModel;
use App\Http\Models\BrandModel;
use App\Http\Models\StandardBrandModel;
use App\Http\Services\BrandService;
use App\Http\Transformers\BrandTransformer;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Illuminate\Support\Facades\Redis;

class BrandExport implements FromCollection
{

    public $map;

    public function __construct($map)
    {
        $this->map = $map;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return new Collection($this->createData());
    }

    public function createData()
    {
        ini_set('memory_limit', -1);
        $model = new BrandModel();
        $query = $model->orderBy('brand_id', 'desc');
        $query = (new BrandService())->filter($query, $this->map);
        $excelData = [];
        $redis = Redis::connection('sku');
        $brandSkuNum = $redis->hgetall('hhs_brand_id_num');
        $brandMapping = $redis->hgetall('standard_brand_mapping');
        $standardBrands = $redis->hgetall('standard_brand');
        $result = $query->select([
            'brand_id',
            'brand_name',
            'insert_type',
            'brand_logo',
            'create_time',
            'is_unusual',
            'status',
            'ignore_handle',
        ])->chunk(
            10000,
            function ($brands) use (&$excelData, $redis, $brandSkuNum, $brandMapping, $standardBrands) {
                $brands = $brands->toArray();
                foreach ($brands as $key => $brand) {
                    $standardBrandId = Arr::get($brandMapping, $brand['brand_id']);
                    $standardBrandName = '';
                    if ($standardBrandId) {
                        $standardBrand = Arr::get($standardBrands, $standardBrandId);
                        if ($standardBrand) {
                            $standardBrand = json_decode($standardBrand, true);
                            $standardBrandName = $standardBrand['brand_name'];
                        }
                    }
                    $hasMapping = $redis->hget('standard_brand_mapping', $brand['brand_id']);
                    $excelData[] = [
                        $brand['brand_id'],
                        $brand['brand_name'],
                        $standardBrandName,
                        $hasMapping ? '是' : '否',
                        $brand['is_unusual'] == 0 ? '正常' : '异常',
                        $brand['ignore_handle'] == 1 ? '是' : '否',
                        $brand['insert_type_name'] = Arr::get(
                            config('field.BrandInsertType'),
                            $brand['insert_type'],
                            '未知状态'
                        ),
                        '是否有图片 : ' . (!empty($brand['brand_logo']) ? "有" : "无") . ' | 商品数量 : ' . Arr::get(
                            $brand,
                            'sku_number',
                            0
                        ),
                        $brand['brand_logo'] ? '有' : '无',
                        $brand['create_time'] ? date('Y-m-d H:i:s', $brand['create_time']) : '',
                        $brand['status'] == 3 ? '是' : '否',
                    ];
                }
            }
        );
        $header = ['品牌ID', '品牌名称', '标准品牌', '是否有映射', '异常状态', '是否忽略处理', '创建来源', '数据情况', '图片', '创建时间', '是否隐藏'];
        array_unshift($excelData, $header);
        return $excelData;
    }
}
