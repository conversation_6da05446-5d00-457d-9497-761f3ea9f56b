<?php

namespace App\Exports;

use App\Http\Models\AlikeSpuModel;
use App\Http\Models\StandardBrandModel;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;

class AlikeSpuUploadResultExport implements FromCollection, WithColumnWidths
{

    public function __construct($resultData)
    {
        $this->resultData = $resultData;
    }

    public function collection()
    {
        return new Collection($this->createData());
    }

    public function createData()
    {
        return $this->resultData;
    }

    public function columnWidths(): array
    {
        return [
            'A' => 30, // 第一列宽度
            'B' => 20, // 第二列宽度
            'C' => 20, // 第三列宽度
            'D' => 20, // 第三列宽度
            'E' => 20, // 第三列宽度
            'F' => 20, // 第三列宽度
            'G' => 25, // 第三列宽度
            'H' => 25, // 第三列宽度
            'I' => 25, // 第三列宽度
            'J' => 25, // 第三列宽度
        ];
    }
}
