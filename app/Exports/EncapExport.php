<?php

namespace App\Exports;

use App\Http\Models\EncapModel;
use App\Http\Models\StandardBrandModel;
use App\Http\Models\StandardEncapModel;
use App\Http\Services\BrandService;
use App\Http\Services\EncapService;
use App\Http\Services\StandardBrandService;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;

class EncapExport implements FromCollection
{

    public $map;

    public function __construct($map)
    {
        $this->map = $map;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return new Collection($this->createData());
    }

    public function createData()
    {
        $model = new EncapModel();
        $excelData = [];
        $header = [
            'ID',
            '封装名称',
            '更新时间',
            '创建时间',
            '创建人',
            '更新人'
        ];
        $query = $model->orderBy('encap_id', 'asc');
        $query = (new EncapService())->filter($query, $this->map);
        $result = $query->get()->toArray();
        foreach ($result as $key => $item) {
            $excelData[] = [
                $item['standard_encap_id'],
                $item['encap_name'],
                $item['update_time'] ? date('Y-m-d H:i', $item['update_time']) : '',
                $item['create_time'] ? date('Y-m-d H:i', $item['create_time']) : '',
                $item['create_name'],
                $item['update_name'],
            ];
        }
        array_unshift($excelData, $header);

        return $excelData;
    }
}
