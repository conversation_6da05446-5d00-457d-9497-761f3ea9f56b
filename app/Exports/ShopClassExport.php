<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use App\Http\Services\BrandService;
use App\Http\Models\StandardBrandModel;
use App\Http\Services\ShopClassService;
use App\Http\Services\StandardBrandService;
use App\Http\Services\ShopBrandMappingService;
use App\Http\Services\ShopClassMappingService;
use Maatwebsite\Excel\Concerns\FromCollection;

class ShopClassExport implements FromCollection
{

    public $map;

    public function __construct($map)
    {
        $this->map = $map;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return new Collection($this->createData());
    }

    public function createData()
    {
        $excelData = [];
        $header = [
            '序号',
            '父级ID',
            '分类名称',
            '分类ID',
            '外部平台',
            '状态',
            '创建人',
            '创建时间',
        ];
        $this->map['is_export'] = 1;
        $result = (new ShopClassService())->getShopClassList($this->map);
        foreach ($result as $key => $item) {
            $excelData[] = [
                $item['id'],
                $item['parent_id'],
                $item['class_name'],
                $item['class_id'],
                $item['platform_name'],
                $item['status_name'],
                $item['create_name']??'',
                $item['create_time'],
            ];
        }
        array_unshift($excelData, $header);

        return $excelData;
    }
}
