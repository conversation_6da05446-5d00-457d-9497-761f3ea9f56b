<?php

namespace App\Exports;

use App\Http\Models\StandardBrandModel;
use App\Http\Services\BrandService;
use App\Http\Services\ShopBrandMappingService;
use App\Http\Services\StandardBrandService;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;

class ShopBrandMappingExport implements FromCollection
{

    public $map;

    public function __construct($map)
    {
        $this->map = $map;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return new Collection($this->createData());
    }

    public function createData()
    {
        $service = new ShopBrandMappingService();
        $excelData = [];
        $header = [
            'ID',
            '猎芯品牌ID',
            '猎芯品牌名称',
            '外部平台',
            '内部品牌ID',
            '外部品牌ID',
            '外部平台品牌名称',
            '创建人',
            '创建时间',
        ];
        $this->map['is_export'] = 1;
        $result = $service::getShopBrandMappingList($this->map);
        foreach ($result as $key => $item) {
            $excelData[] = [
                $item['id'],
                $item['lie_brand_id'],
                $item['lie_brand']['brand_name'],
                $item['platform_name'],
                $item['shop_brand_id'],
                $item['shop_brand']['brand_id'],
                $item['shop_brand']['brand_name'],
                $item['create_name'],
                $item['create_time'],
            ];
        }
        array_unshift($excelData, $header);

        return $excelData;
    }
}
