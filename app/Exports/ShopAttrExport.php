<?php

namespace App\Exports;

use App\Http\Services\ShopAttrService;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;

class ShopAttrExport implements FromCollection
{
    public $map;

    public function __construct($map)
    {
        $this->map = $map;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return new Collection($this->createData());
    }

    public function createData()
    {
        $excelData = [];
        $header = [
            '序号',
            '属性名称',
            '属性ID',
            '外部平台',
            '分类ID',
            '录入方式',
            '是否必选',
            '默认单位',
            '状态',
            '是否映射',
            '创建人',
            '创建时间',
        ];
        $this->map['is_export'] = 1;
        $result = ShopAttrService::getShopAttrList($this->map);
        foreach ($result as $key => $item) {
            $excelData[] = [
                $item['id'],
                $item['attr_name'],
                $item['attr_id'],
                $item['platform_name'],
                $item['class_id'],
                $item['input_type_name'],
                $item['is_required_name'],
                $item['default_unit'],
                $item['status_name'],
                $item['is_mapping_name'],
                $item['create_name'] ?? '',
                $item['create_time'],
            ];
        }
        array_unshift($excelData, $header);

        return $excelData;
    }
}
