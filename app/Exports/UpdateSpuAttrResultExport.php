<?php

namespace App\Exports;

use App\Http\Models\AlikeSpuModel;
use App\Http\Models\StandardBrandModel;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;

class UpdateSpuAttrResultExport implements FromCollection,WithColumnWidths
{

    public function __construct($resultData)
    {
        $this->resultData = $resultData;
    }

    public function collection()
    {
        return new Collection($this->createData());
    }

    public function createData()
    {
        return $this->resultData;
    }


    public function columnWidths(): array
    {
        $formats = [];

        foreach (range('A', 'Z') as $column) {
            $formats[$column] = 20;
        }

        return $formats;
    }

}
