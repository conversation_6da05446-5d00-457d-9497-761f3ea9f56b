<?php

namespace App\Exports;

use App\Http\Models\AlikeSpuModel;
use App\Http\Models\StandardBrandModel;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;

class UpdateSkuResultExport implements FromCollection
{

    public function __construct($resultData)
    {
        $this->resultData = $resultData;
    }

    public function collection()
    {
        return new Collection($this->createData());
    }

    public function createData()
    {
        return $this->resultData;
    }


}
