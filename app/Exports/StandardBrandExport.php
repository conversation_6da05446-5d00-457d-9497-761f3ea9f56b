<?php

namespace App\Exports;

use App\Http\Models\StandardBrandModel;
use App\Http\Services\BrandService;
use App\Http\Services\StandardBrandService;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;

class StandardBrandExport implements FromCollection
{

    public $map;

    public function __construct($map)
    {
        $this->map = $map;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return new Collection($this->createData());
    }

    public function createData()
    {
        $model = new StandardBrandModel();
        $excelData = [];
        $header = [
            'ID',
            '品牌名称',
            '英文',
            '中文',
            '地区',
            '状态',
            '是否有图片',
            '更新时间',
            '品牌定位',
            '英文简称',
            '中文简称',
            '网站展示',
            '创建时间',
            '创建人',
            '更新人'
        ];
        $query = $model->orderBy('standard_brand_id', 'asc');
        $query = (new StandardBrandService())->filter($query, $this->map);
        $result = $query->get()->toArray();
        foreach ($result as $key => $item) {
            $excelData[] = [
                $item['standard_brand_id'],
                $item['brand_name'],
                $item['brand_name_en'],
                $item['brand_name_cn'],
                \Arr::get(config('field.StandardBrandArea'), $item['brand_area'], '未知'),
                \Arr::get(config('field.StandardBrandStatus'), $item['status'], '未知状态'),
                !empty($item['brand_logo']) ? '有' : '无',
                $item['update_time'] ? date('Y-m-d H:i', $item['update_time']) : '',
                $item['brand_positioning'],
                $item['brand_short_name_en'],
                $item['brand_short_name_cn'],
                $item['is_show'] == 1 ? '是' : '否',
                $item['create_time'] ? date('Y-m-d H:i', $item['create_time']) : '',
                $item['create_name'],
                $item['update_name'],
            ];
        }
        array_unshift($excelData, $header);

        return $excelData;
    }
}
