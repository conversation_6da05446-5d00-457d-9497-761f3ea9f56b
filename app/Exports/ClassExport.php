<?php

namespace App\Exports;

use App\Http\Models\PoolClass\PoolClassModel;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ClassExport implements FromCollection, WithHeadings
{
    protected $params;

    public function __construct($params)
    {
        $this->params = $params;
    }

    public function collection()
    {
        $query = PoolClassModel::query();

        if (!empty($this->params['class_name'])) {
            $query->where('class_name', 'like', '%' . $this->params['class_name'] . '%');
        }

        if (!empty($this->params['show_name'])) {
            $query->where('show_name', 'like', '%' . $this->params['show_name'] . '%');
        }

        if (isset($this->params['status'])) {
            $query->where('status', $this->params['status']);
        }

        if (isset($this->params['class_type'])) {
            $query->where('class_type', $this->params['class_type']);
        }

        return $query->get()->map(function ($item) {
            return [
                'class_id' => $item->class_id,
                'class_name' => $item->class_name,
                'show_name' => $item->show_name,
                'parent_id' => $item->parent_id,
                'status' => $item->status ? '启用' : '禁用',
                'class_type' => config('field.ClassType')[$item->class_type] ?? '无',
                'sort' => $item->sort,
                'add_time' => !empty($item->add_time) ? date('Y-m-d H:i:s', $item->add_time) : '',
                'update_time' => !empty($item->update_time) ? date('Y-m-d H:i:s', $item->update_time) : ''
            ];
        });
    }

    public function headings(): array
    {
        return [
            '分类ID',
            '分类名称',
            '显示名称',
            '父级ID',
            '状态',
            '分类类型',
            '排序',
            '创建时间',
            '更新时间'
        ];
    }
}
