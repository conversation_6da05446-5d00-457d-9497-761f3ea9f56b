<?php

namespace App\Exports;

use App\Http\Models\AlikeSpuModel;
use App\Http\Models\StandardBrandModel;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;

class AlikeSpuExport implements FromCollection
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return new Collection($this->createData());
    }

    public function createData()
    {
        $model = new AlikeSpuModel();
        $result = AlikeSpuModel::where('is_deleted', 0)->get()->toArray();
        $excelData = [];
        $header = ['国产替代型号', '被替代型号', 'pin to pin', '创建人', '创建时间'];
        foreach ($result as $key => $item) {
            $excelData[] = [
                $item['alike_spu_name'],
                $item['spu_name'],
                $value['pin_to_pin'] = config('field.PinToPinMap')[$item['pin_to_pin']],
                $item['admin_name'],
                date('Y-m-d H:i:s', $item['add_time']),
            ];
        }
        array_unshift($excelData, $header);

        return $excelData;
    }
}
