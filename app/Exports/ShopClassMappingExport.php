<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use App\Http\Services\BrandService;
use App\Http\Models\StandardBrandModel;
use App\Http\Services\StandardBrandService;
use App\Http\Services\ShopBrandMappingService;
use App\Http\Services\ShopClassMappingService;
use Maatwebsite\Excel\Concerns\FromCollection;

class ShopClassMappingExport implements FromCollection
{

    public $map;

    public function __construct($map)
    {
        $this->map = $map;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return new Collection($this->createData());
    }

    public function createData()
    {
        $service = new ShopClassMappingService();
        $excelData = [];
        $header = [
            '序号',
            '猎芯所属组织',
            '猎芯分类ID',
            '猎芯分类',
            '外部平台',
            '外部平台分类ID',
            '外部平台分类名称',
            '内部分类ID',
            '创建人',
            '创建时间',
        ];
        $this->map['is_export'] = 1;
        $result = $service::getShopClassMappingList($this->map);
        foreach ($result as $key => $item) {
            $excelData[] = [
                $item['id'],
                '猎芯',
                $item['lie_class']['class_id'] ?? '',
                $item['lie_class']['class_name'] ?? '',
                $item['platform_name'],
                $item['shop_class_id'],
                $item['shop_class']['class_name'] ?? '',
                $item['create_name'],
                $item['create_time'],
            ];
        }
        array_unshift($excelData, $header);

        return $excelData;
    }
}
