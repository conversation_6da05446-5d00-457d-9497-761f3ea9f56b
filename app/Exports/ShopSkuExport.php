<?php

namespace App\Exports;

use App\Http\Services\SkuService;
use Illuminate\Support\Collection;
use App\Http\Services\ShopSkuService;
use App\Http\Models\StandardBrandModel;
use App\Http\Models\Supplier\SupplierChannelModel;
use App\Http\Services\StandardBrandService;
use Maatwebsite\Excel\Concerns\FromCollection;


class ShopSkuExport implements FromCollection
{

    public $map;

    public function __construct($map)
    {
        $this->map = $map;
    }

    /**
     * @return Collection
     */
    public function collection()
    {
        return new Collection($this->createData());
    }


    public function createData()
    {
        $excelData = [];
        $header = [
            'ID',
            '猎芯SKUID',
            '猎芯分类',
            '猎芯供应商',
            '第三方店铺SKUID',
            '型号',
            '品牌',
            '商品标题',
            '库存',
            '价格',
        ];
        $this->map['is_export'] = 1;
        $result = ShopSkuService::getShopSkuList($this->map);
        $skuIds = array_column($result, 'lx_sku_id');
        $skuList = [];
        $canalList = [];
        foreach ($skuIds as $k => $skuId) {
            $sku = (new SkuService())->getSkuCacheInfo($skuId);
            $skuList[$skuId] = $sku;
            if (!empty($sku['canal'])) {
                $canalList[] = $sku['canal'];
            }
        }
        $canalList = array_unique($canalList);
        $canalMap = [];
        $supplierCodeMap = SupplierChannelModel::whereIn('supplier_code', $canalList)->pluck('supplier_name', 'supplier_code')->toArray();
        foreach ($canalList as $k => $canal) {
            $canalMap[$canal] = $supplierCodeMap[$canal];
        }
        foreach ($result as $key => $item) {
            if (!empty($item['lx_sku_id'])) {
                //补充sku的信息
                $sku = $skuList[$item['lx_sku_id']];
                if (!empty($sku)) {
                    $item['lx_class_name'] = $sku['class_name2'];
                    $item['supplier_name'] = $canalMap[$sku['canal']] ?? '';
                }
            }
            $excelData[] = [
                $item['id'] . "\t",
                $item['lx_sku_id'] . "\t",
                $item['lx_class_name'] ?? '' . "\t",
                $item['supplier_name'] ?? '' . "\t",
                $item['shop_sku_id'] . "\t",
                $item['goods_name'] . "\t",
                $item['brand_name'] . "\t",
                $item['goods_title'],
                $item['stock'] . "\t",
                $item['shop_price'] . "\t",
            ];
        }
        array_unshift($excelData, $header);

        return $excelData;
    }
}
