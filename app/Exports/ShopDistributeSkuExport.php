<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;

class ShopDistributeSkuExport implements FromCollection
{
    protected $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        $header = [
            'SKU ID',
            'SPU名称',
            '分类',
            '标准品牌',
            '京东采购价系数',
            '京东采购价',
            '京东销售价系数',
            '京东销售价',
            '店铺ID',
            '店铺名称',
            '状态',
            '失败原因',
            '创建时间',
            '更新时间'
        ];

        array_unshift($this->data, $header);
        return new Collection($this->data);
    }
}
