<?php

namespace App\Exports;

use App\Http\Models\StandardBrandMappingModel;
use App\Http\Models\StandardBrandModel;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;

class StandardBrandMappingExport implements FromCollection
{

    public $map;

    public function __construct($map)
    {
        $this->map = $map;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return new Collection($this->createData());
    }

    public function createData()
    {
        $this->map = array_filter($this->map,function ($value) {
            return !empty($value);
        });
        $model = new StandardBrandMappingModel();
        $query = $model->select([
            'lie_brand.brand_id',
            'lie_brand.brand_name',
            'lie_brand_standard.standard_brand_id',
            'lie_brand_standard.brand_name as standard_brand_name'
        ])->leftjoin('lie_brand', 'lie_brand_standard_mapping.brand_id', '=', 'lie_brand.brand_id')
            ->leftjoin('lie_brand_standard', 'lie_brand_standard_mapping.standard_brand_id', '=',
                'lie_brand_standard.standard_brand_id');
        if ($this->map) {
            if (!empty($this->map['add_time'])) {
                $time = explode('~', $this->map['add_time']);
                if (count($time) != 2) {
                    return $this;
                }
                $query->whereBetween('add_time',[strtotime($time[0]), strtotime($time[1])]);
                unset($this->map['add_time']);
            }
            if (!empty($this->map['standard_brand_name'])) {
                $query->where('lie_brand_standard.brand_name', $this->map['standard_brand_name']);
            }
            if (!empty($this->map['brand_name'])) {
                $query->where('lie_brand.brand_name', $this->map['brand_name']);
            }
        }
        $result = $query->get()->toArray();
        $excelData = [];
        $header = ['映射品牌Id', '映射品牌名称', '标准品牌Id', '标准品牌名称'];
        foreach ($result as $key => $item) {
            $excelData[] = [
                $item['brand_id'],
                $item['brand_name'],
                $item['standard_brand_id'],
                $item['standard_brand_name'],
            ];
        }
        array_unshift($excelData, $header);

        return $excelData;
    }
}
