<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use App\Http\Services\BrandService;
use App\Http\Models\AgentBrandModel;
use App\Http\Models\StandardBrandModel;
use App\Http\Services\AgentBrandService;
use App\Http\Services\StandardBrandService;
use Maatwebsite\Excel\Concerns\FromCollection;
use App\Http\Transformers\AgentBrandTransformer;

class AgentBrandExport implements FromCollection
{

    public $map;

    public function __construct($map)
    {
        $this->map = $map;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return new Collection($this->createData());
    }

    public function createData()
    {
        $model = new AgentBrandModel();
        $excelData = [];
        $header = [
            'ID',
            '代理品牌名称',
            '英文全称',
            '中文全称',
            // '品牌logo',
            '品牌等级',
            '品牌区域',
            '主营分类',
            '专题页所属分类',
            '供应商编码',
            '品牌官网链接',
            '应用领域',
            '代理证',
            '代理证有效时间',
            'PM负责人',
            '代理产品数量',
            '状态',
            '创建人',
            '创建时间',
            '更新人',
            '更新时间'
        ];
        $query = $model->with('standard_brand')->withCount('spu')->orderBy('id', 'desc');
        $query = (new AgentBrandService())->filter($query, $this->map);
        $result = $query->get()->toArray();
        $result = (new AgentBrandTransformer())->listTransformer($result);
        foreach ($result as $key => $item) {

            $excelData[] = [
                $item['id'],
                isset($item['standard_brand']) ? $item['standard_brand']['brand_name'] : '',
                isset($item['standard_brand']) ? $item['standard_brand']['brand_name_en'] : '',
                isset($item['standard_brand']) ? $item['standard_brand']['brand_name_cn'] : '',
                // isset($item['standard_brand']) ? $item['standard_brand']['brand_logo'] : '',
                $item['brand_level_name'] ?? '',
                $item['brand_area_name'] ?? '',
                $item['main_product_class_name'] ?? '',
                $item['agent_class_name'] ?? '',
                $item['supplier_code'] ?? '',
                isset($item['standard_brand']) ? $item['standard_brand']['brand_brief'] : '',
                $item['application_area_name'] ?? '',
                $item['agent_certificate'] ?? '',
                $item['certificate_effective_time'] ?? '',
                $item['pm_user_name'] ?? '',
                $item['spu_count'] ,
                $item['status'] === 1 ? '启用' : '禁用',
                $item['create_name'] ?? '',
                $item['create_time'] ?? '',
                $item['update_name'] ?? '',
                $item['update_time'] ?? '',
            ];
        }
        array_unshift($excelData, $header);

        return $excelData;
    }
}
