<?php

namespace App\Jobs;

use App\Exceptions\InvalidRequestException;
use App\Http\Utils\Sku;
use App\Imports\AlikeSpuImport;
use Illuminate\Bus\Queueable;
use App\Http\Models\BrandModel;
use App\Http\Models\AlikeSpuModel;
use Illuminate\Support\Facades\DB;
use App\Http\Models\UploadLogModel;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Models\AlikeSpuLogModel;
use Illuminate\Support\Facades\Redis;
use Illuminate\Queue\SerializesModels;
use App\Http\Models\StandardBrandModel;
use App\Http\Models\AlikeSpuCenterModel;
use Illuminate\Queue\InteractsWithQueue;
use App\Exports\AlikeSpuUploadResultExport;
use App\Http\Models\PoolClass\PoolClassAttrModel;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Http\Models\StandardBrandMappingModel;

//class UploadAlikeSpu implements ShouldQueue
class UploadAlikeSpu implements ShouldQueue
// class UploadAlikeSpu
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;
    public $timeout = 36000;

    private $excelData;
    private $adminId;
    private $adminName;
    private $logId;
    private $type;
    private $path;

    public function __construct($logId, $path)
    {
        $this->path = $path;
        $log = AlikeSpuLogModel::where('id', $logId)->first()->toArray();
        $this->adminId = $log['admin_id'];
        $this->adminName = $log['admin_name'];
        $this->logId = $logId;
    }


    public function handle()
    {

        ini_set('MEMORY_LIMIT', '2048M');

        $path = $this->path;

        $alikeSpuList = Excel::toArray(new AlikeSpuImport, $path)[0];
        if (count($alikeSpuList) == 1) {
            return true;
        }
        $this->excelData = $alikeSpuList;

        $resultData = [];
        //已经存在的数量
        $repeatedExist = 0;
        $failedCount = 0;
        $totalNum = 0;
        $redis = Redis::connection('sku');
        foreach ($this->excelData as $key => $data) {
            $isRepeated = false;
            if ($key == 0) {
                continue;
            }
            $alikeSpuName = trim($data[0]);
            $alikeBrandName = trim($data[1]);
            $spuName = trim($data[2]);
            $brandName = trim($data[3]);
            $pinToPin = trim($data[4]);
            $remark = trim($data[5]);
            $result = [
                $alikeSpuName,
                $alikeBrandName,
                $spuName,
                $brandName,
                $pinToPin,
                $remark,
            ];
            //先去判断映射是否已经存在
            $exist = AlikeSpuModel::where('spu_name', $spuName)->where('alike_spu_name', $alikeSpuName)->exists();
            if ($exist) {
                $isRepeated = true;
                $repeatedExist++;
            }
            if (empty($spuName) || empty($alikeSpuName)) {
                array_push($result, '失败:国产替代型号或者被替代型号不能为空');
                $failedCount++;
                $resultData[] = $result;
                continue;
            }

            //先去判断并且获取标准品牌是否存在
            $standardBrand = StandardBrandModel::where('brand_name', $alikeBrandName)->first();
            if (empty($standardBrand)) {
                //再去判断普通品牌是否存在
                $brand = BrandModel::where('brand_name', $alikeBrandName)->first();
                if (empty($brand)) {
                    array_push($result, '失败:国产替代品牌不存在');
                    $failedCount++;
                    $resultData[] = $result;
                    continue;
                }
                $standardBrandId = StandardBrandMappingModel::where('brand_id', $brand['brand_id'])->value('standard_brand_id');
                if (empty($standardBrandId)) {
                    array_push($result, '失败:国产替代品牌不存在');
                    $failedCount++;
                    $resultData[] = $result;
                    continue;
                }
                $alikeBrandName = StandardBrandModel::where('standard_brand_id', $standardBrandId)->value('brand_name');
            } else {
                $standardBrandId = $standardBrand['standard_brand_id'];
            }
            //获取国产替代型号对应的所有spu
            $alikeSpuList = DB::connection('mongodb')->collection('spu')->where(
                'spu_name',
                $alikeSpuName
            )->where('s_brand_id', $standardBrandId)->get()->toArray();
            if (empty($alikeSpuList)) {
                array_push($result, '失败:国产替代型号找不到对应的spu');
                $failedCount++;
                $resultData[] = $result;
                continue;
            }
            //先去判断被替代的标准品牌是否存在
            $standardBrand = StandardBrandModel::where('brand_name', $brandName)->first();
            if (empty($standardBrand)) {
                //再去判断普通品牌是否存在
                $brand = BrandModel::where('brand_name', $brandName)->first();
                $standardBrandId = StandardBrandMappingModel::where('brand_id', $brand['brand_id'])->value('standard_brand_id');
                if (empty($standardBrandId)) {
                    array_push($result, '失败:被替代品牌不存在');
                    $failedCount++;
                    $resultData[] = $result;
                    continue;
                }
                $brandName = StandardBrandModel::where('standard_brand_id', $standardBrandId)->value('brand_name');
            } else {
                $standardBrandId = $standardBrand['standard_brand_id'];
            }

            //获取被替代的型号的所有spu_id
            $spuList = DB::connection('mongodb')->collection('spu')->where(
                'spu_name',
                $spuName
            )->where('s_brand_id', $standardBrandId)->get()->toArray();
            if (empty($spuList)) {
                array_push($result, '失败:被替代型号找不到对应的spu');
                $failedCount++;
                $resultData[] = $result;
                continue;
            }

            $spuId = $spuList[0]['spu_id'];

            $spuRedis = Redis::connection('spu');
            $spu = $spuRedis->hget('spu', $spuId);
            $spu = json_decode($spu, true);
            $classId2 = $spu['class_id2'] ?? 0;
            $attrCount = PoolClassAttrModel::where('class_id', $classId2)->count('attr_id');
            //先去找出国产替代spu的参数
            $alikeSpuAttrs = DB::connection('mongodb')->collection('spu_attrs2')->whereIn(
                'spu_id',
                array_column($alikeSpuList, 'spu_id')
            )->where('attr_format', '!=', '')->first();
            //再去找出被替代的型号的参数
            $spuAttrs = DB::connection('mongodb')->collection('spu_attrs2')->whereIn(
                'spu_id',
                array_column($spuList, 'spu_id')
            )->where('attr_format', '!=', '')->first();
            //再去找出被替代的型号的参数
            // dd(\json_encode($alikeSpuAttrs), \json_encode($spuAttrs));
            $matchRate = $this->calculateAttrSimilarity($alikeSpuAttrs, $spuAttrs, $attrCount);
            $alikeSpuIds = $alikeSpuNames = [];
            //然后把对应的所有替代spu存到中间表
            foreach ($alikeSpuList as $alikeSpu) {
                $alikeSpuIds[] = $alikeSpu['spu_id'];
                $alikeSpuNames[] = $alikeSpu['spu_name'];
            }
            //先去中间表找一下是否存在相同的被替代型号
            $alikeSpuCenter = AlikeSpuCenterModel::where('spu_name', $spuName)->first();
            $alikeSpuCenter = !empty($alikeSpuCenter) ? $alikeSpuCenter->toArray() : [];
            //如果存在,那么就要去拼接原有的数据
            if ($alikeSpuCenter) {
                $alikeSpuIdsCenter = array_merge($alikeSpuIds, explode(',', $alikeSpuCenter['alike_spu_ids']));
                $alikeSpuNamesCenter = array_merge($alikeSpuNames, explode(',', $alikeSpuCenter['alike_spu_names']));
                $alikeSpuCenter['spu_name'] = $spuName;
                $alikeSpuCenter['standard_brand_id'] = $standardBrandId;
                $alikeSpuCenter['standard_brand_name'] = $brandName;
                $alikeSpuCenter['spu_id'] = $spuId;
                $alikeSpuCenter['alike_spu_ids'] = implode(',', array_unique($alikeSpuIdsCenter));
                $alikeSpuCenter['alike_spu_names'] = implode(',', array_unique($alikeSpuNamesCenter));
                $alikeSpuCenter['update_time'] = time();
                $alikeSpuCenter['is_sync'] = 0;
                $alikeSpuCenter['batch_sn'] = implode(
                    ',',
                    array_unique(array_merge([$this->logId], explode(',', $alikeSpuCenter['batch_sn'])))
                );
                $res = AlikeSpuCenterModel::where('spu_name', $spuName)->update($alikeSpuCenter);
            } else {
                $alikeSpuCenter['spu_name'] = $spuName;
                $alikeSpuCenter['standard_brand_name'] = $brandName;
                $alikeSpuCenter['spu_id'] = $spuId;
                $alikeSpuCenter['standard_brand_id'] = $standardBrandId;
                $alikeSpuCenter['alike_spu_ids'] = implode(',', array_unique($alikeSpuIds));
                $alikeSpuCenter['alike_spu_names'] = implode(',', array_unique($alikeSpuNames));
                $alikeSpuCenter['add_time'] = time();
                $alikeSpuCenter['admin_id'] = $this->adminId;
                $alikeSpuCenter['batch_sn'] = $this->logId;
                AlikeSpuCenterModel::insert($alikeSpuCenter);
            }
            if ($isRepeated) {
                AlikeSpuModel::where('spu_name', $spuName)->where('alike_spu_name', $alikeSpuName)->update([
                    'alike_brand_name' => $alikeBrandName,
                    'brand_name' => $brandName,
                    'update_time' => time(),
                    'pin_to_pin' => $pinToPin == '是' ? 1 : -1,
                    'is_deleted' => 0,
                    'remark' => $remark,
                    'match_rate' => $matchRate,
                ]);
            } else {
                $insertAlikeSpu = [
                    'alike_spu_name' => $alikeSpuName,
                    'alike_brand_name' => $alikeBrandName,
                    'brand_name' => $brandName,
                    'spu_name' => $spuName,
                    'add_time' => time(),
                    'admin_id' => $this->adminId,
                    'admin_name' => $this->adminName,
                    'pin_to_pin' => $pinToPin == '是' ? 1 : -1,
                    'remark' => $remark,
                    'match_rate' => $matchRate,
                ];
                AlikeSpuModel::insert($insertAlikeSpu);
                $totalNum++;
            }
            //存储redis给搜索使用
            $redis->hset('spu_pin', Sku::DrawLetter2($alikeSpuName) . '@' . Sku::DrawLetter2($spuName), $pinToPin);
            $result[] = '成功,等待映射脚本处理完成';
            $resultData[] = $result;
        }
        if ($resultData) {
            //成功后还要插入一条日志记录
            $content = date('Y-m-d H:i:s', time()) . '上传完成,请耐心等待映射脚本执行完毕,已经存在的映射有' . $repeatedExist .
                '个,新插入的有' . $totalNum . '个,新增失败的有' . $failedCount . '个';
        } else {
            $content = date('Y-m-d H:i:s', time()) . '处理失败,请查看下载结果';
        }
        $resultFileUrl = $this->createResultExcel($resultData);
        AlikeSpuLogModel::where('id', $this->logId)->update([
            'log' => $content,
            'status' => AlikeSpuLogModel::STATUS_IN_PROGRESS,
            'update_time' => time(),
            'result_file_url' => $resultFileUrl,
            'batch_sn' => $this->logId,
        ]);
    }

    //生成结果数据
    public function createResultExcel($resultData)
    {
        $header = ['国产替代型号', '国产替代品牌', '被替代型号', '被替代品牌', 'pin to pin', '备注', '处理结果'];
        array_unshift($resultData, $header);
        $fileName = 'uploadAlikeSpuResult_' . date('Y-m-d_H-i-s', time()) . '.csv';
        $path = 'uploadAlikeSpuResult' . DIRECTORY_SEPARATOR . $fileName;
        Excel::store(new AlikeSpuUploadResultExport($resultData), $path);
        return $path;
    }

    /**
     * 计算参数相似度
     * 比较两个属性数组中相同属性的占比，先比对attrs_format，再比对attrs_extend
     *
     * @param array|null $alikeSpuAttrs 第一个属性数组
     * @param array|null $spuAttrs 第二个属性数组（基准）
     * @return float 相似度，0-1之间的小数，保留4位小数
     */
    public function calculateAttrSimilarity($alikeSpuAttrs, $spuAttrs, $attrCount)
    {
        if (empty($attrCount)) {
            return 0;
        }
        // 如果基准属性为空或null，直接返回0
        if ($spuAttrs === null || empty($spuAttrs)) {
            return 0;
        }

        // 如果比较属性为空或null，相似度也是0
        if ($alikeSpuAttrs === null || empty($alikeSpuAttrs)) {
            return 0;
        }

        // 提取实际需要比较的格式化属性数组
        $alikeSpuAttrsFormat = isset($alikeSpuAttrs['attrs_format']) ? $alikeSpuAttrs['attrs_format'] : [];
        $spuAttrsFormat = isset($spuAttrs['attrs_format']) ? $spuAttrs['attrs_format'] : [];

        // 提取需要比较的扩展属性数组
        $alikeSpuAttrsExtend = isset($alikeSpuAttrs['attrs_extend']) ? $alikeSpuAttrs['attrs_extend'] : [];
        $spuAttrsExtend = isset($spuAttrs['attrs_extend']) ? $spuAttrs['attrs_extend'] : [];

        // 记录已经匹配过的属性ID，避免重复计算
        $matchedAttrIds = [];
        $totalAttrCount = 0;
        $matchCount = 0;
        // 第一步：比对attrs_format属性
        if (!empty($spuAttrsFormat)) {
            // 根据attr_id对attrs_format属性进行索引，便于比较
            $alikeSpuAttrFormatMap = [];
            foreach ($alikeSpuAttrsFormat as $attr) {
                $attr = is_array($attr) ? $attr : (array)$attr;
                if (isset($attr['attr_id'])) {
                    $alikeSpuAttrFormatMap[$attr['attr_id']] = $attr;
                }
            }

            // 遍历基准format属性数组
            foreach ($spuAttrsFormat as $spuAttr) {
                $spuAttr = is_array($spuAttr) ? $spuAttr : (array)$spuAttr;
                $totalAttrCount++;

                // 如果没有attr_id，跳过
                if (!isset($spuAttr['attr_id'])) {
                    continue;
                }

                // 查找相同attr_id的属性
                if (isset($alikeSpuAttrFormatMap[$spuAttr['attr_id']])) {
                    $alikeAttr = $alikeSpuAttrFormatMap[$spuAttr['attr_id']];

                    // 比较attr_name和standard_attr_unit是否一致
                    if ($this->compareAttrBasicInfo($alikeAttr, $spuAttr)) {
                        // 再比较值是否一致
                        if ($this->compareAttrValues($alikeAttr, $spuAttr)) {
                            $matchCount++;
                        }
                    }

                    // 记录已匹配的属性ID
                    $matchedAttrIds[] = $spuAttr['attr_id'];
                }
            }
        }
        // 第二步：比对attrs_extend属性，但跳过已经在format中匹配过的
        if (!empty($spuAttrsExtend)) {
            // 根据attr_id对attrs_extend属性进行索引
            $alikeSpuAttrExtendMap = [];
            foreach ($alikeSpuAttrsExtend as $attr) {
                $attr = is_array($attr) ? $attr : (array)$attr;
                if (isset($attr['attr_id'])) {
                    $alikeSpuAttrExtendMap[$attr['attr_id']] = $attr;
                }
            }

            // 遍历基准extend属性数组
            foreach ($spuAttrsExtend as $spuAttr) {
                $spuAttr = is_array($spuAttr) ? $spuAttr : (array)$spuAttr;

                // 如果没有attr_id或已在format中匹配过，跳过
                if (!isset($spuAttr['attr_id']) || in_array($spuAttr['attr_id'], $matchedAttrIds)) {
                    continue;
                }

                $totalAttrCount++;

                // 查找相同attr_id的属性
                if (isset($alikeSpuAttrExtendMap[$spuAttr['attr_id']])) {
                    $alikeAttr = $alikeSpuAttrExtendMap[$spuAttr['attr_id']];

                    // 对于extend属性，直接比较attr_name和attr_value
                    if (
                        isset($alikeAttr['attr_name']) && isset($spuAttr['attr_name']) &&
                        $alikeAttr['attr_name'] === $spuAttr['attr_name'] &&
                        isset($alikeAttr['attr_value']) && isset($spuAttr['attr_value']) &&
                        $alikeAttr['attr_value'] === $spuAttr['attr_value']
                    ) {
                        $matchCount++;
                    }
                }
            }
        }

        // 计算相似度并保留4位小数
        $similarity = $totalAttrCount > 0 ? $matchCount / $attrCount : 0;
        return round($similarity, 4);
    }

    /**
     * 比较两个属性的基本信息（名称和单位）
     *
     * @param array $attr1 第一个属性
     * @param array $attr2 第二个属性
     * @return bool 是否匹配
     */
    private function compareAttrBasicInfo($attr1, $attr2)
    {
        return isset($attr1['attr_name']) && isset($attr2['attr_name']) &&
            $attr1['attr_name'] === $attr2['attr_name'] &&
            (!isset($attr1['standard_attr_unit']) || !isset($attr2['standard_attr_unit']) ||
                $attr1['standard_attr_unit'] === $attr2['standard_attr_unit']);
    }

    /**
     * 比较两个属性的值
     *
     * @param array $attr1 第一个属性
     * @param array $attr2 第二个属性
     * @return bool 值是否匹配
     */
    private function compareAttrValues($attr1, $attr2)
    {
        // 比较attr_value_origin
        if (
            isset($attr1['attr_value_origin']) && isset($attr2['attr_value_origin']) &&
            $attr1['attr_value_origin'] !== $attr2['attr_value_origin']
        ) {
            return false;
        }

        // 比较attr_value_multi
        if (isset($attr1['attr_value_multi']) && isset($attr2['attr_value_multi'])) {
            if (count($attr1['attr_value_multi']) !== count($attr2['attr_value_multi'])) {
                return false;
            }

            $attr1['attr_value_multi'] = array_map('intval', $attr1['attr_value_multi']);
            $attr2['attr_value_multi'] = array_map('intval', $attr2['attr_value_multi']);

            // 对两个数组进行排序后比较
            $multi1 = $attr1['attr_value_multi'];
            $multi2 = $attr2['attr_value_multi'];
            sort($multi1);
            sort($multi2);

            if ($multi1 !== $multi2) {
                return false;
            }
        }

        // 比较attr_value_range
        if (isset($attr1['attr_value_range']) && isset($attr2['attr_value_range'])) {
            if (count($attr1['attr_value_range']) !== count($attr2['attr_value_range'])) {
                return false;
            }

            foreach ($attr1['attr_value_range'] as $i => $range1) {
                if (!isset($attr2['attr_value_range'][$i])) {
                    return false;
                }

                $range2 = $attr2['attr_value_range'][$i];

                if ((isset($range1['attr_value_min']) && isset($range2['attr_value_min']) &&
                        (int)$range1['attr_value_min'] !== (int)$range2['attr_value_min']) ||
                    (isset($range1['attr_value_max']) && isset($range2['attr_value_max']) &&
                        (int)$range1['attr_value_max'] !== (int)$range2['attr_value_max'])
                ) {
                    return false;
                }
            }
        }

        return true;
    }
}
