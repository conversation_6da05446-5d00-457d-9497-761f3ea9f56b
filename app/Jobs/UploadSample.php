<?php

namespace App\Jobs;

use App\Exports\AlikeSpuUploadResultExport;
use App\Exports\SampleUploadResultExport;
use App\Http\Models\AlikeSpuCenterModel;
use App\Http\Models\BrandModel;
use App\Http\Models\SampleClassModel;
use App\Http\Models\SampleLogModel;
use App\Http\Models\AlikeSpuModel;
use App\Http\Models\SampleModel;
use App\Http\Models\UploadLogModel;
use App\Imports\AlikeSpuImport;
use App\Imports\SampleImport;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class UploadSample implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;
    public $timeout = 36000;

    private $excelData;
    private $excelPath;
    private $adminId;
    private $adminName;
    private $logId;
    private $type;

    public function __construct($logId, $excelPath)
    {
        $this->excelPath = $excelPath;
        $log = SampleLogModel::where('id', $logId)->first()->toArray();
        $this->adminId = $log['admin_id'];
        $this->adminName = $log['admin_name'];
        $this->logId = $logId;
    }


    public function handle()
    {
        $sampleList = Excel::toArray(new SampleImport(), $this->excelPath)[0];
        $this->excelData = $sampleList;
        $resultData = [];
        //已经存在的数量
        $repeatedExist = 0;
        $failedCount = 0;
        $totalNum = 0;
        $redis = Redis::connection('sku');
        $spuRedis = Redis::connection('spu');
        $mongo = \DB::connection('mongodb');
        try {
            foreach ($this->excelData as $key => $sample) {
                $isRepeated = false;
                if ($key == 0) {
                    continue;
                }
                $goodsId = trim($sample[0]);
                $classId = trim($sample[1]);
                $maxNumber = trim($sample[2]);
                $maxNumber = $maxNumber ?: 1;
                $sampleStock = trim($sample[3]);
                $result = [
                    $goodsId . "\t",
                    $classId,
                    $maxNumber,
                    $sampleStock,
                ];
                //判断样品分类是否存在
                $classExist = SampleClassModel::where('id', $classId)
                    ->where('status', SampleClassModel::STATUS_OK)->exists();
                if (!$classExist) {
                    array_push($result, '失败:找不到对应的样品分类');
                    dump('失败:找不到对应的样品分类');
                    $failedCount++;
                    $resultData[] = $result;
                    continue;
                }
                //先去判断样品是否已经存在
                $exist = SampleModel::where('goods_id', $goodsId)->exists();
                if (strlen($goodsId) > 10) {
                    $sku = $redis->hget('sku', $goodsId);
                    if (empty($sku)) {
                        array_push($result, '失败:找不到联营对应的SKU');
                        dump('失败:找不到联营对应的SKU');
                        $failedCount++;
                        $resultData[] = $result;
                        continue;
                    }
                    $sku = json_decode($sku, true);
                    $spu = $spuRedis->hget('spu', $sku['spu_id']);
                    if (empty($spu)) {
                        array_push($result, '失败:找不到联营对应的SPU');
                        dump('失败:找不到联营对应的SPU');
                        $failedCount++;
                        $resultData[] = $result;
                        continue;
                    }
                    $sampleType = 2;
                } else {
                    //如果是自营的话,那么就要去取自营的信息
                    $sku = $redis->hget('Self_SelfGoods', $goodsId);
                    if (empty($sku)) {
                        array_push($result, '失败:找不到自营SKU');
                        dump('失败:找不到自营SKU');
                        $failedCount++;
                        $resultData[] = $result;
                        continue;
                    }
                    $sku = json_decode($sku, true);
                    if (empty($sku['spu_id'])) {
                        array_push($result, '失败:找不到自营对应的SPU');
                        dump('失败:找不到自营对应的SPU');
                        $failedCount++;
                        $resultData[] = $result;
                        continue;
                    }
                    $spu = $spuRedis->hget('spu', $sku['spu_id']);
                    if (empty($spu)) {
                        array_push($result, '失败:找不到自营对应的SPU');
                        dump('失败:找不到自营对应的SPU');
                        $failedCount++;
                        $resultData[] = $result;
                        continue;
                    }
                    $sampleType = 1;
                }
                if (!$spu) {
                    continue;
                }

                $spu = !is_array($spu) ? json_decode($spu, true) : $spu;
                $brandName = $redis->hget('brand', $spu['brand_id']);
                $standardBrandId = $redis->hget('standard_brand_mapping', $spu['brand_id']);
                $standardBrand = $redis->hget('standard_brand', $standardBrandId);
                $standardBrand = json_decode($standardBrand, true);
                if (empty($standardBrandId)) {
                    array_push($result, '失败:没有找到对应的标准品牌');
                    dump('失败:没有找到对应的标准品牌');
                    $failedCount++;
                    $resultData[] = $result;
                    continue;
                }
                if (empty($sampleStock)) {
                    array_push($result, '失败:样品库存不能为0');
                    dump('失败:样品库存不能为0');
                    $failedCount++;
                    $resultData[] = $result;
                    continue;
                }
                $sampleData = [
                    'goods_id' => $goodsId,
                    'class_id' => $classId,
                    'sample_stock' => $sampleStock,
                    'max_number' => $maxNumber,
                    'brand_id' => $spu['brand_id'],
                    'brand_name' => $brandName,
                    'goods_name' => $spu['spu_name'],
                    'sample_type' => $sampleType,
                    'standard_brand_id' => $standardBrandId,
                    'standard_brand_name' => $standardBrand['brand_name'],
                ];
                if ($exist) {
                    $isRepeated = true;
                    $repeatedExist++;
                }
                if (empty($goodsId) || empty($classId)) {
                    array_push($result, '失败:样品的商品ID或者分类ID都不能为空');
                    dump('失败:样品的商品ID或者分类ID都不能为空');
                    $failedCount++;
                    $resultData[] = $result;
                    continue;
                }

                if ($isRepeated) {
                    $sampleData['update_time'] = time();
                    $sampleData['status'] = SampleModel::STATUS_OK;
                    SampleModel::where('goods_id', $goodsId)->update($sampleData);
                    $sampleId = SampleModel::where('goods_id', $goodsId)->value('id');
                } else {
                    $sampleData['admin_id'] = $this->adminId;
                    $sampleData['admin_name'] = $this->adminName;
                    $sampleData['add_time'] = time();
                    $sampleId = SampleModel::insertGetId($sampleData);
                    $totalNum++;
                }
                $sample = SampleModel::where('id', $sampleId)->first()->toArray();
                Redis::connection('sku')->hset('lie_sample_list', $goodsId, json_encode($sample));
                $result[] = '样品上传成功';
                $resultData[] = $result;
            }
            if ($resultData) {
                //成功后还要插入一条日志记录
                $content = date('Y-m-d H:i:s', time()) . '上传完成,已经存在的样品有' . $repeatedExist .
                    '个,新插入的有' . $totalNum . '个,新增失败的有' . $failedCount . '个';
            } else {
                $content = date('Y-m-d H:i:s', time()) . '处理失败,请查看下载结果';
            }
            $resultFileUrl = $this->createResultExcel($resultData);
            SampleLogModel::where('id', $this->logId)->update([
                'log' => $content,
                'status' => SampleLogModel::STATUS_OK,
                'update_time' => time(),
                'result_file_url' => $resultFileUrl,
                'batch_sn' => $this->logId,
            ]);
        } catch (\Exception $exception) {
            SampleLogModel::where('id', $this->logId)->update([
                'log' => $exception->getMessage() . ' ' . $exception->getLine(),
                'status' => SampleLogModel::STATUS_IN_PROGRESS,
                'update_time' => time(),
                'result_file_url' => '',
                'batch_sn' => $this->logId,
            ]);
            dump($exception->getMessage() . ' ' . $exception->getLine());
            \Log::error($exception->getMessage() . ' ' . $exception->getLine());
        } finally {
            unlink($this->excelPath);
        }

    }

    //生成结果数据
    public function createResultExcel($resultData)
    {
        $header = ['SKUID', '分类ID', '单次申请数量上限', '样品库存', '结果'];
        array_unshift($resultData, $header);
        $fileName = 'uploadSampleResult_' . date('Y-m-d_H-i-s', time()) . '.xlsx';
        $path = 'uploadSampleResult' . DIRECTORY_SEPARATOR . $fileName;
        Excel::store(new SampleUploadResultExport($resultData), $path);
        return $path;
    }
}
