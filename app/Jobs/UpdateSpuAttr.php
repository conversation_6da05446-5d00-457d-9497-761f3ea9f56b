<?php

namespace App\Jobs;

use App\Exports\UpdateSpuAttrResultExport;
use App\Exports\UpdateSpuResultExport;
use App\Http\Models\PoolClass\ClassAttrUnitModel;
use App\Http\Models\PoolClass\ClassAttrValueModel;
use App\Http\Models\PoolClass\PoolClassAttrModel;
use App\Http\Models\TaskLogModel;
use App\Http\Queue\RabbitQueueModel;
use App\Http\Services\ClassAttrService;
use App\Http\Services\SpuAttrService;
use App\Http\Services\SpuService;
use App\Imports\UpdateSpuAttrImport;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class UpdateSpuAttr implements ShouldQueue
//class UpdateSpuAttr
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 1;
    public $timeout = 36000;


    private $excelData;
    private $excelPath;
    private $adminId;
    private $adminName;
    private $logId;
    private $type;

    public function __construct($logId, $excelPath)
    {
        $this->excelPath = $excelPath;
        $log = TaskLogModel::where('id', $logId)->first()->toArray();
        $this->adminId = $log['admin_id'];
        $this->adminName = $log['admin_name'];
        $this->logId = $logId;
    }


    public function handle()
    {
        $log = new RabbitQueueModel();
        $log->log("queue_insert", "Start spu batch update , file path is : " . $this->excelPath, '');
        ini_set('memory_limit', -1);
        $import = new UpdateSpuAttrImport();
        $data = Excel::toArray($import, $this->excelPath);
        $this->excelData = $data[0];
        $resultData = [];
        //已经存在的数量
        $successCount = 0;
        $failedCount = 0;
        $totalNum = 0;
        $redis = Redis::connection('sku');
        $spuRedis = Redis::connection('spu');
        $taskLogModel = new TaskLogModel();
        $queue = new RabbitQueueModel('trading');
        try {
            if (count($this->excelData) <= 1) {
                $taskLogModel->updateTaskLog($this->logId, [
                    'status' => TaskLogModel::STATUS_FAILED,
                    'log' => '上传处理的excel数据不能为空',
                ]);
                return true;
            }
            //第一列是spu以外,后面都是属性+单位两列的形式
            $excelHeader = $this->excelData[0];
            $header = $this->excelData[0];
            $spuRow = $excelHeader[0];
            unset($excelHeader[0]);
            $headerAttrNameList = $excelHeader;
            $attrClassId = preg_replace('/[^0-9.]+/', '', $spuRow);
            //获取表头的属性是属于什么分类的
            foreach ($this->excelData as $key => $attr) {
                if ($key == 0) {
                    continue;
                }

                if ($key % 100 == 0) {
                    $log->log('queue_insert', 'Handled Item : '.$key);
                }

                $attr[0] = $attr[0] . "\t";
                $result = $attr;
                $spuId = trim($attr[0]);
                //删除第一列的spuid,剩下的都是参数值
                unset($attr[0]);
                $attrValueList = $attr;
                if (empty($attr)) {
                    array_push($result, '失败:没有设置任何参数');
                    //dump('失败:没有设置任何参数');
                    $failedCount++;
                    $resultData[] = $result;
                    continue;
                }
                //判断SPU是否存在
                $spu = $spuRedis->hget('spu', $spuId);
                if (!$spu) {
                    array_push($result, '失败:找不到对应的SPU');
                    //dump('失败:找不到对应的SPU');
                    $failedCount++;
                    $resultData[] = $result;
                    continue;
                }
                $spu = json_decode($spu, true);
                if (empty($spu['class_id2'])) {
                    array_push($result, 'spu没有二级分类,请先完善分类信息');
                    //dump('spu没有二级分类,请先完善分类信息');
                    $failedCount++;
                    $resultData[] = $result;
                    continue;
                }
                //判断当前spu是否属于该模板的分类
                if ($spu['class_id2'] != $attrClassId) {
                    array_push($result, 'spu对应的分类和该模板对于的属性分类不一致,请确认spu分类是否属于该模板分类');
                    //dump('spu对应的分类和该模板对于的属性分类不一致,请确认spu分类是否属于该模板分类');
                    $failedCount++;
                    $resultData[] = $result;
                    continue;
                }
                //$dbInfo = resolveDB($spuId);
                //$spuDB = (array)DB::connection($dbInfo['db'])->table($dbInfo['table'])
                //    ->where('spu_id', $spuId)->first();
                $validAttrs = [];
                $attrExtraList = [];
                //组装属性数据
                foreach ($attrValueList as $k => $value) {
                    $attrName = $headerAttrNameList[$k];
                    //去掉括号以及包裹部分
                    //$attrName = preg_replace("/\([^)]+\)/", "", $attrName);
                    $attrName = preg_replace("/\([^()]*\)$/", "", $attrName);
                    if (empty($attrName)) {
                        continue;
                    }
                    //获取attrId
                    $attr = PoolClassAttrModel::where('class_id', $attrClassId)->where('attr_name',
                        $attrName)->first();
                    if (empty($attr)) {
                        array_push($result, '失败:' . '找不到对应的属性 : ' . $attrName);
                        //dump('失败:' . '找不到对应的属性 : ' . $attrName);
                        $failedCount++;
                        $resultData[] = $result;
                        continue 2;
                    }
                    $attr = $attr->toArray();
                    $attrId = $attr['attr_id'];
                    if (empty($value)) {
                        continue;
                    }
                    //提取参数出来
                    $extractResult = (new ClassAttrService())->extractAttr($attrId, $value);
                    if (!is_array($extractResult)) {
                        array_push($result, '失败:' . $extractResult);
                        //dump('失败:' . $extractResult);
                        $failedCount++;
                        $resultData[] = $result;
                        continue 2;
                    }

                    $attrExtraList[$attr['attr_id']] = $extractResult;

                    $validAttr['valid'] = 1;
                    $validAttr['attr_id'] = $attrId;
                    $validAttr['attr_name'] = $attrName;
                    $validAttr['origin_value'] = $value;
                    $validAttr['default_unit_convert_id'] = $attr['default_unit_convert_id'];
                    $validAttrs[] = $validAttr;
                }
                $validAttrs = array_values($validAttrs);
                //去更新spu
                $spuAttr = (new SpuAttrService())->saveSpuAttr($spuId, $validAttrs, $attrExtraList);
                //推送通知ES
                (new SpuService())->pushSpuUpdate($spuId);
                if (empty($spuAttr)) {
                    array_push($result, 'SPU处理参数失败,没法生成正确参数');
                    $failedCount++;
                    $resultData[] = $result;
                    continue;
                }
                //推送辐射属性
                $queue->insertRawQueue('', [
                    'spu_id' => (string)$spuId,
                    'spu_data_info' => [
                        'class_id1' => $spu['class_id1'],
                        'class_id2' => $spu['class_id2'],
                        'attrs_extend' => $spuAttr['attrs_extend']??[],
                        'attrs_format' => $spuAttr['attrs_format']??[],
                        'attrs_mix' => $spuAttr['attrs_mix']??'',
                    ],
                    'type' => 3
                ], 'spu_update_Info');
                $successCount++;
                $result[] = 'SPU处理成功';
                $resultData[] = $result;
            }
            if ($resultData) {
                $fileName = TaskLogModel::where('id', $this->logId)->value('file_name');
                //成功后还要插入一条日志记录
                $content = date('Y-m-d H:i:s', time()) . '上传完成,上传文件名称 "' . $fileName . '" ,处理的成功的SPU有' . $successCount .
                    '个,处理失败的有' . $failedCount . '个';
            } else {
                $content = date('Y-m-d H:i:s', time()) . '处理失败,请查看下载结果';
            }
            $resultFileUrl = $this->createResultExcel($header, $resultData);
            TaskLogModel::where('id', $this->logId)->update([
                'log' => $content,
                'status' => TaskLogModel::STATUS_OK,
                'update_time' => time(),
                'result_file_url' => $resultFileUrl,
                'batch_sn' => $this->logId,
            ]);

            $log->log('queue_insert','Batch update spu task end....');

        } catch (\Exception $exception) {
            TaskLogModel::where('id', $this->logId)->update([
                'log' => $exception->getMessage() . ' ' . $exception->getLine(),
                'status' => TaskLogModel::STATUS_IN_PROGRESS,
                'result_file_url' => '',
                'batch_sn' => $this->logId,
            ]);
            //dump($exception->getMessage() . ' ' . $exception->getLine());
            \Log::error($exception->getMessage() . ' ' . $exception->getTraceAsString());
        } finally {
            unlink($this->excelPath);
        }
    }

    //生成结果数据
    public function createResultExcel($header, $resultData)
    {
        array_push($header, '结果');
        array_unshift($resultData, $header);
        $fileName = 'updateSpuClassResult_' . date('Y-m-d_H-i-s', time()) . '.xlsx';
        $path = 'updateSpuClassResult' . DIRECTORY_SEPARATOR . $fileName;
        Excel::store(new UpdateSpuAttrResultExport($resultData), $path);
        return $path;
    }
}
