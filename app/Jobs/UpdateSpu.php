<?php

namespace App\Jobs;

use App\Exports\AlikeSpuUploadResultExport;
use App\Exports\SampleUploadResultExport;
use App\Exports\UpdateSpuResultExport;
use App\Http\Models\AlikeSpuCenterModel;
use App\Http\Models\BrandModel;
use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Models\SampleClassModel;
use App\Http\Models\TaskLogModel;
use App\Http\Models\AlikeSpuModel;
use App\Http\Models\SampleModel;
use App\Http\Models\UploadLogModel;
use App\Http\Queue\RabbitQueueModel;
use App\Http\Services\SpuService;
use App\Imports\AlikeSpuImport;
use App\Imports\SampleImport;
use App\Imports\UpdateSpuClassImport;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class UpdateSpu implements ShouldQueue
// class UpdateSpu
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;
    public $timeout = 36000;

    private $excelData;
    private $excelPath;
    private $adminId;
    private $adminName;
    private $logId;
    private $type;

    public function __construct($logId, $excelPath)
    {
        $this->excelPath = $excelPath;
        $log = TaskLogModel::where('id', $logId)->first()->toArray();
        $this->adminId = $log['admin_id'];
        $this->adminName = $log['admin_name'];
        $this->logId = $logId;
    }


    public function handle()
    {
        ini_set('pcre.backtrack_limit', 5000000);
        ini_set('pcre.recursion_limit', 5000000);
        ini_set('memory_limit', -1);
        $data = Excel::toArray(new UpdateSpuClassImport(), $this->excelPath)[0];
        $this->excelData = $data;
        $resultData = [];
        //已经存在的数量
        $successCount = 0;
        $failedCount = 0;
        $totalNum = 0;
        $redis = Redis::connection('sku');
        $spuRedis = Redis::connection('spu');
        $mongo = \DB::connection('mongodb');
        $taskLogModel = new TaskLogModel();
        $queue = new RabbitQueueModel('trading');
        try {
            if (count($this->excelData) <= 1) {
                $taskLogModel->updateTaskLog($this->logId, [
                    'status' => TaskLogModel::STATUS_FAILED,
                    'log' => '上传处理的excel数据不能为空',
                ]);
                return true;
            }
            $this->excelData[0] = array_filter($this->excelData[0], function ($item) {
                return !empty($item);
            });

            if (count($this->excelData[0]) != 14) {
                $taskLogModel->updateTaskLog($this->logId, [
                    'status' => TaskLogModel::STATUS_FAILED,
                    'log' => '上传的excel表头不一致,请确认是否使用最新的模板',
                ]);
                return true;
            }
            if (count($this->excelData[0]) < 13) {
                $taskLogModel->updateTaskLog($this->logId, [
                    'status' => TaskLogModel::STATUS_FAILED,
                    'log' => '上传处理的excel表头不完整',
                ]);
                return true;
            }
            foreach ($this->excelData as $key => $sample) {
                if ($key == 0) {
                    continue;
                }
                $spuId = trim($sample[0]);
                $encap = trim($sample[1]);
                $className2 = trim($sample[2]);
                $classId2 = trim($sample[3]);
                $spuTitle = trim($sample[4]);
                $rohs = trim($sample[5]);
                $eccn = trim($sample[6]);
                $humistor = trim($sample[7]);
                $series = trim($sample[8]);
                $mpq = trim($sample[9]);
                $brand_pack = trim($sample[10]);
                $lifecycle = trim($sample[11]);
                $applicationLevel = trim($sample[12]);
                $spuBrief = trim($sample[13]);
                $result = [
                    $spuId . "\t",
                    $encap,
                    $className2,
                    $classId2 . "\t",
                    $spuTitle,
                    $rohs,
                    $eccn,
                    $humistor,
                    $series,
                    $mpq,
                    $brand_pack,
                    $lifecycle,
                    $applicationLevel,
                    $spuBrief,
                ];
                //判断SPU是否存在
                $spuExists = $spuRedis->hget('spu', $spuId);
                if (!$spuExists) {
                    array_push($result, '失败:找不到对应的SPU');
                    //dump('失败:找不到对应的SPU');
                    $failedCount++;
                    $resultData[] = $result;
                    continue;
                }

                //有指定分类id的,应该先去判断id
                if (!empty($classId2)) {
                    //判断分类名称是否存在
                    $class2 = PoolClassModel::where('class_id', $classId2)
                        ->where('parent_id', '!=', 0)->first();
                    if (empty($class2)) {
                        array_push($result, '失败:末级分类ID不存在');
                        //dump('失败:末级分类名称不存在');
                        $failedCount++;
                        $resultData[] = $result;
                        continue;
                    }
                    $class2 = $class2->toArray();
                    $classId1 = PoolClassModel::where('class_id', $class2['parent_id'])->value('class_id');
                } else if (!empty($className2)) {
                    //判断分类名称是否存在
                    $class2 = PoolClassModel::where('class_name', $className2)
                        ->where('parent_id', '!=', 0)->first();
                    if (empty($class2)) {
                        array_push($result, '失败:末级分类名称不存在');
                        //dump('失败:末级分类名称不存在');
                        $failedCount++;
                        $resultData[] = $result;
                        continue;
                    }
                    $class2 = $class2->toArray();
                    $classId1 = PoolClassModel::where('class_id', $class2['parent_id'])->value('class_id');
                }

                //然后去修改SPU信息
                $spuData = [
                    'encap' => $encap,
                    'class_id1' => isset($classId1) ? (int)$classId1 : 0,
                    'class_id2' => isset($class2) ? (int)$class2['class_id'] : 0,
                    'has_rohs' => $rohs == '是' ? 1 : 0,
                    'spu_title' => $spuTitle,
                    'eccn' => $eccn,
                    'humistor' => $humistor,
                    'series' => $series,
                    'mpq' => (int)$mpq,
                    'brand_pack' => $brand_pack,
                    'lifecycle' => $lifecycle,
                    'application_level' => $applicationLevel,
                    'spu_brief' => $spuBrief,
                ];

                foreach ($spuData as $key => $item) {
                    if (empty($item)) {
                        unset($spuData[$key]);
                    }
                }

                $dbInfo = resolveDB($spuId);
                $spuData['update_time'] = time();
                DB::connection($dbInfo['db'])->table($dbInfo['table'])
                    ->where('spu_id', $spuId)->update($spuData);
                //处理redis
                $spu = (array)DB::connection($dbInfo['db'])->table($dbInfo['table'])
                    ->where('spu_id', $spuId)->first();
                SpuService::handleSpu($spu);
                //推送通知ES
                (new SpuService())->pushSpuUpdate($spuId);
                unset($spuData['update_time']);
                ksort($spuData);
                //还要去分发到spu的辐射服务
                $queue->insertRawQueue('', [
                    'spu_id' => (string)$spuId,
                    'spu_data_info' => $spuData,
                    'type' => 3,
                ], 'spu_update_Info');
                $successCount++;
                $result[] = 'SPU处理成功';
                $resultData[] = $result;
            }
            $fileName = TaskLogModel::where('id', $this->logId)->value('file_name');
            if ($resultData) {
                //成功后还要插入一条日志记录
                $content = date('Y-m-d H:i:s', time()) . '上传完成,文件名为 : ' . $fileName . ' 处理的成功的SPU有' . $successCount .
                    '个,处理失败的有' . $failedCount . '个';
            } else {
                $content = date('Y-m-d H:i:s', time()) . '处理失败,文件名为 : ' . $fileName . '请查看下载结果';
            }
            $resultFileUrl = $this->createResultExcel($resultData);
            TaskLogModel::where('id', $this->logId)->update([
                'log' => $content,
                'status' => TaskLogModel::STATUS_OK,
                'update_time' => time(),
                'result_file_url' => $resultFileUrl,
                'batch_sn' => $this->logId,
            ]);
        } catch (\Exception $exception) {
            TaskLogModel::where('id', $this->logId)->update([
                'log' => $exception->getMessage() . ' ' . $exception->getLine(),
                'status' => TaskLogModel::STATUS_IN_PROGRESS,
                'result_file_url' => '',
                'batch_sn' => $this->logId,
            ]);
            dump($exception->getMessage() . ' ' . $exception->getLine());
            \Log::error($exception->getMessage() . ' ' . $exception->getLine());
        } finally {
            unlink($this->excelPath);
        }

    }

    //生成结果数据
    public function createResultExcel($resultData)
    {
        $header = ['SPUID', '封装', '末级分类名称', '末级分类ID', 'RoHS', 'ECCN', '湿敏等级', '系列', '标准包装量', '制造商包装', '生命周期', '应用级别', '商品描述', '处理结果'];
        array_unshift($resultData, $header);
        $fileName = 'updateSpuClassResult_' . date('Y-m-d_H-i-s', time()) . '.xlsx';
        $path = 'updateSpuClassResult' . DIRECTORY_SEPARATOR . $fileName;
        Excel::store(new UpdateSpuResultExport($resultData), $path);
        return $path;
    }
}
