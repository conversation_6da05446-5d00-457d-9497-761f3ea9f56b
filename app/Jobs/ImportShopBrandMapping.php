<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use App\Http\Models\TaskLogModel;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Queue\SerializesModels;
use App\Http\Models\StandardBrandModel;
use App\Imports\ShopBrandMappingImport;
use Illuminate\Queue\InteractsWithQueue;
use App\Exports\SampleUploadResultExport;
use App\Http\Models\BigData\ShopBrandModel;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Http\Models\BigData\ShopBrandMappingModel;

class ImportShopBrandMapping implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;
    public $timeout = 36000;

    private $excelData;
    private $excelPath;
    private $adminId;
    private $adminName;
    private $logId;
    private $type;

    public function __construct($logId, $excelPath)
    {
        $this->excelPath = $excelPath;
        $log = TaskLogModel::where('id', $logId)->first()->toArray();
        $this->adminId = $log['admin_id'];
        $this->adminName = $log['admin_name'];
        $this->logId = $logId;
    }


    public function handle()
    {
        $mappingList = Excel::toArray(new ShopBrandMappingImport(), $this->excelPath)[0];
        $this->excelData = $mappingList;
        $resultData = [];
        //已经存在的数量
        $repeatedExist = 0;
        $failedCount = 0;
        $totalNum = 0;
        try {
            foreach ($this->excelData as $key => $mapping) {
                if ($key == 0) {
                    continue;
                }
                $standardBrandName = trim($mapping[0]);
                $shopPlatformName = trim($mapping[1]);
                $shopBrandName = trim($mapping[2]);
                $result = [
                    $standardBrandName . "\t",
                    $shopPlatformName,
                    $shopBrandName,
                ];
                //判断标准品牌是否存在
                $standardBrandId = StandardBrandModel::where('brand_name', $standardBrandName)->value('standard_brand_id');
                if (!$standardBrandId) {
                    array_push($result, '失败:找不到对应的标准品牌');
                    // dump('失败:找不到对应的标准品牌');
                    $failedCount++;
                    $resultData[] = $result;
                    continue;
                }
                $platformList = config('field.ShopPlatform');
                $platformList = array_flip($platformList);
                $platformId = $platformList[$shopPlatformName] ?? null;
                if (!$platformId) {
                    array_push($result, '失败:找不到对应的平台');
                    // dump('失败:找不到对应的平台');
                    $failedCount++;
                    $resultData[] = $result;
                    continue;
                }

                //先去判断样品是否已经存在
                $shopBrandId = ShopBrandModel::where('platform', $platformId)
                    ->where('brand_name', $shopBrandName)->value('id');
                if (!$shopBrandId) {
                    array_push($result, '失败:找不到对应的店铺品牌');
                    // dump('失败:找不到对应的店铺品牌');
                    $failedCount++;
                    $resultData[] = $result;
                    continue;
                }

                $mappingData = [
                    'platform' => $platformId,
                    'lie_brand_id' => $standardBrandId,
                    'shop_brand_id' => $shopBrandId,

                ];
                //判断映射是否存在
                $id = ShopBrandMappingModel::where('platform', $platformId)
                    ->where('lie_brand_id', $standardBrandId)
                    ->where('shop_brand_id', $shopBrandId)
                    ->value('id');
                if ($id) {
                    $mappingData['update_name'] = $this->adminName;
                    $mappingData['update_uid'] = $this->adminId;
                    $mappingData['update_time'] = time();
                    ShopBrandMappingModel::where('id', $id)->update($mappingData);
                    $repeatedExist++;
                } else {
                    $mappingData['create_name'] = $this->adminName;
                    $mappingData['create_uid'] = $this->adminId;
                    $mappingData['create_time'] = time();
                    ShopBrandMappingModel::insert($mappingData);
                    $totalNum++;
                }

                $result[] = '店铺品牌映射成功';
                $resultData[] = $result;
            }
            if ($resultData) {
                //成功后还要插入一条日志记录
                $content = date('Y-m-d H:i:s', time()) . '上传完成,已经存在的映射有' . $repeatedExist .
                    '个,新插入的有' . $totalNum . '个,新增失败的有' . $failedCount . '个';
            } else {
                $content = date('Y-m-d H:i:s', time()) . '处理失败,请查看下载结果';
            }
            $resultFileUrl = $this->createResultExcel($resultData);
            TaskLogModel::where('id', $this->logId)->update([
                'log' => $content,
                'status' => TaskLogModel::STATUS_OK,
                'update_time' => time(),
                'result_file_url' => $resultFileUrl,
                'batch_sn' => $this->logId,
            ]);
        } catch (\Exception $exception) {
            TaskLogModel::where('id', $this->logId)->update([
                'log' => $exception->getMessage() . ' ' . $exception->getLine(),
                'status' => TaskLogModel::STATUS_IN_PROGRESS,
                'result_file_url' => '',
                'batch_sn' => $this->logId,
            ]);
            dump($exception->getMessage() . ' ' . $exception->getLine());
            \Log::error($exception->getMessage() . ' ' . $exception->getLine());
        } finally {
            unlink($this->excelPath);
        }
    }

    //生成结果数据
    public function createResultExcel($resultData)
    {
        $header = ['标准品牌', '平台', '店铺品牌', '结果'];
        array_unshift($resultData, $header);
        $fileName = 'uploadShopBrandMappingResult_' . date('Y-m-d_H-i-s', time()) . '.xlsx';
        $path = 'uploadShopBrandMappingResult' . DIRECTORY_SEPARATOR . $fileName;
        Excel::store(new SampleUploadResultExport($resultData), $path);
        return $path;
    }
}
