<?php

namespace App\Jobs;

use App\Exports\AlikeSpuUploadResultExport;
use App\Exports\SampleUploadResultExport;
use App\Exports\UpdateSpuResultExport;
use App\Http\Models\AlikeSpuCenterModel;
use App\Http\Models\BrandModel;
use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Models\SampleClassModel;
use App\Http\Models\TaskLogModel;
use App\Http\Models\AlikeSpuModel;
use App\Http\Models\SampleModel;
use App\Http\Models\UploadLogModel;
use App\Http\Queue\RabbitQueueModel;
use App\Http\Services\SpuService;
use App\Imports\AlikeSpuImport;
use App\Imports\SampleImport;
use App\Imports\UpdateSpuClassImport;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

//class UpdateSpuPDF implements ShouldQueue
class UpdateSpuPDF
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;
    public $timeout = 36000;

    private $spuPDFData;
    private $excelPath;
    private $adminId;
    private $lang;
    private $adminName;
    private $logId;
    private $type;

    public function __construct($logId, $spuPDFData, $lang)
    {
        $this->spuPDFData = $spuPDFData;
        $log = TaskLogModel::where('id', $logId)->first()->toArray();
        $this->adminId = $log['admin_id'];
        $this->adminName = $log['admin_name'];
        $this->logId = $logId;
        $this->lang = $lang;
    }


    public function handle()
    {
        $resultData = [];
        //已经存在的数量
        $successCount = 0;
        $failedCount = 0;
        $totalNum = 0;
        $spuRedis = Redis::connection('spu');
        $redis = Redis::connection('sku');
        $mongo = \DB::connection('mongodb');
        $queue = (new RabbitQueueModel('trading'));
        $taskLogModel = new TaskLogModel();
        try {
            if (count($this->spuPDFData) < 1) {
                $taskLogModel->updateTaskLog($this->logId, [
                    'status' => TaskLogModel::STATUS_FAILED,
                    'log' => '上传处理的PDF数据不能为空',
                ]);
                return true;
            }
            foreach ($this->spuPDFData as $key => $item) {
                $fileName = trim($item['file_name']);
                $spuId = str_replace('.pdf', '', $fileName);
                $pdf = trim($item['file_url']);
                $result = [
                    $fileName . "\t",
                    $pdf,
                ];

                //判断SPU是否存在
                $spuExists = $spuRedis->hget('spu', $spuId);
                if (!$spuExists) {
                    array_push($result, '失败:找不到对应的SPU');
                    //dump('失败:找不到对应的SPU');
                    $failedCount++;
                    $resultData[] = $result;
                    continue;
                }

                //区分中文还是英文,中文的话直接存到db和redis,如果是英文的话,要存到mongo
                if ($this->lang == 1) {
                    //然后去修改SPU信息
                    $spu = [
                        'spu_id' => $spuId,
                        'pdf' => $pdf,
                    ];
                    $dbInfo = resolveDB($spuId);
                    $spu['update_time'] = time();
                    DB::connection($dbInfo['db'])->table($dbInfo['table'])
                        ->where('spu_id', $spuId)->update($spu);
                    //处理redis
                    $spu = (array)DB::connection($dbInfo['db'])->table($dbInfo['table'])
                        ->where('spu_id', $spuId)->first();
                    SpuService::handleSpu($spu);

                } else {
                    //$spuExtra = $mongo->table('spu_extra')->where('spu_id', $spuId)->first();
                    //$spuExtra['en_pdf_url'] = $pdf;
                    $mongo->table('spu_extra')->where('spu_id', $spuId)->update([
                        'en_pdf_url' => $pdf,
                    ]);

                }

                //推送通知ES
                (new SpuService())->pushSpuUpdate($spuId);
                $data = [
                    'file_type' => 2,
                    'spu_id' => $spuId,
                    'url' => $pdf
                ];
                if ($this->lang == 2) {
                    $data['lang'] = 'en';
                }
                $queue->insertRawQueue('', $data, 'spu_update_image_pdf');
                $successCount++;
                $result[] = 'SPU处理成功';
                $resultData[] = $result;
            }


            if ($resultData) {
                //成功后还要插入一条日志记录
                $content = date('Y-m-d H:i:s', time()) . '上传完成,处理的成功的SPU有' . $successCount .
                    '个,处理失败的有' . $failedCount . '个';
            } else {
                $content = date('Y-m-d H:i:s', time()) . '处理失败,请查看下载结果';
            }
            $resultFileUrl = $this->createResultExcel($resultData);
            TaskLogModel::where('id', $this->logId)->update([
                'log' => $content,
                'status' => TaskLogModel::STATUS_OK,
                'update_time' => time(),
                'result_file_url' => $resultFileUrl,
                'batch_sn' => $this->logId,
            ]);
        } catch (\Exception $exception) {
            TaskLogModel::where('id', $this->logId)->update([
                'log' => $exception->getMessage() . ' ' . $exception->getLine(),
                'status' => TaskLogModel::STATUS_IN_PROGRESS,
                'result_file_url' => '',
                'batch_sn' => $this->logId,
            ]);
            dump($exception->getMessage() . ' ' . $exception->getLine());
            \Log::error($exception->getMessage() . ' ' . $exception->getLine());
        }
    }

    //生成结果数据
    public function createResultExcel($resultData)
    {
        $header = ['上传文件名', 'PDF链接', '处理结果'];
        array_unshift($resultData, $header);
        $fileName = 'updateSpuPDFResult_' . date('Y-m-d_H-i-s', time()) . '.xlsx';
        $path = 'updateSpuPDFResult' . DIRECTORY_SEPARATOR . $fileName;
        Excel::store(new UpdateSpuResultExport($resultData), $path);
        return $path;
    }
}
