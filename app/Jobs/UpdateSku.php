<?php

namespace App\Jobs;

use App\Exports\AlikeSpuUploadResultExport;
use App\Exports\SampleUploadResultExport;
use App\Exports\UpdateSkuResultExport;
use App\Exports\UpdateSpuResultExport;
use App\Http\Models\AlikeSpuCenterModel;
use App\Http\Models\BrandModel;
use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Models\SampleClassModel;
use App\Http\Models\TaskLogModel;
use App\Http\Models\AlikeSpuModel;
use App\Http\Models\SampleModel;
use App\Http\Models\UploadLogModel;
use App\Http\Queue\RabbitQueueModel;
use App\Http\Services\SpuService;
use App\Imports\AlikeSpuImport;
use App\Imports\SampleImport;
use App\Imports\UpdateSkuImport;
use App\Imports\UpdateSpuClassImport;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class UpdateSku implements ShouldQueue
// class UpdateSku
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;
    public $timeout = 36000;

    private $excelData;
    private $excelPath;
    private $adminId;
    private $adminName;
    private $logId;
    private $type;

    public function __construct($logId, $excelPath)
    {
        $this->excelPath = $excelPath;
        $log = TaskLogModel::where('id', $logId)->first()->toArray();
        $this->adminId = $log['admin_id'];
        $this->adminName = $log['admin_name'];
        $this->logId = $logId;
    }


    public function handle()
    {
        ini_set('pcre.backtrack_limit', 5000000);
        ini_set('pcre.recursion_limit', 5000000);
        ini_set('memory_limit', -1);
        $data = Excel::toArray(new UpdateSkuImport(), $this->excelPath)[0];
        unset($data[0]);
        $data = array_values($data);
        $this->excelData = $data;
        $resultData = [];
        //已经存在的数量
        $successCount = 0;
        $failedCount = 0;
        $taskLogModel = new TaskLogModel();
        $queue = new RabbitQueueModel('sku');
        try {
            if (count($this->excelData) < 1) {
                $taskLogModel->updateTaskLog($this->logId, [
                    'status' => TaskLogModel::STATUS_FAILED,
                    'log' => '上传处理的excel数据不能为空',
                ]);
                return true;
            }
            if (count($this->excelData[0]) != 2) {
                $taskLogModel->updateTaskLog($this->logId, [
                    'status' => TaskLogModel::STATUS_FAILED,
                    'log' => '上传的excel表头不一致,请确认是否使用最新的模板',
                ]);
                return true;
            }
            //分批更新
            $excelData = collect($this->excelData)->chunk(10)->toArray();
            foreach ($excelData as $key => $skuList) {
                //组装分批数据
                $postData = [];
                foreach ($skuList as $item) {
                    $skuId = trim($item[0]);
                    if (empty($skuId)) {
                        array_push($result, '失败:SKUID不能为空');
                        $failedCount++;
                        $resultData[] = $result;
                        continue;
                    }
                    $goodsLabelString = trim($item[1]);
                    $result = [
                        $skuId . "\t",
                        $goodsLabelString,
                    ];
                    $goodsLabel = config('field.GoodsLabelForIedgeStringMap')[$goodsLabelString] ?? '';
                    $goodsLabel = empty($goodsLabel) ? config('field.GoodsLabelStringMap')[$goodsLabelString] ?? '' : $goodsLabel;
                    if (empty($goodsLabel) || empty($goodsLabelString)) {
                        array_push($result, '失败:找不到对应的显示类型');
                        $failedCount++;
                        $resultData[] = $result;
                        continue;
                    }
                    $postData[] = [
                        "goods_id" => $skuId,
                        "goods_label" => $goodsLabel
                    ];

                    $resultData[] = [
                        $skuId . "\t", $goodsLabelString, '校验通过,已经投放更新队列,请耐心等待完成',
                    ];
                    $successCount++;
                }
                //还要去分发到spu的辐射服务
                $queue->insertRawQueue('', $postData, 'sku_update');

            }
            $fileName = TaskLogModel::where('id', $this->logId)->value('file_name');
            if ($resultData) {
                //成功后还要插入一条日志记录
                $content = date('Y-m-d H:i:s', time()) . '上传完成,文件名为 : ' . $fileName . ' 处理的成功的SKU有' . $successCount .
                    '个,处理失败的有' . $failedCount . '个';
            } else {
                $content = date('Y-m-d H:i:s', time()) . '处理失败,文件名为 : ' . $fileName . ' 请查看下载结果';
            }
            $resultFileUrl = $this->createResultExcel($resultData);
            TaskLogModel::where('id', $this->logId)->update([
                'log' => $content,
                'status' => TaskLogModel::STATUS_OK,
                'update_time' => time(),
                'result_file_url' => $resultFileUrl,
                'batch_sn' => $this->logId,
            ]);

        } catch (\Exception $exception) {
            TaskLogModel::where('id', $this->logId)->update([
                'log' => $exception->getMessage() . ' ' . $exception->getLine(),
                'status' => TaskLogModel::STATUS_IN_PROGRESS,
                'result_file_url' => '',
                'batch_sn' => $this->logId,
            ]);
            dump($exception->getMessage() . ' ' . $exception->getLine());
            \Log::error($exception->getMessage() . ' ' . $exception->getLine());
        } finally {
            unlink($this->excelPath);
        }

    }

    //生成结果数据
    public function createResultExcel($resultData)
    {
        $header = ['SKUID', '显示类型', '处理结果'];
        array_unshift($resultData, $header);
        $fileName = 'updateSkuClassResult_' . date('Y-m-d_H-i-s', time()) . '.xlsx';
        $path = 'updateSkuClassResult' . DIRECTORY_SEPARATOR . $fileName;
        Excel::store(new UpdateSkuResultExport($resultData), $path);
        return $path;
    }
}
