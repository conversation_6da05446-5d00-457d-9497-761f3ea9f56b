<?php

namespace App\Jobs;

use App\Http\Models\SkuSeriesItemModel;
use App\Http\Models\SkuSeriesModel;
use App\Http\Services\MessageService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Redis;

//class CheckSkuSeries implements ShouldQueue
class CheckSkuSeries
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $redis = Redis::connection('sku');
        $spuRedis = Redis::connection('spu');
        $skuSeries = SkuSeriesModel::select(['id', 'class_id1', 'class_id2', 'standard_brand_id'])->get()->toArray();
        $hasAbnormalSeries = false;
        foreach ($skuSeries as $series) {
            //遍历sku_series_item表里面的所有数据,发现如果分类对不上则记录下来
            $abnormalCount = 0;
            SkuSeriesItemModel::where('series_id', $series['id'])->chunk(50, function ($itemList) use ($redis, $spuRedis, &$abnormalCount, $series) {
                $abnormalSkuIds = [];
                $normalSkuIds = [];
                $itemList = $itemList->toArray();
                $skuIdClassIdMap = [];
                foreach ($itemList as $item) {
                    $sku = $redis->hget('sku', $item['sku_id']);
                    $sku = json_decode($sku, true);
                    $spu = json_decode($spuRedis->hget('spu', $sku['spu_id']), true);
                    //获取对应的分类
                    $skuIdClassIdMap[$item['sku_id']] = [
                        'class_id1' => $spu['class_id1'] ?? 0,
                        'class_id2' => $spu['class_id2'] ?? 0,
                    ];
                }
                //判断当前的分类id和redis的分类id是否对的上
                foreach ($itemList as $item) {
                    $skuId = $item['sku_id'];
                    $itemSku = \Arr::get($skuIdClassIdMap, $skuId);
                    if (empty($itemSku)) {
                        continue;
                    }
                    if ($series['class_id1'] != $itemSku['class_id1']) {
                        $abnormalSkuIds[] = $skuId;
                        $abnormalCount++;
                        continue;
                    }
                    if (!empty($series['class_id2']) && $series['class_id2'] != $itemSku['class_id2']) {
                        $abnormalSkuIds[] = $skuId;
                        $abnormalCount++;
                        continue;
                    }
                    $spu['s_brand_id'] = $spu['s_brand_id'] ?? 0;
                    if ($series['standard_brand_id'] != $spu['s_brand_id']) {
                        $abnormalSkuIds[] = $skuId;
                        $abnormalCount++;
                        continue;
                    }
                    $normalSkuIds[] = $skuId;
                }
                SkuSeriesItemModel::whereIn('sku_id', $abnormalSkuIds)->update([
                    'is_abnormal' => 1,
                    'update_time' => time()
                ]);
                SkuSeriesItemModel::whereIn('sku_id', $normalSkuIds)->update([
                    'is_abnormal' => -1,
                    'update_time' => time()
                ]);
            });

            if ($abnormalCount) {
                $hasAbnormalSeries = true;
            }
            SkuSeriesModel::where('id', $series['id'])->update([
                'update_time' => time(),
                'abnormal_count' => $abnormalCount
            ]);
        }

        //如果有异常,则发邮件出去
        if ($hasAbnormalSeries) {
            return;
            $data['data']['title'] = "【商品系列管理】异常预警";
            $data['data']['content'] = "商品系列管理中存在商品绑定的分类/品牌异常，请前往【基石系统】-【商品管理】-【商品系列管理】中查看详情";
            (new MessageService())->sendMessage('supplier_apply_admin_notify', $data, '<EMAIL>', true);
            //(new MessageService())->sendMessage('supplier_apply_admin_notify', $data, '<EMAIL>', true);
        }
    }
}
