<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use App\Http\Models\TaskLogModel;
use App\Http\Services\SkuService;
use Illuminate\Support\Facades\Log;
use App\Http\Queue\RabbitQueueModel;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Redis;
use App\Exports\UpdateSpuResultExport;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use App\Imports\ShopDistributeSkuImport;
use Illuminate\Queue\InteractsWithQueue;
use App\Http\Models\BigData\ShopSkuModel;
use App\Http\Models\BigData\ShopInfoModel;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Http\Services\ShopDistributeSkuService;
use App\Http\Models\BigData\ShopDistributeSkuModel;

class ImportShopDistributeSkuJob implements ShouldQueue
 //class ImportShopDistributeSkuJob
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $filePath;
    protected $shopId;
    protected $userId;
    protected $userName;
    protected $logId;

    /**
     * Create a new job instance.
     *
     * @param array $skuIds
     * @param int $shopId
     * @param int $userId
     * @param string $userName
     * @param string $taskId
     * @return void
     */
    public function __construct(int $logId, string $filePath, int $shopId, int $userId, string $userName)
    {
        $this->filePath = $filePath;
        $this->shopId = $shopId;
        $this->userId = $userId;
        $this->userName = $userName;
        $this->logId = $logId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        ini_set('pcre.backtrack_limit', 5000000);
        ini_set('pcre.recursion_limit', 5000000);
        ini_set('memory_limit', -1);
        $taskLogModel = new TaskLogModel();
        try {
            $fileName = TaskLogModel::where('id', $this->logId)->value('file_name');
            $platform = ShopInfoModel::where('shop_id', $this->shopId)->value('platform');
            $filePath = $this->filePath;
            $data = Excel::toCollection(new ShopDistributeSkuImport, $filePath, null);
            $excelData = $data[0];
            $allItems = [];
            $queue = (new RabbitQueueModel('trading'));
            $taskLogModel->updateTaskLog($this->logId, [
                'status' => TaskLogModel::STATUS_IN_PROGRESS,
                'log' => '开始执行任务,总数量为 : ' . count($excelData),
            ]);
            $resultData = [];
            foreach ($excelData as $index => $item) {
                $item = $item->toArray();
                if ($index == 0) {
                    continue;
                }
                if (count($item) != 8) {
                    $taskLogModel->updateTaskLog($this->logId, [
                        'status' => TaskLogModel::STATUS_FAILED,
                        'log' => '表头数量不规范,表头数量不为8',
                    ]);
                    continue;
                }
                $failedCount = 0;
                $successCount = 0;
                $skuId = (string)(trim($item[0]));
                $purchaseCoefficient = (float)trim($item[1]);
                $purchasePrice = (float)trim($item[2]);
                $saleCoefficient = (float)trim($item[3]);
                $salePrice = (float)trim($item[4]);
                $moq = (int)trim($item[5]);
                $multiple = (int)trim($item[6]);
                $goodsTitle = trim($item[7]);
                $result = [
                    $skuId,
                    $purchaseCoefficient,
                    $purchasePrice,
                    $saleCoefficient,
                    $salePrice,
                    $moq,
                    $multiple,
                    $goodsTitle,
                ];
                if (empty($skuId)) {
                    array_push($result, '失败:sku_id为空');
                    $failedCount++;
                    $resultData[] = $result;
                    continue;
                }
                //先判断是否存在
                $exist = ShopDistributeSkuModel::where('sku_id', $skuId)->where('shop_id', $this->shopId)->exists();
                if ($exist) {
                    $existItem = ShopDistributeSkuModel::where('sku_id', $skuId)->where('shop_id', $this->shopId)->first();
                    if ($purchaseCoefficient) {
                        $existItem->purchase_coefficient = $purchaseCoefficient;
                    }
                    if ($saleCoefficient) {
                        $existItem->sale_coefficient = $saleCoefficient;
                    }
                    if ($moq) {
                        $existItem->moq = $moq;
                    }
                    if ($multiple) {
                        $existItem->multiple = $multiple;
                    }
                    if ($goodsTitle) {
                        $existItem->goods_title = $goodsTitle;
                    }
                    if ($salePrice) {
                        $existItem->sale_price = $salePrice;
                    }
                    if ($purchasePrice) {
                        $existItem->purchase_price = $purchasePrice;
                    }
                    $existItem->update_time = time();
                    $existItem->update_name = $this->userName;
                    $existItem->save();
                    $queue->insertRawQueue('', (int)$existItem['id'], 'liexin_nplm_update_push_pool_sku');
                    array_push($result, '成功');
                    $resultData[] = $result;
                    $successCount++;
                    continue;
                } else {
                    $skuInfo = (new SkuService())->getSkuCacheInfoTemp($skuId);
                    if ($skuInfo) {
                        $skuItem['sku_id'] = $skuId;
                        $skuItem['shop_id'] = $this->shopId;
                        if ($purchaseCoefficient) {
                            $skuItem['purchase_coefficient'] = $purchaseCoefficient;
                        }
                        if ($saleCoefficient) {
                            $skuItem['sale_coefficient'] = $saleCoefficient;
                        }
                        $skuItem['create_time'] = time();
                        $skuItem['create_name'] = $this->userName;
                        $skuItem['spu_id'] = $skuInfo['spu_id'];
                        $skuItem['platform'] = $platform;
                        if ($moq) {
                            $skuItem['moq'] = $moq;
                        }
                        if ($multiple) {
                            $skuItem['multiple'] = $multiple;
                        }
                        if ($goodsTitle) {
                            $skuItem['goods_title'] = $goodsTitle;
                        }
                        if ($salePrice) {
                            $skuItem['sale_price'] = $salePrice;
                        }
                        if ($purchasePrice) {
                            $skuItem['purchase_price'] = $purchasePrice;
                        }
                        $skuItem['supplier_id'] = $skuInfo['supplier_id'];
                        $skuItem['supplier_code'] = $skuInfo['canal'];
                        $skuItem['class_id2'] = $skuInfo['class_id2'];
                        $skuItem['ladder_price'] = json_encode($skuInfo['ladder_price']);
                        $skuItem['mpq'] = $skuInfo['mpq'];
                        if (empty($skuInfo['s_brand_id'])) {
                            array_push($result, '失败:没有对应的标准品牌');
                            $failedCount++;
                            $resultData[] = $result;
                            continue;
                        }
                        $skuItem['standard_brand_id'] = $skuInfo['s_brand_id'];
                        $skuItem['spu_name'] = $skuInfo['spu_name'];
                        $skuItem['stock'] = $skuInfo['stock'];
                        //获取京东标题和sn
                        $shopSku = ShopSkuModel::where('lx_sku_id', $skuId)->where('shop_id', $this->shopId)->first();
                        if (empty($shopSku)) {
                            // continue;
                        }
                        $skuItem['sku_sn'] = $shopSku['sku_sn'] ?? '';
                        $allItems[] = $skuItem;
                        $id = ShopDistributeSkuModel::insertGetId($skuItem);
                        $queue->insertRawQueue('', $id, 'liexin_nplm_update_push_pool_sku');
                        array_push($result, '成功');
                        $resultData[] = $result;
                        $successCount++;
                        if (($index + 1) % 100 == 0) {
                            $taskLogModel->updateTaskLog($this->logId, [
                                'log' => '上传处理中,文件名为 : ' . $fileName . ' 处理的成功的有' . $successCount .
                                    '个,处理失败的有' . $failedCount . '个',
                                'status' => TaskLogModel::STATUS_IN_PROGRESS,
                                'update_time' => time(),
                            ]);
                        }
                    } else {
                        if (($index + 1) % 100 == 0) {
                            $taskLogModel->updateTaskLog($this->logId, [
                                'log' => '上传处理中,文件名为 : ' . $fileName . ' 处理的成功的有' . $successCount .
                                    '个,处理失败的有' . $failedCount . '个',
                                'status' => TaskLogModel::STATUS_IN_PROGRESS,
                                'update_time' => time(),
                            ]);
                        }
                        array_push($result, '失败:没有对应的sku缓存 : '.$skuId);
                        $failedCount++;
                        $resultData[] = $result;
                    }
                }
            }
            $resultFileUrl = $this->createResultExcel($resultData);
            TaskLogModel::where('id', $this->logId)->update([
                'log' => '上传完成,文件名为 : ' . $fileName . ' 处理的成功的有' . $successCount .
                    '个,处理失败的有' . $failedCount . '个',
                'status' => TaskLogModel::STATUS_OK,
                'update_time' => time(),
                'result_file_url' => $resultFileUrl,
                'batch_sn' => $this->logId,
            ]);
        } catch (\Throwable $th) {
            //throw $th;
            Log::error($th->getMessage());
            \dump($th->getMessage());
        } finally {
            unlink($this->filePath);
        }

        // $allItems = array_chunk($allItems, 1000);
        // foreach ($allItems as $items) {
        //     ShopDistributeSkuModel::insert($items);
        // }
    }

    //生成结果数据
    public function createResultExcel($resultData)
    {
        $header = ['SKUID', '京东采购价系数', '京东采购价', '京东销售价系数', '京东销售价', '起订量', '递增量', '京东商品标题', '处理结果'];
        array_unshift($resultData, $header);
        $fileName = 'importShopDistributeSkuResult_' . date('Y-m-d_H-i-s', time()) . '.xlsx';
        $path = 'importShopDistributeSkuResult' . DIRECTORY_SEPARATOR . $fileName;
        Excel::store(new UpdateSpuResultExport($resultData), $path);
        return $path;
    }
}
