<?php

namespace App\Jobs;

use App\Exceptions\InvalidRequestException;
use App\Exports\AlikeSpuUploadResultExport;
use App\Exports\SampleUploadResultExport;
use App\Http\Models\AlikeSpuCenterModel;
use App\Http\Models\BigData\ShopSkuModel;
use App\Http\Models\BrandModel;
use App\Http\Models\SampleClassModel;
use App\Http\Models\SampleLogModel;
use App\Http\Models\AlikeSpuModel;
use App\Http\Models\SampleModel;
use App\Http\Models\UploadLogModel;
use App\Imports\AlikeSpuImport;
use App\Imports\SampleImport;
use App\Imports\ShopSkuStockImport;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class UpdateShopSkuStock
// class UpdateShopSkuStock implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, SerializesModels;

    //use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;
    public $timeout = 36000;

    private $excelPath;
    private $adminId;
    private $adminName;
    private $logId;
    private $type;

    public function __construct($excelPath)
    {
        $this->excelPath = $excelPath;
    }


    public function handle()
    {
        $skuList = Excel::toArray(new ShopSkuStockImport(), $this->excelPath)[0];
        try {
            foreach ($skuList as $key => $sku) {
                $id = (int)$sku[0];
                $stock = (int)$sku[8];
                ShopSkuModel::where('id', $id)->update([
                    'stock' => $stock,
                    'update_time' => time(),
                ]);
                $shopSku = ShopSkuModel::where('id', $id)->first();
                if (empty($shopSku)) {
                    continue;
                }
                $shopSku = $shopSku->toArray();
                $result = Http::post(config('website.ShopApi') . '/update_stock', [
                    'shop_id' => $shopSku['shop_id'],
                    'shop_sku_id' => $shopSku['shop_sku_id'],
                    'stock' => $stock,
                ])->json();
            }
        } catch (\Exception $exception) {
            \Log::error($exception->getMessage() . ' ' . $exception->getLine());
            throw new InvalidRequestException($exception->getMessage());
        } finally {
            // unlink($this->excelPath);
        }
    }
}
