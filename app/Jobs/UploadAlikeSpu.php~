<?php

namespace App\Jobs;

use App\Exports\AlikeSpuUploadResultExport;
use App\Http\Models\AlikeSpuCenterModel;
use App\Http\Models\AlikeSpuLogModel;
use App\Http\Models\AlikeSpuModel;
use App\Http\Models\UploadLogModel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class UploadAlikeSpu
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;
    public $timeout = 120;

    private $excelData;
    private $adminId;
    private $adminName;
    private $logId;
    private $type;

    public function __construct($logId, $alikeSpuList)
    {
        $this->excelData = $alikeSpuList;
        $log = AlikeSpuLogModel::where('id', $logId)->first()->toArray();
        $this->adminId = $log['admin_id'];
        $this->adminName = $log['admin_name'];
        $this->logId = $logId;
    }


    public function handle()
    {
        $resultData = [];
        //已经存在的数量
        $repeatedExist = 0;
        $failedCount = 0;
        $totalNum = 0;
        $isRepeated = false;
        foreach ($this->excelData as $key => $data) {
            if ($key == 0) {
                continue;
            }
            $alikeSpuName = trim($data[0]);
            $spuName = trim($data[1]);
            $pinToPin = trim($data[2]);
            $result = [
                $spuName,
                $alikeSpuName,
                $pinToPin,
            ];

            //先去判断映射是否已经存在
            $exist = AlikeSpuModel::where('spu_name', $spuName)->where('alike_spu_name', $alikeSpuName)->exists();
            if ($exist) {
                $isRepeated = true;
                $repeatedExist++;
            }

            if (empty($spuName) || empty($alikeSpuName)) {
                array_push($result, '失败:国产替代型号或者被替代型号不能为空');
                $failedCount++;
                $resultData[] = $result;
                continue;
            }

            //获取国产替代型号对应的所有spu
            $alikeSpuList = DB::connection('mongodb')->collection('spu')->where('spu_name',
                $alikeSpuName)->get()->toArray();
            if (empty($alikeSpuList)) {
                array_push($result, '失败:国产替代型号找不到对应的spu');
                $failedCount++;
                $resultData[] = $result;
                continue;
            }
            //判断被替代型号是否存在
            $existSpu = DB::connection('mongodb')->collection('spu')->where('spu_name', $spuName)->exists();
            if (!$existSpu) {
                array_push($result, '失败:被替代型号找不到对应的spu');
                $failedCount++;
                $resultData[] = $result;
                continue;
            }
            $alikeSpuIds = $alikeSpuNames = [];
            //然后把对应的所有替代spu存到中间表
            foreach ($alikeSpuList as $alikeSpu) {
                $alikeSpuIds[] = $alikeSpu['spu_id'];
                $alikeSpuNames[] = $alikeSpu['spu_name'];
            }

            //先去中间表找一下是否存在相同的被替代型号
            $alikeSpuCenter = AlikeSpuCenterModel::where('spu_name', $spuName)->first();
            $alikeSpuCenter = !empty($alikeSpuCenter) ? $alikeSpuCenter->toArray() : [];
            //如果存在,那么就要去拼接原有的数据
            if ($alikeSpuCenter) {
                $alikeSpuIdsCenter = array_merge($alikeSpuIds, explode(',', $alikeSpuCenter['alike_spu_ids']));
                $alikeSpuNamesCenter = array_merge($alikeSpuNames, explode(',', $alikeSpuCenter['alike_spu_names']));
                $centerData['alike_spu_ids'] = implode(',', array_unique($alikeSpuIdsCenter));
                $centerData['alike_spu_names'] = implode(',', array_unique($alikeSpuNamesCenter));
                $centerData['update_time'] = time();
                $centerData['is_sync'] = 0;
                $centerData['batch_sn'] = implode(',',array_merge([$this->logId], explode(',',  $alikeSpuCenter['batch_sn'])));
                AlikeSpuCenterModel::where('spu_name', $spuName)->update($centerData);
            } else {
                $centerData['spu_name'] = $spuName;
                $centerData['alike_spu_ids'] = implode(',', array_unique($alikeSpuIds));
                $centerData['alike_spu_names'] = implode(',', array_unique($alikeSpuNames));
                $centerData['add_time'] = time();
                $centerData['admin_id'] = $this->adminId;
                $centerData['batch_sn'] = $this->logId;
                AlikeSpuCenterModel::insert($centerData);
                $totalNum++;
            }

            if ($isRepeated) {
                AlikeSpuModel::where('spu_name', $spuName)->where('alike_spu_name', $alikeSpuName)->update([
                    'update_time' => time(),
                ]);
            } else {
                $insertAlikeSpu = [
                    'alike_spu_name' => $alikeSpuName,
                    'spu_name' => $spuName,
                    'add_time' => time(),
                    'admin_id' => $this->adminId,
                    'admin_name' => $this->adminName,
                    'pin_to_pin' => $pinToPin == '是' ? 1 : -1
                ];
                AlikeSpuModel::insert($insertAlikeSpu);
            }

            $result[] = '成功,等待映射脚本处理完成';
            $resultData[] = $result;
        }
        if ($resultData) {
            //成功后还要插入一条日志记录
            $content = date('Y-m-d H:i:s', time()) . '上传完成,请耐心等待映射脚本执行完毕,已经存在的映射有' . $repeatedExist .
                '个,新插入的有' . $totalNum . '个,新增失败的有' . $failedCount . '个';
        } else {
            $content = date('Y-m-d H:i:s', time()) . '处理失败,请查看下载结果';
        }
        $resultFileUrl = $this->createResultExcel($resultData);
        AlikeSpuLogModel::where('id', $this->logId)->update([
            'log' => $content,
            'status' => AlikeSpuLogModel::STATUS_IN_PROGRESS,
            'update_time' => time(),
            'result_file_url' => $resultFileUrl,
            'batch_sn' => $this->logId,
        ]);
    }

    //生成结果数据
    public function createResultExcel($resultData)
    {
        $header = ['国产替代型号', '被替代型号', 'pin to pin', '处理结果'];
        array_unshift($resultData, $header);
        $fileName = 'uploadAlikeSpuResult_' . date('Y-m-d_H-i-s', time()) . '.csv';
        $path = 'uploadAlikeSpuResult' . DIRECTORY_SEPARATOR . $fileName;
        Excel::store(new AlikeSpuUploadResultExport($resultData), $path);
        return $path;
    }
}
