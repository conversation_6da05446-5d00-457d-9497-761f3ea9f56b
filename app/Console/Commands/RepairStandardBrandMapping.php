<?php

namespace App\Console\Commands;

use App\Http\Models\BrandModel;
use Illuminate\Console\Command;
use App\Http\Models\SampleModel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use App\Http\Models\StandardBrandMappingModel;
use App\Http\Services\StandardBrandMappingService;

class RepairStandardBrandMapping extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'repair:standard-brand-mapping';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '修复标准品牌映射';

    /**
     * Create a new command instance.
     *
     * @return void
     */

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $isUpdate = false;
        //先找出BrandModel里面名称重复的品牌
        $brandList = BrandModel::where('brand_name', '!=', '')->groupBy('brand_name')->having(DB::raw('count(brand_id)'), '>', 1)
            ->limit(1000)->get()->toArray();
        //然后判断是否有标准品牌映射,没有的话,那就拿有的那个同名的标准品牌做映射
        foreach ($brandList as $brand) {
            //找出所有brand_id
            $brandIds = BrandModel::where('brand_name', $brand['brand_name'])->pluck('brand_id')->toArray();
            //找出所有标准品牌映射
            $standardBrandMapping = StandardBrandMappingModel::whereIn('brand_id', $brandIds)->get()->toArray();
            //如果标准品牌映射存在,那就拿标准品牌映射的brand_id做映射
            if (count($brandIds) == count($standardBrandMapping)) {
                continue;
            }

            if (empty($standardBrandMapping)) {
                // \dump('完全没有映射 :' . $brand['brand_name']);
                continue;
            }

            if (count($brandIds) > count($standardBrandMapping) && !empty($standardBrandMapping)) {
                foreach ($brandIds as $brandId) {
                    // \dump('有映射,但是映射不全 :' . $brand['brand_name'] . ' | brand_id:' . $brandId);
                    //拿第一个映射的standard_brand_id
                    $mapping = $standardBrandMapping[0];
                    $standardBrandId = $mapping['standard_brand_id'];
                    \dump("找到需要映射映射的standard_brand_id:" . $standardBrandId . '和需要映射的 | brand_id:' . $brandId);

                    $mapping = [
                        'brand_id' => $brandId,
                        'standard_brand_id' => $standardBrandId,
                        'status' => 1,
                        'add_time' => time(),
                        'admin_id' => 1000,
                        'update_time' => time(),
                        'remark' => '系统主动修复品牌映射',
                    ];
                    //先判断是否存在
                    $exist = StandardBrandMappingModel::where('brand_id', $brandId)->first();
                    if (empty($exist)) {
                        \dump('不存在映射 :' . $brand['brand_name'] . ' | standard_brand_id:' . $standardBrandId . ' | brand_id:' . $brandId);
                        continue;
                    }
                    if (empty($exist) && $isUpdate) {
                        StandardBrandMappingModel::insertGetId($mapping);
                        (new StandardBrandMappingService())->saveStandardBrandMappingToRedis($mapping);
                    } else {
                        // \dump('已经存在映射 :' . $brand['brand_name'] . ' | standard_brand_id:' . $standardBrandId . ' | brand_id:' . $brandId);
                    }
                }
            }
        }
    }

    public static function deleteSystemMapping()
    {
        $mapping = StandardBrandMappingModel::where('remark', "系统主动修复品牌映射")->get()->toArray();
        $redis = Redis::connection('sku');
        foreach ($mapping as $item) {
            $redis->hdel("standard_brand_mapping", $item['brand_id']);
            StandardBrandMappingModel::where('id', $item['id'])->delete();
        }
    }
}
