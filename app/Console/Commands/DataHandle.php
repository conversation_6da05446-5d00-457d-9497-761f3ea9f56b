<?php

namespace App\Console\Commands;

use App\Http\Models\SampleModel;
use App\Http\Services\DataService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;

class DataHandle extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'DataHandle';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '数据处理';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // DataService::init();
        DataService::emptyEncap();
        DataService::initEncapCache();
    }
}
