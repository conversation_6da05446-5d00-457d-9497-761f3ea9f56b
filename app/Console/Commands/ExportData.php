<?php

namespace App\Console\Commands;

use App\Scopes\SkuScope;
use Illuminate\Support\Arr;
use Illuminate\Console\Command;
use App\Http\Models\SampleModel;
use App\Http\Services\SkuService;
use App\Http\Services\DataService;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Services\SpuAttrService;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Concerns\FromArray;
use App\Http\Models\Cms\CmsUserIntraCodeModel;

class ExportData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ExportData';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '导出数据';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ini_set('memory_limit', '4096M');
        $isSingle = request()->get('isSingle', 0);
        if ($isSingle) {
            $canalList = ['********'];
        } else {
            $canalList = [
                '********',
            ];
        }
        $excelHeader = [
            'SPUID',
            'SKUID',
            'SPU型号',
            'SKU型号',
            '品牌',
            '标准品牌',
            'SKU分类一级类目',
            '分类二级类目',
            '参数',
            '来源',
            '供应商编码',
            'SKU采购',
            '库存',
            '批次',
            '封装',
            '标准包装量',
            '起订量',
            '递增量',
            '成本阶梯价',
            '状态',
            '上架有效期',
            '交期'
        ];

        $userList = (new CmsUserIntraCodeModel())->getCodeUsersForSelect();
        foreach ($canalList as $canal) {
            $excelData = [];
            //遍历100张sku表
            for ($i = 0; $i < 10; $i++) {
                $db = 'sku_' . $i;
                for ($j = 0; $j < 10; $j++) {
                    $table = 'sku_' . $j;
                    $data = DB::connection($db)->table($table)->where('supplier_id', 17)
                        ->where('canal', $canal)
                        ->where('goods_status', 1)
                        ->select(['goods_id'])->get();
                    if ($isSingle && count($data) > 0) {
                        dd($data);
                    }
                    dump('获取到数据 : ' . $db . '/' . $table . '/' . count($data));
                    foreach ($data as $item) {
                        $sku = (new SkuService())->getSkuCacheInfo($item->goods_id);
                        $ladderPrice = $sku['ladder_price'];
                        $ladderPriceStr = '';
                        foreach ($ladderPrice as $key => $value) {
                            $ladderPriceStr .= "阶梯数量 : {$value['purchases']} 人民币价格 : {$value['price_cn']} 美金价格 : {$value['price_us']} \n";
                        }
                        $hasSpuAttr = SpuAttrService::checkHasSpuAttr($sku['spu_id']);
                        if (!$hasSpuAttr) {
                            continue;
                        }
                        $excelData[] = [
                            $sku['spu_id'] . "\t",
                            $item->goods_id . "\t",
                            $sku['spu_name'] ?? '',
                            $sku['goods_name'] ?? '',
                            $sku['brand_name'] ?? '',
                            $sku['standard_brand_name'] ?? '',
                            $sku['class_name1'] ?? '',
                            $sku['class_name2'] ?? '',
                            $hasSpuAttr ? '有' : '无',
                            Arr::get(\config('field.SkuSource'), $sku['source'] ?? 0),
                            $sku['canal'] ?? '',
                            $userList[$sku['encoded']] ?? '',
                            $sku['stock'] ?? 0,
                            $sku['batch'] ?? '',
                            $sku['encap'] ?? '',
                            $sku['mpq'] ?? 0,
                            $sku['moq'] ?? 0,
                            $sku['multiple'] ?? 0,
                            $ladderPriceStr,
                            '上架',
                            $sku['cp_time'] ? date('Y-m-d H:i:s', $sku['cp_time']) : '',
                            '大陆交期: ' . ($sku['cn_delivery_time'] ?: '无') . '| 香港交期: ' . ($sku['hk_delivery_time'] ?: '无')
                        ];
                    }
                }
            }
            //直接保存成excel,用laravel-excel
            array_unshift($excelData, $excelHeader);
            Excel::store(new class($excelData) implements FromArray {
                protected $data;
                public function __construct($data)
                {
                    $this->data = $data;
                }
                public function array(): array
                {
                    return $this->data;
                }
            }, 'data/' . $canal . '.xlsx', 'public');
        }
    }
}
