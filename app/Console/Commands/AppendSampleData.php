<?php

namespace App\Console\Commands;

use App\Http\Models\SampleModel;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;

class AppendSampleData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'AppendSampleData';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '补充样品数据';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        SampleModel::chunk(50, function ($samples) {
            $redis = Redis::connection('sku');
            $spuRedis = Redis::connection('spu');
            $mongo = \DB::connection('mongodb');
            foreach ($samples as $sample) {
                if (strlen($sample['goods_id']) > 10) {
                    $sku = $redis->hget('sku', $sample['goods_id']);
                    if (empty($sku)) {
                        echo '找不到联营对应的SKU' . PHP_EOL;
                        continue;
                    }
                    $sku = json_decode($sku, true);
                    $spu = $spuRedis->hget('spu', $sku['spu_id']);
                    if (empty($spu)) {
                        echo '找不到联营对应的SPU' . PHP_EOL;
                        continue;
                    }
                    $sampleType = 2;
                } else {
                    //如果是自营的话,那么就要去取自营的信息
                    $sku = $redis->hget('Self_SelfGoods', $sample['goods_id']);
                    if (empty($sku)) {
                        echo '找不到自营SKU' . PHP_EOL;
                        continue;
                    }
                    $sku = json_decode($sku, true);
                    $brand = $redis->hget('Self_Brand', $sku['brand_id']);
                    $brand = json_decode($brand, true);
                    $spu = $mongo->table('spu')->where('spu_name', $sku['goods_name'])
                        ->where('brand_id', $brand['brand_id'])->first();
                    if (empty($spu)) {
                        echo '找不到自营对应的SPU' . PHP_EOL;
                        continue;
                    }
                    $sampleType = 1;
                }
                if (!$spu) {
                    continue;
                }
                $spu = json_decode($spu, true);
                $brandName = $redis->hget('brand', $spu['brand_id']);
                dump([
                    'brand_id' => $spu['brand_id'],
                    'brand_name' => $brandName,
                    'goods_name' => $spu['spu_name'],
                    'sample_type' => $sampleType,
                ]);
                SampleModel::where('id', $sample['id'])->update([
                    'brand_id' => $spu['brand_id'],
                    'brand_name' => $brandName,
                    'goods_name' => $spu['spu_name'],
                    'sample_type' => $sampleType,
                ]);
            }
        });
    }
}
