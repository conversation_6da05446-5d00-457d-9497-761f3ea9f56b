<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Carbon\CarbonPeriod;
use DateInterval;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class IndexStatistic extends Command
{
    /**
     * 首页数据统计
     *
     * @var string
     */
    protected $signature = 'statistic:index';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '首页数据统计';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     */
    public function handle()
    {
        //获取上一次跑数据的时间,用来防止中间出现停止统计的情况
        $timeInterval = self::getTimeInterval('all');
        $purchaseUsers = PurchaseUserService::getPurchaseUsers();
        self::statistics($purchaseUsers, $timeInterval, 'day');
        self::statistics($purchaseUsers, $timeInterval, 'month');
        echo "Completed";
    }

    const DATE_TYPE_DAY = 1;
    const DATE_TYPE_WEEK = 2;
    const DATE_TYPE_MONTH = 3;

    const CURRENCY_RMB = 1;
    const CURRENCY_USD = 2;
    const CURRENCY_USD_TO_RMB = 3;

    /*
     * 统计
     * 需要记录每日,每月的记录
     * 粒度是采购员
    */
    public static function statistics($purchaseUsers, $totalTimeInterval, $type = 'day')
    {
        //参数这个$totalTimeInterval是一个大的时间区间,要根据类型去拆成小的时间区间,因为存到mongo里面是分天或者分月的
        $perTimeInterval = self::getPerTimeInterval($totalTimeInterval, $type);
        $mongo = DB::connection('mongodb');
        foreach ($perTimeInterval as $timeInterval) {
            //找出所有采购员
            foreach ($purchaseUsers as $key => $user) {
                $purchaseOrders = self::getPurchaseOrders($user['userId'], $timeInterval);//采购单列表,用于下面的计算
                if (!$purchaseOrders) {
                    continue;
                }
                $purchaseAmount = self::calculatePurchaseAmount($purchaseOrders);//采购金额
                $grossProfit = self::calculateGrossProfit($purchaseOrders);//毛利
                $date = $timeInterval[0];
                $dayRecord = [
                    'purchase_uid' => $user['userId'],
                    'purchase_name' => $purchaseOrders[0]['purchase_name'],
                    'purchase_amount_rmb' => (float)Arr::get($purchaseAmount, self::CURRENCY_RMB, 0),
                    'purchase_amount_usd' => (float)Arr::get($purchaseAmount, self::CURRENCY_USD, 0),
                    'purchase_amount_usd_to_rmb' => (float)Arr::get($purchaseAmount, self::CURRENCY_USD_TO_RMB, 0),
                    'gross_profit' => (float)$grossProfit,
                    'date' => $timeInterval[0],
                    'date_string' => date('Y-m-d', $timeInterval[0]),
                    'create_time' => time(),
                ];
                if ($type == 'day') {
                    //先去mongo判断是否有今天的记录,有的话跳过
                    $existDayRecord = $mongo->collection('pur_statistics_index_day')
                        ->where('date', $date)->where('purchase_uid', (int)$user['userId'])->first();
                    if ($existDayRecord) {
                        continue;
                    }
                    $mongo->collection('pur_statistics_index_day')->insert($dayRecord);
                } else {
                    $month = $timeInterval[0];
                    $dayRecord['date'] = $month;
                    $dayRecord['date_string'] = date('Y-m',$month);
                    $monthRecord = $dayRecord;
                    $existRecord = $mongo->collection('pur_statistics_index_month')
                        ->where('date', $month)->where('purchase_uid', $user['userId'])->first();
                    if (!$existRecord) {
                        //如果不存在这条记录,那就取目前这条当作这个月的初始化数据
                        $mongo->collection('pur_statistics_index_month')->insert($monthRecord);
                    } else {
                        $mongo->collection('pur_statistics_index_month')
                            ->where('date', $month)->where('purchase_uid', $user['userId'])
                            ->update($monthRecord);
                    }
                }
            }
        }
    }

    //默认是获取所有时间,否则的话就是获取上一次跑的时间,去mongodb里面获取最后一条记录的天数
    private static function getTimeInterval($type = 'all')
    {
        $mongo = DB::connection('mongodb');
        $now = Carbon::now()->getTimestamp();
        if ($type == 'all') {
            //设定一个开始时间
            $start = Carbon::createFromDate('2021-11-11')->getTimestamp();
        } else {
            //去mongo找出最后一条记录的日期
            $lastRecord = $mongo->collection('pur_statistics_index_day')->orderBy('_id', 'desc')->first();
            $start = $lastRecord ? $lastRecord['create_time'] : Carbon::createFromDate('2021-11-11')->getTimestamp();;
        }
        return [$start, $now];
    }

    //获取分解后的每天间隔日期或者每月间隔日期
    public static function getPerTimeInterval($timeInterval, $type)
    {
        $perTimeInterval = [];
        $carbonStart = Carbon::createFromTimestamp($timeInterval[0]);
        $carbonEnd = Carbon::createFromTimestamp($timeInterval[1]);
        //根据传过来的timeInterval分成时间段
        if ($type == 'day') {
            //如果类型是天,那就分成多个天的时间段去统计
            $period = CarbonPeriod::create($carbonStart->toDateTime(), $carbonEnd->toDateTime());
            foreach ($period as $date) {
                $start = $date->startOfDay()->getTimestamp();
                $end = $date->endOfDay()->getTimestamp();
                $perTimeInterval[] = [$start, $end];
            }
        } else {
            //如果类型是月,那就分成多个月的时间段去统计,注意还要把当月也算出来
            $interval = DateInterval::createFromDateString('1 month');
            $period = CarbonPeriod::create($carbonStart->toDateTime(), $interval, $carbonEnd->toDateTime());
            foreach ($period as $month) {
                $start = $month->startOfMonth()->getTimestamp();
                $end = $month->endOfMonth()->getTimestamp();
                $perTimeInterval[] = [$start, $end];
            }
            //注意还要把当月也算出来
            if (count($perTimeInterval)>0 && end($perTimeInterval)[1] < $carbonEnd->timestamp) {
                $perTimeInterval[] = [
                    end($perTimeInterval)[1]+1,
                    $carbonEnd->timestamp,
                ];
            }
        }
        return $perTimeInterval;
    }

    //根据时间区间获取统采购单信息
    //不让数据库去帮我们做统计,而是全部拿出来自己算
    protected static function getPurchaseOrders($purchaseUid, $timeInterval)
    {
        return PurchaseOrderModel::select([
            'purchase_amount',
            'gross_profit',
            'currency',
            'exchange_rate',
            'purchase_name',
        ])->whereBetween('create_time', $timeInterval)
            ->where('purchase_uid', $purchaseUid)->get()->toArray();
    }

    //计算采购金额
    private static function calculatePurchaseAmount($purchaseOrders)
    {
        $amountRMB = $amountUSD = $amountUSDToRMB =  0;
        foreach ($purchaseOrders as $order) {
            //要区分USD和RMB
            if ($order['currency'] == self::CURRENCY_RMB) {
                $amountRMB += $order['purchase_amount'];
            } else {
                $amountUSD += $order['purchase_amount'];
                $amountUSDToRMB += $order['purchase_amount'] * $order['exchange_rate'];
            }
        }
        return [
            self::CURRENCY_RMB => round($amountRMB, 2),
            self::CURRENCY_USD => round($amountUSD, 2),
            self::CURRENCY_USD_TO_RMB => round($amountUSDToRMB,2),
        ];
    }

    //计算毛利
    private static function calculateGrossProfit($purchaseOrders)
    {
        $grossProfit = 0;
        foreach ($purchaseOrders as $order) {
            //如果是美金的,还要x汇率
            if ($order['currency'] == self::CURRENCY_USD) {
                $grossProfit += ($order['gross_profit'] * $order['exchange_rate']);
            } else {
                $grossProfit += $order['gross_profit'];
            }
        }

        return number_format($grossProfit, 2);
    }
}
