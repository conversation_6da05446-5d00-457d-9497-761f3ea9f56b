<?php

namespace App\Http\Middleware;

use Error;
use Closure;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Contracts\Container\Container;
use Illuminate\Contracts\Debug\ExceptionHandler;
use Psy\Exception\ErrorException;

class Monitor
{
    /**
     * The App container
     *
     * @var Container
     */
    protected $container;

    /**
     * The Monitor Client
     *
     * @var
     */
    protected $monitor;

    /**
     * Create a new middleware instance.
     *
     * @param Container $container
     */
    public function __construct(Container $container)
    {
        $this->container = $container;
    }

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        return $next($request);
    }

    /**
     * Handle the given exception.
     *
     * (Copy from Illuminate\Routing\Pipeline by <PERSON>)
     *
     * @param $passable
     * @param Exception $e
     * @return mixed
     * @throws Exception
     */
    protected function handleException($passable, Exception $e)
    {
        if (!$this->container->bound(ExceptionHandler::class) || !$passable instanceof Request) {
            throw $e;
        }

        $handler = $this->container->make(ExceptionHandler::class);

        $handler->report($e);

        return $handler->render($passable, $e);
    }


    /**
     * 发送文本类型的消息
     *
     * @param $content string 消息内容
     * @param array $atMobiles 被@人的手机号
     * @param bool $isAtAll 是否 @ 所有人
     * @throws SendErrorException
     */
    public function sendText($content, $atMobiles = [], $isAtAll = false)
    {
        $params = [
            'msgtype' => 'text',
            'text' => [
                'content' => $content,
            ],
            'at' => [
                'atMobiles' => $atMobiles,
                'isAtAll' => $isAtAll
            ]
        ];
        $this->send($params);
    }

    /**
     * 发送
     * @param array $params 请求需要的参数
     * @throws SendErrorException
     */
    private function send($params = [])
    {
        if (!config('monitorDing.enabled')) {
            \Log::info('~~ Monitor Ding ~~');
            \Log::info($params);
        } else {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, config("monitorDing.webhook"));
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json;charset=utf-8'));
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            if (config()) {
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
            }
            $data = json_decode(curl_exec($ch), true);
            curl_close($ch);

            if ($data['errcode']) {
                //                throw new SendErrorException($data['errmsg']);
            }
        }
    }
}
