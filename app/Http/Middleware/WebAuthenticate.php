<?php

namespace App\Http\Middleware;

use Illuminate\Support\Facades\Config;
use Closure;
use Illuminate\Support\Facades\Log;

class WebAuthenticate
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */

    public function handle($request, Closure $next)
    {
        $userId = $request->cookie('oa_user_id');
        $skey   = $request->cookie('oa_skey');

        $login = Config::get('website.login');
        if (!$userId || !$skey || (string)((int)$userId) != $userId || !preg_match('/^[a-zA-Z0-9]+$/', $skey)) {
            return $this->redirectTo($request);
        }

        $cookie = 'oa_user_id=' . $userId . '; oa_skey=' . $skey;
        $client = new \GuzzleHttp\Client();
        $rsp = $client->request('GET', $login['check'], [
            'headers'         => ['Cookie' => $cookie],
            'connect_timeout' => 5,
            'timeout'         => 10,
            'verify'          => false,
        ]);

        if ($rsp->getStatusCode() != 200) {
            Log::error("login error");
            abort(500);
        }

        $ret = json_decode($rsp->getBody());
        if ($ret->retcode != 0) {
            return $this->redirectTo($request);
        }

        $user = $ret->data;
        $user->header = $request->cookie('oa_header');
        $request->user = $user;
        $request->attributes->add(['user' => $user]);
        return $next($request);
    }

    protected function redirectTo($request)
    {
        $login = Config::get('website.login');
        if (! $request->expectsJson()) {
            return redirect($login['login'] . '?redirect=' . urlencode($request->url()));
        }
    }
}
