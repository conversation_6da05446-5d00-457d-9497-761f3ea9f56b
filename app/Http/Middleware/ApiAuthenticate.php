<?php

namespace App\Http\Middleware;

use App\Http\ApiHelper\Response;
use App\Http\Services\PermService;
use Illuminate\Support\Facades\Config;
use Closure;
use Illuminate\Support\Facades\Log;

class ApiAuthenticate
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */

    public function handle($request, Closure $next)
    {
        $source = $request->header("source"); //来源端：内部后台:pc  芯链系统：yunxin App: app 小程序：h5_app
        $userId = $skey = null;

        if ($source == 'pc') {
            $userId = $request->header('oa-user-id');
            $skey = $request->header('oa-skey');
        } else {
            if ($request->cookie()) {
                $userId = $request->cookie('oa_user_id');
                $skey = $request->cookie('oa_skey');
            }
        }

        if (empty($userId) || empty($skey)) {
            return $this->redirectTo($request);
        }

        $login = config::get('website.login');
        if (!$userId || !$skey || (string)((int)$userId) != $userId || !preg_match('/^[a-zA-Z0-9]+$/', $skey)) {
            return $this->redirectTo($request);
        }

        $cookie = 'oa_user_id=' . $userId . '; oa_skey=' . $skey;
        $client = new \GuzzleHttp\Client();
        $rsp = $client->request('GET', $login['check'], [
            'headers' => ['Cookie' => $cookie],
            'connect_timeout' => 5,
            'timeout' => 10,
            'verify' => false,
        ]);

        if ($rsp->getStatusCode() != 200) {
            Log::error("login error");
            abort(500);
        }

        $ret = json_decode($rsp->getBody());

        if ($ret->retcode != 0) {
            return $this->redirectTo($request);
        }



        $user = $ret->data;
        $user->header = $request->cookie('oa_header');
        $request->user = $user;
        $request->attributes->add(['user' => $user]);
        if (empty($user)) {
            return $this->redirectTo($request);
        }
        return $next($request);
    }

    protected function redirectTo($request)
    {
        return Response::setError("need login..");
    }
}
