<?php

namespace App\Http\Middleware;

use App\Http\ApiHelper\Response;
use App\Http\Services\PermService;
use Closure;

class Permission
{
    public function handle($request, Closure $next)
    {
        // 如果是admin账号，那么跳过检验权限
        $user = request()->user;
        $userId = $user->userId;
        $email = $user->email;
        $roles = PermService::getUserRoles($userId, $email);

        $userPerms = PermService::getUserPerms();
        $request->perms = $userPerms;
        $request->attributes->add(['perms' => $userPerms]);

        if (PermService::hasRole(PermService::ROLE_ADMIN, $roles)) {
            return $next($request);
        }

        $url = $request->url();
        $url_info = parse_url($url);
        if (isset($url_info['path'])) {
            $path_arr = explode("/", $url_info['path']);
            $path_arr = array_filter($path_arr);
            $path_arr = array_values($path_arr);
            $perm_id = implode("_", $path_arr);
            //添加忽略判断权限逻辑
            if (in_array(end($path_arr), config('perm_args.ignore_perm_check_functions'))) {
                return $next($request);
            }
            $res = PermService::hasPerm($perm_id);
            if (!$res) {
                if ($path_arr && isset($path_arr[0]) && ($path_arr[0] == 'api')) {
                    return Response::setError("没有访问权限");
                } else {
                    return response("没有访问权限");
                }
            }
        }
        return $next($request);
    }
}
