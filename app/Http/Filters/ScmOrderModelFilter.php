<?php

namespace App\Http\Filters;

use App\Http\Models\ScmOrderTemporaryModel;
use App\Http\Services\PermService;
use EloquentFilter\ModelFilter;

class ScmOrderModelFilter extends ModelFilter
{
    /**
     * Related Models that have ModelFilters as well as the method on the ModelFilter
     * As [relationMethod => [input_key1, input_key2]].
     *
     * @var array
     */
    public $relations = [];

    //去判断权限
    public function setup()
    {
        $userId = request()->user->userId;
        //能查看全部
        if (PermService::hasPerm('pur_scmOrder_viewAllList')) {
            return;
        }
        //能查看下级
        if (PermService::hasPerm('pur_scmOrder_viewSubList')) {
            $subUserIds = PermService::getSubUserId($userId);
            $this->whereIn('create_uid', $subUserIds);
            return;
        }
        //只能查看已经同步的委托单(去暂存的状态里面找)
        $this->whereHasIn('temporary', function ($q) {
            $q->where('status', ScmOrderTemporaryModel::STATUS_SYNC_COMPLETED);
        });
        //默认只能查看自己的记录
        $this->where('create_uid', $userId);
    }

    public function purchaseSn($sn)
    {
        return $this->where('purchase_sn', 'like', "${sn}%");

    }

    public function scmOrderSn($sn)
    {
        return $this->where('scm_order_sn', 'like', "${sn}%");

    }

    public function warehouseReceiptSn($sn)
    {
        return $this->where('warehouse_receipt_sn', 'like', "${sn}%");

    }

    public function stockInSn($sn)
    {
        return $this->where('stock_in_sn', 'like', "${sn}%");
    }

    public function supplierName($name)
    {
        return $this->where('supplier_name', 'like', "${name}%");
    }

    public function goodsName($name)
    {
        return $this->where('goods_name', 'like', "${name}%");
    }

    public function CustomsStatus($status)
    {
        return $this->where('customs_status', $status);
    }

    public function Status($status)
    {
        return $this->where('status', $status);
    }

    public function createUid($uid)
    {
        return $this->where('create_uid', $uid);
    }

    public function createTime($time)
    {
        $time = explode('~', $time);
        if (count($time) != 2) {
            return $this;
        }
        return $this->whereBetween('create_time', [strtotime($time[0]), strtotime($time[1])]);
    }

}
