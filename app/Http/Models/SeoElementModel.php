<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;
use DB;
use Illuminate\Support\Facades\Redis;

class SeoElementModel extends Model
{
    protected $connection = 'spu';
    protected $table = 'lie_seo_element';
    public $timestamps = false;

    const TYPE_BRAND = 1;
    const TYPE_SUPPLIER = 2;

    public function getSeoElementCache($data)
    {
        $redis = Redis::connection('sku');
        $key = $data['type'] . '_' . $data['key_id'];
        $cache = $redis->hget('jishi_seo_element', $key);
        $cache = json_decode($cache, true);
        return $cache;
    }

    public function setSeoElementCache($data)
    {
        $Redis = Redis::connection('sku');
        $key = $data['type'] . '_' . $data['key_id'];
        $Redis->hset('jishi_seo_element', $key, json_encode($data));
        return true;
    }


    public function addSeoElement($data)
    {
        $data['add_time'] = time();
        $data['update_time'] = time();
        $id = $this->insertGetId($data);
        if (!$id) {
            return false;
        }
        $data['id'] = $id;
        $this->setSeoElementCache($data);
        return true;
    }

    public function saveSeoElement($data)
    {
        $data['update_time'] = time();
        $result = $this->where('id', '=', $data['id'])->update($data);
        if (!$result) {
            return false;
        }
        $this->setSeoElementCache($data);
        return true;
    }

}
