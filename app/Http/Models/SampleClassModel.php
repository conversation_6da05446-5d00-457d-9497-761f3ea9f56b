<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;

class SampleClassModel extends Model
{
    protected $connection = 'liexin_data';
    protected $table = 'lie_sample_class';
    public $timestamps = false;

    const STATUS_OK = 1;
    const STATUS_DISABLE = 2;
    const STATUS_DELETED = 3;

    public function sample_one()
    {
        return $this->hasOne(SampleModel::class, 'class_id', 'id');
    }
}
