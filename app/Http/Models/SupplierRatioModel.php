<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;
use DB;
use Illuminate\Support\Facades\Redis;

class SupplierRatioModel extends Model
{
    protected $connection = 'spu';
    protected $table = 'lie_supplier_ratio';
    public $timestamps = false;

    public function getSupplierRatioList($map = [], $limit = 10)
    {
        foreach ($map as $k => $v) {
            $map['lie_supplier_ratio.' . $k] = $v;
            unset($map[$k]);
        }
        return $this->where($map)->select('id', 'sort', 'lie_supplier_ratio.supplier_id', 'cn', 'hk', 'tax',
            'extra_ratio', 'ratio', 'is_default', 'lie_supplier_ratio.status', 'supplier_name')
            ->join('lie_supplier', 'lie_supplier_ratio.supplier_id', '=', 'lie_supplier.supplier_id')
            ->paginate($limit)->toArray();
    }

    public function getSupplierRatio($id)
    {
        if (empty($id)) {
            return false;
        }
        return $this->where('id', '=', $id)->select(
            'id',
            'supplier_id',
            'cn',
            'hk',
            'ratio',
            'extra_ratio',
            'tax',
            'is_default',
            'status',
            'brand_name',
            'file_url',
            'is_default',
            'sort'
        )->first()->toArray();
    }

    public function addSupplierRatio($data, &$id, $adminId = 0)
    {
        $tax = config('field.Tax');
        if (!$tax) {
            return false;
        }
        $data['tax'] = $tax;
        DB::connection($this->connection)->beginTransaction();
        if ($data['is_default'] == 2) {
            $this->where('supplier_id', '=', $data['supplier_id'])->update(['is_default' => 1]);
        }

        $data['add_time'] = time();
        $data['update_time'] = $data['add_time'];
        $data['admin_id'] = $adminId;
        $id = $this->insertGetId($data);
        if (!$id) {
            return false;
        }

        DB::connection($this->connection)->commit();
        $result = $this->produceRatio($data['supplier_id'], $adminId);
        if (!$result) {
            return false;
        }

        return true;
    }

    public function saveSupplierRatio($data, &$id, $adminId = 0)
    {
        $tax = config('field.Tax');
        if (!$tax) {
            return false;
        }
        $data['tax'] = $tax;

        DB::connection($this->connection)->beginTransaction();
        if (!empty($data['is_default']) && $data['is_default'] == 2) {
            $this->where('supplier_id', '=', $data['supplier_id'])->update(['is_default' => 1]);
        }

        $data['update_time'] = time();
        $result = $this->where('id', '=', $data['id'])->update($data);
        if (!$result) {
            return false;
        }

        $result = $this->produceRatio($data['supplier_id'], $adminId);
        if (!$result) {
            return false;
        }

        DB::connection($this->connection)->commit();

        $id = $data['id'];
        return true;
    }

    public function HDUploadFile($Url)
    {
        $filename = date('Y-m-d-H-i-s') . '-' . uniqid() . '.csv';
        $file = getFile($Url, storage_path() . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR . 'uploads', $filename,
            1);
        if (empty($file['save_path'])) {
            return false;
        }
        $f = fopen($file['save_path'], 'r');
        $str = fread($f, filesize($file['save_path']));//指定读取大小，这里把整个文件内容读取出来
        fclose($f);
        unlink($file['save_path']);
        $str = str_replace("\r\n", ",", $str);
        return iconv('GBK', 'utf-8//IGNORE', $str);
    }

    public function produceRatio($supplierId, $adminId = 0)
    {
        $list = $this->where('supplier_id', '=', $supplierId)->where('status', '=', 1)->select([
            'id',
            'cn',
            'hk',
            'extra_ratio',
            'is_default',
            'extra_ratio',
            'ratio',
            'brand_name',
            'file_url',
            'sort'
        ])->orderBy('sort', 'desc')->get();
        if (!$list) {
            return true;
        }//没有数据
        $list = $list->toArray();
        $redis = Redis::connection('sku');
        $cache = json_decode($redis->hget('pool_supplier_ratio', $supplierId), true);
        if (empty($list)) {
            return true;
        }
        foreach ($list as $k => $v) {
            unset($list[$k]);
            if (!empty($cache[$v['id']]['goods_name'])) {
                $v['goods_name'] = $cache[$v['id']]['goods_name'];
            }

            if (!empty($v['file_url']) && (empty($cache[$v['id']]) || empty($cache[$v['id']]['file_url']) ||
                    $cache[$v['id']]['file_url'] != $v['file_url'])) {
                $v['goods_name'] = $this->HDUploadFile($v['file_url']);
            }
            $data[$v['id']] = $v;
            $idArr[] = $v['id'];
        }

        //开始生成缓存数据
        $data = arraySequence($data, 'sort', 'SORT_DESC');
        $redis->hset('pool_supplier_ratio', $supplierId, json_encode($data));
        foreach ($data as $k => $v) {
            if (empty($cache[$k]) || $cache[$k] !== $v) {
                //todo : 发通知
//                dispatch(new SendNotice(2, $v['id'], $adminId));
            }
        }
        return true;
    }

    public function deleteSupplierRatio($id, $supplierId)
    {
        DB::connection($this->connection)->beginTransaction();
        $result = $this->where('id', '=', $id)->delete();
        if (!$result) {
            return false;
        }
        $result = $this->produceRatio($supplierId);
        if (!$result) {
            return false;
        }
        DB::connection($this->connection)->commit();
        return true;
    }

    public function getSupplierRatioStatus($id)
    {
        $info = $this->where('id', $id)->select(['supplier_id', 'status'])->first()->toArray();
        return $info;
    }
}
