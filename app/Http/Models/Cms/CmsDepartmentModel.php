<?php

namespace App\Http\Models\Cms;

use Illuminate\Database\Eloquent\Model;

class CmsDepartmentModel extends Model
{
    protected $table = 'department';
    public $timestamps = false;
    protected $primaryKey = 'departmentId';

    //采购部门的总ID
    const PURCHASE_DEPARTMENT_ID = 8;

    //销售部门的总ID
    const SALES_DEPARTMENT_ID = 7;

    // 获取部门人员
    public static function getUsersByDepartmentId($departmentId, $status = '', $filter = '')
    {
        $departmentIds = [];

        self::getSubDepartmentId($departmentId, $departmentIds);

        return CmsUserInfoModel::whereIn('department_id', $departmentIds)
            ->where(function ($query) use ($status) {
                if ($status !== '') {
                    $query->where('status', '=', $status);
                }
            })
            ->where(function ($query) use ($filter) {
                if ($filter) {
                    $query->whereRaw($filter);
                }
            })
            ->get()->toArray();
    }

    // 获取下级部门ID
    public static function getSubDepartmentId($departmentId, &$departmentIds)
    {
        // 获取下级部门
        $sub_department = CmsUserDepartmentModel::where('parent_id', $departmentId)->select([
            'department_id',
            'department_name'
        ])->get();
        if ($sub_department) {
            foreach ($sub_department as $v) {
                self::getSubDepartmentId($v['department_id'], $departmentIds);
            }
        }

        $departmentIds[] = $departmentId;

        return $departmentIds;
    }
}
