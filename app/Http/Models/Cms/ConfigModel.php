<?php
namespace App\Http\Models\Cms;

use Illuminate\Database\Eloquent\Model;

class ConfigModel extends Model
{
    protected $connection = 'cms';

    protected $table = 'config';

    public $timestamps = false;

    public static function getMenusByName($name)
    {
        $res = self::where('config_title', $name)->first();
        $menus = [];
        if ($res){
            $menus = (json_decode($res['config_data'])) ? json_decode($res['config_data'], true) : [];
        }
        return $menus;
    }
}
