<?php

namespace App\Http\Models\Cms;


use Arr;
use Illuminate\Database\Eloquent\Model;


class CmsUserIntraCodeModel extends Model
{
    public $connection = 'cms';

    protected $table = 'lie_intracode';

    public $timestamps = false;

    public function user_info()
    {
        return $this->hasOne(CmsUserInfoModel::class, 'userId', 'admin_id');
    }


    public static function getAdmUidsByCodeIds($code_ids)
    {
        $res = self::whereIn('code_id', $code_ids)->get();
        return ($res) ? $res->toArray() : [];
    }

    public static function getListByAdminIds($admin_ids)
    {
        $res = self::whereIn('admin_id', $admin_ids)->get();
        return ($res) ? $res->toArray() : [];
    }

    //根据用户id获取code_id
    public static function getCodeIdByAdminId($adminId)
    {
        return self::where('admin_id', $adminId)->value('code_id');
    }

    public function getCodeUsersForSelect()
    {
        $list = $this::with('user_info')->get();
        $result = [];
        foreach ($list as $item) {
            if (($item['user_info']['status'] ?? 0) == 0) {
                $result[$item['code_id']] = Arr::get($item['user_info'], 'name');
            } else {
                $result[$item['code_id']] = Arr::get($item['user_info'], 'name') . '（已离职）';
            }
        }

        return $result;
    }

    public static function getUserForSelect()
    {
        $list = self::with('user_info')->get();
        $result = [];
        foreach ($list as $item) {
            $result[$item['admin_id']] = Arr::get($item['user_info'], 'name');
        }

        return $result;
    }

    public static function getUserForXmSelect()
    {
        $list = self::with('user_info')->get();
        $result = [];
        foreach ($list as $item) {
            $result[] = [
                'value' => $item['code_id'],
                'name' => Arr::get($item['user_info'], 'name')
            ];
        }

        return $result;
    }
}
