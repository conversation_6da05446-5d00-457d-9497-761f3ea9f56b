<?php
/**
 * Created by PhpStorm.
 * User: duwen<PERSON>
 * Date: 2021/8/10
 * Time: 5:59 PM
 */

namespace App\Http\Models\Cms;

use Illuminate\Database\Eloquent\Model;

class CmsUserPermModel extends Model
{
    protected $connection = 'cms';

    protected $table = 't_user_perm';


    public $timestamps = false;


    public static function getUserPermByUidAndBid($uid, $bid)
    {
        $res = self::where(["userId" => $uid, "bid" => $bid])->first();
        return ($res) ? $res->toArray() : [];
    }

}
