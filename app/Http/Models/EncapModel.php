<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;

class EncapModel extends Model
{
    public $connection = 'spu';
    protected $table = 'lie_encap';
    public $timestamps = false;

    public function standard_encap_mapping()
    {
        return $this->hasOne(StandardEncapMappingModel::class, 'encap_id', 'encap_id');
    }

    public function standard_encap()
    {
        return $this->hasOneThrough(StandardEncapModel::class, StandardEncapMappingModel::class, 'standard_encap_id',
            'standard_encap_id', 'encap_id');
    }

}
