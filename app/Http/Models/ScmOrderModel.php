<?php

namespace App\Http\Models;

use App\Http\Filters\ScmOrderModelFilter;
use App\Http\Filters\ScmOrderStockInModelFilter;
use EloquentFilter\Filterable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

class ScmOrderModel extends Model
{
    protected $connection = 'mysql';
    protected $table = 'lie_scm_order';
    protected $primaryKey = 'ScmOrderModelFilter';
    public $timestamps = false;

    use Filterable;

    //待报关
    const CUSTOMS_STATUS_NEED_CUSTOMS = -1;
    //报关中
    const CUSTOMS_STATUS_PENDING = 1;
    //已报关
    const CUSTOMS_STATUS_COMPLETED = 2;

    //状态
    const STATUS_NEED_AUDIT = -1;
    const STATUS_COMPLETED = 1;

    public function stock_in()
    {
        return $this->belongsTo(StockInModel::class, 'stock_in_id', 'stock_in_id');
    }

    public function stock_in_item()
    {
        return $this->belongsTo(StockInItemModel::class, 'stock_in_item_id', 'stock_in_item_id');
    }

    public function purchase_order()
    {
        return $this->belongsTo(PurchaseOrderModel::class, 'purchase_id', 'purchase_id');
    }

    public function temporary()
    {
        return $this->belongsTo(ScmOrderTemporaryModel::class, 'temporary_id', 'temporary_id');
    }

    //获取暂存列表
    public static function getScmOrderList($params)
    {
        $limit = Arr::get($params, 'limit', 10);
        $query = self::with(['stock_in']);
        //ScmOrderModelFilter 文件里面有逻辑
        $query->filter($params, ScmOrderModelFilter::class);
        return $query->orderBy('scm_order_item_id', 'desc')->paginate($limit)->toArray();
    }

    //获取委托入库明细列表
    public static function getScmOrderStockInList($params)
    {
        $limit = Arr::get($params, 'limit', 10);
        $query = StockInItemModel::with(['stock_in','payable_items']);
        //ScmOrderStockInModelFilter 文件里面有逻辑
        $query->filter($params, ScmOrderStockInModelFilter::class);
        return $query->orderBy('stock_in_item_id', 'desc')->paginate($limit)->toArray();
    }

    //根据ids获取委托单列表,并且是 审单状态：完成，报关状态：待报关
    public static function getScmOrderListByItemIds($scmOrderItemIds)
    {
        return self::select([
            'scm_order_item_id',
            'scm_order_sn',
            'goods_sn',
            'goods_name',
            'brand_name',
            'goods_number',
            'purchase_prices',
            'warehouse_receipt_sn'
        ])->whereIn('scm_order_item_id', $scmOrderItemIds)
            ->where('status', self::STATUS_COMPLETED)
            ->where('customs_status', self::CUSTOMS_STATUS_NEED_CUSTOMS)->get()->toArray();
    }

    public static function updateCustomsStatus($scmOrderItemIds, $status)
    {
        return self::whereIn('scm_order_item_id', $scmOrderItemIds)->update([
            'customs_status' => $status,
            'update_time' => time(),
        ]);
    }

    public static function addScmOrder($scmOrderData = [])
    {
        return self::insert($scmOrderData);
    }

    public static function updateScmOrder($scmOrderItemId, $scmOrder)
    {
        $scmOrder['update_time'] = time();
        return self::where('scm_order_item_id', $scmOrderItemId)->update($scmOrder);
    }

    public static function updateScmOrderByErpGoodsId($erpGoodsId, $scmOrder)
    {
        $scmOrder['update_time'] = time();
        return self::where('erp_goods_id', $erpGoodsId)->update($scmOrder);
    }

    //获取所有供应商列表
    public static function getScmOrderSuppliers($params)
    {
        $limit = Arr::get($params, 'limit', 10);
        $supplierName = Arr::get($params, 'keyword');
        $query = self::select(['supplier_id', 'supplier_name'])->groupBy('supplier_id');
        if (!empty($supplierName)) {
            $query->where('supplier_name', 'like', "${supplierName}%");
        }
        return $query->limit($limit)->get()->toArray();
    }

    public static function updateScmOrderScmBrandName($temporaryIds, $scmBrandName)
    {
        return self::whereIn('temporary_id', $temporaryIds)->update([
            'scm_brand_name' => $scmBrandName,
            'update_time' => time(),
        ]);
    }
}
