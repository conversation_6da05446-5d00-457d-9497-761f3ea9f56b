<?php

namespace App\Http\Models\PoolClass;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;

class PoolClassModel extends Model
{
    public $connection = 'class';
    protected $table = 'class';
    public $timestamps = false;

    const STATUS_OK = 1;

    const CLASS_TYPE_NORMAL = 1;
    const CLASS_TYPE_MRO = 2;
    const CLASS_TYPE_IEDGE = 3;

    const Pre = 'pool_class_info';

    public $cacheField = [
        'class_id',
        'show_name',
        'class_name',
        'status',
        'parent_id',
        'sign',
        'class_brief',
        'class_icon',
        'sort',
        'class_type',
    ];

    public function map($data)
    {
        $map = [];
        if (empty($data)) {
            return $map;
        }

        foreach ($data as $k => $v) {
            if (empty($v) && $v !== 0 && $v !== '0') {
                continue;
            }
            switch ($k) {
                case 'class_name':
                    $map[] = [$k, 'like', '%' . $v . '%'];
                    break;
                default :
                    $map[] = [$k, '=', $v];
                    break;
            }
        }

        return $map;
    }

    public function sub()
    {
        return $this->hasMany(PoolClassModel::class, 'parent_id', 'class_id')
            ->select('class_id', 'show_name', 'class_name', 'parent_id', 'sign', 'status',
                'sort', 'parent_id')->withCount('attrCount');
    }

    public function parent()
    {
        return $this->hasOne(PoolClassModel::class, 'class_id', 'parent_id');

    }

    public function attrCount()
    {
        return $this->hasMany(PoolClassAttrModel::class, 'class_id', 'class_id')
            ->where('status', '=', 1);
    }

    public function saveClass($data)
    {
        $data['update_time'] = time();
        $result = $this->where('class_id', '=', $data['class_id'])->update($data);
        if (!$result) {
            return false;
        }
        $this->setClassCache($data['class_id'], $data);
        return true;
    }

    public function addClass($data)
    {
        $default = [
            'parent_id' => 0,
            'status' => 1,
            'sort' => 0,
            'add_time' => time(),
            'update_time' => time()
        ];

        foreach ($data as $k => &$v) {
            if (empty($v)) {
                $v = Arr::get($default, $k, '');
            }
        }

        if (!empty($data['parent_id'])) {
            //获取class_type
            $data['class_type'] = PoolClassModel::where('class_id', $data['parent_id'])->value('class_type');
        }
        $result = $this->insertGetId($data);
        if (!$result) {
            return false;
        }
        $this->setClassCache($result, $data);
        return true;
    }

    //设置redis缓存
    public function setClassCache($classId, $data)
    {
        ini_set('memory_limit', '2056M');
        $redis = Redis::connection('sku');
        $data['class_id'] = $classId;
        $redis->hset('pool_class_info', $classId, json_encode($data));

        return true;
    }

    //设置分类状态
    public function setClassStatus($classId, $status)
    {
        $result = $this->where('class_id', '=', $classId)->update(['status' => $status, 'update_time' => time()]);
        if (!$result) {
            return false;
        }
        $find = $this->where('class_id', '=', $classId)->select($this->cacheField)->first()->toArray();
        $this->setClassCache($classId, $find);
        return true;
    }

    //获取分类名字
    public function getClassInfo($classId)
    {
        $redis = Redis::connection('sku');
        $cache = json_decode($redis->hget(self::Pre, $classId), true);

        if (!$cache) {
            $cache = $this->where('class_id', '=', $classId)->select($this->cacheField)->first();
            if (!$cache) {
                return false;
            }
            $cache = $cache->toArray();
            $this->setClassCache($classId, $cache);
        }

        return $cache;
    }
}
