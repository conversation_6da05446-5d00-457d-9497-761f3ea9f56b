<?php

namespace App\Http\Models\PoolClass;

use App\Http\Models\AgentBrandSpuModel;
use App\Http\Models\StandardBrandModel;
use Illuminate\Database\Eloquent\Model;
use DB;
use Illuminate\Support\Facades\Redis;

class ClassAttrUnitConvertItemModel extends Model
{
    public $connection = 'class';
    protected $table = 'class_attr_unit_convert_item';
    protected $primaryKey = 'id';
    public $timestamps = false;

}
