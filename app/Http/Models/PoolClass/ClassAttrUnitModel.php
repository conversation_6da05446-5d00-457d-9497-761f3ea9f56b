<?php

namespace App\Http\Models\PoolClass;

use Illuminate\Database\Eloquent\Model;

class ClassAttrUnitModel extends Model
{
    protected $connection = 'class';
    protected $table = 'class_attr_unit';
    public $timestamps = false;

    const STATUS_OK = 1;

    //默认空单位(即名称为 : 其他)的unit_id
    const DEFAULT_NULL_UNIT_ID = 26;

    public function map($data)
    {
        $map = [];
        if (empty($data)) {
            return $map;
        }

        foreach ($data as $k => $v) {
            if (empty($v) && $v !== 0 && $v !== '0') {
                continue;
            }
            switch ($k) {
                case 'attr_unit_name':
                    $map[] = [$k, 'like', '%' . $v . '%'];
                    break;
                default;
                    $map[] = [$k, '=', $v];
                    break;
            }
        }

        return $map;
    }

    public function setAttrValueUnitStatus($attr_unit_id, $status)
    {
        return $this->where('attr_unit_id', '=', $attr_unit_id)->update(['status' => $status, 'update_time' => time()]);
    }

    public static function getAllClassAttrUnitsForSelect()
    {
        return self::where('status', self::STATUS_OK)->pluck('attr_unit_name', 'attr_unit_id')->toArray();
    }
}
