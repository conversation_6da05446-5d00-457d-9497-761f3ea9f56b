<?php

namespace App\Http\Models\PoolClass;

use Illuminate\Database\Eloquent\Model;

class ClassAttrUnitConversionModel extends Model
{
    public $connection = 'class';
    protected $table = 'class_attr_unit_conversion';
    public $timestamps = false;

    public function AttrUnitId1()
    {
        return $this->hasOne(ClassAttrUnitModel::class, 'attr_unit_id', 'attr_unit_id_1')
            ->whereIn('status', [1, 2])->select('attr_unit_id', 'attr_unit_name');
    }

    public function AttrUnitId2()
    {
        return $this->hasOne(ClassAttrUnitModel::class, 'attr_unit_id', 'attr_unit_id_2')
            ->whereIn('status', [1, 2])->select('attr_unit_id', 'attr_unit_name');
    }

    public function setAttrValueUnitConversionStatus($conversion_id, $status)
    {
        return $this->where('conversion_id', '=', $conversion_id)->update(
            ['conversion_id' => $conversion_id, 'status' => $status, 'update_time' => time()]
        );
    }
}
