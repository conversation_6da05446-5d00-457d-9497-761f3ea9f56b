<?php

namespace App\Http\Models\PoolClass;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

//这个是新版分类属性
class PoolClassAttrModel extends Model
{
    public $connection = 'class';
    protected $table = 'class_attr';
    public $timestamps = false;

    //查找默认单位
    public function UnitInfo()
    {
        return $this->hasOne(ClassAttrUnitModel::class, 'attr_unit_id', 'attr_unit_id')
            ->select('attr_unit_id', 'attr_unit_name');
    }

    public function unit_convert()
    {
        return $this->hasOne(ClassAttrUnitConvertModel::class, 'id', 'default_unit_convert_id');
    }

    public function map($data)
    {
        $map = [];
        if (empty($data)) {
            return $map;
        }

        foreach ($data as $k => $v) {
            if (empty($v) && $v !== 0 && $v !== '0') {
                continue;
            }
            switch ($k) {
                case 'attr_name':
                    $map[] = ['class_attr.' . $k, 'like', '%' . $v . '%'];
                    break;
                case 'class_name':
                    $map[] = ['c.class_name', 'like', '%' . $v . '%'];
                    break;
                case 'attr_id':
                    $map[] = ['attr_id', '=', $v];
                    break;
                case 'class_id':
                    $map[] = ['class_attr.class_id', '=', $v];
                    break;
                case 'default_unit_convert_id':
                    $map[] = ['class_attr.default_unit_convert_id', '=', $v];
                    break;
                case 'insert_type':
                    $map[] = ['class_attr.insert_type', '=', $v];
                    break;
                default:
                    $map[] = ['class_attr.' . $k, '=', $v];
                    break;
            }
        }

        return $map;
    }

    public function ClassInfo()
    {
        return $this->hasOne(PoolClassModel::class, 'class_id', 'class_id')
            ->select('class_id', 'class_name', 'parent_id', 'sign', 'status');
    }

    /*
     * 统计参数下面的数值
     */
    public function ValueNum()
    {
        return $this->hasOne(ClassAttrValueModel::class, 'attr_id', 'attr_id');
    }

    /*
     * 设置属性的状态
     */
    public function setAttrStatus($attr_id, $status)
    {
        return $this->where('attr_id', '=', $attr_id)->update(['status' => $status, 'update_time' => time()]);
    }
}
