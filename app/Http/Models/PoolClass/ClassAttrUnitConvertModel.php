<?php

namespace App\Http\Models\PoolClass;

use App\Http\Models\AgentBrandSpuModel;
use App\Http\Models\StandardBrandModel;
use Illuminate\Database\Eloquent\Model;
use DB;
use Illuminate\Support\Facades\Redis;

class ClassAttrUnitConvertModel extends Model
{
    public $connection = 'class';
    protected $table = 'class_attr_unit_convert';
    protected $primaryKey = 'id';
    public $timestamps = false;

    const STATUS_DISABLE = -1;
    const STATUS_ENABLE = 1;

    public function items()
    {
        return $this->hasMany(ClassAttrUnitConvertItemModel::class, 'convert_id', 'id');
    }

}
