<?php

namespace App\Http\Models\PoolClass;

use Illuminate\Database\Eloquent\Model;
use Request;
use DB;

//这个是自营的
class ClassifyAttrModel extends Model
{
    protected $connection='self';
    protected $table='classify_attr';
    public $timestamps = false;
    public function ClassifyAttrList($class_id='')
    {
        if (!$class_id) {
            $collert=Request::only('class_id');
            $collert=TrimX($collert, true, ['class_id']);
            if (empty($collert['class_id'])) {
                return false;
            }
            $class_id=$collert['class_id'];
        }

        $data=$this->where('class_id', '=', $class_id)->orderBy('name_sort', 'desc')
            ->select('attr_id', 'attr_name', 'class_id', 'value', 'is_name', 'name_sort','is_similar_rule','similar_rule_type')
            ->get()->toArray();
        if (!empty($data) && is_array($data)) {
            foreach ($data as $k=>$v) {
                $data[$k]['value']=json_decode($v['value'], true);
            }
        }
        return $data;
    }

    public function ApiSaveClassAttr()
    {
        $collert=Request::only('class_id', 'attr_id', 'attr_name', 'value','is_similar_rule','similar_rule_type');
        $collert=TrimX($collert, true, ['class_id','attr_id','attr_name','value','is_similar_rule','similar_rule_type']);
        if (empty($collert)) {
            return [10001,'缺少必须参数'];
        }

        if(empty($collert['is_similar_rule']) || $collert['is_similar_rule'] != 2){
            $collert['is_similar_rule'] = 1;
        }
        if (!empty($collert['value'])) {//去掉重复值，转换成json
            foreach ($collert['value'] as $k=>$v) {
                if (!empty($v)) {
                    $collert['value'][$k]=TrimX($v);
                }
            }
            $collert['value']=json_encode(array_unique($collert['value']));
        }
        $collert['update_time']=time();
        if (!empty($collert['attr_id'])) {
            unset($collert['class_id']);
            $result=$this->where('attr_id', '=', $collert['attr_id'])->update($collert);
            $errinfo='修改';
        } else {
            $collert['add_time']=$collert['update_time'];
            $result=$this->insertGetid($collert);
            $errinfo='新增';
        }
        if (!$result) {
            return [10002,$errinfo.'失败'];
        } else {
            if (!empty($collect['attr_id'])) {
                $result=$collect['attr_id'];
            }
            return [0,$errinfo.'成功',$result];
        }
    }

    public function MergeAttr($Attrs, $GoodsAttr)
    {
        if (is_array($Attrs)) {
            foreach ($Attrs as $k=>$v) {
                if (is_array($GoodsAttr)) {
                    foreach ($GoodsAttr as $kk=>$vv) {
                        if ($v['attr_name']==$vv['attr_name']) {
                            $Attrs[$k]['Selected']=$vv['attr_value'];
                            $Attrs[$k]['value'][]=$vv['attr_value'];
                        }
                    }
                    empty($Attrs[$k]['value']) || $Attrs[$k]['value']=array_unique($Attrs[$k]['value']);
                }
            }
        }
        return $Attrs;
    }

    public function DownloadClassTemplate($class_id='')
    {
        if (empty($class_id)) {
            $arr=['class_id'];
            $collert=Request::only($arr);
            $collert=TrimX($collert, true, $arr);
        } else {
            $collert['class_id']=$class_id;
        }
        if (empty($collert['class_id'])) {
            return false;
        }
        $data=$this->where($collert)->select('attr_name')->get()->toArray();

        $ExcelTitle=Config('api.SelfEXCELTITLE');
        if (is_array($data)) {
            foreach ($data as $k=>$v) {
                array_push($ExcelTitle, $v['attr_name']);
            }
        }
        array_push($ExcelTitle, '分类ID'.$class_id);
        return $ExcelTitle;
    }

    public function ApiDeleteAttr()
    {
        $arr=['class_id','attr_id'];
        $collert=Request::only($arr);
        $collert=TrimX($collert, true, $arr);
        if (empty($collert['attr_id']) || empty($collert['class_id'])) {
            return [10001,'缺少必须参数'];
        }
        $result=$this->where('attr_id', '=', $collert['attr_id'])->delete();
        if (!$result) {
            return [10002,'删除失败'];
        }
        return [0,'删除成功'];
    }

    //查找参数
    public function FindAttr($ClassID='', $AttrName='')
    {
        if (empty($ClassID)) {
            return false;
        }
        if (empty($AttrName)) {
            return false;
        }

        $map[]=['attr_name','=',$AttrName];
        $map[]=['class_id','=',$ClassID];
        $result=$this->where($map)->select('attr_id')->first();

        if (!$result) {
            return false;
        }
        $result=$result->toArray();
        return $result['attr_id'];
    }

    //新增参数
    public function AddAttr($ClassID='', $AttName='')
    {
        if (empty($ClassID)) {
            return false;
        }
        if (empty($AttName)) {
            return false;
        }

        $Add['attr_name']=$AttName;
        $Add['class_id']=$ClassID;
        $Add['add_time']=time();
        $Add['update_time']=time();
        $result=$this->insertGetId($Add);
        if (!$result) {
            return $result;
        }

        return $result;
    }

    public function ClassifyList()
    {
        $collert=Request::only('class_id');
        $collert=TrimX($collert, true, ['class_id']);
        if (empty($collert['class_id'])) {
            return false;
        }
        $list=$this->where('class_id', '=', $collert['class_id'])->select('class_id', 'attr_id', 'attr_name', 'is_name', 'name_sort')->get();
        if (!$list) {
            return false;
        }
        return $list->toArray();
    }

    public function ApiSetUpIsName($UserID=0)
    {
        $Arr=['attr_id','attr_name','is_name','name_sort'];
        $collert=Request::only($Arr);
        $ClassID=Request::only('class_id');
        if (empty($ClassID['class_id'])) {
            return [10003,'缺少分类ID'];
        }

        $collert=TrimX($collert, true, $Arr);
        foreach ($Arr as $k=>$v) {
            if (empty($collert[$v]) || !is_array($collert[$v])) {
                return [10001,'参数错误',$v];
            }
        }

        DB::connection($this->connection)->beginTransaction();
        foreach ($collert['attr_id'] as $k=>$v) {
            if (strstr($v, 'custom')) {
                $custom[]=[
                    'attr_name'=>$collert['attr_name'][$k],
                    'attr_id'=>$v,
                    'is_name'=>(!empty($collert['is_name'][$k]) && $collert['is_name'][$k]==2) ? 2:1,
                    'name_sort'=>$collert['name_sort'][$k]
                ];
                continue;
            }
            $data['is_name']=(!empty($collert['is_name'][$k]) && $collert['is_name'][$k]==2) ? 2:1;
            $data['name_sort']=$collert['name_sort'][$k];
            $data['update_time']=time();
            $result=$this->where('attr_id', '=', $v)->update($data);
            if (!$result && $result!==0) {
                return [10002,'保存失败'];
            }
        }
        $SelfClassIfyModel=new SelfClassifyModel();
        $result=$SelfClassIfyModel->where('class_id', '=', $ClassID['class_id'])->update(['custom'=>json_encode($custom)]);
        if (empty($result) && $result!=0) {
            return [10004,'设置失败'];
        }

        $data['log']=json_encode([
            date('Y-m-d H:i', time()). '建立任务'
        ]);

        $LogModel=new UploadLogModel();
        $LogData['create_time']=time();
        $LogData['update_time']=time();
        $LogData['type']=8;
        $LogData['encoded']=$ClassID['class_id'];
        $LogData['admin_id']=$UserID;
        $LogData['log']=json_encode([
            date('Y-m-d H:i', time()). '建立任务'
        ]);
        $insert=$LogModel->insertGetId($LogData);
        if ($insert) {
            $return=dispatch(new SelfGoodsNameRule($insert));
            if (!$return) {
                return [10004,'设置失败'];
            }
        } else {
            return [10003,'设置失败'];
        }
        DB::connection($this->connection)->commit();
        return [0,'保存成功'];
    }
}
