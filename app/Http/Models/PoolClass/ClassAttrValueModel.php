<?php

namespace App\Http\Models\PoolClass;

use Illuminate\Database\Eloquent\Model;

class ClassAttrValueModel extends Model
{
    public $connection = 'class';
    protected $table = 'class_attr_value';
    public $timestamps = false;

    public function setAttrValueStatus($attr_value_id, $status)
    {
        return $this->where('attr_value_id', '=', $attr_value_id)->update(['status' => $status, 'update_time' => time()]);
    }

    public function map($data)
    {
        $map = [];
        if (empty($data)) {
            return $map;
        }

        foreach ($data as $k => $v) {
            if (empty($v) && $v !== 0 && $v !== '0') {
                continue;
            }
            switch ($k) {
                case 'attr_name':
                case 'show_name':
                    $map[] = ['a.' . $k, 'like', '%' . $v . '%'];
                    break;
                case 'class_name':
                    $map[] = ['c.' . $k, 'like', '%' . $v . '%'];
                    break;
                case 'status':
                    if (!is_array($v)) {
                        $map[] = ['class_attr_value.'.$k, '=', $v];
                    }
                    break;
                default;
                    $map[] = ['class_attr_value.' . $k, '=', $v];
                    break;
            }
        }

        return $map;
    }

    public function DeUnitName()
    {
        return $this->hasOne(ClassAttrUnitModel::class, 'attr_unit_id', 'de_attr_unit_id')
            ->select('attr_unit_id', 'attr_unit_name');
    }

    public function UnitName()
    {
        return $this->hasOne(ClassAttrUnitModel::class, 'attr_unit_id', 'attr_unit_id')
            ->select('attr_unit_id', 'attr_unit_name');
    }

    /*
     * 获取参数值的参数信息
     */
    public function AttrInfo()
    {
        return $this->hasOne(PoolClassAttrModel::class, 'attr_id', 'attr_id')
            ->select('attr_id', 'class_id')->with('ClassInfo');
    }


}
