<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;

class SampleModel extends Model
{
    public $connection = 'liexin_data';
    protected $table = 'lie_sample';
    public $timestamps = false;

    const STATUS_OK = 1;
    const STATUS_DISABLE = 2;
    const STATUS_DELETED = 3;

    public function sample_class()
    {
        return $this->hasOne(SampleClassModel::class, 'id', 'class_id');
    }

}
