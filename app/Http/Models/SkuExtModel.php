<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Redis;

class SkuExtModel extends Model
{
    public $connection = 'spu';
    protected $table = 'lie_sku_ext';
    public $timestamps = false;

    //修改sku額外信息
    public function skuExtOp($goods_id,$up_data){
        $redis_spu = Redis::connection("spu");
        $infoObj = $this->where(["goods_id"=>$goods_id])->select("standard_high","standard_width","standard_length","standard_gross_weight","standard_net_weight")->first();
        if ($infoObj){ //存在更新
            $infoOne = $infoObj->toArray();
            foreach ($up_data as $a=>$b){
                $infoOne[$a] = $b;
            }
            $infoOne["update_time"] = time();
            $this->where(["goods_id"=>$goods_id])->update($infoOne);

            $redis_spu->hset("sku_ext",$goods_id,json_encode($infoOne));
        }else{ //不存在新增
            $up_data["goods_id"] = $goods_id;
            $up_data["create_time"] = time();
            $infoOne["update_time"] = time();
            $this->insertGetId($up_data);

            unset($up_data["goods_id"]);
            $redis_spu->hset("sku_ext",$goods_id,json_encode($up_data));
        }
    }
}
