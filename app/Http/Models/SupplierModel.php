<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;
use DB;

class SupplierModel extends Model
{
    protected $connection = 'spu';
    protected $table = 'lie_supplier';
    public $timestamps = false;

    public function getSuppliersForSelect()
    {
        $data = $this->whereIn('type_id', [1, 2])->where('is_type', 0)->pluck('supplier_name', 'supplier_id');
        $data = $data->toArray();
        $data[-2] = '猎芯自营';
        return $data;
    }

    public static function getSupplierListForXmSelect()
    {
        $data = self::select([
            'supplier_name',
            'supplier_id'
        ])->whereIn('type_id', [1, 2])->where('is_type', 0)->pluck('supplier_name', 'supplier_id')->toArray();
        $result = [];
        foreach ($data as $key => $value) {
            $result[] = [
                'name' => $value,
                'value' => $key
            ];
        }
        $result[] = [
            'name' => '猎芯自营',
            'value' => -2
        ];
        return $result;
    }
}
