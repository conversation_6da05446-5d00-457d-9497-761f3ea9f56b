<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;
use DB;

class StandardBrandModel extends Model
{
    public $connection = 'spu';
    protected $table = 'lie_brand_standard';
    public $timestamps = false;
    public $primaryKey = 'standard_brand_id';


    const STATUS_OK = 1;//正常
    const STATUS_AUDIT_REJECT = 2;//审核不通过
    const STATUS_DISABLE = 3;//禁用

    public function standard_brand_mapping()
    {
        return $this->hasMany(StandardBrandMappingModel::class, 'standard_brand_id', 'standard_brand_id');
    }

}
