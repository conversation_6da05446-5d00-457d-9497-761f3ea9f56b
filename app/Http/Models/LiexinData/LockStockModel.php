<?php

namespace App\Http\Models\LiexinData;

use Illuminate\Database\Eloquent\Model;
use DB;
use Illuminate\Support\Facades\Redis;

class LockStockModel extends Model
{
    protected $connection = 'liexin_data';
    protected $table = 'lie_lock_stock';
    protected $primaryKey = 'id';
    public $timestamps = false;

    public static $status = [
        1=>"锁库锁定" ,
        2=>"取消解锁" ,
        3=>"减少实际库存" ,
    ];

}
