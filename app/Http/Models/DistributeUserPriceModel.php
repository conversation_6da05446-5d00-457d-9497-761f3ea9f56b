<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;

class DistributeUserPriceModel extends Model
{
    public $connection = 'distribution';
    protected $table = 'lie_shop_distribute_user_price';
    public $timestamps = false;

    public function supplier()
    {
        return $this->belongsTo(SupplierModel::class, 'supplier_id', 'supplier_id');
    }

    public function rules()
    {
        return $this->hasMany(DistributeUserPriceRuleModel::class, 'user_price_id', 'id');
    }
}
