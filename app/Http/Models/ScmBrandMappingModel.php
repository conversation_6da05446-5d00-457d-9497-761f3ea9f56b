<?php

namespace App\Http\Models;

use App\Http\Models\Cms\CmsUserInfoModel;
use Illuminate\Database\Eloquent\Model;
use DB;
use Illuminate\Support\Facades\Redis;

class ScmBrandMappingModel extends Model
{
    public $connection = 'spu';
    protected $table = 'lie_scm_brand_mapping';
    protected $primaryKey = 'mapping_id';
    public $timestamps = false;


    const DEL_STATUS_OK = 1;
    const DEL_STATUS_DELETED = 2;


    public function scmBrand(){
        return $this->hasOne(ScmBrandModel::class,'scm_brand_id','scm_brand_id')
            ->select('erp_brand_name','scm_brand_id');
    }

    public function brand(){
        return $this->hasOne(BrandModel::class,'brand_id','brand_id')
            ->select('brand_name','brand_id');
    }

    public function user()
    {
        return $this->hasOne(CmsUserInfoModel::class, 'userId','admin_id');
    }

    public function map($data = [])
    {
        $map = [];
        foreach ($data as $k => $v) {
            if (empty($v) && $v !== 0) {
                continue;
            }
            switch ($k) {
                case 'start_time':
                    $map[] = ['m.add_time', '>=', strtotime($v)];
                    break;
                case 'end_time':
                    $map[] = ['m.add_time', '<=', strtotime($v) + 60 * 60 * 24];
                    break;
                case 'brand_name':
                    $map[] = ['b.brand_name', 'like', $v . '%'];
                    break;
                case 'erp_brand_name':
                    $map[] = ['s.erp_brand_name', 'like', $v . '%'];
                    break;
                default:
                    $map[] = ['m.' . $k, '=', $v];
            }
        }

        return $map;
    }

}
