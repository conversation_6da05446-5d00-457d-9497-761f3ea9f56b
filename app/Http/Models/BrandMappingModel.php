<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;

class BrandMappingModel extends Model
{
    protected $connection='spu';
    protected $table='lie_brand_mapping';
    public $timestamps = false;

    public function BrandInfoName($BrandName='')
    {
        if (empty($BrandName)) {
            return false;
        }
        $info=$this->where('supplier_brand_name', '=', $BrandName)->where('liexin_brand_id', '>', 0)->select('liexin_brand_id')->first();
        return $info;
    }

    public function BrandInfoLXID($liexin_brand_id=0)
    {
        if (empty($liexin_brand_id)) {
            return false;
        }
        $info=$this->where('liexin_brand_id', '=', $liexin_brand_id)->lists('supplier_brand_name', 'id');
        if (!$info) {
            return false;
        }
        return $info->toArray();
    }

    public function AddMapplierBrand($BrandName, $LiexinID, $SupplierName)
    {
        $data['status']=1;
        $data['supplier_brand_name']=$BrandName;
        $data['liexin_brand_id']=$LiexinID;
        $data['supplier_name']=$SupplierName;
        $data['insert_type']=1;
        $data['create_time']=time();
        return $this->insertGetId($data);
    }

    public function SaveBrand($ID, $Data)
    {
        if (empty($ID)) {
            return false;
        }
        return $this->where('id', '=', $ID)->update($Data);
    }
}
