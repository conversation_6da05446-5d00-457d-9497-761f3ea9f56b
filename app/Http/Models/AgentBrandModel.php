<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;
use DB;
use Illuminate\Support\Facades\Redis;

class AgentBrandModel extends Model
{
    public $connection = 'spu';
    protected $table = 'lie_agent_brand';
    protected $primaryKey = 'id';
    public $timestamps = false;

    const STATUS_DISABLE = -1;
    const STATUS_ENABLE = 1;

    public function standard_brand()
    {
        return $this->hasOne(StandardBrandModel::class, 'standard_brand_id', 'standard_brand_id');
    }

    public function spu()
    {
        return $this->hasMany(AgentBrandSpuModel::class, 'agent_brand_id', 'id');
    }
}
