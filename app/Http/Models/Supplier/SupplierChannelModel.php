<?php

namespace App\Http\Models\Supplier;

use Illuminate\Database\Eloquent\Model;
use DB;

class SupplierChannelModel extends Model
{
    public $connection = 'supplier';
    protected $table = 'supplier_channel';
    public $timestamps = false;

    const STATUS_PASSED = 2;
    const TYPE_official = 0;

    public function getSupplierList($userId = '')
    {
        if ($userId > 0) {
            $supplierList = $this->select([
                'supplier_code',
                'supplier_name',
                'channel_uid',
            ])->where('is_type', '=', 0)
                ->whereRaw("(channel_uid like '%${userId}%' or purchase_uid = ${userId})")
                ->get();
        } else {
            $supplierList = $this->select([
                'supplier_code',
                'supplier_name',
                'channel_uid',
            ])->where('is_type', '=', 0)->get();
        }
        $data = array('canal' => '', 'canal_list' => [], 'canal_user' => []);
        if (!$supplierList) {
            return $data;
        } else {
            $supplierList = $supplierList->toArray();
            foreach ($supplierList as $v) {
                $data['canal'] .= ',' . $v['supplier_code'];
                $data['canal_list'][$v['supplier_code']] = $v['supplier_name'];
                $data['canal_user'][$v['supplier_code']] = $v['channel_uid'];
            }
            $data['canal'] = trim($data['canal'], ',');
            return $data;
        }
    }

    public static function getCanalListForXmSelect()
    {
        $supplierList =  SupplierChannelModel::select([
            'supplier_code',
            'supplier_name',
        ])->get()->toArray();


        // 获取二级分类并拼接一级分类名称
        foreach ($supplierList as $supplier) {
            $result[] = [
                'name' => $supplier['supplier_name'] . ' | ' . $supplier['supplier_code'],
                'value' =>  $supplier['supplier_code']
            ];
        }

        return $result;
    }
}
