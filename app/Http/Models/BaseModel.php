<?php

namespace App\Http\Models;

use App\Http\Services\PermService;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Model;


class BaseModel extends Model
{


    /*
     * 过滤字段
     * insert插入多条数据的时候可以使用
     */
    public function filterField($data){
        if(isMultipleArray($data)){
            //多维数组
            foreach($data as $k1=>$v1){
                if(!is_array($v1)) break;
                foreach($data as $k2=>$v2){
                    if(!in_array($k2,$this->getTableFields())){
                        unset($data[$k1][$k2]);
                    }
                }
            }
        }else{
            foreach($data as $k1=>$v1){
                if(!in_array($k1,$this->getTableFields())){
                    unset($data[$k1]);
                }
            }
        }

        return $data;
    }



    /*
     * 获取数据库表的字段
     * @return  array
     * @param string
     */
    public function getTableFields($tableName=""){
        $tableFieldCache = new TableFieldCache("frq");
        $tableName = $tableName ? $tableName : $this->table;
        $oldFields = $tableFieldCache->getTableFieldsCache($tableName);
        if(!$oldFields){
            $tableFields = \Illuminate\Support\Facades\Schema::getColumnListing($tableName);
            //此处考虑缓存数据库字段
            $tableFieldCache->setTableFieldsCache($tableName,json_encode($tableFields));
        }else{
            $tableFields = json_decode($oldFields,true);
        }
        return $tableFields;
    }


    /*
     *过滤不要的字段
     */
    public function scopeExceptField($query,$fields=[]){
        $tableFields = $this->getTableFields();
        $fields = array_diff($tableFields,$fields);
        $query->select($fields);
        return $query;
    }

    /*
     * where("aaaa","=","123")
     * 构建查询语句
     * 等于 =
     */
    public function scopeBuildEqualQuery($query,$array=[]){
        $tableFields = $this->getTableFields();
        foreach($array as $field=>$value){
            if(!in_array($field,$tableFields) || empty($value) ){
                continue;
            }
            $query->where($field,"=",$value);
        }
        return  $query;
    }


    /*
     * whereIn("status",[1,2,3])
     * 构建查询语句
     * in
     */
    public function scopeBuildWhereInQuery($query,$array=[]){
        $tableFields = $this->getTableFields();
        foreach($array as $field=>$value){
            if(!in_array($field,$tableFields) ||  empty($array) ){
                continue;
            }
            $query->whereIn($field,$value);
        }
        return  $query;
    }


    /*
     * 左匹配
     * where("a","like","a%")
     */
    public function scopeBuildLeftLikeQuery($query,$array=[]){
        $tableFields = $this->getTableFields();
        foreach($array as $field=>$value){
            if(!in_array($field,$tableFields) || empty($value)){
                continue;
            }

            $query->where($field,"like",trim($value).'%');
        }
        return  $query;
    }

    /*
     * 打印sql语句
     */
    public function echoSql($query){
        $tmp = str_replace('?', '"'.'%s'.'"', $query->toSql());
        $tmp = vsprintf($tmp, $query->getBindings());
        dd($tmp);
    }


    /*
     * 时间查询
     * 构建单个时间查询
     */
    public function scopeSearchByTime($query,$field="",$begin_time='',$end_time=''){
        if (!$begin_time && !$end_time) return $query;
        $begin_time = $begin_time ? strtotime($begin_time." 00:00:00") : 0;
        $end_time = $end_time ? strtotime($end_time." 23:59:59") : 0;
        if($begin_time){
            if($end_time){
                $query = $query->where($field,">=",$begin_time)->where($field,"<=",$end_time);
            }else{
                $query = $query->where($field,">=",$begin_time);
            }
        }elseif($end_time){
            $query = $query->where($field,"<=",$end_time);
        }
        return $query;
    }


    /*
     * 构建时间查询语句
     * 构建都条记录查询
     * $timeArray["create_time"] = ["begin_time"=>"2021-11-01","end_time"=>"2021-11-01"]
     * $timeArray["pur_time"] = ["begin_time"=>"2021-11-01","end_time"=>"2021-11-01"]
     */
    public function scopeBuildTimeQuery($query,$timeArray=[]){
        $tableFields = $this->getTableFields();
        foreach($timeArray as $field=>$timeList){
            if(!in_array($field,$tableFields)) continue;
            $begin_time = $timeList["begin_time"];
            $end_time   = $timeList["end_time"];
            if (!$begin_time && !$end_time){
                continue;
            }
            $begin_time = $begin_time ? strtotime($begin_time." 00:00:00") : 0;
            $end_time = $end_time ? strtotime($end_time." 23:59:59") : 0;
            if($begin_time){
                if($end_time){
                    $query = $query->where($field,">=",$begin_time)->where($field,"<=",$end_time);
                }else{
                    $query = $query->where($field,">=",$begin_time);
                }
            }elseif($end_time){
                $query = $query->where($field,"<=",$end_time);
            }

        }
        return $query;
    }

    /*
     * 获取数据
     */
    public function scopeGetList($query,$page,$limit){
        return  $query->paginate($limit,[],'page',$page);
    }



    /*
     * 权限
     */
    public function scopeRule($query,$viewList){
        //        //获取用户部门下的所有用户
//        $userIds = PermService::getSubUserId(getAdminUserId());
////        dd($userIds);
//
//
//        //获取用户角色
//        $a = PermService::getUserRoles(1642);
////        dd($a);
//
//
//        //获取登录用户的所有权限
//        $c = PermService::getUserPerms(1642);
////        dd($c);
//
//        //判断登录用户是否拥有某个固定权限
//        $d = PermService::hasPerm("frq_viewsub_list");
//        dd($d);

        //查看所有
        if(PermService::hasPerm($viewList[0])){
            return $query;
        }
        //查看下级
        if(PermService::hasPerm($viewList[1])){//查看下级的权限//获取用户部门下的所有用户
            $userIds = PermService::getSubUserId(getAdminUserId());
            if(!empty($userIds)){
//                $query->where(function($q){
//                    $q->whereIn("purchase_uid",$userIds)->orWhere("cid",1);
//                })
                return $query->whereIn("purchase_uid",$userIds);
            }

        }

        $query = $query->where("purchase_uid",getAdminUserId());

        return $query;
    }

    public  static  function checkRule($query,$viewList){
        if(PermService::hasPerm($viewList[0])){
            return $query;
        }
        //查看下级
        if(PermService::hasPerm($viewList[1])){//查看下级的权限//获取用户部门下的所有用户
            $userIds = PermService::getSubUserId(getAdminUserId());
            if(!empty($userIds)){
//                $query->where(function($q){
//                    $q->whereIn("purchase_uid",$userIds)->orWhere("cid",1);
//                })
                return $query->whereIn("purchase_uid",$userIds);
            }

        }

        $query = $query->where("purchase_uid",getAdminUserId());

        return $query;
    }

}
