<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;
use DB;
use Illuminate\Support\Facades\Redis;

class ScmBrandModel extends Model
{
    protected $connection = 'spu';
    protected $table = 'lie_scm_brand';
    protected $primaryKey = 'scm_brand_id';
    public $timestamps = false;

    public function mappingCount()
    {
        return $this->hasMany(ScmBrandMappingModel::class, 'scm_brand_id', 'scm_brand_id')
            ->where('del_status', ScmBrandMappingModel::DEL_STATUS_OK);
    }

    public function getAllScmBrandIdAndName()
    {
        return $this->pluck('erp_brand_name', 'scm_brand_id')->toArray();
    }
}
