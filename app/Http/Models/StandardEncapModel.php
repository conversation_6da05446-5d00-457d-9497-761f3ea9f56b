<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;

class StandardEncapModel extends Model
{
    public $connection = 'spu';
    protected $table = 'lie_encap_standard';
    public $timestamps = false;


    const STATUS_OK = 1;//正常
    const STATUS_AUDIT_REJECT = 2;//审核不通过
    const STATUS_DISABLE = 3;//禁用

    public function standard_encap_mapping()
    {
        return $this->hasMany(StandardEncapMappingModel::class, 'standard_encap_id', 'standard_encap_id');
    }

}
