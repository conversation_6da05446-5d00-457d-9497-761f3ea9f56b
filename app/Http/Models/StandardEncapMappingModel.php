<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;

class StandardEncapMappingModel extends Model
{
    public $connection = 'spu';
    protected $table = 'lie_encap_standard_mapping';
    public $timestamps = false;

    const STATUS_OK = 1;

    public function encap()
    {
        return $this->hasOne(EncapModel::class, 'encap_id', 'encap_id');
    }

    public function standard_encap()
    {
        return $this->hasOne(StandardEncapModel::class, 'standard_encap_id', 'standard_encap_id');
    }


}
