<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;

class TaskLogModel extends Model
{
    public $connection = 'spu';
    protected $table = 'lie_task_log';
    public $timestamps = false;

    const STATUS_NEED_DEAL = 0;
    const STATUS_IN_PROGRESS = 1;
    const STATUS_OK = 2;
    const STATUS_FAILED = -1;

    const TYPE_UPDATE_SPU = 1;
    const TYPE_UPDATE_SPU_ATTR = 2;
    const TYPE_UPDATE_SPU_PDF = 3;
    const TYPE_UPDATE_SKU = 4;
    const TYPE_IMPORT_SHOP_BRAND_MAPPING = 5;
    const TYPE_IMPORT_SHOP_DISTRIBUTE_SKU = 6;

    public function updateTaskLog($logId, $data)
    {
       return TaskLogModel::where('id', $logId)->update($data);
    }
}
