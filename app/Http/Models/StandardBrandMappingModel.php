<?php

namespace App\Http\Models;

use App\Http\Models\Cms\CmsUserInfoModel;
use Illuminate\Database\Eloquent\Model;

//新版的标准品牌,以这个为准
class StandardBrandMappingModel extends Model
{
    public $connection = 'spu';
    protected $table = 'lie_brand_standard_mapping';
    public $timestamps = false;

    public function brand()
    {
        return $this->hasOne(BrandModel::class, 'brand_id', 'brand_id');
    }

    public function standard_brand()
    {
        return $this->hasOne(StandardBrandModel::class, 'standard_brand_id', 'standard_brand_id');
    }

    public function user()
    {
        return $this->hasOne(CmsUserInfoModel::class, 'userId','admin_id');
    }
}
