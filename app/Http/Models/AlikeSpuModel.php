<?php

namespace App\Http\Models;

use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Services\AlikeSpuService;
use Arr;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use PhpParser\PrettyPrinter\Standard;

class AlikeSpuModel extends Model
{
    public $connection = 'spu';
    protected $table = 'lie_alike_spu';
    public $timestamps = false;

    public function brand()
    {
        return $this->hasOne(BrandModel::class, 'brand_id', 'brand_id');
    }

    public function alike_brand()
    {
        return $this->hasOne(BrandModel::class, 'brand_id', 'alike_brand_id');
    }

    public function standard_brand()
    {
        return $this->hasOne(StandardBrandModel::class, 'brand_name', 'brand_name');
    }

    public function alike_standard_brand()
    {
        return $this->hasOne(StandardBrandModel::class, 'brand_name', 'alike_brand_name');
    }

    //获取后台用户信息
    public static function getCreateUsersInfo($userIds)
    {
        $model = new CmsUserInfoModel();
        $users = $model->whereIn('userId', $userIds)->pluck('email', 'userId');
        return $users ? $users->toArray() : [];
    }

    //删除替代型号映射
    public function deleteAlikeSpu($id)
    {
        $spuName = $this->where('id', $id)->value('spu_name');
        return DB::connection('spu')->transaction(function () use ($id, $spuName) {
            //先删掉主表,然后去修改中间表
            $res = $this->where('id', $id)->update([
                'is_deleted' => 1,
                'update_time' => time(),
            ]);
            $itemModel = new AlikeSpuItemsModel();
            $itemModel->where('main_id', $id)->delete();
            $alikeSpu = AlikeSpuModel::where('id', $id)->first()->toArray();
            if ($res) {
                //找出要删除alike_spu_ids和alike_spu_name
                $alikeSpuName = $alikeSpu['alike_spu_name'];
                $alikeSpuList = DB::connection('mongodb')->collection('spu')->where('spu_name',
                    $alikeSpuName)->get()->toArray();
                if (empty($alikeSpuList)) {
                    return true;
                }
                $alikeSpuIds = $alikeSpuNames = [];
                foreach ($alikeSpuList as $alikeSpu) {
                    $alikeSpuIds[] = $alikeSpu['spu_id'];
                    $alikeSpuNames[] = $alikeSpu['spu_name'];
                }
                $centerData = AlikeSpuCenterModel::where('spu_name', $spuName)->first()->toArray();
                $centerAlikeSpuIds = explode(',', $centerData['alike_spu_ids']);
                $centerAlikeSpuNames = explode(',', $centerData['alike_spu_names']);
                $centerAlikeSpuIds = array_unique(array_diff($centerAlikeSpuIds, $alikeSpuIds));
                $centerAlikeSpuNames = array_unique(array_diff($centerAlikeSpuNames, $alikeSpuNames));
                $centerModel = new AlikeSpuCenterModel();
                $centerModel->where('spu_name', $spuName)->update([
                    'alike_spu_ids' => trim(implode(',', $centerAlikeSpuIds), ','),
                    'alike_spu_names' => trim(implode(',', $centerAlikeSpuNames), ','),
                    'update_time' => time(),
                    'is_deleted' => 0,
                    'is_sync' => 0,
                ]);
                AlikeSpuService::reloadAlikeSpu($id);
                return true;
            }
            return false;
        });
    }
}
