<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;
use App\Http\Models\Supplier\SupplierChannelModel;

class AbilityLevelRuleModel extends Model
{
    public $connection = 'spu';
    protected $table = 'lie_ability_level_rule';
    public $timestamps = false;

    const STATUS_ENABLED = 1;
    const STATUS_DISABLED = -1;

    public function supplier()
    {
        return $this->belongsTo(SupplierModel::class, 'supplier_id', 'supplier_id');
    }

    public function supplier_channel()
    {
        return $this->belongsTo(SupplierChannelModel::class, 'supplier_code', 'supplier_code');
    }
}
