<?php

namespace App\Http\Models;

use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Models\Supplier\SupplierChannelModel;
use App\Http\Services\AlikeSpuService;
use App\Http\Services\EncodedService;
use App\Http\Transformers\SkuTransformer;
use Arr;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Http;

class AlikeSpuItemsModel extends Model
{
    public $connection = 'spu';
    protected $table = 'lie_alike_spu_items';
    public $timestamps = false;

    public function brand()
    {
        return $this->hasOne(BrandModel::class, 'brand_id', 'brand_id');
    }

    public function getCreateUsersInfo($userIds)
    {
        $model = new CmsUserInfoModel();
        $users = $model->whereIn('userId', $userIds)->pluck('email', 'userId');
        return $users ? $users->toArray() : [];
    }

    //删除替代型号item映射
    public function deleteAlikeSpuItem($id)
    {
        $spuItem = $this->where('id', $id)->first();
        $spuItem = !empty($spuItem) ? $spuItem->toArray() : [];
        if (!empty($spuItem)) {
            //先去将items表里面的数据删除
            $this->where('id', $id)->update(['update_time' => time(), 'is_deleted' => 1]);
            $alikeSpuModel = new AlikeSpuModel();
            $spuItem['alike_spu_id'] = $alikeSpuModel->where('id', $spuItem['main_id'])->value('alike_spu_id');
            $centerModel = new AlikeSpuCenterModel();
            $spuName = $spuItem['spu_name'];
            $alikeSpuId = $spuItem['alike_spu_id'];
            //然后去处理中间表数据
            //先判断是否已经完全没有这个spu_name的数据了
            //完全没有的话去中间表删除对应的spu_name
            $count = $this->where('spu_name', $spuItem['spu_name'])->where('main_id', $spuItem['main_id'])->count();
            if ($count == 0) {
                //查找原来的alike_spu_name
                $alikeSpuModel = new AlikeSpuModel();
                $alikeSpuName = $alikeSpuModel->where('alike_spu_id', $alikeSpuId)->value('alike_spu_name');
                $centerData = $centerModel->where('spu_name', $spuName)->first();
                $centerData = !empty($centerData) ? $centerData->toArray() : [];
                if (!empty($centerData)) {
                    //分别去删除里面的数据
                    $alikeSpuIds = explode(',', $centerData['alike_spu_ids']);
                    $alikeSpuIds = array_filter($alikeSpuIds, function ($id) use ($alikeSpuId) {
                        return $id != $alikeSpuId;
                    });
                    $alikeSpuNames = explode(',', $centerData['alike_spu_names']);
                    $alikeSpuNames = array_filter($alikeSpuNames, function ($name) use ($alikeSpuName) {
                        return $name != $alikeSpuName;
                    });
                    $isDeleted = empty($alikeSpuIds) && empty($alikeSpuNames) ? 1 : 0;
                    //整合回原来的表
                    return $centerModel->where('spu_name', $spuName)->update([
                        'alike_spu_ids' => trim(implode(',', $alikeSpuIds), ','),
                        'alike_spu_names' => trim(implode(',', $alikeSpuNames), ','),
                        'is_deleted' => $isDeleted,
                        'update_time' => time(),
                        'is_sync' => 0,
                    ]);
                }
            } else {
                //否则需要更新下中间表,而且还要排除掉那些删除的商品ID
                AlikeSpuService::reloadAlikeSpu($id);
            }
        }
        return false;
    }

}
