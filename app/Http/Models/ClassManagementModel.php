<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;
use App\Http\Models\PoolClass\PoolClassModel;

class ClassManagementModel extends Model
{
    public $connection = 'spu';
    protected $table = 'lie_class_management';
    public $timestamps = false;

    const STATUS_PENDING = 0;
    const STATUS_PROCESSED = 1;

    public function class()
    {
        return $this->belongsTo(PoolClassModel::class, 'class_id', 'class_id');
    }
}
