<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;

class DistributeUserPriceRuleModel extends Model
{
    public $connection = 'distribution';
    protected $table = 'lie_shop_distribute_user_price_rule';
    public $timestamps = false;

    public function supplier()
    {
        return $this->belongsTo(SupplierModel::class, 'supplier_id', 'supplier_id');
    }

    public function userPrice()
    {
        return $this->belongsTo(DistributeUserPriceModel::class, 'user_price_id', 'id');
    }
}
