<?php

namespace App\Http\Models;

use App\Http\Models\Cms\CmsUserInfoModel;
use Illuminate\Database\Eloquent\Model;

//标准品牌和供应商品牌的映射
class StandardBrandScmMappingModel extends Model
{
    protected $connection = 'spu';
    protected $table = 'lie_brand_standard_scm_mapping';
    public $timestamps = false;

    const STATUS_OK = 1;

    public function getScmBrandIdByStandardBrandId($standardBrandId)
    {
        return $this->where('standard_brand_id', $standardBrandId)->value('scm_brand_id');
    }

    //保存映射
    public function saveMapping($standardId, $scmBrandId)
    {
        if ($this->where('standard_brand_id', $standardId)->exists()) {
            return $this->where('standard_brand_id', $standardId)->update([
                'scm_brand_id' => $scmBrandId,
                'update_time' => time(),
            ]);
        } else {
            return $this->insert([
                'scm_brand_id' => $scmBrandId,
                'standard_brand_id' => $standardId,
                'status' => self::STATUS_OK,
                'add_time' => time(),
                'admin_id' => request()->user->userId,
            ]);
        }
    }
}
