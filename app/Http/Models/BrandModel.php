<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;
use DB;
use Illuminate\Support\Facades\Redis;

class BrandModel extends Model
{
    public $connection = 'spu';
    protected $table = 'lie_brand';
    public $timestamps = false;

    const STATUS_OK = 1;

    public function standard_brand_mapping()
    {
        return $this->hasOne(StandardBrandMappingModel::class, 'brand_id', 'brand_id');
    }

    public function standard_brand()
    {
        return $this->hasOneThrough(StandardBrandModel::class, StandardBrandMappingModel::class, 'standard_brand_id',
            'standard_brand_id', 'brand_id');
    }

    public function findBrand($brandName)
    {
        $map['brand_name'] = $brandName;
        $findL = $this->where($map)->select('brand_id')->first();
        if ($findL) {
            $findL = $findL->toArray();
        }
        if (empty($findL['brand_id'])) {
            $findY = DB::connection('spu')
                ->table('lie_brand_mapping')
                ->where('supplier_brand_name', $brandName)
                ->select('liexin_brand_id')
                ->first();
            $findY = (array)$findY;
            if (empty($findY['liexin_brand_id'])) {
                return false;
            }
        }
        return !empty($findL['brand_id']) ? $findL['brand_id'] : $findY['liexin_brand_id'];
    }

    public function HDBrandAll()
    {
        return $this->select('brand_id', 'brand_name')->get()->toArray();
    }

    //设置品牌唯一以及品牌信息
    public function HDBrandName($brandId = '', $redis = '', $isUpload = false)
    {
        if (empty($brandId)) {
            return false;
        }
        if (empty($redis)) {
            $redis = Redis::connection('sku');
        }
        $brandName = $redis->hget('brand', $brandId);
        if (!$brandName || $isUpload === true) {
            $find = $this->where('brand_id', '=', $brandId)->select('brand_name')->first();
            if (!$find) {
                return false;
            }
            $find = $find->toArray();
            $redis->hset('brand', $brandId, $find['brand_name']);
            $key = md5(strtolower($find['brand_name']));
            if (!$redis->hget('brand_name_all', $key)) {
                $redis->hset('brand_name_all', $key, $brandId);
            }
            return $find['brand_name'];
        } else {
            return $brandName;
        }
    }

    public function map($data = [])
    {
        $map = [];
        foreach ($data as $k => $v) {
            if (empty($v) && $v !== 0) {
                continue;
            }
            switch ($k) {
                case 'start_time':
                    $map[] = ['lie_brand.add_time', '>=', strtotime($v)];
                    break;
                case 'end_time':
                    $map[] = ['lie_brand.add_time', '<=', strtotime($v) + 60 * 60 * 24];
                    break;
                case 'brand_name':
                    $map[] = ['lie_brand.brand_name', 'like', $v . '%'];
                    break;
                default:
                    $map[] = ['lie_brand.' . $k, '=', $v];
            }
        }

        return $map;
    }

    public function changeStatus($brandId, $status)
    {
        $result = $this->where('brand_id', $brandId)->update([
            'brand_id' => $brandId,
            'status' => $status
        ]);
        //同时还要将修改的东西放到缓存里面去
        if ($result) {
            $redis = Redis::connection('sku');
            $brand = $this->where('brand_id', $brandId)->first();
            if (!empty($brand)) {
                $brand = $brand->toArray();
                $redis->hset('brand_info', $brandId, $brand);
                return true;
            }
        }
        return false;
    }
}
