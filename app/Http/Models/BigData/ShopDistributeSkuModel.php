<?php

namespace App\Http\Models\BigData;

use App\Http\Models\SupplierModel;
use Illuminate\Database\Eloquent\Model;
use App\Http\Models\Supplier\SupplierChannelModel;

class ShopDistributeSkuModel extends Model
{
    public $connection = 'distribution';
    protected $table = 'lie_shop_distribute_sku';
    public $timestamps = false;

    const STATUS_ENABLED = 1;
    const STATUS_DISABLED = -1;

    public function shop()
    {
        return $this->hasOne(ShopInfoModel::class, 'shop_id', 'shop_id');
    }

    public function channel()
    {
        return $this->hasOne(SupplierModel::class, 'supplier_id', 'supplier_id');
    }

    public function supplier_channel()
    {
        return $this->hasOne(SupplierChannelModel::class, 'supplier_code', 'supplier_code');
    }
}
