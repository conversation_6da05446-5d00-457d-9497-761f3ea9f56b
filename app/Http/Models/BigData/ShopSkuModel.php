<?php

namespace App\Http\Models\BigData;

use Illuminate\Database\Eloquent\Model;

class ShopSkuModel extends Model
{
    public $connection = 'distribution';
    protected $table = 'lie_shop_sku';
    public $timestamps = false;

    const STATUS_AUDITED = 2;
    const STATUS_UNAUDITED = -1;
    //审核中
    const STATUS_AUDITING = 1;


    public function shop()
    {
        return $this->hasOne(ShopInfoModel::class, 'shop_id', 'shop_id');
    }

    public function push_log()
    {
        return $this->hasOne(ShopSkuPushLogModel::class, 'sku_sn', 'sku_sn');
    }
}
