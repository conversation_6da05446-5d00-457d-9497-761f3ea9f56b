<?php

namespace App\Http\Models\BigData;

use App\Http\Models\PoolClass\PoolClassModel;
use Illuminate\Database\Eloquent\Model;

class ShopClassMappingModel extends Model
{
    public $connection = 'distribution';
    protected $table = 'lie_shop_class_mapping';
    public $timestamps = false;


    public function shop_class()
    {
        return $this->hasOne(ShopClassModel::class, 'id', 'shop_class_id');
    }

    public function lie_class()
    {
        return $this->hasOne(PoolClassModel::class, 'class_id', 'lie_class_id');
    }
}
