<?php

namespace App\Http\Models\BigData;

use App\Http\Models\PoolClass\PoolClassAttrModel;
use App\Http\Models\PoolClass\PoolClassModel;
use Illuminate\Database\Eloquent\Model;

class ShopAttrMappingModel extends Model
{
    public $connection = 'distribution';
    protected $table = 'lie_shop_attr_mapping';
    public $timestamps = false;


    public function shop_attr()
    {
        return $this->hasOne(ShopAttrModel::class, 'id', 'shop_attr_id');
    }

    public function lie_attr()
    {
        return $this->hasOne(PoolClassAttrModel::class, 'attr_id', 'lie_attr_id');
    }
}
