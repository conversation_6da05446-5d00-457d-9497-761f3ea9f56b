<?php

namespace App\Http\Models\BigData;

use App\Http\Models\StandardBrandModel;
use Illuminate\Database\Eloquent\Model;

class ShopBrandMappingModel extends Model
{
    public $connection = 'distribution';
    protected $table = 'lie_shop_brand_mapping';
    public $timestamps = false;

    public function lie_brand()
    {
        return $this->hasOne(StandardBrandModel::class , 'standard_brand_id' , 'lie_brand_id');
    }

    public function shop_brand()
    {
        return $this->hasOne(ShopBrandModel::class , 'id' , 'shop_brand_id');
    }
}
