<?php

namespace App\Http\Models\BigData;

use App\Http\Models\PoolClass\ClassAttrUnitConvertModel;
use Illuminate\Database\Eloquent\Model;

class ShopUnitMappingModel extends Model
{
    public $connection = 'distribution';
    protected $table = 'lie_shop_unit_mapping';
    public $timestamps = false;

    public function lie_unit_convert()
    {
        return $this->hasOne(ClassAttrUnitConvertModel::class, 'standard_unit_name', 'standard_unit_name');
    }
}
