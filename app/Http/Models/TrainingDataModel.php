<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;

class TrainingDataModel extends Model
{
    public $connection = 'spu';
    protected $table = 'lie_training_data';
    public $timestamps = false;

    const TYPE_BRAND = 1; // 品牌
    const TYPE_MODEL = 2; // 型号
    const TYPE_CATEGORY = 3; // 分类

    const TYPE_NAMES = [
        self::TYPE_BRAND => '品牌',
        self::TYPE_MODEL => '型号',
        self::TYPE_CATEGORY => '分类',
    ];

    public function getTypeNameAttribute()
    {
        return self::TYPE_NAMES[$this->type] ?? '';
    }
}
