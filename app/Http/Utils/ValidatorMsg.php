<?php
/**
 * Created by PhpStorm.
 * User: du<PERSON><PERSON>
 * Date: 2021/9/1
 * Time: 9:40 AM
 */

namespace App\Http\Utils;


class ValidatorMsg
{

    // 只返回错误的第一条错误
    public static function getMsg($error_list)
    {
        $err_msg = '';
        foreach ($error_list as $err_key => $err_msgs) {
            foreach ($err_msgs as $msg) {
                $err_msg .= "{$err_key}|{$msg}";
                break;
            }
            break;
        }
        return $err_msg;
    }
}