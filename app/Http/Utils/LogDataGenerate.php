<?php

namespace App\Http\Utils;

//日志数据构建类
use Illuminate\Support\Facades\Lang;

class LogDataGenerate
{


    //获取新增日志,根据插入数据来的
    //objId是主键ID
    public static function getInsertLog($objId, $insertData = [])
    {
        $logData = '';
        foreach ($insertData as $key => $item) {
            if (!Lang::has('log-fields.' . $key)) {
                continue;
            }
            $logData .= trans('log-fields.' . $key) . '为' . $item . '; ';
        }
        return $logData ? [
            'obj_id' => $objId,
            'log_data' => ['message' => '新增数据 : ' . $logData],
        ] : [];
    }

    //获取修改的日志,根据更新数据来的
    public static function getUpdateLog($objId, $updateData = [], $originData = [])
    {
        $logData = '';
        foreach ($updateData as $key => $item) {
            if (!Lang::has('log-fields.' . $key)) {
                continue;
            }
            $originItem = \Arr::get($originData, $key);
            if ($originItem == $item) {
                continue;
            }
            //单独处理币种
            switch ($key) {
                case 'currency':
                    $originItem = \Arr::get(config('field.Currency'), $originItem);
                    $item = \Arr::get(config('field.Currency'), $item);
            }
            $logData .= trans('log-fields.' . $key) . "由 ${originItem} 修改为 ${item} ;";
        }
        return $logData ? [
            'obj_id' => $objId,
            'log_data' => ['message' => '修改数据 : ' . $logData],
        ] : [];
    }

    //获取删除的日志
    public static function getDeleteLog($objId, $deleteData = [])
    {
        $logData = '';
        foreach ($deleteData as $key => $item) {
            if (!Lang::has('log-fields.' . $key)) {
                continue;
            }
            $logData .= trans('log-fields.' . $key) . '为' . $item . '; ';
        }
        return $logData ? [
            'obj_id' => $objId,
            'log_data' => ['message' => '删除数据 : ' . $logData]
        ] : [];
    }
}
