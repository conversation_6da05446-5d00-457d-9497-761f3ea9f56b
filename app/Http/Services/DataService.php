<?php


namespace App\Http\Services;

use App\Http\Utils\Sku;
use App\Model\ArticleModel;
use Illuminate\Support\Arr;
use App\Imports\ClassImport;
use App\Http\Models\BrandModel;
use App\Http\Models\EncapModel;
use App\Imports\ClassAttrImport;
use App\Imports\IEdgeClassImport;
use App\Http\Models\AlikeSpuModel;
use App\Http\Models\SupplierModel;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use PhpAmqpLib\Message\AMQPMessage;
use App\Http\Models\AgentBrandModel;
use App\Http\Queue\RabbitQueueModel;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Redis;
use App\Http\Models\BrandMappingModel;
use App\Http\Models\EncapMappingModel;
use App\Http\Models\StandardBrandModel;
use App\Http\Models\StandardEncapModel;
use App\Http\Models\BigData\ShopSkuModel;
use App\Http\Models\ClassManagementModel;
use App\Http\Models\AbilityLevelRuleModel;
use App\Exports\AlikeSpuUploadResultExport;
use App\Http\Models\BigData\ShopBrandModel;
use App\Http\Services\StandardEncapService;
use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Models\StandardBrandMappingModel;
use App\Http\Models\StandardEncapMappingModel;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use App\Http\Models\BigData\ShopSpuPushLogModel;
use App\Http\Models\PoolClass\ClassifyAttrModel;
use App\Http\Models\PoolClass\ClassAttrUnitModel;
use App\Http\Models\PoolClass\PoolClassAttrModel;
use App\Http\Models\BigData\ShopBrandMappingModel;
use App\Http\Models\PoolClass\ClassAttrValueModel;
use App\Http\Models\BigData\ShopDistributeSkuModel;
use App\Http\Models\OperationLogModel;
use App\Http\Models\PoolClass\ClassAttrUnitConvertModel;
use App\Http\Models\PoolClass\ClassAttrUnitConvertItemModel;
use Carbon\Carbon;

class DataService
{
    //初始化spu_pin的redis
    public static function initSpuPin()
    {
        $alikeSpus = AlikeSpuModel::all()->toArray();
        $redis = Redis::connection('sku');
        foreach ($alikeSpus as $spu) {
            $redis->hset(
                'spu_pin',
                Sku::DrawLetter2($spu['alike_spu_name']) . '@' . Sku::DrawLetter2($spu['spu_name']),
                $spu['pin_to_pin'] == 1 ? '是' : '否'
            );
        }
    }

    //初始化标准品牌映射到队列
    public static function initStandardBrandMappingQueue()
    {

        $conn = new AMQPStreamConnection(
            config('database.connections.rabbitmq.host'),
            config('database.connections.rabbitmq.port'),
            config('database.connections.rabbitmq.login'),
            config('database.connections.rabbitmq.password')
        );
        $channel = $conn->channel();
        $channel->queue_declare('stand_brand_map', false, true, false, false);


        $standardBrands = StandardBrandModel::all()->keyBy('standard_brand_id')->toArray();
        $mappings = StandardBrandMappingModel::chunk(
            1000,
            function ($mappings) use ($standardBrands, $channel) {
                foreach ($mappings as $mapping) {
                    $msg = new AMQPMessage(json_encode([
                        'brand_id' => $mapping['brand_id'],
                        'stand_brand_id' => $mapping['standard_brand_id'],
                        'stand_brand_name' => Arr::get(
                            Arr::get($standardBrands, $mapping['standard_brand_id']),
                            'brand_name'
                        ),
                    ]), array('content_type' => 'text/plain'));
                    $channel->basic_publish($msg, '', 'stand_brand_map');
                }
            }
        );
    }

    //初始化标准品牌到redis
    public static function initStandardBrand()
    {
        $standardBrands = StandardBrandModel::all()->toArray();
        $redis = Redis::connection('sku');
        foreach ($standardBrands as $standardBrand) {
            $redis->hset('standard_brand', $standardBrand['standard_brand_id'], json_encode($standardBrand));
        }
    }

    public static function initStandardBrandMapping()
    {
        $mappings = StandardBrandMappingModel::all()->toArray();
        $redis = Redis::connection('sku');
        foreach ($mappings as $mapping) {
            $redis->hset('standard_brand_mapping', $mapping['brand_id'], $mapping['standard_brand_id']);
        }
    }

    //初始化首字母数据到标准品牌
    public static function initLetterForStandardBrand()
    {
        $selfBrand['brand_name'] = preg_replace("/\p{Han}+/u", '', 'SEMTECH(升特)');
        $selfBrand['brand_name'] = preg_replace("/([x80-xff]*)/i", '', 'SEMTECH(升特)');
        $selfBrand['brand_name'] = str_replace('(', '', $selfBrand['brand_name']);
        $selfBrand['brand_name'] = str_replace(')', '', $selfBrand['brand_name']);
        dump($selfBrand['brand_name']);
        $mongo = \DB::connection('mongodb');
        $brandId = BrandModel::where('brand_name', trim($selfBrand['brand_name']))->value('brand_id');
        $spu = $mongo->table('spu')->where('spu_name', 'SX1278IMLTRT')
            ->where('brand_id', $brandId)->first();
        dd($spu);
    }

    //初始化分类到redis
    public static function initClass()
    {
        $redis = Redis::connection('sku');
        $redis->del('pool_class_info');
        $path = public_path('template/class/目录.xlsx');
        $classList = Excel::toArray(new ClassImport(), $path)[0];
        //区分左侧的合并单元格
        $topClassList = [];
        foreach ($classList as $key => $class) {
            if ($key == 0) {
                continue;
            }
            if ($class[0]) {
                $topClassList[] = trim($class[0]);
            }
        }
        $topClassData = [];
        foreach ($topClassList as $class) {
            $class = trim($class);
            if (PoolClassModel::where('class_name', $class)->exists()) {
                continue;
            }
            $topClassData[] = [
                'class_name' => trim($class),
                'add_time' => time(),
            ];
        }
        //先插入一级分类
        PoolClassModel::insert($topClassData);
        $parentId = 0;
        foreach ($classList as $key => $class) {
            if ($key == 0) {
                continue;
            }
            //先找出一级分类名称
            $topClassName = trim($class[0]);
            if ($topClassName) {
                $parentId = PoolClassModel::where('class_name', $topClassName)->value('class_id');
                $data['class_name'] = trim($class[1]);
                $data['parent_id'] = $parentId;
                $data['add_time'] = time();
                if (PoolClassModel::where('class_name', trim($class[1]))->exists()) {
                    continue;
                }
                PoolClassModel::insert($data);
            } else {
                if ($parentId != 0) {
                    if (PoolClassModel::where('class_name', trim($class[1]))->exists()) {
                        continue;
                    }
                    $data['class_name'] = trim($class[1]);
                    $data['parent_id'] = $parentId;
                    $data['add_time'] = time();
                    PoolClassModel::insert($data);
                }
            }
        }
        $classList = PoolClassModel::where('class_id', '>', 0)->get()->toArray();
        $classModel = new PoolClassModel();
        foreach ($classList as $class) {
            $classModel->setClassCache($class['class_id'], $class);
        }
    }

    public static function initClassCache()
    {
        $classList = PoolClassModel::where('class_id', '>', 0)->get()->toArray();
        $classModel = new PoolClassModel();
        foreach ($classList as $class) {
            $classModel->setClassCache($class['class_id'], $class);
        }
    }

    //转移MRO分类到新的分类表
    public static function transferMROClassToNewClass()
    {
        $classIdList = [
            14854,
        ];
        //先找出所有mro分类
        $mroClassList = DB::connection('class')->table('class_back')->whereIn('class_id', $classIdList)->get()->toArray();
        //插入到新表
        $mroClassList = array_map(function ($class) {
            $class = (array)$class;
            $class['class_type'] = 3;
            $class['add_time'] = time();
            $class['update_time'] = 0;
            return $class;
        }, $mroClassList);
        $mroClassList = array_filter($mroClassList, function ($class) use ($classIdList) {
            $exist = PoolClassModel::where('class_name', $class['class_name'])->whereIn('class_id', $classIdList)
                ->exists();
            if ($exist) {
                return false;
            }
            return true;
        });
        PoolClassModel::insert($mroClassList);
        $classList = PoolClassModel::where('class_id', '>', 0)->get()->toArray();
        $classModel = new PoolClassModel();
        foreach ($classList as $class) {
            $classModel->setClassCache($class['class_id'], $class);
        }
    }

    public static function importClassAttrUnit()
    {
        $path = public_path('template/class/单位.xlsx');
        $data = Excel::toArray(new Collection(), $path);
        $classAttrUnitList = $data[0];
        $classAttrUnitList = array_map(function ($value) {
            $value = array_map(function ($v) {
                return trim($v);
            }, $value);
            return $value;
        }, $classAttrUnitList);
        foreach ($classAttrUnitList as $key => $item) {
            if ($key == 0) {
                continue;
            }
            $unitName = $item[1];
            if (empty($unitName)) {
                $unitName = $item[0];
            }
            if (!ClassAttrUnitModel::where('attr_unit_name', $unitName)->exists()) {
                $unit = [
                    'attr_unit_name' => $unitName,
                    'remark' => $item[3],
                    'add_time' => time(),
                ];
                ClassAttrUnitModel::insert($unit);
            }
        }
    }

    public static function importClassAttrUnitConvert()
    {
        //猎芯-得捷映射关系.xlsx
        $path = public_path('template/class/单位.xlsx');
        $data = Excel::toArray(new Collection(), $path);
        $classAttrUnitList = $data[0];
        $classAttrUnitList = array_map(function ($value) {
            $value = array_map(function ($v) {
                return trim($v);
            }, $value);
            return $value;
        }, $classAttrUnitList);
        $classAttrUnitList = collect($classAttrUnitList)->groupBy(0)->toArray();
        foreach ($classAttrUnitList as $key => $items) {
            if ($key == '标准单位') {
                continue;
            }
            if (count($items) == 0) {
                continue;
            }
            if (ClassAttrUnitConvertModel::where('standard_unit_name', $key)->exists()) {
                continue;
            }
            //先插入主表
            $convertId = ClassAttrUnitConvertModel::insertGetId([
                'convert_name' => $items[0][3],
                'standard_unit_name' => $key,
                'create_name' => 'admin',
                'create_uid' => 1000,
                'create_time' => time(),
            ]);
            $convertItemList = [];
            foreach ($items as $item) {
                $standardUnitName = $item[0];
                $unitName = $item[1];
                $ratio = $item[2];
                $remark = $item[3];
                $convertItemList[] = [
                    'convert_id' => $convertId,
                    'standard_unit_name' => $standardUnitName,
                    'unit_name' => $unitName ?: $standardUnitName,
                    'ratio' => $ratio ?: 1,
                    'remark' => $remark,
                    'create_name' => 'admin',
                    'create_uid' => 1000,
                    'create_time' => time(),
                ];
            }
            ClassAttrUnitConvertItemModel::insert($convertItemList);
        }
    }

    public static function importClassAttr()
    {
        $directory = public_path('template/class/classAttr');
        $files = File::allFiles($directory);
        foreach ($files as $file) {
            $filePath = $file->getRealPath();
            $fileName = $file->getFilename();
            $data = Excel::toArray(new Collection(), $filePath);
            //清楚两边空格
            $data = array_map(function ($value) {
                return array_map(function ($v) {
                    return array_map(function ($item) {
                        return trim($item);
                    }, $v);
                }, $value);
            }, $data);
            foreach ($data as $key => $item) {
                $secondClassId = 0;
                $secondClassName = '';
                foreach ($item as $k => $v) {
                    //第一个元素的第一个数据就是sheet名称
                    //也是二级分类名称
                    if ($k == 0) {
                        //先找出二级分类id
                        $secondClassName = $v[$k];
                        $secondClassId = PoolClassModel::where('class_name', $secondClassName)->value('class_id');
                        continue;
                    }
                    if ($k == 1) {
                        continue;
                    }
                    //进行数据操作
                    $attrName = $v[1];
                    if (empty($attrName)) {
                        continue;
                    }
                    $attrUnitName = $v[2];
                    $isMain = $v[3];
                    //找出参数单位id
                    $attrUnitId = ClassAttrUnitModel::where('remark', $attrUnitName)->orWhere('attr_unit_name', $attrUnitName)->value('attr_unit_id');
                    if (empty($secondClassId)) {
                        dump('文件名 : ' . $fileName . ' 分类名称不存在 : ' . $secondClassName);
                        continue 2;
                    }
                    $attrData = [
                        'attr_name' => $attrName,
                        'class_id' => $secondClassId,
                        'attr_unit_id' => $attrUnitId ?? 0,
                        'is_main' => $isMain,
                        'dgk_attr_id' => 0,
                        'input_way' => '自动',
                        'create_name' => 'admin'
                    ];
                    $attrId = PoolClassAttrModel::where('attr_name', $attrName)
                        ->where('class_id', $secondClassId)->value('attr_id');
                    if ($attrId) {
                        dump('更新属性 : ' . $attrName);
                        $attrData['update_time'] = time();
                        PoolClassAttrModel::where('attr_id', $attrId)->update($attrData);
                    } else {
                        $attrData['add_time'] = time();
                        PoolClassAttrModel::insert($attrData);
                    }
                }
            }
        }
    }

    //提取分类并且给他们完善
    public function getClassFromArticle()
    {
        $data = [['文章ID', '文章标题', '发布时间', '原分类标签名称']];
        $articleList = DB::connection('ichunt')->table('lie_article')->select(['art_id', 'title', 'publish_time', 'sku_class_ids'])->where('sku_class_ids', '!=', '')->get()->toArray();
        foreach ($articleList as $article) {
            $article = (array)$article;
            $classIds = $article['sku_class_ids'];
            $classIds = explode(',', $classIds);
            $classNameList = DB::connection('class')->table('class_back')->whereIn('class_id', $classIds)->pluck('class_name')->toArray();
            $classNameList = implode(';', $classNameList);
            $data[] = [
                $article['art_id'],
                $article['title'],
                date('Y-m-d H:i:s', $article['publish_time']),
                $classNameList
            ];
        }

        return Excel::download(new AlikeSpuUploadResultExport($data), '文章分类.xlsx');
    }

    public static function init()
    {
        ini_set('memory_limit', -1);
        $redis = Redis::connection('sku');
        $redis->hdel('class_attr_value_by_id');
        $redis->hdel('class_attr_value_by_val');
        $attrValueModel = new ClassAttrValueModel();
        $values = $attrValueModel->get()->toArray();
        foreach ($values as $value) {
            $redis->hset('class_attr_value_by_id', $value['attr_value_id'], $value['value']);
            $redis->hset('class_attr_value_by_val', $value['value'], $value['attr_value_id']);
        }
    }

    //导入华云的分类,class_type=3
    public static function importIEdgeClass()
    {
        $path = public_path('template/data/iedge_class.xlsx');
        $data = Excel::toArray(new IEdgeClassImport(), $path)[0];
        $classListGroupByTopClass = [];
        foreach ($data as $key => $item) {
            if ($key == 0 || $key == 1) {
                continue;
            }
            $topClassName = trim($item[2]);
            if ($topClassName) {
                $top = $topClassName;
                $classListGroupByTopClass[$top] = [];
            }
            $secondClassName = trim($item[3]);
            if ($secondClassName) {
                $classListGroupByTopClass[$top][] = $secondClassName;
            }
        }

        //插入到数据库,并且初始化到redis
        foreach ($classListGroupByTopClass as $topClass => $secondClassList) {
            //先插入顶级分类
            $topClassId = PoolClassModel::where('class_name', $topClass)->where('parent_id', 0)
                ->where('class_type', 3)->value('class_id');
            if (!$topClassId) {
                $topClassData = [
                    'class_name' => $topClass,
                    'add_time' => time(),
                    'class_type' => 3,
                ];
                $topClassId = PoolClassModel::insertGetId($topClassData);
            }
            //操作二级分类
            foreach ($secondClassList as $secondClass) {
                if (!PoolClassModel::where('class_name', $secondClass)->where('parent_id', '!=', 0)
                    ->where('class_type', 3)->exists()) {
                    $secondClassData = [
                        'class_name' => $secondClass,
                        'add_time' => time(),
                        'parent_id' => $topClassId,
                        'class_type' => 3,
                    ];
                    PoolClassModel::insert($secondClassData);
                }
            }
        }

        $classList = PoolClassModel::where('class_type', 3)->get()->toArray();
        foreach ($classList as $class) {
            (new PoolClassModel())->setClassCache($class['class_id'], $class);
        }
    }

    public static function getSpuAttr()
    {
        $mongo = DB::connection('mongodb');
        $spuAttr = $mongo->table('spu_attrs2')->where('spu_id', (int)2171386043730684800)->first();
        dd($spuAttr);
    }


    private static function removeSpecialChars($str)
    {
        // 定义要移除的特殊字符数组
        $special_chars = ["\t", "\n"];
        // 使用 str_replace 函数替换特殊字符
        $result = str_replace($special_chars, '', $str);

        return $result;
    }

    //导入封装映射和映射
    public static function importStandardEncap()
    {
        $path = public_path('template/data/encap_data.xlsx');
        $data = Excel::toArray(new IEdgeClassImport(), $path)[0];
        $standardEncapList = [];
        foreach ($data as $key => $item) {
            if ($key == 0) {
                continue;
            }
            if (empty($item[1])) {
                continue;
            }
            $item[1] = self::removeSpecialChars($item[1]);
            $standardEncapList[] = $item[1];
        }
        $redis = Redis::connection('sku');
        //插入到数据库,并且初始化到redis
        foreach ($standardEncapList as $encap) {
            DB::transaction(function () use ($encap, $redis) {
                //插入标准库,获取id后,再插入普通库,获取id,然后两个关联起来
                $standardEncapId = StandardEncapModel::insertGetId([
                    'encap_name' => $encap,
                    'create_time' => time(),
                    'status' => 1
                ]);
                $encapId = EncapModel::insertGetId([
                    'encap_name' => $encap,
                    'create_time' => time(),
                    'status' => 1
                ]);

                StandardEncapMappingModel::insert([
                    'encap_id' => $encapId,
                    'standard_encap_id' => $standardEncapId,
                    'create_time' => time(),
                    'status' => 1
                ]);

                $redis->hset('standard_encap_mapping', strtoupper($encap), $encap);
            });
        }
    }

    //修复spu数据
    public static function repairIedgeSpuData()
    {
        //先去搜索找出所有华云的数据
        $data = self::searchIedgeSku();
        //获取总数
        $total = $data['data']['total'] ?? 0;
        if (!$total) {
            dump("找不到sku数据");
            return;
        }
        dump("需要处理的总数 : " . $total);
        $queue = new RabbitQueueModel('trading');
        $count = 0;
        $totalPage = (int)ceil($total / 20);
        //进入修复循环
        for ($i = 1; $i <= $totalPage; $i = $i + 1) {
            $itemList = self::searchIedgeSku($i);
            //根据搜索出来的sku去获取spu
            foreach ($itemList['data']['goods_id'] ?? [] as $goodsId) {
                $count++;
                $sku = (new SkuService())->getSkuCacheInfo($goodsId);
                if (empty($sku['class_id1']) && empty($sku['class_id2'])) {
                    continue;
                }
                //dump([
                //    'spu_id' => $sku['spu_id'],
                //    'class_id1' => $sku['class_id1'],
                //    'class_id2' => $sku['class_id2'],
                //]);
                //去推送到spu辐射队列
                $queue->insertRawQueue('', [
                    'spu_id' => (string)$sku['spu_id'],
                    'spu_data_info' => [
                        'class_id1' => $sku['class_id1'],
                        'class_id2' => $sku['class_id2'],
                    ],
                    'type' => 3,
                ], 'spu_update_Info');
            }
            dump('已处理spu数量 : ' . $count);
            //dd(123);
        }
    }

    //修复spu数据
    public static function repairIedgeImage()
    {
        //先去搜索找出所有华云的数据
        $data = self::searchIedgeSku();
        //获取总数
        $total = $data['data']['total'] ?? 0;
        if (!$total) {
            dump("找不到sku数据");
            return;
        }
        dump("需要处理的总数 : " . $total);
        $count = 0;
        $totalPage = (int)ceil($total / 20);
        $redis = Redis::connection('sku');
        //进入修复循环
        for ($i = 1; $i <= $totalPage; $i = $i + 1) {
            $itemList = self::searchIedgeSku($i);
            //根据搜索出来的sku去获取spu
            foreach ($itemList['data']['goods_id'] ?? [] as $goodsId) {
                $skuCache = $redis->hget('sku', $goodsId);
                $skuCache = json_decode($skuCache, true);
                if (!empty($skuCache['goods_images'])) {
                    continue;
                }
                $dbInfo = getSpuSkuDb($goodsId);
                $connection = DB::connection($dbInfo['db']);
                $count++;

                //删除多余字段
                $select = config('field.SkuField'); //生成查询字段，避免速度过慢
                foreach ($select as $k => $v) {
                    $SelectField[] = $k;
                }
                $SelectField[] = 'goods_id';
                $skuDb = $connection->table($dbInfo['table'])->where('goods_id', $goodsId)
                    ->select($SelectField)->first();
                $skuDb = (array)$skuDb;
                if (empty($skuDb['goods_images'])) {
                    continue;
                }
                (new SkuService())->saveSkuToRedis($skuDb);
                dump("已修复 : " . $goodsId);
            }
            dump('已处理sku数量 : ' . $count);
            //dd(123);
        }
    }

    private static function searchIedgeSku($page = 1)
    {
        $url = config('website.search_domain_new') . '/search/es/searchSku';
        $map['p'] = $page;
        $map['show_status'] = 1;

        $map['offset'] = 20;

        $map['supplier_id/sr'] = "gt,0";
        $map['admin'] = 1; // 后台搜索
        $map['no_rule'] = "1122"; // 后台搜索
        $map['org_id/eq'] = 3;
        $res = Http::get($url, $map)->body();
        $data = json_decode($res, true);

        return $data;
    }

    public static function deleteStandardEncap()
    {
        ini_set('memory_limit', -1);
        $standardEncapList = StandardEncapModel::select(['standard_encap_id', 'encap_name'])->where('status', '!=', 0)->limit(2000)->get()->toArray();

        foreach ($standardEncapList as $standardEncap) {
            $exists = StandardEncapMappingModel::where('standard_encap_id', $standardEncap['standard_encap_id'])->exists();
            if (!$exists) {
                dump($standardEncap['encap_name']);
                StandardEncapModel::where('standard_encap_id', $standardEncap['standard_encap_id'])->update([
                    'status' => 0,
                    'update_time' => time(),
                ]);
            }
        }
    }

    public static function getSpuAttrs()
    {
        $spuId = request()->input('spu_id');
        $mogno = DB::connection('mongodb')->table('spu_attrs2')->where('spu_id', (int)$spuId)->first();

        return $mogno['attrs_format'];
    }

    //导入新的映射
    public static function importStandardBrandMapping()
    {
        // $path = public_path('template/data/standard_brand_mapping.xls');
        $path = public_path('template/data/待映射品牌处理 6.13.xlsx');
        $data = Excel::toArray(new IEdgeClassImport(), $path)[0];
        foreach ($data as $index => $row) {
            if ($index == 0) {
                continue;
            }
            $brandId = trim($row[0]);
            $standardBrandId = trim($row[2]);
            if (empty($standardBrandId)) {
                continue;
            }
            //先去判断品牌是否存在,存在则跳过
            if ($brandId = BrandModel::where('brand_id', $brandId)->value('brand_id')) {
                if (empty($brandId)) {
                    dump('普通品牌 : ' . $brandId . '不存在');
                    continue;
                }
                if (StandardBrandMappingModel::where('brand_id', $brandId)->exists()) {
                    dump('普通品牌 : ' . $brandId . '已经存在,映射也存在');
                    continue;
                }
            }

            //判断标准品牌是否存在
            $standardBrandId = StandardBrandModel::where('standard_brand_id', $standardBrandId)->value('standard_brand_id');
            if (!$standardBrandId) {
                dump('标准品牌 : ' . $standardBrandId . '不存在');
                continue;
            }

            if (empty($brandId)) {
                dump('普通品牌 : ' . $brandId . '不存在');
                continue;
            }

            DB::connection('spu')->transaction(function () use ($brandId, $standardBrandId) {
                //添加映射
                $mapping = [
                    'brand_id' => $brandId,
                    'standard_brand_id' => $standardBrandId,
                    'add_time' => time(),
                    'status' => 1,
                    'admin_id' => 1000,
                ];
                StandardBrandMappingModel::insert($mapping);
                (new StandardBrandMappingService())->saveStandardBrandMappingToRedis($mapping);
            });
        }
    }

    //导入新的映射
    public static function updateIedgeGoodsName()
    {
        ini_set('memory_limit', -1);
        $path = public_path('template/data/爱智平台SKU汇总表-2024.09.19.xlsx');
        $data = Excel::toArray(new IEdgeClassImport(), $path)[0];
        foreach ($data as $index => $item) {
            if ($index == 0) {
                continue;
            }

            //if ($index > 10) {
            //    continue;
            //}

            $skuId = trim(trim($item[1]), "\t");
            $oldSkuName = trim(trim($item[2], "\t"));
            $newSkuName = trim(trim($item[3], "\t"));
            dump($skuId, '旧名称 : ' . $oldSkuName, '新名称 : ' . $newSkuName);

            //直接去修改sku的sku_name
            $dbInfo = getSpuSkuDb($skuId);
            $connection = DB::connection($dbInfo["db"]);
            $table = $dbInfo['table'];

            $skuName = $connection->table($table)->where('goods_id', $skuId)->value('goods_name');
            if (empty($skuName)) {
                dump('sku_id找不到');
                continue;
            }

            $connection->table($table)->where('goods_id', $skuId)->update([
                'goods_name' => $newSkuName,
            ]);

            $select = config('field.SkuField'); //生成查询字段，避免速度过慢
            foreach ($select as $k => $v) {
                $selectField[] = $k;
            }
            $selectField[] = 'goods_id';
            $skuDb = $connection->table($dbInfo['table'])->where('goods_id', $skuId)
                ->select($selectField)->first();
            $skuDb = (array)$skuDb;
            (new SkuService())->saveSkuToRedis($skuDb);


            dd($skuName);
        }
    }

    //提取分类并且给他们完善
    public static function exportAttrUnitConvert()
    {
        $data = [['标准单位id', '标准单位名称', '单位id', '单位名称', '换算关系']];
        $standardUnitList = ClassAttrUnitConvertModel::get()->toArray();

        foreach ($standardUnitList as $standardUnit) {
            $items = ClassAttrUnitConvertItemModel::where('convert_id', $standardUnit['id'])->get()->toArray();
            foreach ($items as $item) {
                $data[] = [
                    $standardUnit['id'],
                    $standardUnit['standard_unit_name'],
                    $item['id'],
                    $item['unit_name'],
                    $item['ratio'] . "\t",
                ];
            }
        }
        return Excel::download(new AlikeSpuUploadResultExport($data), '单位.xlsx');
    }


    public static function importMKTClassOld($platform)
    {
        $path = public_path('template/data/双侧品牌映射 1012.xls');
        $data = Excel::toArray(new IEdgeClassImport(), $path)[0];
        foreach ($data as $index => $item) {
            if ($index == 0) {
                continue;
            }
            $MKTClass = trim($item[1]);
            // 查找左圆括号的位置
            $openPos = strpos($MKTClass, '(');
            // 查找右圆括号的位置
            $closePos = strpos($MKTClass, ')');
            // 分离出字符串部分
            $name = trim(substr($MKTClass, 0, $openPos));
            // 分离出数字部分，使用加 1 和减去 1 是为了去除括号
            $number = substr($MKTClass, $openPos + 1, $closePos - $openPos - 1);

            $name = trim($name);
            $number = trim($number);
            if (empty($name)) {
                continue;
            }

            if (ShopBrandModel::where('brand_name', $name)->where('platform', $platform)->exists()) {
                dump('品牌名称已经存在');
                continue;
            }

            if (ShopBrandModel::where('brand_id', $number)->where('platform', $platform)->exists()) {
                dump('品牌ID已经存在');
                continue;
            }

            //插入到shop_brand库
            ShopBrandModel::insert([
                'brand_name' => $name,
                'brand_id' => $number,
                'status' => 1,
                'platform' => $platform,
                'create_time' => time(),
            ]);
        }
    }

    //映射关系处理
    public static function mappingMKTClassOld($platform)
    {
        $path = public_path('template/data/双侧品牌映射 1012.xls');
        $data = Excel::toArray(new IEdgeClassImport(), $path)[0];
        foreach ($data as $index => $item) {
            if ($index == 0) {
                continue;
            }
            $MKTClass = trim($item[1]);
            // 查找左圆括号的位置
            $openPos = strpos($MKTClass, '(');
            // 查找右圆括号的位置
            $closePos = strpos($MKTClass, ')');
            // 分离出字符串部分
            $name = trim(substr($MKTClass, 0, $openPos));
            // 分离出数字部分，使用加 1 和减去 1 是为了去除括号
            $number = substr($MKTClass, $openPos + 1, $closePos - $openPos - 1);

            $name = trim($name);
            $number = trim($number);
            if (empty($name)) {
                continue;
            }

            if (!ShopBrandModel::where('brand_name', $name)->where('platform', $platform)->exists()) {
                dump('品牌名称' . $name . '不存在');
                continue;
            }

            if (!ShopBrandModel::where('brand_id', $number)->where('platform', $platform)->exists()) {
                dump('品牌ID' . $number . '不存在');
                continue;
            }

            //左边是猎芯标准品牌名称
            $lieBrandName = trim($item[0]);
            $lieBrandId = StandardBrandModel::where('brand_name', $lieBrandName)->value('standard_brand_id');
            if (empty($lieBrandId)) {
                dump('标准品牌不存在 : ' . $lieBrandName);
                continue;
            }

            if (ShopBrandMappingModel::where('platform', $platform)->where('lie_brand_id', $lieBrandId)->exists()) {
                continue;
            }

            $shopBrandId = SHopBrandModel::where('brand_id', $number)->where('platform', $platform)->value('id');

            if (ShopBrandMappingModel::where('platform', $platform)->where('shop_brand_id', $shopBrandId)->exists()) {
                continue;
            }
            //插入到映射
            ShopBrandMappingModel::insert([
                'shop_brand_id' => $shopBrandId,
                'lie_brand_id' => $lieBrandId,
                'platform' => $platform,
                'create_time' => time(),
                'create_uid' => 1000,
                'create_name' => 'admin',
            ]);
        }
    }

    public static function importMKTClass($platform)
    {
        $path = public_path('template/data/双侧品牌映射 1016.xls');
        $data = Excel::toArray(new IEdgeClassImport(), $path)[0];
        foreach ($data as $index => $item) {
            if ($index == 0) {
                continue;
            }
            $name = trim($item[1]);
            $number = trim($item[2]);
            if (empty($name)) {
                continue;
            }

            if (ShopBrandModel::where('brand_name', $name)->where('platform', $platform)->exists()) {
                dump('品牌名称已经存在');
                continue;
            }

            if (ShopBrandModel::where('brand_id', $number)->where('platform', $platform)->exists()) {
                dump('品牌ID已经存在');
                continue;
            }

            //插入到shop_brand库
            ShopBrandModel::insert([
                'brand_name' => $name,
                'brand_id' => $number,
                'status' => 1,
                'platform' => $platform,
                'create_time' => time(),
            ]);
        }
    }

    //映射关系处理
    public static function mappingMKTClass($platform)
    {
        $path = public_path('template/data/双侧品牌映射 1016.xls');
        $data = Excel::toArray(new IEdgeClassImport(), $path)[0];
        foreach ($data as $index => $item) {
            if ($index == 0) {
                continue;
            }
            $name = trim($item[1]);
            // 分离出数字部分，使用加 1 和减去 1 是为了去除括号
            $number = trim($item[2]);

            $name = trim($name);
            $number = trim($number);
            if (empty($name)) {
                continue;
            }

            if (!ShopBrandModel::where('brand_name', $name)->where('platform', $platform)->exists()) {
                dump('品牌名称' . $name . '不存在');
                continue;
            }

            if (!ShopBrandModel::where('brand_id', $number)->where('platform', $platform)->exists()) {
                dump('品牌ID' . $number . '不存在');
                continue;
            }

            //左边是猎芯标准品牌名称
            $lieBrandName = trim($item[0]);
            $lieBrandId = StandardBrandModel::where('brand_name', $lieBrandName)->value('standard_brand_id');
            if (empty($lieBrandId)) {
                dump('标准品牌不存在 : ' . $lieBrandName);
                continue;
            }

            if (ShopBrandMappingModel::where('platform', $platform)->where('lie_brand_id', $lieBrandId)->exists()) {
                continue;
            }

            $shopBrandId = SHopBrandModel::where('brand_id', $number)->where('platform', $platform)->value('id');

            if (ShopBrandMappingModel::where('platform', $platform)->where('shop_brand_id', $shopBrandId)->exists()) {
                continue;
            }
            //插入到映射
            ShopBrandMappingModel::insert([
                'shop_brand_id' => $shopBrandId,
                'lie_brand_id' => $lieBrandId,
                'platform' => $platform,
                'create_time' => time(),
                'create_uid' => 1000,
                'create_name' => 'admin',
            ]);
        }
    }

    //批量修改供应商
    public static function batchUpdateSupplierCode()
    {
        $queue = new RabbitQueueModel('trading');
        $path = public_path('template/data/供应商编码修改-爱智.csv');
        $data = Excel::toArray(new IEdgeClassImport(), $path)[0];
        foreach ($data as $index => $item) {
            if ($index == 0) {
                continue;
            }
            $skuId = trim($item[0]);
            $supplierCode = trim($item[1]);
            \dump([[
                'goods_id' => (string)$skuId,
                'canal' => $supplierCode
            ]]);
            //还要去分发到spu的辐射服务
            $queue->insertRawQueue('', [[
                'goods_id' => (string)$skuId,
                'canal' => $supplierCode
            ]], 'sku_update');
        }
    }

    //到处品牌数据
    public static function exportBrandMappingOfVc()
    {
        $platform = 1;

        $list = ShopBrandMappingService::getShopBrandMappingList([
            'platform' => 1,
            'limit' => 1000
        ]);

        foreach ($list['data'] as $item) {
            $data[] = [
                $item['lie_brand_id'],
                $item['lie_brand']['brand_name'] ?? '',
                $item['shop_brand']['brand_id'] ?? '',
                $item['shop_brand']['brand_name'] ?? '',
            ];
        }
        array_unshift($data, [
            '猎芯标品id',
            '猎芯标品名称',
            '第三方品牌id',
            '第三方品牌名称'
        ]);

        return Excel::download(new AlikeSpuUploadResultExport($data), '京东VC品牌映射导出.xlsx');
    }

    //导出shop_error
    public static function exportShopError()
    {
        ini_set('memory_limit', -1);
        //获取shop_error表中的数据
        $data = DB::connection('distribution')->table('lie_shop_error')->offset(15000)->limit(15000)->get()->toArray();
        $redis = Redis::connection('spu');
        foreach ($data as $item) {
            $item = (array)$item;
            $skuInfo = [];
            if (!empty($item['sku_id'])) {
                //redis获取
                $skuInfo = (new SkuService())->getSkuCacheInfo($item['sku_id']);
            }
            $spuInfo = [];
            if (!empty($item['spu_id'])) {
                $spuInfo = $redis->hget('spu', $item['spu_id']);
                $spuInfo = json_decode($spuInfo, true);
            }
            $parentClassName = (new ClassService())->getParentClassNameByClassId($spuInfo['class_id1']);
            $className = (new ClassService())->getClassNameFromCache($spuInfo['class_id2']);

            $brandName = StandardBrandModel::where('standard_brand_id', $spuInfo['s_brand_id'])->value('brand_name');
            //匹配msg中的分类id和品牌id
            preg_match('/\[(\d+)\].*?\[(\d+)\]/', $item['msg'], $matches);
            if (!empty($matches)) {
                $jdCategoryId = $matches[1];
                $jdBrandId = $matches[2];
                //获取京东分类名称
                $jdCategoryName = DB::connection('distribution')
                    ->table('lie_shop_class')
                    ->where('class_id', $jdCategoryId)
                    ->where('platform', 1) //京东平台
                    ->value('class_name');

                //获取京东品牌名称
                $jdBrandName = DB::connection('distribution')
                    ->table('lie_shop_brand')
                    ->where('brand_id', $jdBrandId)
                    ->where('platform', 1) //京东平台
                    ->value('brand_name');

                $item['jd_category_id'] = $jdCategoryId;
                $item['jd_category_name'] = $jdCategoryName;
                $item['jd_brand_id'] = $jdBrandId;
                $item['jd_brand_name'] = $jdBrandName;
            }


            $exportData[] = [
                'id' => $item['id'] . "\t",
                'msg' => $item['msg'] . "\t",
                'spu_id' => $item['spu_id'] . "\t",
                'sku_id' => $item['sku_id'] . "\t",
                'sku_sn' => $skuInfo['sku_sn'] ?? '',
                'ware_id' => $skuInfo['ware_id'] ?? '',
                'category_name' => $parentClassName . ' / ' . $className,
                'brand_name' => $brandName ?? '',
                'jd_category_id' => $item['jd_category_id'] ?? '',
                'jd_category_name' => $item['jd_category_name'] ?? '',
                'jd_brand_id' => $item['jd_brand_id'] ?? '',
                'jd_brand_name' => $item['jd_brand_name'] ?? ''
            ];
        }
        $header = [
            '编号',
            '错误信息',
            'SPU ID',
            'SKU ID',
            'SKU编码',
            'WARE ID',
            '分类名称',
            '品牌名称',
            '京东分类ID',
            '京东分类名称',
            '京东品牌ID',
            '京东品牌名称'
        ];

        array_unshift($exportData, $header);
        return Excel::download(new AlikeSpuUploadResultExport($exportData), 'shop_error.xlsx');
    }

    //导出shop_error
    public static function exportShopSpuError()
    {
        ini_set('memory_limit', -1);
        //获取shop_error表中的数据
        $data = DB::connection('distribution')->table('lie_shop_push_spu_log3')->get()->toArray();
        $redis = Redis::connection('spu');
        foreach ($data as $item) {
            $item = (array)$item;
            $spuInfo = [];
            if (!empty($item['spu_id'])) {
                $spuInfo = $redis->hget('spu', $item['spu_id']);
                $spuInfo = json_decode($spuInfo, true);
            }
            $brandName = StandardBrandModel::where('standard_brand_id', $spuInfo['s_brand_id'])->value('brand_name');
            $exportData[] = [
                'id' => $item['id'] . "\t",
                'platform' => config('field.ShopPlatform')[$item['platform']] ?? '' . "\t",
                'task_code' => $item['task_code'] . "\t",
                'batch_sn' => '',
                'push_status_name' => config('field.ShopSpuPushStatus')[0] ?? '无',
                'verify_status_name' => config('field.ShopSpuVerifyStatus')[$item['verify_status']] ?? '无',
                'spu_id' => $item['spu_id'] . "\t",
                'goods_name' => $spuInfo['spu_name'] . "\t",
                'brand_name' => $brandName ?? '',
                'push_time' => !empty($item['push_time']) ? date('Y-m-d H:i:s', $item['push_time']) : '',
                'error_msg' => $item['error_msg'],
            ];
        }
        $header = [
            '序号',
            '外部平台',
            '推送任务号',
            '批次号',
            '推送状态',
            '第三方校验状态',
            'SPU ID',
            '商品型号',
            '标准品牌',
            '推送时间',
            '异常原因',
        ];

        array_unshift($exportData, $header);
        return Excel::download(new AlikeSpuUploadResultExport($exportData), 'shop_error.xlsx');
    }

    public static function batchUpdateSkuMoq()
    {

        $totalData = [];
        //先读取excel
        $path = public_path('template/data/爱智神武-最低起订量.xlsx');
        $data = Excel::toArray(new IEdgeClassImport(), $path)[0];
        foreach ($data as $index => $item) {
            if ($index == 0) {
                continue;
            }

            $skuId = (int)trim($item[1]);
            $moq = (int)trim($item[3]);
            if (!$skuId) {
                continue;
            }
            $totalData[$skuId] = $moq;
        }


        $path = public_path('template/data/爱智亚德客-最低起订量.xlsx');
        $data = Excel::toArray(new IEdgeClassImport(), $path)[0];
        foreach ($data as $index => $item) {
            if ($index == 0) {
                continue;
            }

            $skuId = (int)trim($item[1]);
            $moq = (int)trim($item[3]);
            if (!$skuId) {
                continue;
            }
            $totalData[$skuId] = $moq;
        }
        dump($totalData);
        $queue = new RabbitQueueModel('sku');
        foreach ($totalData as $skuId => $moq) {
            $postData[] = [
                "goods_id" => $skuId,
                "moq" => $moq,
            ];

            $queue->insertRawQueue('', $postData, 'sku_update');
            if (request()->get('break')) {
                break;
            }
        }
    }

    public static function rohmDataAnalysis()
    {
        $isTest = request()->get('is_test', 1);
        ini_set('memory_limit', -1);
        // $path = public_path('template/data/Ameya（罗姆）比价表-250310 (1) - 副本.xlsx');
        $path = public_path('template/data/Ameya（罗姆）比价表-250310 (1).xlsx');
        $data = Excel::toArray(new IEdgeClassImport(), $path)[0];
        if ($isTest) {
            $data = array_slice($data, 0, 50);
        }

        $outputData = [];
        foreach ($data as $index => $item) {

            if ($index == 0) {
                continue;
            }
            $outputItem = [
                'sku_name' => trim($item[0]),
            ];
            $skuName = trim($item[0]);
            $moq = (int)trim($item[1]);

            //唯样获取
            $wySkuInfo = self::getSkuInfo('L0005966', $moq, $skuName);
            $outputItem['wySkuId'] = (string)$wySkuInfo['skuId'];
            $outputItem['wyPrice'] = (string)$wySkuInfo['price'];
            //立创获取
            $lcSkuInfo = self::getSkuInfo('L0001175', $moq, $skuName);
            $outputItem['lcSkuId'] = (string)$lcSkuInfo['skuId'];
            $outputItem['lcPrice'] = (string)$lcSkuInfo['price'];
            $outputData[] = $outputItem;
        }
        //写回到原始excel的最后一列
        $path = public_path('template/data/Ameya（罗姆）比价表-250310 (1).xlsx');
        $data = Excel::toArray(new IEdgeClassImport(), $path)[0];
        $data[0][] = '唯样sku';
        $data[0][] = '唯样价格';
        $data[0][] = '立创sku';
        $data[0][] = '立创价格';
        foreach ($outputData as $index => $item) {
            $data[$index + 1][9] = (string)$item['wySkuId'];
            $data[$index + 1][10] = (string)$item['wyPrice'];
            $data[$index + 1][11] = (string)$item['lcSkuId'];
            $data[$index + 1][12] = (string)$item['lcPrice'];
        }
        return Excel::download(new AlikeSpuUploadResultExport($data), 'Ameya（罗姆）比价表-250310.xlsx');
    }

    public static function getSkuInfo($canal, $moq, $skuName)
    {
        $baseUrl = config('website.search_domain_new') . '/search/es/searchSku';
        $url = $baseUrl . '?canal/condition=' . $canal . '&goods_name/eq=' . $skuName;
        // $url = "http://search.liexindev.net/search/es/searchSku?canal/condition=L0007829&goods_name/eq=RR0603(0201)L20R0FT";
        $response = Http::get($url);
        $response = json_decode($response->body(), true);
        if ($response['error_code'] != 0) {
            $outputItem['skuId'] = 'sku_id找不到';
            $outputItem['price'] = '';
        } else {
            if (empty($response['data']['goods_id'])) {
                $outputItem['skuId'] = 'sku_id找不到';
                $outputItem['price'] = '';
            } else {
                $goodsIds = $response['data']['goods_id'];
                $hasPrice = false;
                $skuId = '';
                foreach ($goodsIds as $goodsId) {
                    $sku = (new SkuService())->getSkuCacheInfo($goodsId);
                    $ladderPrice = $sku['ladder_price'];
                    if (empty($ladderPrice)) {
                        continue;
                    }
                    $priceToUse = null;
                    foreach ($ladderPrice as $price) {
                        if ($moq <= $price['purchases']) {
                            $priceToUse = $price['price_cn'];
                            break;
                        }
                    }
                    if (is_null($priceToUse)) {
                        $priceToUse = end($ladderPrice)['price_cn'];
                    }
                    $outputItem['skuId'] = (string)$goodsId . "\t";
                    $outputItem['price'] = (string)$priceToUse . "\t";
                    $hasPrice = true;
                    $skuId = $goodsId;
                }
                if (!$hasPrice) {
                    $outputItem['skuId'] = !empty($goodsIds[0]) ? (string)$goodsIds[0] . "\t" : 'sku_id找不到';
                    $outputItem['price'] = '价格为空';
                }
            }
        }
        return $outputItem;
    }

    /**
     * 更新标准品牌的英文简称
     * 通过Excel导入，根据ID更新标准品牌的英文简称
     *
     * @return array 更新结果统计
     */
    public static function updateStandardBrandEnShortName()
    {
        // 初始化结果统计
        $result = [
            'total' => 0,      // 总记录数
            'success' => 0,    // 成功更新数
            'failed' => 0,     // 失败数
            'not_found' => 0,  // 未找到的品牌ID数
            'errors' => []     // 错误信息
        ];

        // 读取Excel文件
        $path = public_path('template/data/标准品牌导出 (1).xlsx');
        try {
            $data = Excel::toArray(new Collection(), $path)[0];
            $result['total'] = count($data) - 1; // 减去表头

            // 获取Redis连接
            $redis = Redis::connection('sku');

            // 遍历Excel数据
            foreach ($data as $index => $row) {
                // 跳过表头
                if ($index === 0) {
                    continue;
                }

                // 获取ID和英文简称
                $standardBrandId = trim($row[0]);
                $enShortName = trim($row[2]);

                // 检查ID是否存在
                $standardBrand = StandardBrandModel::where('standard_brand_id', $standardBrandId)->first();
                if (!$standardBrand) {
                    $result['not_found']++;
                    $result['errors'][] = "标准品牌ID {$standardBrandId} 不存在";
                    continue;
                }

                try {
                    // 更新英文简称
                    $standardBrand->brand_short_name_en = $enShortName;
                    $standardBrand->update_time = time();
                    $updated = $standardBrand->save();

                    if ($updated) {
                        // 更新Redis缓存
                        $brand = $standardBrand->toArray();
                        $redis->hset('standard_brand', $standardBrandId, json_encode($brand));
                        $result['success']++;
                    } else {
                        $result['failed']++;
                        $result['errors'][] = "标准品牌ID {$standardBrandId} 更新失败";
                    }
                } catch (\Exception $e) {
                    $result['failed']++;
                    $result['errors'][] = "标准品牌ID {$standardBrandId} 更新异常: " . $e->getMessage();
                }
            }
        } catch (\Exception $e) {
            $result['errors'][] = "Excel处理异常: " . $e->getMessage();
        }

        return $result;
    }

    public static function markUnusualBrand()
    {
        ini_set('memory_limit', -1);
        $path = public_path('template/data/异常品牌.xlsx');
        $data = Excel::toArray(new Collection(), $path)[0];
        foreach ($data as $index => $item) {
            if ($index == 0) {
                continue;
            }
            // if ($index > 10) {
            //     break;
            // }
            $brandId = trim($item[0]);
            $isUnusual = trim($item[6]);
            if ($isUnusual == 1) {
                BrandModel::where('brand_id', $brandId)->update(['is_unusual' => 1, 'update_time' => time()]);
            }
        }
    }

    //修复distribute_sku表的数据
    public static function repairDistributeSku()
    {
        //先找出所有supplier_id为空的数据
        $data = ShopDistributeSkuModel::where('supplier_id', 0)->get()->toArray();
        \dump(count($data));
        foreach ($data as $item) {
            $sku = (new SkuService())->getSkuCacheInfo($item['sku_id']);
            if (empty($sku)) {
                continue;
            }
            $supplierId  = $sku['supplier_id'];
            $supplierCode = $sku['canal'];
            $classId2 = $sku['class_id2'];
            $ladderPrice = $sku['ladder_price'];
            $mpq = $sku['mpq'];
            $moq = $sku['moq'];
            $multiple = $sku['multiple'];
            $standardBrandId = $sku['s_brand_id'];
            $spuName = $sku['spu_name'];
            $stock = $sku['stock'];
            //获取京东标题和sn
            $shopSku = ShopSkuModel::where('lx_sku_id', $item['sku_id'])->where('shop_id', $item['shop_id'])->first();
            if (empty($shopSku)) {
                // continue;
            }
            $goodsTitle = $shopSku['goods_title'] ?? '';
            $goodsSn = $shopSku['sku_sn'] ?? '';
            ShopDistributeSkuModel::where('id', $item['id'])->update([
                'supplier_id' => $supplierId,
                'supplier_code' => $supplierCode,
                'standard_brand_id' => $standardBrandId,
                'class_id2' => $classId2,
                'ladder_price' => json_encode($ladderPrice),
                'mpq' => $mpq,
                'moq' => $moq,
                'multiple' => $multiple,
                'goods_title' => $goodsTitle,
                'sku_sn' => $goodsSn,
                'spu_name' => $spuName,
                'stock' => $stock,
            ]);
        }
    }

    public static function exportStandardBrand()
    {
        $standardBrandList = StandardBrandModel::select([
            'brand_name',
            'brand_area'
        ])->get();
        $data = [];
        foreach ($standardBrandList as $brand) {
            $data[] = [
                'brand_name' => $brand->brand_name,
                'brand_area' => $brand->brand_area
            ];
        }

        $fileName = 'standard_brand_' . date('YmdHis') . '.xlsx';

        // 准备导出数据
        $exportData = [
            ['品牌名称', '地区id']
        ];

        foreach ($data as $item) {
            $exportData[] = [
                $item['brand_name'],
                $item['brand_area']
            ];
        }

        return Excel::download(new AlikeSpuUploadResultExport($exportData), $fileName);
    }


    public static function repairShopDistributeSku()
    {
        //找出sku_id不为空但是supplier_id为空的
        $list = ShopDistributeSkuModel::where('sku_id', '!=', '')
            ->whereRaw('length(supplier_code) = 5')->get()->toArray();
        foreach ($list as $item) {
            $sku = (new SkuService())->getSkuCacheInfo($item['sku_id']);
            if (empty($sku)) {
                continue;
            }
            // $item['spu_name'] = $sku['spu_name'];
            // $item['supplier_id'] = $sku['supplier_id'];
            // $item['standard_brand_id'] = $sku['standard_brand_id'];
            // $item['class_id2'] = $sku['class_id2'];
            // $item['ladder_price'] = $sku['ladder_price'];
            // $item['mpq'] = $sku['mpq'];
            // $item['moq'] = $sku['moq'];
            // $item['stock'] = $sku['stock'];
            // $item['multiple'] = $sku['multiple'];
            $item['supplier_code'] = $sku['canal'];
            ShopDistributeSkuModel::where('id', $item['id'])->update($item);
        }
    }

    public static function repairShopDistributeSkuByStandardBrandName()
    {
        $list = ShopDistributeSkuModel::where('standard_brand_id', '')->where('sku_id', '!=', '')
            ->get()
            ->toArray();
        foreach ($list as $item) {
            $sku = (new SkuService())->getSkuCacheInfo($item['sku_id']);
            if (empty($sku)) {
                continue;
            }
            if (empty($sku['standard_brand_id'])) {
                dd($sku);
                \dump('该sku不存在standard_brand_id : ' . $sku['goods_id']);
            } else {
                ShopDistributeSkuModel::where('id', $item['id'])->update([
                    'standard_brand_id' => $sku['standard_brand_id']
                ]);
            }
        }
    }

    public static function initBrandCache()
    {
        $brandList = BrandModel::all();
        $redis = Redis::connection('sku');
        foreach ($brandList as $brand) {
            $redis->hset('brand', $brand->brand_id, $brand->brand_name);
        }
    }

    public static function repairPacking()
    {
        //请求搜索接口
        $baseUrl = config('website.search_domain_new') . '/search/es/searchSku';
        $url = $baseUrl;
        $map['admin'] = 1; // 后台搜索
        $map['no_rule'] = "1122"; // 后台搜索
        $map['p'] = 1;
        $map['offset'] = 10;
        $map['source/eq'] = 12;
        $map['show_status'] = 1;
        $map['supplier_id/sr'] = "gt,0";
        $map['admin'] = 1;
        $map['no_rule'] = "1122";
        $map['offset'] = 300;

        $response = Http::get($url, $map);
        $response = json_decode($response->body(), true);
        if ($response['error_code'] != 0) {
            dd($response);
        }
        $goodsIdList = $response['data']['goods_id'];
        $spuIds = [];
        $redis = Redis::connection('spu');
        foreach ($goodsIdList as $goodsId) {
            $sku = (new SkuService())->getSkuCacheInfo($goodsId);
            if (empty($sku)) {
                continue;
            }

            //先判断是否有sku_raw_map
            $skuRawMap = $redis->hget('sku_raw_map', $goodsId);
            if (!empty($skuRawMap)) {
                $skuRawMap = json_decode($skuRawMap, true);
                if (!empty($skuRawMap['pack']) || !empty($skuRawMap['raw_goods_id']) || !empty($skuRawMap['raw_brand_name'])) {
                    continue;
                }
            }

            $spu = $redis->hget('spu', $sku['spu_id']);
            $spu = json_decode($spu, true);
            $brandPack = $spu['brand_pack'] ?? '';
            if (empty($brandPack)) {
                continue;
            }
            //写入一个skuRawMap
            $redis->hset('sku_raw_map', $goodsId, json_encode([
                "pack" => $brandPack,
            ]));
        }
    }

    public static function deletePushData()
    {
        $count = ShopDistributeSkuModel::where('shop_id', 5)->where('status', 0)->count();
        if ($count == 5120) {
            \dump('删除了5120条数据');
            ShopDistributeSkuModel::where('shop_id', 5)->where('status', 0)->delete();
        }
    }

    //修复异常品牌
    public static function repairExceptionBrand()
    {
        $limit = request()->input('limit', 1);
        $update = request()->input('update', 0);
        //先找出所有异常品牌
        $list = BrandModel::where('is_unusual', 1)->limit($limit)->get()->toArray();
        //然后判断是否有映射
        foreach ($list as $item) {
            $brandId = $item['brand_id'];
            $brandName = $item['brand_name'];
            $brand = BrandModel::where('brand_id', $brandId)->first();
            if (empty($brand)) {
                continue;
            }
            $brandMapping = StandardBrandMappingModel::where('brand_id', $brandId)->first();
            if (empty($brandMapping)) {
                continue;
            }
            \dump('处理异常品牌: ' . $brandName . ' 品牌ID: ' . $brandId);
            if ($update == 1) {
                BrandModel::where('brand_id', $brandId)->update(['is_unusual' => 0, 'update_time' => time()]);
            }
        }
    }

    public static function deleteInvalidClassManagement()
    {
        ClassManagementModel::where('order_sn', '')->delete();
    }

    public static function repairDeliveryTime()
    {
        //请求搜索接口
        $baseUrl = config('website.search_domain_new') . '/search/es/searchSku';
        $url = $baseUrl;
        $map['admin'] = 1; // 后台搜索
        $map['no_rule'] = "1122"; // 后台搜索
        $map['p'] = 1;
        $map['offset'] = 10;
        $map['source/eq'] = 12;
        $map['show_status'] = 1;
        $map['supplier_id/sr'] = "gt,0";
        $map['admin'] = 1;
        $map['no_rule'] = "1122";
        $map['offset'] = 300;

        $response = Http::get($url, $map);
        $response = json_decode($response->body(), true);
        if ($response['error_code'] != 0) {
            dd($response);
        }
        $goodsIdList = $response['data']['goods_id'];
        $redis = Redis::connection('sku');
        // dd($goodsIdList);
        foreach ($goodsIdList as $goodsId) {
            $dbInfo = getSpuSkuDb($goodsId);
            $connection = DB::connection($dbInfo['db']);
            //更新sku
            $result = $connection->table($dbInfo['table'])->where('goods_id', $goodsId)
                ->update(['cn_delivery_time' => '0.5工作日']);
            if (!$result) {
                \dump('更新sku失败: ' . $goodsId);
            } else {
                #更新对应的redis
                $redisInfo = $redis->hget('sku', $goodsId);
                $redisInfo = json_decode($redisInfo, true);
                $redisInfo['cn_delivery_time'] = '0.5工作日';
                $redis->hset('sku', $goodsId, json_encode($redisInfo));
            }
        }
    }

    public static function emptyEncap()
    {
        ini_set('memory_limit', '4024M');
        DB::connection('spu')->table('lie_encap')->truncate();
        DB::connection('spu')->table('lie_encap_standard')->truncate();
        DB::connection('spu')->table('lie_encap_standard_mapping')->truncate();

        // 导入excel
        $path = public_path('template/data/封装.xlsx');
        $data = Excel::toArray(new Collection(), $path)[0];

        // 收集所有有效数据
        $validData = [];
        $encapNames = [];
        $standardEncapNames = [];

        foreach ($data as $key => $value) {
            if ($key == 0) continue;

            $encapName = trim($value[3]);
            $standardEncapName = trim($value[6]);

            if (empty($encapName)) {
                \dump('封装名称不能为空: ' . $key);
                continue;
            }
            if (empty($standardEncapName)) {
                \dump('标准封装名称不能为空: ' . $key);
                continue;
            }

            $validData[] = ['encap' => $encapName, 'standard' => $standardEncapName];
            $encapNames[] = $encapName;
            $standardEncapNames[] = $standardEncapName;
        }

        // 去重
        $encapNames = array_unique($encapNames);
        $standardEncapNames = array_unique($standardEncapNames);

        // 批量查询现有封装
        $existingEncaps = EncapModel::whereIn('encap_name', $encapNames)
            ->pluck('encap_id', 'encap_name')
            ->toArray();

        // 批量查询现有标准封装
        $existingStandardEncaps = StandardEncapModel::whereIn('encap_name', $standardEncapNames)
            ->pluck('standard_encap_id', 'encap_name')
            ->toArray();

        // 准备批量插入数据
        $encapsToInsert = [];
        $standardEncapsToInsert = [];
        $now = time();

        foreach ($encapNames as $name) {
            if (!isset($existingEncaps[$name])) {
                $encapsToInsert[] = [
                    'encap_name' => $name,
                    'status' => 1,
                    'create_time' => $now,
                    'create_uid' => 1000,
                    'create_name' => 'admin',
                ];
            }
        }

        foreach ($standardEncapNames as $name) {
            if (!isset($existingStandardEncaps[$name])) {
                $standardEncapsToInsert[] = [
                    'encap_name' => $name,
                    'status' => 1,
                    'create_time' => $now,
                    'create_uid' => 1000,
                    'create_name' => 'admin',
                ];
            }
        }

        // 批量插入
        if (!empty($encapsToInsert)) {
            EncapModel::insert($encapsToInsert);
        }

        if (!empty($standardEncapsToInsert)) {
            StandardEncapModel::insert($standardEncapsToInsert);
        }

        // 重新查询所有ID
        $allEncaps = EncapModel::whereIn('encap_name', $encapNames)
            ->pluck('encap_id', 'encap_name')
            ->toArray();

        $allStandardEncaps = StandardEncapModel::whereIn('encap_name', $standardEncapNames)
            ->pluck('standard_encap_id', 'encap_name')
            ->toArray();

        // 准备映射关系
        $mappingsToInsert = [];
        foreach ($validData as $item) {
            $encapId = $allEncaps[$item['encap']];
            $standardEncapId = $allStandardEncaps[$item['standard']];

            $mappingsToInsert[] = [
                'encap_id' => $encapId,
                'standard_encap_id' => $standardEncapId,
                'status' => 1,
                'admin_id' => 1000,
                'admin_name' => 'admin',
                'create_time' => $now,
            ];
        }

        // 批量插入映射关系
        if (!empty($mappingsToInsert)) {
            StandardEncapMappingModel::insert($mappingsToInsert);
        }

        return count($validData) . '条封装数据已成功处理';
    }

    public static function initEncapCache()
    {
        ini_set('memory_limit', '1024M');
        $redis = Redis::connection('sku');
        $redis->del('encap');
        $redis->del('encap_name_all');
        $redis->del('standard_encap');
        $redis->del('standard_encap_mapping');
        $encapIdList = EncapModel::get()->toArray();
        foreach ($encapIdList as $item) {
            $redis->hset('encap', $item['encap_id'], $item['encap_name']);

            $key = md5(strtolower($item['encap_name']));
            if (!$redis->hget('encap_name_all', $key)) {
                $redis->hset('encap_name_all', $key, $item['encap_id']);
            }
        }

        $standardEncapList = StandardEncapModel::get()->toArray();
        foreach ($standardEncapList as $item) {
            $redis->hset('standard_encap', $item['standard_encap_id'], json_encode($item));
        }

        $standardEncapMappingList = StandardEncapMappingModel::get()->toArray();
        foreach ($standardEncapMappingList as $item) {
            $encapName = EncapModel::where('encap_id', $item['encap_id'])->value('encap_name');
            $standardEncapName = StandardEncapModel::where('standard_encap_id', $item['standard_encap_id'])->value('encap_name');
            $redis->hset('standard_encap_mapping', strtoupper($encapName), $standardEncapName);
        }
    }


    public static function repairClassManagement()
    {
        $classManagementList = ClassManagementModel::where('brand_name', '')->get()->toArray();
        foreach ($classManagementList as $item) {
            //先找出orderSn
            $orderId = DB::connection("order_v2")->table('order')->where('order_sn', $item['order_sn'])->value('order_id');
            if (empty($orderId)) {
                continue;
            }
            //找出详情
            $orderItem = DB::connection("order_v2")->table('order_items')->where('order_id', $orderId)->where('goods_name', $item['goods_name'])->first();
            if (empty($orderItem)) {
                continue;
            }
            $orderItem = (array)$orderItem;
            \dump($item['id'],$orderItem['standard_brand_name'] ?: $orderItem['brand_name']);
            //获取品牌
            ClassManagementModel::where('id', $item['id'])->update([
                'update_time' => time(),
                'update_uid' => 1000,
                'update_name' => 'admin',
                'brand_name' => $orderItem['standard_brand_name'] ?: $orderItem['brand_name'],
            ]);

        }
    }

    public static function initAlikeSpu()
    {
        DB::connection('spu')->table('lie_alike_spu')->truncate();
        DB::connection('spu')->table('lie_alike_spu_center')->truncate();
    }

    public static function exportOperationLog()
    {
        $time = Carbon::now();
        $startTime = $time->startOfMonth()->timestamp;
        $endTime = Carbon::now()->timestamp;
        $logList = OperationLogModel::where('operation_name','校正物料')
        ->whereBetween('create_time',[$startTime,$endTime])
        ->get()->toArray();
        //导出成csv
        $data = [];
        foreach ($logList as $key => $log) {
            $bomItemId = $log['obj_id'];
            $bomId = DB::connection('frq')->table('lie_bom_match_item')->where('id',$bomItemId)->value('bom_id');
            $bomMatch = DB::connection('frq')->table('lie_bom_match')->where('id',$bomId)->first();
            $bomMatch = (array)$bomMatch;
            if (empty($bomMatch)) {
                continue;
            }
            $data[] = [
                $bomMatch['bom_sn'],
                $bomMatch['bom_name'],
                $bomMatch['create_name'],
                date('Y-m-d H:i:s',$bomMatch['create_time']),
                $log['create_name'],
                date('Y-m-d H:i:s',$log['create_time']),
                $log['content']
            ];
        }
        array_unshift($data, [
            'Bom单号',
            'Bom名称',
            'Bom创建人',
            'Bom创建时间',
            '操作人',
            '操作时间',
            '内容'
        ]);
        $fileName = 'operation_log_'.date('Y-m-d H:i:s',time()).'.csv';
        //导出
      return  Excel::download(new AlikeSpuUploadResultExport($data), $fileName);
    }
}
