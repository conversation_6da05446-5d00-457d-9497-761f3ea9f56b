<?php

namespace App\Http\Services;

use App\Exports\AlikeSpuUploadResultExport;
use App\Http\Models\AlikeSpuCenterModel;
use App\Http\Models\AlikeSpuLogModel;
use App\Http\Models\AlikeSpuModel;
use App\Http\Models\SampleClassModel;
use App\Http\Models\SampleModel;
use App\Http\Models\Supplier\SupplierChannelModel;
use App\Http\Models\UploadLogModel;
use App\Http\Transformers\SkuTransformer;
use App\Imports\AlikeSpuImport;
use App\Imports\AlikeSpuUploadResultImport;
use App\Jobs\UploadAlikeSpu;
use http\Exception\InvalidArgumentException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Response;
use Maatwebsite\Excel\Facades\Excel;

class SampleClassService
{

    public static function saveSampleClass($data)
    {
        if (!empty($data['id'])) {
            $id = $data['id'];
            $data['update_time'] = time();
            SampleClassModel::where('id', $data['id'])->update($data);
        } else {
            $data['add_time'] = time();
            $id = SampleClassModel::insertGetId($data);
        }
        $sampleClass = SampleClassModel::where('id', $id)->first()->toArray();
        Redis::connection('sku')->hset('lie_sample_class', $id, json_encode($sampleClass));

        return true;
    }

    public static function getSampleClassList($map)
    {
        $query = SampleClassModel::with('sample_one')->where('status', '!=', SampleClassModel::STATUS_DELETED);
        if (!empty($map['class_name'])) {
            $query->where('class_name', 'like', '%' . $map['class_name'] . '%');
        }
        if (!empty($map['id'])) {
            $query->where('id', $map['id']);
        }
        if (!empty($map['status'])) {
            $query->where('status', $map['status']);
        }
        if (!empty($map['add_time'])) {
            $startTime = strtotime(explode('~', $map['add_time'])[0]);
            $endTime = strtotime(explode('~', $map['add_time'])[1]);
            $query->whereBetween('add_time', [$startTime, $endTime]);
        }
        $limit = Arr::get($map, 'limit', 10);
        $result = $query->orderBy('add_time', 'desc')->paginate($limit);
        $result = $result ? $result->toArray() : [];
        if (!empty($result)) {
            foreach ($result['data'] as $key => &$value) {
                $value['no'] = $key + 1;
                $value['add_time'] = date('Y-m-d H:i:s', $value['add_time']);
                $value['update_time'] = $value['update_time'] ? date('Y-m-d H:i:s', $value['update_time']) : '';
                $value['status_name'] = Arr::get(config('field.SampleClassStatus'), $value['status']);
                $value['has_sample'] = $value['sample_one'] ? 1 : 0;
                $value['sample_count'] = SampleModel::where('class_id', $value['id'])->count();
            }
            unset($value);
        }

        return [
            'data' => Arr::get($result, 'data', []),
            'count' => Arr::get($result, 'total'),
            'total' => Arr::get($result, 'total'),
        ];
    }

    public static function deleteSampleClass($id)
    {
        Redis::connection('sku')->hdel('lie_sample_class', $id);
        return SampleClassModel::where('id', $id)->update([
            'status' => SampleClassModel::STATUS_DELETED,
            'update_time' => time(),
        ]);
    }


    public static function batchDeleteSampleClass($ids = [])
    {
        return SampleClassModel::whereIn('id', $ids)->update([
            'status' => SampleClassModel::STATUS_DELETED,
            'update_time' => time(),
        ]);
    }

}
