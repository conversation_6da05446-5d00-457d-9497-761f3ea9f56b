<?php

namespace App\Http\Services;

//标准封装服务

use App\Exceptions\InvalidRequestException;
use App\Exports\EncapExport;
use App\Http\Models\EncapModel;
use App\Http\Models\SeoElementModel;
use App\Http\Transformers\EncapTransformer;
use App\Http\Transformers\StandardEncapTransformer;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class EncapService
{

    public function getEncap($encapId)
    {
        $encap = EncapModel::where('encap_id', $encapId)->first()->toArray();
        $encap['seo'] = SeoElementService::getSeoElement($encap['encap_id'], SeoElementModel::TYPE_BRAND);
        return $encap;
    }

    public function getEncapList($map)
    {
        $model = new EncapModel();
        $query = $model->orderBy('encap_id', 'desc');
        $query = $this->filter($query, $map);
        $limit = \Arr::get($map, 'limit', 15);
        $result = $query->with('standard_encap_mapping')->paginate($limit);

        $result = $result ? $result->toArray() : [];
        $transformer = new EncapTransformer();
        $result['data'] = $transformer->listTransformer($result['data']);
        return [
            'data' => \Arr::get($result, 'data', []),
            'count' => \Arr::get($result, 'total'),
        ];
    }


    public function filter($query, $map)
    {

        if (!empty($map['standard_encap_name'])) {
            $query->whereHas('standard_encap', function ($q) use ($map) {
                $q->where('encap_name', 'like', "${map['encap_name']}%")
                    ->orWhere('show_name', 'like', "${map['encap_name']}%")
                    ->orWhere('encap_name_en', 'like', "${map['encap_name']}%")
                    ->orWhere('encap_short_name_en', 'like', "${map['encap_name']}%")
                    ->orWhere('encap_name_cn', 'like', "${map['encap_name']}%")
                    ->orWhere('encap_short_name_cn', 'like', "${map['encap_name']}%");
            });
        }


        if (!empty($map['encap_id'])) {
            $query->where('encap_id', $map['encap_id']);
        }

        if (!empty($map['encap_name'])) {
            $query->where('encap_name', 'like', "${map['encap_name']}%");
        }
        if (!empty($map['has_mapping']) || (isset($map['has_mapping']) && $map['has_mapping'] === '0')) {
            if ($map['has_mapping'] == -1) {
                $query->where('ignore_handle', 1);
            } else {
                if ($map['has_mapping']) {
                    $query->whereHas('standard_encap_mapping', function ($q) use ($map) {
                        $q->where('encap_id', '!=', '');
                    });
                } else {
                    $query->whereNotIn('encap_id', function ($q) {
                        $q->select('encap_id')->from('lie_encap_standard_mapping');
                    });
                }
            }

        }
        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }

        return $query;
    }

    public function changeEncapStatus($encapId, $status)
    {
        $result = EncapModel::where('encap_id', $encapId)->update([
            'encap_id' => $encapId,
            'status' => $status
        ]);
        //同时还要将修改的东西放到缓存里面去
        if ($result) {
            $redis = Redis::connection('sku');
            $encap = EncapModel::where('encap_id', $encapId)->first();
            if (!empty($encap)) {
                $encap = $encap->toArray();
                $redis->hset('encap_info', $encapId, $encap);
                return true;
            }
        }
        return false;
    }

    public function searchEncap($encapName)
    {
        $query = EncapModel::where('encap_name', 'like', '%' . $encapName . '%');
        $count = $query->count();
        $encapList = $query->select('encap_name', 'encap_id')->paginate(17);
        $encapList = $encapList ? $encapList->toArray() : [];
        $data = Arr::get($encapList, 'data');
        $lastPage = Arr::get($encapList, 'last_page');
        return [
            'errcode' => 0,
            'errmsg' => 'ok',
            'total' => $count,
            'count' => $count,
            'data' => $data,
            'last_page' => $lastPage
        ];
    }

    public function saveEncap($data)
    {
        $seoData = \Arr::only($data, ['title', 'keywords', 'description']);
        unset($data['title'], $data['keywords'], $data['description']);
        $data['encap_name'] = trim($data['encap_name']);
        if (!empty($data['encap_id'])) {
            $existEncapName = EncapModel::where('encap_id', '!=', $data['encap_id'])
//            $existEncapName = EncapModel::where('encap_id', '>', 0)
                ->whereRaw('BINARY encap_name = ?', $data['encap_name'])->count();
        } else {
            $existEncapName = EncapModel::where('encap_name', $data['encap_name'])->count();
        }
        if ($existEncapName) {
            throw new InvalidRequestException('添加的封装已经存在,请确认封装名称');
        }
        if (!empty($data['encap_id'])) {
            $encapId = $data['encap_id'];
            $seoData['key_id'] = $data['encap_id'];
            $data['update_time'] = time();
            $data['update_uid'] = request()->user->userId;
            $data['update_name'] = request()->user->name;
            EncapModel::where('encap_id', $data['encap_id'])->update($data);
        } else {
            $data['create_time'] = time();
            $encapId = EncapModel::insertGetId($data);
            $seoData['key_id'] = $encapId;
        }
        $this->saveEncapNameToRedis($encapId, true);
        $seoService = new SeoElementService();
        $seoData['type'] = SeoElementModel::TYPE_BRAND;
        return $seoService->saveSeoElement($seoData);

    }

    public function saveEncapNameToRedis($encapId = '', $isUpload = false)
    {
        if (empty($encapId)) {
            return false;
        }
        $redis = Redis::connection('sku');
        $encapName = $redis->hget('encap', $encapId);
        if (!$encapName || $isUpload === true) {
            $encap = EncapModel::where('encap_id', '=', $encapId)->select('encap_name')->first();
            if (!$encap) {
                return false;
            }
            $encap = $encap->toArray();
            $redis->hset('encap', $encapId, $encap['encap_name']);
            $key = md5(strtolower($encap['encap_name']));
            if (!$redis->hget('encap_name_all', $key)) {
                $redis->hset('encap_name_all', $key, $encapId);
            }
            return $encap['encap_name'];
        } else {
            return $encapName;
        }
    }

    public function getEncapListForSelect($encapName)
    {
        $query = EncapModel::select(['encap_id', 'encap_name']);
        if ($encapName) {
            $query->where('encap_name', 'like', "%${encapName}");
        }
        $result = $query->paginate(10)->toArray();
        return $result;
    }

    public function exportEncaps($map)
    {
        return Excel::download(new EncapExport($map), '封装导出.csv');
    }

    public function updateIgnoreHandle($encapId, $ignoreHandle, $ignoreHandleReason = '')
    {
        return EncapModel::where('encap_id', $encapId)->update([
            'update_time' => time(),
            'update_name' => request()->user->name,
            'update_uid' => request()->user->userId,
            'ignore_handle' => $ignoreHandle,
            'ignore_handle_reason' => $ignoreHandleReason
        ]);
    }
    public function batchUpdateIgnoreHandle($encapIds, $ignoreHandle, $ignoreHandleReason = '')
    {
        return EncapModel::whereIn('encap_id', $encapIds)->update([
            'update_time' => time(),
            'update_name' => request()->user->name,
            'update_uid' => request()->user->userId,
            'ignore_handle' => $ignoreHandle,
            'ignore_handle_reason' => $ignoreHandleReason
        ]);
    }

    public function getEncapListForXmSelect()
    {
        $encapList = EncapModel::select(['encap_id', 'encap_name'])->get();
        $encapList = $encapList ? $encapList->toArray() : [];
        $encapListForSelect = [];
        foreach ($encapList as $encap) {
            $encapListForSelect[] = [
                'value' => $encap['encap_name'],
                'name' => $encap['encap_name']
            ];
        }
        return $encapListForSelect;
    }

}
