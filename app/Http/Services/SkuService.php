<?php


namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\Cms\CmsUserIntraCodeModel;
use App\Http\Models\LiexinData\GoodsModel;
use App\Http\Models\LiexinData\LockStockModel;
use App\Http\Models\StandardBrandModel;
use App\Http\Models\Supplier\SupplierChannelModel;
use App\Http\Models\TaskLogModel;
use App\Http\Transformers\SkuTransformer;
use App\Http\Utils\ValidatorMsg;
use App\Jobs\UpdateSku;
use App\Jobs\UpdateSpu;
use App\Map\GoodsMap;
use App\Model\RedisModel;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Validator;

class SkuService
{

    const OPERATE_TYPE_PUTAWAY = 1;
    const OPERATE_TYPE_OFF_SHELVES = -1;

    const LONG_TERM_TIMESTAMP = 2051193600; //长期有效sku的时间戳

    public function getSkuList($map)
    {
        $userId = request()->user->userId;
        $perm = PermService::hasPerm('footstone_sku_viewAllList');
        //没有查看所有sku的权限,只能查看自己的
        if ((!$perm) && $userId != config('website.adminId')) {
            $codeId = CmsUserIntraCodeModel::getCodeIdByAdminId($userId);
            $map['encoded/condition'] = $codeId;
        }

        if (!empty($map['stock_compare_type']) && !empty($map['stock_num'])) {
            $key = 'stock/' . $map['stock_compare_type'];
            $map[$key] = $map['stock_num'];
        }
        unset($map['stock_compare_type']);
        unset($map['stock_num']);

        if (!empty($map['standard_brand_ids'])) {
            $map['standard_brand_id/eqs'] = $map['standard_brand_ids'];
            unset($map['standard_brand_area_ids']);
            unset($map['standard_brand_ids']);
        }

        if (!empty($map['standard_brand_area_ids']) && empty($map['standard_brand_ids'])) {
            $areaIds = explode(',', $map['standard_brand_area_ids']);
            $standardBrandIds = StandardBrandModel::whereIn('brand_area', $areaIds)->pluck('standard_brand_id')->toArray();
            $map['standard_brand_id/eqs'] = implode(',', $standardBrandIds);
            unset($map['standard_brand_area_ids']);
        }

        if (!empty($map['is_temp'])) {
            if ($map['is_temp'] == 1) {
                $map['source/eq'] = 12;
            } else {
                $map['source/eq'] = 12;
            }
            unset($map['is_temp']);
        }

        if (!empty($map['has_attr'])) {
            if ($map['has_attr'] == 1) {
                $map['has_attr'] = 1;
            } else {
                $map['has_attr'] = 0;
            }
        }

        if (!empty($map['has_stock'])) {
            if ($map['has_stock'] == 1) {
                $map['stock/sr'] = "gt,0";
            } else {
                $map['stock/eq'] = 0;
            }
        }
        if (!empty($map['has_class'])) {
            if ($map['has_class'] == 1) {
                $map['class_id1/sr'] = 'gt,0';
            } else {
                $map['class_id1/eq'] = '0';
            }
            unset($map['has_class']);
        }

        if (!empty($map['class_id1'])) {
            $map['class_id1/eqs'] = $map['class_id1'];
            unset($map['class_id1']);
        }

        if (Arr::get($map,'ability_level',"") != "") {
            $map['ability_level/eqs'] = $map['ability_level'];
            unset($map['ability_level']);
        }

        if (!empty($map['org_id'])) {
            $map['org_id/eqs'] = $map['org_id'];
            unset($map['org_id']);
        }

        if (!empty($map['source'])) {
            $map['source/eqs'] = $map['source'];
            unset($map['source']);
        }

        if (!empty($map['encoded'])) {
            $map['encoded/eqs'] = $map['encoded'];
            unset($map['encoded']);
        }

        if (!empty($map['goods_label'])) {
            $map['goods_label/eqs'] = $map['goods_label'];
            unset($map['goods_label']);
        }

        if (!empty($map['goods_status'])) {
            $map['goods_status/eqs'] = $map['goods_status'];
            unset($map['goods_status']);
        }

        if (!empty($map['supplier_id'])) {
            $map['supplier_id/eqs'] = $map['supplier_id'];
            unset($map['supplier_id']);
        }

        if (!empty($map['encap'])) {
            $encapList = explode(',', $map['encap']);
            $map['encap/eqs'] = implode('€', $encapList);
            unset($map['encap']);
        }

        if ((!empty($map['create_time']))) {
            $times = explode('~', $map['create_time']);
            $startTime = strtotime($times[0]) ?: 1420041600;
            $endTime = strtotime($times[1]) ?: time();
            $map['create_time/range'] = $startTime . ',' . $endTime;
        }
        if ((!empty($map['update_time']))) {
            $times = explode('~', $map['update_time']);
            $startTime = strtotime($times[0]) ?: 1420041600;
            $endTime = strtotime($times[1]) ?: time();
            $map['update_time/range'] = $startTime . ',' . $endTime;
        }

        foreach ($map as $k => $v) {
            if (empty($v) and $v != '0') {
                unset($map[$k]);
            }
        }

        //搜索的是否是假供应商
        $isFakeSupplier = false;
        if (!empty($map['canal/condition']) && request()->user->userId == 1442) {
            $originCanal = $map['canal/condition'];
            //先去判断这个编码对应的供应商的对接方式是不是api对接,如果是api对接
            //而且如果is_type=0,则不需要用映射,否则用映射
            $supplierMapping = SupplierChannelModel::where('supplier_code', $map['canal/condition'])
                ->where('outside_contact_type', 1)
                ->value('supplier_mapping');
            $map['canal/condition'] = $supplierMapping ?: $map['canal/condition'];
            $isFakeSupplier = (bool)$supplierMapping;
        }
        unset($map['class_name/condition'], $map['brand_name/condition'], $map['create_time'], $map['update_time']);
        $url = config('website.search_domain_new') . '/search/es/searchSku';
        if (!empty($map['p']) && $map['p'] > 1000) {
            $map['p'] = rand(5, 1000);
        }
        $map['show_status'] = 1;
        unset($map['brand_id']);

        if (empty($map['offset'])) {
            $map['offset'] = 10;
        }
        if(isset($map["page_size"])){
            $map['offset'] = $map["page_size"];
        }
        if (!empty($map['supplier_id'])) {
            if ($map['supplier_id'] == 1689) { //10楼库存
                unset($map["supplier_id"]);
                $map['canal_new/eq'] = config('website.ten_floor');
            }
            if ($map['supplier_id'] == 1690) { //香港备货
                unset($map["supplier_id"]);
                $map['canal_new/eq'] = config('website.hk_floor');
            }
        }

        if (!empty($map['page_size'])) {
            $map['offset'] = $map['page_size'];
            unset($map['page_size']);
        }


        $map['supplier_id/sr'] = "gt,0"; //固定查代购数据
        $map['admin'] = 1; // 后台搜索
        $map['no_rule'] = "1122"; // 后台搜索
        $res = Http::get($url, $map)->body();
        $data = json_decode($res, true);

        if (Arr::get($map, "debugt") == 1) {
            print_r($url);
            print_r("<br/>");
            print_r(json_encode($map));
            print_r("<br/>");
            print_r($data);
            die();
        }
        $redis = Redis::connection('sku');
        $spuRedis = Redis::connection('spu');
        $skuList = [];

        $prev_sku = request()->input("prev_sku");

        if (isset($data['error_code']) && $data['error_code'] == 0) {
            //这里是处理后的sku列表数据
            $skuList = SkuTransformer::listTransformer($data,$prev_sku);
            //这个1442的用户id是竞调用户
            if (request()->user->userId == 1442) {
            }

            //判断有搜索供应商编码并且供应商是假的
            if (!empty($originCanal) && $isFakeSupplier) {
                if (!empty($map['canal/condition'])) {
                    //减去供应商编码后两位数字,如果减完小于0,那就不减了
                    //获取改供应商最后两位数字
                    $number = substr($map['canal/condition'], strlen($map['canal/condition']) - 2, 2);
                    $number = intval($number);
                    $total = $data['data']['total'];
                    $data['data']['total'] = $total - $number > 10 ? $total - $number : $total;
                }
            }
            $encodedService = new EncodedService();
            $encodedList = array_column($skuList, 'encoded');
            $encodedUsers = $encodedService->getEncodedUserByEncoded($encodedList);
            $skuList = array_map(function ($item) use ($encodedUsers, $redis, $spuRedis) {
                $encoded = Arr::get($item, 'encoded', 0);
                $item['spu_id'] = (string)Arr::get($item, 'spu_id');
                $item['cp_time'] = !empty($item['cp_time']) ? date('Y-m-d H:i:s', $item['cp_time']) : '';
                $item['encoded_user_name'] = Arr::get($encodedUsers, $encoded, '');
                $item['brand_name'] = isset($item['brand_name']) && !empty($item['brand_name']) ? $item['brand_name'] : '';
                $item['source_name'] = Arr::get(config('field.SkuSource'), @$item['source'], '');
                #digikey返回包装
                $item["raw_goods_packing"] = "";
                if (Arr::get($item, 'supplier_id') == 7) {
                    $digikeyInfo = $spuRedis->hget("sku_raw_map", $item["goods_id"]);
                    $item["raw_goods_packing"] = Arr::get(json_decode($digikeyInfo, true), "pack");
                }

                return $item;
            }, $skuList);
        }
        //判断如果是导出的数据,那么就要整理数据排好给导出使用
        if (Arr::get($map, 'is_export')) {
            $skuList = array_map(function ($sku) {
                if (empty($sku['goods_id'])) {
                    return $sku;
                }
                $orgId = request()->input('org_id');
                $data = [
                    "SPUID" => $sku['spu_id'] . "\t",
                    "SKUID" => $sku['goods_id'] . "\t",
                    "SKU型号" => $sku['goods_name'],
                    "SPU型号" => $sku['spu_name'],
                    "品牌" => $sku['brand_name'],
                    "标准品牌" => $sku['standard_brand_name'],
                    "SKU分类" => $sku['class_name'],
                    "来源" => $sku['source_name'],
                    "供应商编码" => $sku['canal'],
                    "SKU采购" => $sku['encoded_user_name'],
                    "库存" => $sku['stock'],
                    "阶梯成本价" => json_encode($sku['ladder_price']),
                    "批次" => $sku['batch_sn'],
                    "封装" => Arr::get($sku, "encap"),
                    "标准包装量" => $sku['mpq'],
                    "起订量" => $sku['moq'],
                    "递增量" => 0,
                    "状态" => $sku['status_name'] === '上架' ? $sku['status_name'] : '下架',
                    "过期" => $sku['is_expire'],
                    "上架有效期" => $sku['cp_time'],
                    "大陆交期" => $sku['cn_delivery_time'],
                    "国外交期" => $sku['hk_delivery_time'],
                    "渠道标签" => $sku['goods_label_name'],
                    "标签" => "",
                    "特殊规则" => "",
                    "ECCN" => $sku['eccn'],
                    "活动标签" => "",
                    "更新时间" => $sku['update_time'],
                    "创建时间" => $sku['create_time'] ?? '',
                ];
                if ($orgId == 3) {
                    $data["标准价"] = ($sku['show_price'][0]['price_cn'] ?? '') . "\t";
                    $data["企业价"] = ($sku['show_price'][1]['price_cn'] ?? '') . "\t";
                }
                return $data;
            }, $skuList);
        } else {
            $mongo = \DB::connection('mongodb');
            //获取参数,为啥放在导出后面,因为考虑到性能
            $skuList = array_map(function ($sku) use ($mongo) {
                if (isOnlineEnv()) {
                    $spuAttrs = $mongo->table('spu_attrs2')->where('spu_id', (int)$sku['spu_id'])->first();
                }
                $sku['spu_attrs'] = $spuAttrs ?? [];
                return $sku;
            }, $skuList);
        }

        //判断是否包含来源为芯链寄售上传的sku
        $isContainXlStock = collect($skuList)->contains('supplier_id', 17);
        if ($isContainXlStock) { //锁库数量
            $lockStock = (new LockStockModel())
                ->selectRaw('goods_id, sum(num) as lock_stock')
                ->whereIn('goods_id', array_column($skuList, 'goods_id'))
                ->whereIn('status', [1])
                ->groupBy('goods_id')
                ->get()->keyby("goods_id")->toArray();  //锁库明细
        }

        foreach ($skuList as $key => $sku) {
            if (empty($sku['goods_id'])) {
                continue;
            }
            $skuList[$key]['lock_stock'] = $lockStock[$sku['goods_id']]['lock_stock'] ?? 0;
        }

        return [
            'data' => $skuList,
            'count' => !empty($data['data']['total']) ? $data['data']['total'] : 0,
            'search_params' => json_encode($map),
        ];
    }

    //判断SKU是否唯一
    public function checkSkuUnique($spuId, $supplierId, $moq, $code = '', $canal = '', $mongodb = '')
    {
        if (!$mongodb) {
            $mongodb = DB::connection('mongodb')->collection('sku');
        }
        $map['spu_id'] = $spuId;
        $map['supplier_id'] = (int)$supplierId;
        $map['moq'] = (int)$moq;
        $map['encoded'] = (int)$code;
        $map['canal'] = $canal;
        foreach ($map as $k => $v) {
            if (empty($v)) {
                unset($map[$k]);
            }
        }
        $result = $mongodb->where($map)->first();
        if (!$result) {
            return false;
        } else {
            return $result['goods_id'];
        }
    }


    //新增和修改mongo sku判断唯一数据
    public function saveSkuToMongo(
        $spuId,
        $supplierId,
        $moq,
        $skuId,
        $type = '',
        $code = '',
        $canal = '',
        $mongodb = '',
        $old_goods_id = 0
    ) {
        if (empty($spuId) || empty($supplierId) || empty($moq) || empty($skuId)) {
            return false;
        }
        if (!$mongodb) {
            $mongodb = DB::connection('mongodb')->collection('sku');
        }
        if (empty($type)) {
            $map = array(
                'goods_id' => (int)$skuId
            );
            $result = $mongodb->where($map)->first();
        } else {
            $result = null;
        }
        $data['spu_id'] = (int)$spuId;
        $data['supplier_id'] = (int)$supplierId;
        $data['moq'] = (int)$moq;
        $data['goods_id'] = (int)$skuId;
        $data['encoded'] = (int)$code;
        $data['canal'] = $canal;
        $data['old_goods_id'] = (int)$old_goods_id;
        if ($result) {
            $map['goods_id'] = (int)$skuId;
            $save = $mongodb->where($map)->update($data);
        } else {
            $save = $mongodb->insert($data);
        }
        if ($save) {
            return true;
        } else {
            return false;
        }
    }

    /*
     * 保存sku信息到redis
     */
    public function saveSkuToRedis($info)
    {
        $redis = Redis::connection('sku');
        $config = config('field.SkuField');
        $sku = $info;
        foreach ($info as $k => $v) {
            if (!isset($config[$k]) || $config[$k] !== 1) {
                unset($info[$k]);
            }
        }
        if (!empty($info['ladder_price']) && !is_array($info['ladder_price'])) {
            $info['ladder_price'] = json_decode($info['ladder_price'], true);
        }
        //todo 2023.7.18 redis bug
        $skuRedisInfo = $redis->hget("sku", $sku['goods_id']);
        if (empty($skuRedisInfo)) {
            $redis->hset('sku', $sku['goods_id'], json_encode($info));
            $skuRedisInfo = json_encode($info);
        }
        $skuRedisInfoArr = json_decode($skuRedisInfo, true);
        unset($skuRedisInfoArr["is_expire"]);
        //转换spu_id 整形存redis
        $skuInfod = array_merge($skuRedisInfoArr, $info);
        // $skuInfod['sys_update_source'] = 3;
        // $skuInfod['sys_update_time'] = time();

        if (Arr::get($skuInfod,"canal") == "L0018319"){ //同步联营
            (new ThirdApi())->pushLyWms($sku['goods_id'],request()->input("debug"));
        }

        $skuInfo = json_encode($skuInfod);
        $redis->hset('sku', $sku['goods_id'], $skuInfo);
        return $sku;
    }

    //批量更新sku状态,走队列的
    public function batchUpdateSkuStatus($data)
    {
        try {
            $skuIds = explode(',', $data['sku_ids']);
            //构建队列数据
            //上架
            $queueData = [];
            if ($data['operate_type'] == self::OPERATE_TYPE_PUTAWAY) {
                $queueName = 'lie_footstone_batch_upsku_queue';
                $data['cp_time'] = $data['is_long_term'] == 1 ? self::LONG_TERM_TIMESTAMP : strtotime($data['cp_time']);
                foreach ($skuIds as $skuId) {
                    $queueData[] = [
                        'sku_id' => $skuId,
                        'cp_time' => $data['cp_time'],
                    ];
                }
            } else {
                $queueName = 'lie_footstone_batch_downsku_queue';
                $queueData['down_type'] = 2;
                $queueData['data'] = $skuIds;
            }
            QueueService::publishQueue($queueName, $queueData);
            //// 更新上下架状态还要告诉推送那边
            //foreach ($skuIds as $skuId) {
            //    $result = Http::post(config('website.ShopApi') . '/update_sales_status', [
            //        'shop_id' => 3,
            //        'lx_sku_id' => $skuId,
            //        'status' => $data['operate_type'] == self::OPERATE_TYPE_PUTAWAY ? 1 : 2,
            //    ])->json();
            //    if ($result['code'] != 0) {
            //        throw new InvalidRequestException($result['msg']);
            //    }
            //}
        } catch (\Exception $exception) {
            throw new InvalidRequestException($exception);
        }
        return true;
    }


    /*
     * 更新sku库存
     * @param int $types  -1 扣减  1 增加 2覆盖
     * @param int $id  18位的sku_id
     * @param int $num  操作数量
     * @return  string
     */
    public function updateSkuStock($types = -1, $goods_id, $num)
    {
        $nowTime = time();
        $dbInfo = getSpuSkuDb($goods_id);

        $redisCon = Redis::connection('sku');
        try {
            $con = DB::connection($dbInfo["db"]);
            $con->beginTransaction();

            //查询redis sku库存
            $skuInfo = $redisCon->hget("sku", $goods_id);
            if (!$skuInfo) {
                throw new InvalidRequestException("redis不存在此sku：" . $goods_id);
            }
            $skuArr = json_decode($skuInfo, true);
            if ($types == 2) {
                $nowStock = $num;
            } else {
                if ($types == -1 && $skuArr["stock"] < $num) {
                    throw new InvalidRequestException("当前库存不足扣减：" . $goods_id);
                }

                $nowStock = $types == -1 ? $skuArr["stock"] - $num : $skuArr["stock"] + $num;
            }

            $s = $con->table($dbInfo["table"])->where("goods_id", $goods_id)->update([
                "stock" => $nowStock,
                "update_time" => $nowTime
            ]);
            if (!$s) {
                throw new InvalidRequestException("更新sku失败：" . $goods_id);
            }

            //更新redis
            $skuArr["stock"] = $nowStock;
            $skuArr["update_time"] = $nowTime;
            $redisCon->hset("sku", $goods_id, json_encode($skuArr));

            $con->commit();

            return true;
        } catch (\Exception $e) {
            $con->rollback();
            return $e->getMessage();
        }
    }


    /*
  * 更新sku库存
  * @param $data [{"goods_id":"1170988160911696280","stock":565}]
  */
    public function updateBatchSkuStock($data)
    {
        $goodsIdArr = array_column($data, "goods_id");
        //已经锁了多少库存统计
        $hasLockItems = (new LockStockModel())
            ->select("goods_id", DB::raw("sum(num) as lock_num"))
            ->whereIn("goods_id", $goodsIdArr)
            ->where("status", 1)
            ->groupby("goods_id")
            ->get()->keyby("goods_id")->toArray();  //锁库明细

        //判断当前锁库数量
        foreach ($data as $k => $b) {
            $goods_id = $b["goods_id"];
            $stock = $b["stock"];
            if ($stock < @$hasLockItems[$goods_id]["lock_num"]) {
                throw new InvalidRequestException("当前库存不足当前锁库,当前锁库：" . $hasLockItems[$goods_id]["lock_num"] . " goods_id: $goods_id");
            }
        }

        foreach ($data as $k => $b) {
            self::updateSkuStock(2, $b["goods_id"], $b["stock"]);
        }
        return true;
    }


    /*
     *锁库联营
     * status:1   //1:锁定库存 2：取消锁定库存  3：减少实际库存  4：加回实际库存
     */
    public function lockLyStock($input)
    {
        Log::info("---接收联营锁库参数：" . json_encode($input, JSON_UNESCAPED_SLASHES));
        if (data_get($input, "debug")) {
            $input = json_decode(getUrlData("debug"), true);
        }

        $validator = Validator::make($input, [
            'status' => 'required|digits_between:1,3',
            'order_id' => 'required',
            'skus' => 'required',
        ]);

        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();
            throw new InvalidRequestException(ValidatorMsg::getMsg($errors));
        }
        $input["skus"] = json_decode($input["skus"], true);

        $status = $input["status"];
        $order_id = $input["order_id"];
        $goodsIdArr = array_column($input["skus"], "goods_id");

        $LockStockModel = new LockStockModel();
        $redisCon = Redis::connection('sku');

        //当前订单锁库明细
        $lockItems = $LockStockModel
            ->whereIn("goods_id", array_column($input["skus"], "goods_id"))
            ->where("order_id", $input["order_id"])
            ->get()->keyBy('goods_id')->toArray();  //锁库明细

        //已经锁了多少库存统计
        $hasLockItems = $LockStockModel
            ->select("goods_id", DB::raw("sum(num) as lock_num"))
            ->whereIn("goods_id", $goodsIdArr)
            ->where("status", 1)
            ->groupby("goods_id")
            ->get()->keyby("goods_id")->toArray();  //锁库明细

        try {
            $con = DB::connection("liexin_data");
            $con->beginTransaction();

            switch ($status) {
                case 1: //锁库锁定

                    $insertData = [];
                    foreach ($input["skus"] as $k => $v) {
                        $goods_id = $v["goods_id"];
                        $num = $v["num"];
                        //判断库存是否足够
                        $skuInfo = $redisCon->hget("sku", $goods_id);
                        if (!$skuInfo) {
                            throw new InvalidRequestException("redis不存在此sku：" . $goods_id);
                        }
                        $skuArr = json_decode($skuInfo, true);

                        if (array_key_exists($goods_id, $lockItems)) { //更新
                            if ($skuArr["stock"] < ($num + @$hasLockItems[$goods_id]["lock_num"] - @$lockItems[$goods_id]["num"])) {
                                throw new InvalidRequestException("当前商品实际库存不够锁库,商品ID：" . $goods_id);
                            }

                            $s = $LockStockModel->where("id", $lockItems[$goods_id]["id"])->update([
                                "status" => $status,
                                "num" => $num,
                                "update_time" => time()
                            ]);
                            if (!$s) {
                                throw new InvalidRequestException("更新锁库失败,商品ID：" . $goods_id);
                            }
                        } else {
                            if ($skuArr["stock"] < $num) {
                                throw new InvalidRequestException("当前商品实际库存不够锁库,商品ID：" . $goods_id);
                            }
                            $insertData[] = [
                                "status" => $status,
                                "order_id" => $order_id,
                                "goods_id" => $goods_id,
                                "num" => $num,
                                "add_time" => time(),
                                "update_time" => time()
                            ];
                        }
                    }

                    $s = $LockStockModel->insert($insertData);
                    if (!$s) {
                        throw new InvalidRequestException("插入锁库失败");
                    }
                    break;
                case 2: //取消锁定库存
                    foreach ($input["skus"] as $k => $v) {
                        $goods_id = $v["goods_id"];
                        $num = $v["num"];
                        if (!array_key_exists($goods_id, $lockItems)) {
                            throw new InvalidRequestException("取消锁定失败，此订单不存在锁定商品：" . $goods_id);
                        }
                        $info = $lockItems[$goods_id];
                        if ($info["num"] != $num) {
                            throw new InvalidRequestException("取消锁定数量不一致：" . $goods_id);
                        }
                        $s = $LockStockModel->where("id", $info["id"])->update([
                            "status" => $status,
                            "num" => $info["num"],
                            "update_time" => time()
                        ]);
                        if (!$s) {
                            throw new InvalidRequestException("更新锁库失败,商品ID：" . $goods_id);
                        }
                    }
                    break;
                case 3: //减少实际库存
                    $insertData = [];
                    foreach ($input["skus"] as $k => $v) {
                        $goods_id = $v["goods_id"];
                        $num = $v["num"];

                        if (array_key_exists($goods_id, $lockItems)) { //更新
                            $s = $LockStockModel->where("id", $lockItems[$goods_id]["id"])->update([
                                "status" => $status,
                                "num" => $num,
                                "update_time" => time()
                            ]);
                            if (!$s) {
                                throw new InvalidRequestException("更新锁库失败,商品ID：" . $goods_id);
                            }
                        } else { //插入
                            $insertData[] = [
                                "status" => $status,
                                "order_id" => $order_id,
                                "goods_id" => $goods_id,
                                "num" => $num,
                                "add_time" => time(),
                                "update_time" => time()
                            ];
                        }

                        //更新sku库存
                        $u = $this->updateSkuStock(-1, $goods_id, $num);
                        if ($u !== true) {
                            throw new InvalidRequestException($u);
                        }
                    }
                    if ($insertData) {
                        $s = $LockStockModel->insert($insertData);
                        if (!$s) {
                            throw new InvalidRequestException("插入减少库存失败");
                        }
                    }
                    break;
                case 4: //加回库存，变锁定
                    foreach ($input["skus"] as $k => $v) {
                        $goods_id = $v["goods_id"];
                        $num = $v["num"];
                        if (!array_key_exists($goods_id, $lockItems)) {
                            throw new InvalidRequestException("加回库存失败，此订单不存在锁定商品：" . $goods_id);
                        }
                        $info = $lockItems[$goods_id];
                        if ($info["num"] != $num) {
                            throw new InvalidRequestException("加回数量不一致：" . $goods_id);
                        }

                        if ($info["status"] == 3) { #如果此库存是扣减，要加回来库存
                            //更新sku库存
                            $u = $this->updateSkuStock(1, $goods_id, $num);
                            if ($u !== true) {
                                throw new InvalidRequestException($u);
                            }
                        }

                        $s = $LockStockModel->where("id", $info["id"])->update([
                            "status" => 2,
                            "num" => $info["num"],
                            "update_time" => time()
                        ]);
                        if (!$s) {
                            throw new InvalidRequestException("更新锁库失败,商品ID：" . $goods_id);
                        }
                    }
                    break;
                case 5: // 修改数量（对应当前型号是 减少库存状态  ）

                    foreach ($input["skus"] as $k => $v) {

                        $goods_id = $v["goods_id"];
                        $num = $v["num"];  //最新数量
                        if (!array_key_exists($goods_id, $lockItems)) {
                            throw new InvalidRequestException("此订单不存在锁定商品：" . $goods_id);
                        }
                        $info = $lockItems[$goods_id];
                        if ($info["status"] != 3) { #如果此库存是扣减，要加回来库存
                            throw new InvalidRequestException("不适合调此接口，此商品ID：" . $goods_id);
                        }

                        //先加回来 上次锁定的 库存
                        $u = $this->updateSkuStock(1, $goods_id, $info["num"]);
                        if ($u !== true) {
                            throw new InvalidRequestException($u);
                        }

                        //判断库存是否足够
                        $skuInfo = $redisCon->hget("sku", $goods_id);
                        if (!$skuInfo) {
                            throw new InvalidRequestException("redis不存在此sku：" . $goods_id);
                        }
                        $skuArr = json_decode($skuInfo, true);
                        if ($skuArr["stock"] < ($num + @$hasLockItems[$goods_id]["lock_num"] - $num)) {
                            throw new InvalidRequestException("当前商品实际库存不够锁库,商品ID：" . $goods_id);
                        }
                        //最后更新实际库存
                        $u = $this->updateSkuStock(-1, $goods_id, $num);
                        if ($u !== true) {
                            throw new InvalidRequestException($u);
                        }
                        //更新sku库存
                        $s = $LockStockModel->where("id", $info["id"])->update([
                            "status" => 3,
                            "num" => $num,
                            "update_time" => time()
                        ]);
                        if (!$s) {
                            throw new InvalidRequestException("减少库存失败,商品ID：" . $goods_id);
                        }
                    }
                    break;
            }

            $con->commit();

            //最后更新redis
            $hasLockItems = $LockStockModel
                ->select("goods_id", DB::raw("sum(num) as lock_num"))
                ->whereIn("goods_id", $goodsIdArr)
                ->where("status", 1)
                ->groupby("goods_id")
                ->get()->keyby("goods_id")->toArray();  //锁库明细
            foreach ($hasLockItems as $k => $v) {
                $redisCon->hset("sku_lock_stock", $v["goods_id"], $v["lock_num"]); //锁库数量
            }

            return true;
        } catch (\Exception $e) {
            $con->rollback();
            return $e->getMessage() . "行号：" . $e->getLine() . $e->getFile();
        }
    }

    /*
     *获取联营可购买数量
     */
    public function getLyStock($goods_id)
    {
        $LockStockModel = new LockStockModel();
        $goodsIdArr = explode(",", $goods_id);

        $lockItems = $LockStockModel
            ->select("goods_id", DB::raw("sum(num) as lock_num"))
            ->whereIn("goods_id", $goodsIdArr)
            ->where("status", 1)
            ->groupby("goods_id")
            ->get()->keyby("goods_id")->toArray();  //锁库明细

        $temp = [];
        foreach ($goodsIdArr as $id) {
            $redisCon = Redis::connection('sku');
            $skuInfo = $redisCon->hget("sku", $id);
            if (!$skuInfo) {
                continue;
            }
            $skuArr = json_decode($skuInfo, true);
            $temp[$id] = $skuArr["stock"] - (data_get($lockItems, $id) ? $lockItems[$id]["lock_num"] : 0);
        }
        return $temp;
    }

    //保存sku
    public function saveSku($sku)
    {
        $sku['hk_delivery_time'] = !empty($sku['hk_delivery_time']) ? $sku['hk_delivery_time'] . $sku['hk_period'] : '';
        $sku['cn_delivery_time'] = !empty($sku['cn_delivery_time']) ? $sku['cn_delivery_time'] . $sku['cn_period'] : '';
        unset($sku['hk_period'], $sku['cn_period']);
        $detail = $sku['sku_detail'];
        unset($sku['sku_detail']);
        //处理价格
        $price = [];
        if (!empty($sku['purchases'])) {
            foreach ($sku['purchases'] as $k => $v) {
                $price[$k] = [
                    'purchases' => (int)$v,
                    'price_cn' => (float)$sku['price_cn'][$k],
                    'price_us' => (float)$sku['price_us'][$k]
                ];
                if ($sku['supplier_id'] == 14) {
                    $price[$k]['cost_price'] = (float)$sku['cost_price'][$k];
                    if (!empty($price[$k]['cost_price'])) {
                        $sku['cp_time'] = time();
                    }
                }
            }
            if (!empty($price)) {
                $price = arraySequence($price, 'purchases', 'SORT_ASC');
                $sku['ladder_price'] = json_encode($price);
            }
            //主要是给ES排序的字段,主要是因为美元很多都有
            $sku['single_price'] = !empty($price[count($price) - 1]['price_us']) ? (float)$price[count($price) - 1]['price_us'] : (float)$price[count($price) - 1]['price_cn'] / config('field.SkuRatio');
        } else {
            $sku['ladder_price'] = '';
            $sku['single_price'] = '';
        }

        //处理状态
        if (empty($sku['moq']) || empty($sku['stock']) || empty($price) || !is_array($price) || !count($price) > 0 || $sku['moq'] > $sku['stock']) {
            //如果有传特定的goods_status,就按指定的,没传才走规则
            if (empty($sku['goods_status'])) {
                //下架
                $sku['goods_status'] = 3;
            }
        }
        $sku['goods_status'] = (int)$sku['goods_status'];
        //删除多余字段
        unset($sku['purchases'], $sku['price_cn'], $sku['price_us'], $sku['cost_price']);
        //删除多余字段
        $select = config('field.SkuField'); //生成查询字段，避免速度过慢
        foreach ($select as $k => $v) {
            $SelectField[] = $k;
        }
        $SelectField[] = 'goods_id';
        $sku['update_time'] = time();
        if (!empty($sku['goods_id'])) { //编辑

            $pack = $sku['pack']??'';
            if (!empty($pack)) {
                $spuRedis = Redis::connection('spu');
                $skuRawMap = $spuRedis->hget('sku_raw_map', $sku['goods_id']);
                if (!empty($skuRawMap)) {
                    $skuRawMap = json_decode($skuRawMap, true);
                    $skuRawMap['pack'] = $pack;
                    $spuRedis->hset('sku_raw_map', $sku['goods_id'], json_encode($skuRawMap));
                } else {
                    $spuRedis->hset('sku_raw_map', $sku['goods_id'], json_encode([
                        "pack" => $pack,
                    ]));
                }
            }
            unset($sku['pack']);

            $dbInfo = getSpuSkuDb($sku['goods_id']);
            $connection = DB::connection($dbInfo['db']);
            //更新sku
            $result = $connection->table($dbInfo['table'])->where('goods_id', $sku['goods_id'])
                ->update($sku);
            if (!$result) {
                throw new InvalidRequestException('更新sku失败');
            }

            $skuDb = $connection->table($dbInfo['table'])->where('goods_id', $sku['goods_id'])->select($SelectField)->first();
            $skuDb = (array)$skuDb;
            $this->saveSkuToRedis($skuDb);
            if (isOnlineEnv()) {
                $this->saveSkuToMongo(
                    $skuDb['spu_id'],
                    $skuDb['supplier_id'],
                    $skuDb['moq'],
                    $skuDb['goods_id'],
                    '',
                    !empty($skuDb['encoded']) ? $skuDb['encoded'] : '',
                    !empty($sku['canal']) ? $sku['canal'] : '',
                    '',
                    !empty($skuDb['old_goods_id']) ? $skuDb['old_goods_id'] : 0
                );
            }
            //$this->pushSkuUpdate($sku['goods_id']);
            if (!empty($detail) && isOnlineEnv()) {
                (new SkuDetailService())->saveSkuDetail($skuDb['goods_id'], $detail);
            }
            return true;
        }
        return true;
    }

    //推送redis更新spu
    public function pushSkuUpdate($skuIds)
    {
        $redis = Redis::connection('sku');
        $skuIds = explode(',', $skuIds);
        $pushName = 'update_list_sku';
        if (!empty($skuIds) && is_array($skuIds)) {
            foreach ($skuIds as $k => $v) {
                $redis->rpush($pushName, $v);
            }
            return true;
        } else {
            return false;
        }
    }

    //获取sku信息,单纯走redis,不走商品服务
    public function getSkuCacheInfo($skuId)
    {
        $redis = Redis::connection('sku');
        $spuRedis = Redis::connection('spu');
        $sku = $redis->hget('sku', $skuId);
        if (empty($sku)) {
            return [];
        }
        $sku = json_decode($sku, true);
        $spuId = $sku['spu_id'];
        $spu = $spuRedis->hget('spu', $spuId);
        $spu = json_decode($spu, true);
        $brandName = $redis->hget('brand', $spu['brand_id']);
        $sku['brand_name'] = $brandName;
        if (!empty($spu['s_brand_id'])) {
            $standardBrand = $redis->hget('standard_brand', $spu['s_brand_id']);
            $standardBrand = json_decode($standardBrand, true);
            $sku['standard_brand_name'] = $standardBrand['brand_name'];
            $sku['standard_brand_id'] = $spu['s_brand_id'];
        }
        $sku['class_id1'] = $sku['class_id1'] ?? 0;
        $sku['class_id2'] = $sku['class_id2'] ?? 0;
        $sku['s_brand_id'] = $sku['s_brand_id'] ?? 0;
        $spu = $spu ?? [];
        $sku = array_merge($sku, $spu);
        $sku['class_name1'] = !empty($sku['class_id1']) ? ClassService::getClassNameFromCache($sku['class_id1']) : '';
        $sku['class_name2'] = !empty($sku['class_id2']) ? ClassService::getClassNameFromCache($sku['class_id2']) : '';
        return $sku;
    }

    //获取sku信息,单纯走redis,不走商品服务
    public function getSkuCacheInfoTemp($skuId)
    {
        $redis = Redis::connection('sku');
        $spuRedis = Redis::connection('spu');
        $sku = $redis->hget('sku', $skuId);
        if (empty($sku)) {
            return [];
        }
        $sku = json_decode($sku, true);
        $spuId = $sku['spu_id'];
        $spu = $spuRedis->hget('spu', $spuId);
        $spu = json_decode($spu, true);
        $brandName = $redis->hget('brand', $spu['brand_id']);
        $sku['brand_name'] = $brandName;
        if (!empty($spu['s_brand_id'])) {
            $standardBrand = $redis->hget('standard_brand', $spu['s_brand_id']);
            $standardBrand = json_decode($standardBrand, true);
            $sku['standard_brand_name'] = $standardBrand['brand_name'];
            $sku['standard_brand_id'] = $spu['s_brand_id'];
        }
        $sku['class_id1'] = $sku['class_id1'] ?? 0;
        $sku['class_id2'] = $sku['class_id2'] ?? 0;
        $sku['s_brand_id'] = $sku['s_brand_id'] ?? 0;
        $spu = $spu ?? [];
        $sku = array_merge($sku, $spu);
        $sku['class_name1'] = !empty($sku['class_id1']) ? ClassService::getClassNameFromCache($sku['class_id1']) : '';
        $sku['class_name2'] = !empty($sku['class_id2']) ? ClassService::getClassNameFromCache($sku['class_id2']) : '';
        return $sku;
    }

    //批量修改sku更新时间
    public static function batchUpdateSkuUpdateTime($skuIds = [])
    {
        $nowTime = time();
        $redis = Redis::connection('sku');
        foreach ($skuIds as $skuId) {
            $dbInfo = getSpuSkuDb($skuId);
            $connection = DB::connection($dbInfo["db"]);
            //查询redis sku库存
            $skuInfo = $redis->hget("sku", $skuId);
            if (!$skuInfo) {
                throw new InvalidRequestException("redis不存在此sku：" . $skuId);
            }
            $skuArr = json_decode($skuInfo, true);
            $connection->table($dbInfo["table"])->where("goods_id", $skuId)->update([
                "update_time" => $nowTime
            ]);

            //更新redis
            $skuArr["update_time"] = $nowTime;
            $redis->hset("sku", $skuId, json_encode($skuArr));
        }
    }

    public static function updateSkuTask($file)
    {
        if (empty($file)) {
            throw new InvalidRequestException('请上传文件');
        }
        if ($file->isValid()) {
            $clientName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            if ($extension != "xlsx") {
                throw new InvalidRequestException('上传文件非法');
            }
            $newName = 'update_sku_' . md5(date('ymdhis') . $clientName) . "." . $extension;
            $uploadDir = storage_path() . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR;
            $path = $uploadDir . $newName;
            $file->move($uploadDir, $newName);
            $adminId = request()->user->userId;
            $adminName = request()->user->name;
            $logModel = new TaskLogModel();
            $logData = [
                'file_name' => $clientName,
                'log' => date('Y-m-d H:i:s', time()) . " 开始上传处理SKU,准备丢入异步队列,文件名为 : " . $clientName,
                'status' => 0,
                'admin_id' => $adminId,
                'admin_name' => $adminName,
                'create_time' => time(),
                'update_time' => time(),
                'type' => TaskLogModel::TYPE_UPDATE_SKU,
            ];
            return \DB::connection('spu')->transaction(function () use ($logModel, $logData, $path) {
                $logId = $logModel->insertGetId($logData);

                $hostParts = explode('.', request()->getHost());
                $mainDomain = implode('.', array_slice($hostParts, -2));
                if ($mainDomain != 'ichunt.net') {
                    (new UpdateSku($logId, $path))->handle();
                    $result = true;
                } else {
                    $result = UpdateSku::dispatch($logId, $path);
                }
                if (!$result) {
                    throw new InvalidRequestException('推送上传任务失败');
                }
                return $result;
            });
        } else {
            throw new InvalidRequestException('上传文件非法');
        }
    }

    /*
     * erp查询自营详情
     */
    public static function searchZy($erp_number_arr)
    {
        $GoodsModel = new GoodsModel();
        $temp = [];
        foreach (explode(",", $erp_number_arr) as $erp_number) {
            $info = $GoodsModel->where("erp_number", $erp_number)->orderby("update_time", "desc")->first();
            if ($info) {
                $temp[$erp_number] = $info->toArray();
            } else {
                $temp[$erp_number] = [];
            }
        }
        return $temp;
    }

    /*
     * 批量查询: 包装 长度
     */
    public  function searchLy($skuids = [])
    {
        $skuRedis = Redis::connection('sku');
        $spuRedis = Redis::connection('spu');
        $temp = [];
        foreach ($skuids as $skuid) {
            $skuInfo = json_decode($skuRedis->hget('sku', $skuid), true);
            if ($skuInfo) {
                $orgInfo = json_decode($spuRedis->hget('sku_raw_map', $skuid), true);
                $pack = "";
                if ($orgInfo){
                    $pack = $orgInfo["pack"];
                }
                $spuInfo = json_decode($spuRedis->hget('spu', $skuInfo["spu_id"]), true);
                if ($spuInfo) {
                    $spuExtra = DB::connection('mongodb')->table('spu_extra')->where('spu_id', $skuInfo["spu_id"])->first();
                    $temp[] = ["sku" => (string)$skuid,
                        "spu_id" => (string)$skuInfo["spu_id"],
                        "length" => Arr::get($spuExtra, "length", ""),
                        "brand_pack" => $pack ?: Arr::get($spuInfo, "brand_pack", "")
                    ];
                }
            }
        }
        return $temp;
    }

    /*
    * 更新自营编码
   */
    public static function updateZy($collert)
    {
        $goods_id = Arr::get($collert, "goods_id", 0);
        $GoodsModel = new GoodsModel();
        $info = $GoodsModel->where('goods_id', '=', $goods_id)->first();
        if (!$info) {
            return [1000, "不存在此sku"];
        }
        $redis = Redis::connection('sku');
        $redisInfo = $redis->hget('Self_SelfGoods', $goods_id);
        $redisInfoArr = json_decode($redisInfo, true);
        $temp = [];
        foreach ($collert as $a => $v) {
            if (in_array($a, ["erp_number"])) {
                $temp[$a] = $v;
                $redisInfoArr[$a] = $v;
            }
        }
        $result = $GoodsModel->where('goods_id', '=', $goods_id)->update($temp);

        #更新redis
        $t = $redis->hset('Self_SelfGoods', $goods_id, json_encode($redisInfoArr));

        $t2 = $redis->hset('self_goods_extend', $goods_id, json_encode(["erp_number" => $collert["erp_number"], "update_time" => time()]));

        #推送型号到新wms
        (new ThirdApi())->pushNewWms($goods_id, 0);

        return [0, "成功"];
    }

    /*
     * 推送商品数据给新 wms
     */
    public function pushGoodsToWms($goods_ids = [])
    {
        foreach ($goods_ids as $goods_id) {
            if (strlen($goods_id) == 19) { //联营
                $f = (new ThirdApi())->pushLyWms($goods_id, request()->input("is_debug", 0));
                if ($f != "") {
                    return [1, $f];
                }
            } else { //自营
                (new ThirdApi())->pushNewWms($goods_id, request()->input("is_debug", 0));
            }
        }
        return [0, "成功"];
    }
}
