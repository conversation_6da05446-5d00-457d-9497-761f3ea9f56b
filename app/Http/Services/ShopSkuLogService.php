<?php

namespace App\Http\Services;

use Illuminate\Support\Arr;
use App\Http\Models\TaskLogModel;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Redis;
use App\Exceptions\InvalidRequestException;
use App\Http\Models\BigData\ShopBrandModel;
use App\Http\Models\BigData\ShopClassModel;
use App\Http\Models\BigData\ShopSkuPushLogModel;

class ShopSkuLogService
{
    public static function getShopSkuPushLogList($map)
    {
        $query = ShopSkuPushLogModel::with([
            'shop',
        ])->orderBy('id', 'desc');
        if (!empty($map['sku_id'])) {
            $query->where('sku_id', (int)$map['sku_id']);
        }
        if (!empty($map['platform'])) {
            $query->whereIn('platform', explode(',', $map['platform']));
        }
        if (!empty($map['push_status'])) {
            $query->whereIn('push_status', explode(',', $map['push_status']));
        }
        if (!empty($map['shop_id'])) {
            $query->whereIn('shop_id', explode(',', $map['shop_id']));
        }
        if (!empty($map['audit_status'])) {
            $query->whereIn('audit_status', explode(',', $map['audit_status']));
        }
        if (!empty($map['create_time'])) {
            $startTime = (int)strtotime(explode('~', $map['create_time'])[0]);
            $endTime = (int)strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }
        if (!empty($map['update_time'])) {
            $startTime = (int)strtotime(explode('~', $map['update_time'])[0]);
            $endTime = (int)strtotime(explode('~', $map['update_time'])[1]);
            $query->whereBetween('update_time', [$startTime, $endTime]);
        }
        $limit = Arr::get($map, 'limit', 10);
        $result = $query->paginate($limit);
        $result = $result ? $result->toArray() : [];
        //获取sku信息
        //判断错误信息中是否包含分类id和品牌id
        $classIds = [];
        $brandIds = [];
        $classIdNameMap = [];
        $brandIdNameMap = [];
        if (!empty($result['data'])) {
            foreach ($result['data'] as $value) {
                if (!empty($value['error_msg'])) {
                    preg_match('/\[(\d+)\].*?\[(\d+)\]/', $value['error_msg'], $matches);
                    if (!empty($matches)) {
                        $classId = $matches[1];
                        $brandId = $matches[2];

                        $classIds[] = $classId;
                        $brandIds[] = $brandId;
                    }
                }
            }
        }
        $classIdNameMap = ShopClassModel::whereIn('class_id', $classIds)->pluck('class_name', 'class_id')->toArray();
        $brandIdNameMap = ShopBrandModel::whereIn('brand_id', $brandIds)->pluck('brand_name', 'brand_id')->toArray();
        if (!empty($result)) {
            foreach ($result['data'] as $key => &$value) {
                preg_match('/\[(\d+)\].*?\[(\d+)\]/', $value['error_msg'], $matches);
                if (!empty($matches)) {
                    $classId = $matches[1];
                    $brandId = $matches[2];
                    $value['origin_class_name'] = $classIdNameMap[$classId] ?? '';
                    $value['origin_brand_name'] = $brandIdNameMap[$brandId] ?? '';
                }
                $skuCache = (new SkuService())->getSkuCacheInfo($value['sku_id']);
                $value['brand_name'] = $skuCache['standard_brand_name'] ?? '';
                $value['brand_id'] = $skuCache['standard_brand_id'] ?? '';
                $value['class_name'] = !empty($skuCache['class_name1']) ? $skuCache['class_name1'] . ' / ' . $skuCache['class_name2'] : '';
                $value['spu_name'] = $skuCache['spu_name'] ?? '';
                $value['platform_name'] = Arr::get(config('field.ShopPlatform'), $value['platform']);
                $value['create_time'] = $value['create_time'] ? date('Y-m-d H:i:s', $value['create_time']) : '';
                $value['update_time'] = $value['update_time'] ? date('Y-m-d H:i:s', $value['update_time']) : '';
                $value['push_status_name'] = Arr::get(config('field.ShopSkuPushStatus'), $value['push_status'], '无');
                $value['audit_status_name'] = Arr::get(config('field.ShopSkuAuditStatus'), $value['audit_status']);
                $value['sku_id'] = (string)$value['sku_id'];
            }
            unset($value);
        }
        $isExport = $map['is_export'] ?? 0;
        if ($isExport) {
            //判断如果是导出的数据,那么就要整理数据排好给导出使用
            $result['data'] = array_map(function ($log) {
                if (empty($log['id'])) {
                    return $log;
                }
                return [
                    "序号" => $log['id'] . "\t",
                    "外部平台" => $log['platform_name'] . "\t",
                    "店铺名称" => $log['shop']['shop_name'] ?? '',
                    "推送状态" => $log['push_status_name'],
                    "审核状态" => $log['audit_status_name'],
                    "猎芯分类名称" => $log['class_name'],
                    "SKUID" => $log['sku_id'] . "\t",
                    "商品型号" => $log['spu_name'],
                    "标准品牌" => $log['brand_name'],
                    "推送时间" => $log['create_time'],
                    "异常原因" => $log['error_msg'],
                ];
            }, $result['data']);
            return [
                'data' => Arr::get($result, 'data', []),
                'count' => Arr::get($result, 'total'),
                'total' => Arr::get($result, 'total'),
            ];
        } else {
            return [
                'data' => Arr::get($result, 'data', []),
                'count' => Arr::get($result, 'total'),
                'total' => Arr::get($result, 'total'),
            ];
        }
    }
}
