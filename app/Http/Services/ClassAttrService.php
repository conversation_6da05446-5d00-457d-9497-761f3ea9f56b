<?php


namespace App\Http\Services;

use Arr;
use Illuminate\Support\Str;
use App\Http\Models\SupplierModel;
use Illuminate\Support\Facades\DB;
use App\Http\Models\PoolClass\ClassAttrUnitModel;
use App\Http\Models\PoolClass\PoolClassAttrModel;
use App\Http\Models\PoolClass\ClassAttrValueModel;
use App\Http\Models\PoolClass\ClassAttrUnitConvertModel;
use App\Http\Models\PoolClass\ClassAttrUnitConvertItemModel;

class ClassAttrService
{
    public function getClassAttrList($collert, $limit)
    {
        $attrModel = new PoolClassAttrModel();
        $field = [
            'attr_id',
            'attr_name',
            'class_attr.show_name',
            'class_attr.class_id',
            'attr_unit_id',
            'class_attr.remark',
            'class_attr.status',
            'class_name',
            'dgk_attr_id',
            'is_main',
            'class_attr.add_time',
            'class_attr.create_name',
            'default_unit_convert_id',
            'insert_type',
            'preference'
        ];
        $data =  $attrModel->with(['unit_convert', 'UnitInfo'])->where($attrModel->map($collert))
            ->join('class as c', 'c.class_id', '=', 'class_attr.class_id')
            ->select($field)
            ->withCount('ValueNum')->orderBy('attr_id', 'desc')->paginate($limit)->toArray();

        foreach ($data['data'] as $key => $item) {
            $data['data'][$key]['standard_unit'] = self::getStandardUnitNameByDefaultUnitConvertId($item['default_unit_convert_id']);
        }
        return $data;
    }


    public static function getStandardUnitNameByDefaultUnitConvertId($defaultUnitConvertId)
    {
        if (empty($defaultUnitConvertId)) {
            return '';
        }
        $defaultUnitConvertId = explode(',', $defaultUnitConvertId);
        $defaultUnitConvertId = array_unique($defaultUnitConvertId, SORT_STRING);
        $unitConvert = ClassAttrUnitConvertModel::whereIn('id', $defaultUnitConvertId)->get()->toArray();
        $standardUnitName = [];
        foreach ($unitConvert as $item) {
            $standardUnitName[] = $item['standard_unit_name'];
        }
        return implode(' | ', $standardUnitName);
    }

    public function getClassAttrUnitList($collert)
    {
        $attrUnitId = Arr::get($collert, 'attr_unit_id');
        $attrId = Arr::get($collert, 'attr_id');
        unset($collert['attr_unit_id'], $collert['attr_id']);

        //查找参数默认单位的所有映射
        $attrUnitIdArr = false;
        if (!empty($attrId)) {
            $attrModel = new PoolClassAttrModel();
            $UnitArr = $attrModel->where('attr_id', '=', $attrId)->whereIn('class_attr.status', [1, 2])
                ->select('attr_unit_id_1', 'attr_unit_id_2')
                ->leftjoin('class_attr_unit_conversion as c', function ($join) {
                    $join->on('c.attr_unit_id_1', '=', 'attr_unit_id')->orOn('c.attr_unit_id_2', '=', 'attr_unit_id');
                })->get();

            if ($UnitArr) {
                $UnitArr = $UnitArr->toArray();
                //提取出所有的单位ID
                $attr_unit_id1 = array_column($UnitArr, 'attr_unit_id_1');
                $attr_unit_id2 = array_column($UnitArr, 'attr_unit_id_2');
                $attrUnitIdArr = array_unique(array_merge($attr_unit_id1, $attr_unit_id2));
            }
        }

        $unitModel = new ClassAttrUnitModel();
        $list = $unitModel->where($unitModel->map($collert))->where(function ($query) use ($attrUnitIdArr) {
            if ($attrUnitIdArr) {
                $query->whereIn('attr_unit_id', $attrUnitIdArr);
            }
        })->select('attr_unit_id', 'attr_unit_name')->paginate(10)->toArray();
        $list = Arr::only($list, ['data', 'total', 'last_page']);
        if (!empty($attrUnitId)) {
            $unit = $unitModel->where('attr_unit_id', '=', $attrUnitId)->select(
                'attr_unit_id',
                'attr_unit_name'
            )->first();
            if ($unit) {
                $unit = $unit->toArray();

                if ($unit['attr_unit_id'] == $attrUnitId) {
                    $result = Arr::first($list['data'], function ($key, $value) use ($unit, &$keys) {
                        $keys = $key;
                        return $value['attr_unit_id'] === $unit['attr_unit_id'];
                    }, false);
                    if (!$result) {
                        $unit['selected'] = true;
                        $list['data'][] = $unit;
                    } else {
                        $list['data'][$keys]['selected'] = true;
                    }
                }
            }
        }
        return $list;
    }

    //根据分类id获取所有属性列表(属性名称)
    public function getAttrListByClassId($classId)
    {
        $attrList = PoolClassAttrModel::where('class_id', $classId)->get()->toArray();
        return $attrList;
    }

    //分离参数并且校验
    public function extractAttr($attrId, $value)
    {
        $attr = PoolClassAttrModel::where('attr_id', $attrId)->first()->toArray();
        $insertType = $attr['insert_type'];
        $unitConvertId = $attr['default_unit_convert_id'];
        $standardUnitName = '';
        $unitConvertIdArr = [];
        $unitConvert = [];

        //因为这里要兼容多单位，所以需要把单位转换成数组,然后找出对应的标准单位
        if (str_contains($unitConvertId, ',')) {
            $unitConvertIdArr = explode(',', $unitConvertId);
            $unitConvertList = ClassAttrUnitConvertItemModel::whereIn('convert_id', $unitConvertIdArr)->get()->toArray();
            $unitName = AttrPregService::extractUnit($value);
            //找出对应的标准单位
            foreach ($unitConvertList as $item) {
                if ($item['unit_name'] == $unitName) {
                    $unitConvert = $item;
                    $standardUnitName = $item['standard_unit_name'];
                    $unitConvertId = $item['convert_id'];
                    break;
                }
            }
        } else {
            if ($unitConvertId) {
                $unitConvert = ClassAttrUnitConvertItemModel::where('convert_id', $unitConvertId)->first()->toArray();
                $standardUnitName = $unitConvert['unit_name'];
            }
        }
        $attrNameFormat = ' (' . $attr['attr_name'] . ') ';

        switch ($insertType) {
            //单数值：数字（数字或者数字前面带个“-”号）+字母或者特殊符号（单位），前面的一定要是数字（数字或者数字前面带个“-”号），后面的一定要是字母或者特殊符号（单位），并且校验后面的“字母或者特殊符号（单位）”必须在此参数的默认单位里存在的；
            //5V、-5V、10Ω
            case 1:
                return ClassAttrExtractService::handleSingle($value, $attrNameFormat, $standardUnitName, $unitConvertId, '单数值');

                //范围值：此类型可以填写“范围值”也可以是“单数值”，范围值是由2个单数值组成以“~”符号进行区分如2V~5V；
                //2V~5V、4V、-2V~5V、-2V~-5V、-4V、-20°C ~ 85°C
                //±20%,单数值 这些情况也有,需要做好兼容
            case 2:
                return ClassAttrExtractService::handleRange($value, $attrNameFormat, $standardUnitName, $unitConvertId, '范围值');

                //单数值+文本：此类型使用“@”符号进行区分“单数值”还是“文本”，@符号前面的为单数值，后面的为文本；
                //5mA@15V 5mA
                //也要兼容单数值
            case 3:
                return ClassAttrExtractService::handleSingleAndText($value, $attrNameFormat, $standardUnitName, $unitConvertId, '单数值+文本,也支持单数值');

                //范围值+文本：此类型使用“@”符号进行区分“范围值”还是“文本”，@符号前面的为范围值，后面的为文本；
                //-65°C~175°C@TJ 10H~20H
            case 4:
                return ClassAttrExtractService::handleRangeAndText($value, $attrNameFormat, $standardUnitName, $unitConvertId, '范围值+文本,也支持单数值');

                //多个单数值录入逻辑，每个“单数值”之间使用“€”符号进行分隔开，原有的单数值校验规则不变；
                //多个单数值录入举例：5V€10V€15V  5V
            case 6:
                return ClassAttrExtractService::handleMultiSingle($value, $attrNameFormat, $standardUnitName, $unitConvertId, '多个单数值,支持单个单数值');

                //多个范围值录入逻辑，每个“范围值”之间使用“€”符号进行分隔开，原有的范围值校验规则不变；
                //多个范围值录入举例：2V~3V€4V~5V / 2V~3V / 2V /±5V
            case 7:
                return ClassAttrExtractService::handleMultiRange($value, $attrNameFormat, $standardUnitName, $unitConvertId, '多个范围值值,支持单数值');

                //单数值+文本录入方式增加支持多个单数值+文本录入逻辑，每个“单数值+文本”之间使用“€”符号进行分隔开，原有的单数值+文本校验规则不变；
                //多个单数值+文本录入举例：5mA@15V€15mA@20V
            case 8:
                return ClassAttrExtractService::handleMultiSingleAndText($value, $attrNameFormat, $standardUnitName, $unitConvertId, '录入方式为多个单数值+文本');

                //范围值+文本录入方式增加支持多个范围值+文本录入逻辑，每个“范围值+文本”之间使用“€”符号进行分隔开，原有的范围值+文本校验规则不变；
                //多个范围值+文本录入举例：-65°C~175°C@TJ€200°C~300°C@VB
            case 9:
                //先去分割
                if (!str_contains($value, '€')) {
                    return '录入方式为多个范围值,每个“ 范围值+文本 ”之间使用“€”符号进行分隔开';
                }
                $data = [];
                $items = explode('€', $value);

                foreach ($items as $value) {
                    if (empty($value)) {
                        return '值对应属性' . $attrNameFormat . '录入方式为多个范围值+文本,参数值不完整';
                    }
                    if (!Str::contains($value, '@')) {
                        return '值对应属性' . $attrNameFormat . '录入方式为范围值+文本,但该属性值应该包含特殊符号@';
                    }
                    $valueMinUnit = $valueMaxUnit = '';
                    $explodedData = explode('@', $value);
                    if (count($explodedData) != 2) {
                        return '值对应属性' . $attrNameFormat . '录入方式为范围值+文本,但该属性值包含超过1个@符号';
                    }
                    $rangeAttrValue = $explodedData[0];
                    $attrText = '@' . $explodedData[1];
                    if (!Str::contains($rangeAttrValue, '~')) {
                        return '值对应属性' . $attrNameFormat . '录入方式为范围值+文本,但该属性值范围部分没有 ~ 符号';
                    }
                    $valueMin = $valueMax = 0;
                    $rangeAttrValues = explode('~', $rangeAttrValue);
                    foreach ($rangeAttrValues as $key => $itemValue) {
                        $itemValue = trim($itemValue);
                        $purNumber = AttrPregService::extractPurNumber($itemValue);
                        if (!is_numeric($purNumber[0])) {
                            return '值对应属性' . $attrNameFormat . '录入方式为范围值+文本,' . ($key == 0 ? '左边值' : '右边值') . '不符合规范';
                        }
                        //然后去匹配单位是否存在
                        //$unitName = trim(str_replace($purNumber, '', $itemValue));
                        $unitName = AttrPregService::extractUnit($itemValue);

                        if ($key == 0) {
                            $valueMin = $purNumber[0];
                            $valueMinUnit = $unitName;
                        } else {
                            $valueMax = $purNumber[0];
                            $valueMaxUnit = $unitName;
                        }
                        if (!ClassAttrUnitConvertItemModel::where('convert_id', $unitConvertId)->where('unit_name', $unitName)->exists()) {
                            return '值对应属性' . $attrNameFormat . '录入方式为范围值+文本,但' . ($key == 0 ? '左边值' : '右边值') . '对应的属性单位不属于该属性的默认单位列表中';
                        }
                    }

                    // $originValue = $value;
                    // if (!empty($attrText)) {
                    //     //判断里面是否包含$attrText
                    //     if (!str_contains($originValue, $attrText)) {
                    //         $originValue = $originValue . $attrText;
                    //     }
                    // }

                    $data[] = [
                        'value_min' => $valueMin,
                        'value_min_unit' => $valueMinUnit,
                        'value_max' => $valueMax,
                        'value_max_unit' => $valueMaxUnit,
                        'standard_unit' => $standardUnitName,
                        'standard_unit_id' => $unitConvertId,
                        'origin_value' => $value,
                        'attr_text' => $attrText,
                    ];
                }
                return $data;
                //不做校验；
            default:
                break;
        }
        return [
            'value' => $value,
            'value_unit' => '',
            'origin_value' => $value,
            'unit' => '',
        ];
    }
}
