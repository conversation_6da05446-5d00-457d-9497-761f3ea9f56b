<?php


namespace App\Services;

use App\Model\PoolSupplierChannelModel;
use App\Model\UploadSkuItemModel;
use App\Model\UploadSkuModel;
use Excel;
use Illuminate\Support\Facades\Storage;

class UploadSkuService
{
    //验证数据是否正常
    public function validaUploadExcelData($filePath, $encoded)
    {
        $result = true;
        //先去读取excel文件
        Excel::load($filePath, function ($reader) use ($encoded,$result) {
            //取几行做样本处理即可
            $checkData = $reader->limitRows(3)->toArray();
            if (!empty($checkData)) {
                if (count($checkData[0]) > 38) {
                    $result =  "请检查上传的excel列,是否有不符合模板的excel列(一般是前面和后面几列)";
                }
                $supplerCode = $checkData[1][9];
                //检查内部编码和供应商编码是否对应
                $supplierModel = new PoolSupplierChannelModel();
                $channelUid = $supplierModel->where('supplier_code', $supplerCode)->value('channel_uid');
                if (strpos($channelUid, $encoded) === false) {
                    $result = "存在内部人员编码和供应商编码不对应的数据";
                }
            }
        });
        return $result;
    }



    /**
     * @param $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function DownRes($request){
        $up_sn=$request->input("up_sn");
        if(!$up_sn || $up_sn==""){
            return $this->apiReturn(10404, 'up_sn必传');
        }
        $model=new UploadSkuModel();
        $res=$model->GetUploadInfo($up_sn);
        if(empty($res)){
            return $this->apiReturn(10404, '单号不存在');
        }
        if($res["status"]!=3){
            if($res["upload"]==""){
                return $this->apiReturn(10404, '文件不存在请联系管理员');
            }
            $filename=$res["upload"];
            $fileinfo = pathinfo($filename);
            header('Content-type: application/x-'.$fileinfo['extension']);
            header('Content-Disposition: attachment; filename='.$res["file_name"]);
            readfile($filename);
            die;
            //return response()->download($res["upload"], $res["file_name"]);
        }else{//重新排列

            $url=$this->DownResCsvLib($up_sn);
            $fileinfo = pathinfo($url);
            header('Content-type: application/x-'.$fileinfo['extension']);
            header('Content-Disposition: attachment; filename='.$fileinfo["basename"]);
            readfile($url);
            die;
        }

    }

    //下载源文件

    /**
     * 通用导出
     */
    public function batchZhuanmaiSku($up_sn,$create_uid){
        $params = [
            "type" => 2,  // 类型 1:模板调用 2: api回调 （必填）
            "source_items_id" => 31, //设置来源明细id：http://data.ichunt.net/database/1199（必填）
            "file_name" => 'sku批量导出',//导出后文件名称（必填）
            "excel_suf" => "csv", //导出文件格式 csv,xls（必填）
            "header" => ['是否成功','SPU状态','状态描述','商品型号','制造商','库存数量','最小起订量(MOQ)','标准包装量（MPQ）','封装','批次','大陆交期','香港交期','简短描述','物料编码','显示类型','精选','阶梯数量1','国内含税价1（￥）','香港交货价1（$）','阶梯数量2','国内含税价2（￥）','香港交货价2（$）','阶梯数量3','国内含税价3（￥）','香港交货价3（$）','阶梯数量4','国内含税价4（￥）','香港交货价4（$）','阶梯数量5','国内含税价5（￥）','香港交货价5（$）','阶梯数量6','国内含税价6（￥）','香港交货价6（$）','阶梯数量7','国内含税价7（￥）','香港交货价7（$）','阶梯数量8','国内含税价8（￥）','香港交货价8（$）','阶梯数量9','国内含税价9（￥）','香港交货价9（$）','spu_id','sku_id'], //导出文件头部 （必填，不得用 ID 做头部，数据顺序必须一致）
            "query_param" => ["p"=>1,"up_sn"=>$up_sn], //p 第几页，limit每页多少条 占位符，照抄不需要改 （必填）
            "callbackurl" => ($_SERVER['SERVER_PORT'] == 443 ? 'https' : 'http') . "://" . $_SERVER['HTTP_HOST'] . "/service", //hrpose 数据提供网址（提供导出脚本分页回调获取数据，必填）
            "callbackfuc" => "batchZhuanmaiSku", //hrpose 回调函数（必填）
            "create_uid" => $create_uid, #创建人id（必填）
        ];

        // 调用导出系统
        $url = Config('website.export_url');
        $client = new \Hprose\Http\Client($url . "/insertExport", false);
        $resO = $client->insertExport(json_encode($params));
        $res = json_decode($resO, true);
        return $res;


    }


    //下载上传结果
    public function DownResCsv($up_sn){

        $filename = date('Y-m-d-H-i-s') . '-' . uniqid() . '.csv';
        $x = fopen(storage_path() . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR . $filename,
            'w');
        $res=$this->title=Config('api.EXCEL_TITLE');
        $res[-3] = '是否成功';
        $res[-2] = 'SPU状态';
        $res[-1] = '状态描述';
        $res[] = 'spu_id';
        $res[] = 'sku_id';

        ksort($res);
        foreach ($res as $k => $v) {
            $res[$k] = '"'.$v.'"';
        }
        print_r(implode(",",$res));
        die();

        fputcsv($x, $res);

        $model=new UploadSkuItemModel();
        $res2=$model->GetListByCsv($up_sn);

        foreach ($res2 as $item){
            $csvData=$item["upload_data"];
            switch ($item["status"]){
                case 1:
                    $csvData[-3]="失败";
                    $csvData[-2] = '未知';
                    $csvData[-1] = $item["error_msg"]?$item["error_msg"]:"";;
                    break;
                case 2:
                    $csvData[-3]="待审核";//不会走这一步
                    $csvData[-2] = '未知';
                    $csvData[-1] = '未知';
                    break;
                case 3://通过
                    $csvData[-3]="审核通过";
                    $csvData[-2] = '不存在';
                    if($item["spu_status"]==1){
                        $csvData[-2] = '存在';
                    }
                    $csvData[-1] = 'sku更新成功';
                    if($item["sku_status"]==1){
                        $csvData[-1] = '新增成功';
                    }
                    break;
                case 4:
                    $csvData[-3]="拒绝";
                    $csvData[-2] = '未知';
                    $csvData[-1] = $item["reject_msg"]?$item["reject_msg"]:"";
                    break;

            }
            $csvData[] = (string)$item['spu_id'] . "\n";
            $csvData[] = (string)$item['sku_id'] . "\n";
            foreach ($csvData as $k => $v) {
                $csvData[$k] = iconv('utf-8', 'GBK', $v);
            }
            ksort($csvData);
            fputcsv($x, $csvData);
        }
        $result=json_decode(UploadToOss(storage_path().DIRECTORY_SEPARATOR.'app'.DIRECTORY_SEPARATOR.'uploads'.DIRECTORY_SEPARATOR.$filename), true);
        if (empty($result['code']) || $result['code'] != 200 || empty($result['data'][0])) {
            return false;
        }
        return $result['data'][0];
        $url = $this->SaveDownFile($filename);
        Storage::disk('uploads')->delete($filename);
        if($url==false){
            return $this->apiReturn(10404, '文件下载出错-oss');
        }
        return $url;
    }
}


if (empty($FileName)) {
    return false;
}
$result=json_decode(UploadToOss(storage_path().DIRECTORY_SEPARATOR.'app'.DIRECTORY_SEPARATOR.'uploads'.DIRECTORY_SEPARATOR.$FileName), true);
if (empty($result['code']) || $result['code'] != 200 || empty($result['data'][0])) {
    return false;
}
return $result['data'][0];