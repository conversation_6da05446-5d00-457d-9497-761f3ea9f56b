<?php


namespace App\Http\Services;

use App\Exports\AttrImportTemplateExport;
use App\Http\Models\PoolClass\ClassAttrUnitModel;
use App\Http\Models\PoolClass\ClassAttrValueModel;
use App\Http\Models\PoolClass\PoolClassAttrModel;
use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Models\SupplierModel;
use Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class ClassService
{
    //获取所有参数单位
    public function generateAttrTemplate($classId)
    {
        $className = PoolClassModel::where('class_id', $classId)->value('class_name');
        $result = [];
        $header = [];
        $attrList = PoolClassAttrModel::where('class_id', $classId)->get()->toArray();
        foreach ($attrList as $attr) {
            $column = $attr['attr_name'];
            $column .= "(" . Arr::get(config('field.AttrInsertType'), $attr['insert_type'], '无') . ")";
            $header[] = $column;
        }
        array_unshift($header, "SPUID($classId)");
        $className = str_replace('/', '|', $className);
        $result[] = $header;
        return Excel::download(new AttrImportTemplateExport($classId, $result), $className . ' - 分类参数模板.xlsx');
    }

    //修改为mro分类
    public function changeClassType($classId, $classType)
    {
        $result = PoolClassModel::where('class_id', $classId)->update([
            'update_time' => time(),
            'class_type' => $classType,
        ]);
        if ($result) {
            PoolClassModel::where('parent_id', $classId)->update([
                'update_time' => time(),
                'class_type' => $classType,
            ]);
        }
        if ($result) {
            $classList = PoolClassModel::where('class_id', $classId)->orWhere('parent_id', $classId)->get()->toArray();
            foreach ($classList as $class) {
                (new PoolClassModel())->setClassCache($class['class_id'], $class);
            }
        }
        return $result;
    }

    public function getClassList($classType)
    {
        $redis = Redis::connection('sku');
        $allClassCache = $redis->hgetall('pool_class_info');
        $allClassCache = array_map(function ($value) {
            return json_decode($value, true);
        }, $allClassCache);
        if ($classType) {
            $allClassCache = array_filter($allClassCache, function ($class) use ($classType) {
                if ($class['status'] != 1) {
                    return false;
                }
                if ($classType == 1) {
                    if (!isset($class['class_type']) || ($class['class_type'] == $classType)) {
                        return true;
                    }
                }
                if (isset($class['class_type']) && $class['class_type'] == $classType) {
                    return true;
                }

                return false;
            });
        }

        $allClassCache = array_values($allClassCache);
        usort($allClassCache, function ($a, $b) {
            return $a['class_id'] > $b['class_id'];
        });

        //进行一二级归类
        $categories = $allClassCache;
        $tree = [];
        $children = [];

        // 首先，将所有分类按parent_id分组
        foreach ($categories as $category) {
            $children[$category['parent_id']][] = $category;
        }

        // 其次，构建层次化结构
        foreach ($categories as $category) {
            if ($category['parent_id'] == 0) { // 一级分类
                if (isset($children[$category['class_id']])) {
                    $category['children'] = $children[$category['class_id']];
                }
                $tree[] = $category;
            }
        }
        return $tree ?: [];
    }

    //获取分类名称
    public static function getClassNameFromCache($classId)
    {
        $redis = Redis::connection('sku');
        $class = $redis->hget('pool_class_info', $classId);
        if (empty($class)) {
            return '';
        }
        $class = json_decode($class, true);
        return $class['class_name'];
    }

    //获取多个分类
    public static function getClassListByClassIdsFromCache($classIds)
    {
        if (empty($classIds)) {
            return [];
        }
        if (!is_array($classIds)) {
            $classIds = explode(',', $classIds);
        }
        $redis = Redis::connection('sku');
        $classList = $redis->hmget('pool_class_info', $classIds);
        $classList = array_map(function ($value) {
            return json_decode($value, true);
        }, $classList);
        return $classList;
    }


    //获取一级分类
    public static function getParentClassForXmSelect()
    {
        $classList = PoolClassModel::select([
            'parent_id',
            'class_id',
            'class_name',
            'class_type'
        ])->where('parent_id', 0)->get()->toArray();

        $result = [];
        foreach ($classList as $class) {
            $result[] = [
                'name' => $class['class_name'],
                'value' => $class['class_id']
            ];
        }
        return $result;
    }

    //根据子分类获取父级分类名称
    public static function getParentClassNameByClassId($classId)
    {
        $parentId = PoolClassModel::where('class_id', $classId)->value('parent_id');
        if ($parentId == 0) {
            $parentClassName = PoolClassModel::where('class_id', $classId)->value('class_name');
        } else {
            $parentClassName = PoolClassModel::where('class_id', $parentId)->value('class_name');
        }
        return $parentClassName;
    }

    public static function getAllClassListForXmSelect()
    {
        $classList = PoolClassModel::select([
            'parent_id',
            'class_id',
            'class_name',
            'class_type'
        ])->get()->toArray();

        $result = [];
        $parentClasses = [];

        // 先获取所有一级分类
        foreach ($classList as $class) {
            if ($class['parent_id'] == 0) {
                $parentClasses[$class['class_id']] = $class['class_name'];
            }
        }

        // 获取二级分类并拼接一级分类名称
        foreach ($classList as $class) {
            if ($class['parent_id'] != 0) {
                $parentName = $parentClasses[$class['parent_id']] ?? '';
                $result[] = [
                    'name' => $parentName . ' | ' . $class['class_name'],
                    'value' => $class['class_id']
                ];
            }
        }

        return $result;
    }

    public static function getTopClassListForXmSelect()
    {
        $classList = PoolClassModel::where('parent_id', 0)->get()->toArray();
        $result = [];
        foreach ($classList as $class) {
            $result[] = [
                'name' => $class['class_name'],
                'value' => $class['class_id']
            ];
        }
        return $result;
    }
}
