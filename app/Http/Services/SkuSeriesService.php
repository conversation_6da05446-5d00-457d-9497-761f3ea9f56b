<?php

namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\SkuSeriesItemModel;
use App\Http\Models\SkuSeriesModel;
use App\Http\Transformers\SkuSeriesTransformer;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;

class SkuSeriesService
{

    public static function getSkuSeriesList($map)
    {
        $query = SkuSeriesModel::withCount('series_item')->orderBy('id', 'desc');
        if (!empty($map['series_name'])) {
            $query->where('series_name', 'like', '%' . $map['series_name'] . '%');
        }
        if (!empty($map['class_id1'])) {
            if (empty($map['class_id2'])) {
                $query->where('class_id1', $map['class_id1']);
            } else {
                $query->where('class_id2', $map['class_id2']);
            }
        }
        if (!empty($map['standard_brand_id'])) {
            $value = explode(',', $map['standard_brand_id']);
            $query->whereIn('standard_brand_id', $value);
        }
        if (!empty($map['status'])) {
            $query->where('status', $map['status']);
        }
        if (!empty($map['abnormal_count'])) {
            if ($map['abnormal_count'] > 0) {
                $query->where('abnormal_count', '>', 0);
            } else {
                $query->where('abnormal_count', 0);
            }
        }
        $limit = Arr::get($map, 'limit', 10);
        $result = $query->orderBy('create_time', 'desc')->paginate($limit);
        $result = $result ? $result->toArray() : [];
        $result['data'] = (new SkuSeriesTransformer())->listTransformer($result['data']);
        unset($value);
        return [
            'data' => Arr::get($result, 'data', []),
            'count' => Arr::get($result, 'total'),
            'total' => Arr::get($result, 'total'),
        ];
    }

    public static function saveSkuSeries($data)
    {
        if (empty($data['id'])) {
            if (SkuSeriesModel::where('series_name', $data['series_name'])->exists()) {
                throw new InvalidRequestException('该系列名称已经存在');
            }
            $data['create_time'] = time();
            $data['create_uid'] = request()->user->userId;
            $data['create_name'] = request()->user->name;
            $seriesId = SkuSeriesModel::insertGetId($data);
        } else {
            if (SkuSeriesModel::where('series_name', $data['series_name'])
                ->where('id', '!=', $data['id'])->exists()) {
                throw new InvalidRequestException('该系列名称已经存在');
            }
            $data['update_time'] = time();
            $seriesId = $data['id'];
            SkuSeriesModel::where('id', $data['id'])->update($data);
        }
        self::saveSkuSeriesCache($seriesId);
        return true;
    }

    public static function updateSkuSeriesStatus($id, $status)
    {
        SkuSeriesModel::where('id', $id)->update([
            'status' => $status,
            'update_time' => time()
        ]);

        return self::saveSkuSeriesCache($id);
    }

    public static function saveSkuSeriesCache($seriesId)
    {
        $redis = Redis::connection('sku');
        $series = SkuSeriesModel::where('id', $seriesId)->first()->toArray();
        $seriesSkuIds = SkuSeriesItemModel::where('series_id', $seriesId)->pluck('sku_id')->toArray();
        $series['sku_ids'] = $seriesSkuIds ?: [];
        $redis->hset('sku_series', $seriesId, json_encode($series));
        return true;
    }

    public static function getSkuSeriesItemList($map)
    {
        $query = SkuSeriesItemModel::orderBy('id', 'desc');

        if (!empty($map['is_abnormal'])) {
            $query->where('is_abnormal', $map['is_abnormal']);
        }

        if (!empty($map['series_id'])) {
            $query->where('series_id', $map['series_id']);
        }

        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }

        $limit = Arr::get($map, 'limit', 10);
        $result = $query->paginate($limit);
        $result = $result ? $result->toArray() : [];
        $result['data'] = (new SkuSeriesTransformer())->itemListTransformer($result['data']);
        unset($value);

        return [
            'data' => Arr::get($result, 'data', []),
            'count' => Arr::get($result, 'total'),
            'total' => Arr::get($result, 'total'),
        ];
    }

    //批量删除商品系列商品
    public static function batchDeleteSkuSeriesItem($ids)
    {
        if (empty($ids)) {
            return true;
        }
        $skuIds = SkuSeriesItemModel::whereIn('id', $ids)->pluck('sku_id')->toArray();
        //SkuService::batchUpdateSkuUpdateTime($skuIds);
        $seriesId = SkuSeriesItemModel::where('id', $ids[0])->value('series_id');
        SkuSeriesItemModel::whereIn('id', $ids)->delete();
        self::saveSkuSeriesCache($seriesId);
        return true;
    }

    //检测是否能批量绑定
    public static function checkCanBatchUpdateItemSeries($skuIds)
    {
        $classId1 = $classId2 = $sBrandId = [];
        if ($skuIds) {
            foreach ($skuIds as $skuId) {
                $sku = (new SkuService())->getSkuCacheInfo($skuId);
                if (empty($sku['class_id1'])) {
                    throw new InvalidRequestException('一级分类为空的不能设置商品系列');
                }
                //if (empty($sku['class_id2'])) {
                //    throw new InvalidRequestException('二级分类为空的不能设置商品系列');
                //}
                if (empty($sku['s_brand_id'])) {
                    throw new InvalidRequestException('标准品牌为空的不能设置商品系列');
                }
                $classId1[] = $sku['class_id1'];
                $classId2[] = $sku['class_id2'];
                $sBrandId[] = $sku['s_brand_id'];
            }
        }
        $classId1 = array_unique($classId1);
        $classId2 = array_unique($classId2);
        $sBrandId = array_unique($sBrandId);
        if (count($classId1) > 1 || count($classId2) > 1) {
            throw new InvalidRequestException('要同一个分类的商品才能一起绑定商品系列');
        }
        if (count($sBrandId) > 1) {
            throw new InvalidRequestException('要同一个标准品牌的商品才能一起绑定商品系列');
        }
    }

    //批量修改商品系列商品的系列
    public static function batchUpdateItemSeries($ids, $skuIds, $seriesId)
    {
        $redis = Redis::connection('sku');
        $series = SkuSeriesModel::where('id', $seriesId)->first()->toArray();
        //判断这些skuid对应得分类和标品id是否合法,比如要和系列得保持一致
        if ($ids) {
            $skuIds = SkuSeriesItemModel::whereIn('id', $ids)->pluck('sku_id')->toArray();
        }
        if ($skuIds) {
            foreach ($skuIds as $skuId) {
                $sku = (new SkuService())->getSkuCacheInfo($skuId);
                if ($sku['class_id1'] != $series['class_id1'] || $sku['class_id2'] != $series['class_id2'] || $sku['s_brand_id'] != $series['standard_brand_id']) {
                    throw new InvalidRequestException('绑定系列的sku必须和所属系列的一级分类,二级分类,标准品牌完全一致');
                }
            }
        }
        if ($skuIds) {
            $existsSkuIds = SkuSeriesItemModel::whereIn('sku_id', $skuIds)->pluck('sku_id')->toArray();
            SkuSeriesItemModel::whereIn('sku_id', $existsSkuIds)->update([
                'series_id' => $seriesId,
                'update_time' => time(),
            ]);
            $nonExistSkuIds = array_diff($skuIds, $existsSkuIds);
            $data = array_map(function ($skuId) use ($seriesId) {
                return [
                    'series_id' => $seriesId,
                    'create_time' => time(),
                    'sku_id' => $skuId,
                ];
            }, $nonExistSkuIds);
            //先删除已经存在的,走覆盖路线
            foreach ($data as $item) {
                $skuId = $item['sku_id'];
                SkuSeriesItemModel::where('sku_id', $skuId)->delete();
            }
            SkuSeriesItemModel::insert($data);
            self::saveSkuSeriesCache($seriesId);
        }
        if ($ids) {
            SkuSeriesItemModel::whereIn('id', $ids)->update([
                'series_id' => $seriesId,
                'update_time' => time(),
            ]);
            self::saveSkuSeriesCache($seriesId);
        }

        //还要去批量修改sku更新时间,方便es更新
        SkuService::batchUpdateSkuUpdateTime($skuIds);

        return true;
    }

    //根据sku_id获取系列,并且获取相关的sku_id
    public function getSkuSeriesBySkuId($skuId)
    {
        $redis = Redis::connection('sku');
        $seriesId = SkuSeriesItemModel::where('sku_id', $skuId)->value('series_id');
        $status = SkuSeriesModel::where('id', $seriesId)->value('status');
        if (empty($seriesId) || $status == -1) {
            return [];
        }
        $skuSeries = $redis->hget('sku_series', $seriesId);
        //里面的sku列表也返回部分信息
        if ($skuSeries) {
            $skuSeries = json_decode($skuSeries, true);
            $skuIds = $skuSeries['sku_ids'];
            $seriesItemGroupBySkuId = SkuSeriesItemModel::whereIn('sku_id', $skuIds)->get()->keyBy('sku_id')->toArray();
            $skuList = [];
            foreach ($skuIds as $skuId) {
                //批量获取商品信息,目前只要商品id和商品名称
                $sku = $redis->hget('sku', $skuId);
                $sku = json_decode($sku, true);
                $spu = $redis->hget('spu', $sku['spu_id']);
                $goodsName = Arr::get($sku, 'goods_name');
                if (!empty($seriesItemGroupBySkuId[$skuId]['show_name'])) {
                    $goodsName = $seriesItemGroupBySkuId[$skuId]['show_name'];
                }
                $skuList[] = [
                    'goods_id' => (string)$skuId,
                    'goods_name' => $goodsName,
                    'order' => $seriesItemGroupBySkuId[$skuId]['order'] ?? 100,
                    'goods_images' => Arr::get($sku, 'goods_images') ?: Arr::get($spu, 'images_l'),
                ];
            }

            usort($skuList, function ($a, $b) {
                return $a['order'] > $b['order'];
            });

            $skuSeries['sku_list'] = $skuList;
            if (!empty($skuSeries['sku_ids'])) {
                $skuSeries['sku_ids'] = array_map(function ($value) {
                    return (string)$value;
                }, $skuSeries['sku_ids']);
            }
        }
        return $skuSeries;
    }

    public static function batchGetSkuSeriesBySkuIds($skuIds)
    {
        if (empty($skuIds)) {
            return [];
        }
        if (!is_array($skuIds)) {
            $skuIds = explode(',', $skuIds);
        }
        $skuSeriesItemList = SkuSeriesItemModel::with('series')->whereIn('sku_id', $skuIds)->get()->keyBy('sku_id')->toArray();
        return $skuSeriesItemList;
    }
}
