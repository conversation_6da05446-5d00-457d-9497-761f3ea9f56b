<?php

namespace App\Http\Services;

use App\Http\Models\TrainingDataModel;
use App\Http\Transformers\TrainingDataTransformer;
use App\Imports\TrainingDataImport;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class TrainingDataService
{
    /**
     * 设置需要重新训练BOM数据状态
     */
    private function setNeedRetrainingStatus()
    {
        $redis = Redis::connection('training_data');
        $redis->set('need_retraining_bom_data_status', 1);
    }

    /**
     * 获取MongoDB集合名称
     */
    private function getMongoCollectionName($type)
    {
        switch ($type) {
            case TrainingDataModel::TYPE_BRAND:
                return 'bom_brand_train_data';
            case TrainingDataModel::TYPE_MODEL:
                return 'bom_gn_train_data';
            case TrainingDataModel::TYPE_CATEGORY:
                return 'bom_cate_train_data';
            default:
                return null;
        }
    }

    /**
     * 获取MongoDB字段名称
     */
    private function getMongoFieldName($type)
    {
        switch ($type) {
            case TrainingDataModel::TYPE_BRAND:
                return 'brand';
            case TrainingDataModel::TYPE_MODEL:
                return 'gn';
            case TrainingDataModel::TYPE_CATEGORY:
                return 'cate';
            default:
                return null;
        }
    }

    /**
     * 同步数据到MongoDB
     */
    private function syncToMongo($type, $content, $operation = 'insert', $oldContent = null)
    {
        $collectionName = $this->getMongoCollectionName($type);
        $fieldName = $this->getMongoFieldName($type);

        if (!$collectionName || !$fieldName) {
            return false;
        }

        $mongo = DB::connection('mongodb');

        switch ($operation) {
            case 'insert':
                // 检查是否已存在
                $exists = $mongo->table($collectionName)
                    ->where($fieldName, $content)
                    ->exists();

                if (!$exists) {
                    $mongo->table($collectionName)->insert([
                        $fieldName => $content,
                        'is_manual' => 1
                    ]);
                }
                break;

            case 'update':
                if ($oldContent && $oldContent !== $content) {
                    // 删除旧记录
                    $mongo->table($collectionName)
                        ->where($fieldName, $oldContent)
                        ->where('is_manual', 1)
                        ->delete();

                    // 插入新记录（如果不存在）
                    $exists = $mongo->table($collectionName)
                        ->where($fieldName, $content)
                        ->exists();

                    if (!$exists) {
                        $mongo->table($collectionName)->insert([
                            $fieldName => $content,
                            'is_manual' => 1
                        ]);
                    }
                }
                break;

            case 'delete':
                // 只删除手动添加的记录
                $mongo->table($collectionName)
                    ->where($fieldName, $content)
                    ->where('is_manual', 1)
                    ->delete();
                break;
        }

        return true;
    }
    public function getTrainingDataList($map)
    {
        $model = new TrainingDataModel();
        $query = $model->orderBy('id', 'desc');
        $query = $this->filter($query, $map);
        $limit = Arr::get($map, 'limit', 15);
        $result = $query->paginate($limit);

        $result = $result ? $result->toArray() : [];
        $transformer = new TrainingDataTransformer();
        $result['data'] = $transformer->listTransformer($result['data']);
        return [
            'data' => Arr::get($result, 'data', []),
            'count' => Arr::get($result, 'total'),
        ];
    }

    public function filter($query, $map)
    {
        if (!empty($map['content'])) {
            $query->where('content', 'like', '%' . $map['content'] . '%');
        }
        if (!empty($map['type'])) {
            $query->where('type', $map['type']);
        }
        if (!empty($map['create_name'])) {
            $query->where('create_name', 'like', '%' . $map['create_name'] . '%');
        }
        return $query;
    }

    public function saveTrainingData($map)
    {
        $model = new TrainingDataModel();
        $id = Arr::get($map, 'id');
        $oldContent = null;

        // 如果是更新操作，先获取原内容
        if ($id) {
            $oldData = $model->where('id', $id)->first();
            $oldContent = $oldData ? $oldData->content : null;
        }

        $data = [
            'content' => $map['content'],
            'type' => $map['type'],
            'update_time' => time(),
            'update_name' => request()->user->name ?? 'admin',
        ];

        if ($id) {
            // 更新
            $result = $model->where('id', $id)->update($data);
            // 同步到MongoDB
            if ($result) {
                $this->syncToMongo($map['type'], $map['content'], 'update', $oldContent);
            }
        } else {
            // 新增
            $data['create_time'] = time();
            $data['create_name'] = request()->user->name ?? 'admin';
            $result = $model->insert($data);
            // 同步到MongoDB
            if ($result) {
                $this->syncToMongo($map['type'], $map['content'], 'insert');
            }
        }

        // 数据有变动，设置需要重新训练状态
        if ($result) {
            $this->setNeedRetrainingStatus();
        }

        return $result;
    }

    public function deleteTrainingData($id)
    {
        $model = new TrainingDataModel();

        // 先获取要删除的数据信息
        $trainingData = $model->where('id', $id)->first();
        if (!$trainingData) {
            return false;
        }

        $result = $model->where('id', $id)->delete();

        // 数据有变动，同步到MongoDB和设置需要重新训练状态
        if ($result) {
            $this->syncToMongo($trainingData->type, $trainingData->content, 'delete');
            $this->setNeedRetrainingStatus();
        }

        return $result;
    }

    public function getTrainingDataById($id)
    {
        $model = new TrainingDataModel();
        $trainingData = $model->where('id', $id)->first();
        return $trainingData ? $trainingData->toArray() : [];
    }

    public function importTrainingData($file, $type)
    {
        try {
            $path = $file->store('temp');
            $import = new TrainingDataImport($type);
            Excel::import($import, $path);
            \Storage::delete($path);

            // 导入成功，设置需要重新训练状态
            $this->setNeedRetrainingStatus();

            return [
                'success' => true,
                'message' => '导入成功'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => '导入失败：' . $e->getMessage()
            ];
        }
    }
}
