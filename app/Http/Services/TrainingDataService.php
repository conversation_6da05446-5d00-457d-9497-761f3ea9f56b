<?php

namespace App\Http\Services;

use App\Http\Models\TrainingDataModel;
use App\Http\Transformers\TrainingDataTransformer;
use App\Imports\TrainingDataImport;
use Illuminate\Support\Arr;
use Maatwebsite\Excel\Facades\Excel;

class TrainingDataService
{
    public function getTrainingDataList($map)
    {
        $model = new TrainingDataModel();
        $query = $model->orderBy('id', 'desc');
        $query = $this->filter($query, $map);
        $limit = Arr::get($map, 'limit', 15);
        $result = $query->paginate($limit);

        $result = $result ? $result->toArray() : [];
        $transformer = new TrainingDataTransformer();
        $result['data'] = $transformer->listTransformer($result['data']);
        return [
            'data' => Arr::get($result, 'data', []),
            'count' => Arr::get($result, 'total'),
        ];
    }

    public function filter($query, $map)
    {
        if (!empty($map['content'])) {
            $query->where('content', 'like', '%' . $map['content'] . '%');
        }
        if (!empty($map['type'])) {
            $query->where('type', $map['type']);
        }
        if (!empty($map['create_name'])) {
            $query->where('create_name', 'like', '%' . $map['create_name'] . '%');
        }
        return $query;
    }

    public function saveTrainingData($map)
    {
        $model = new TrainingDataModel();
        $id = Arr::get($map, 'id');
        $data = [
            'content' => $map['content'],
            'type' => $map['type'],
            'update_time' => time(),
            'update_name' => request()->user->name ?? 'admin',
        ];

        if ($id) {
            // 更新
            $result = $model->where('id', $id)->update($data);
        } else {
            // 新增
            $data['create_time'] = time();
            $data['create_name'] = request()->user->name ?? 'admin';
            $result = $model->insert($data);
        }

        return $result;
    }

    public function deleteTrainingData($id)
    {
        $model = new TrainingDataModel();
        return $model->where('id', $id)->delete();
    }

    public function getTrainingDataById($id)
    {
        $model = new TrainingDataModel();
        $trainingData = $model->where('id', $id)->first();
        return $trainingData ? $trainingData->toArray() : [];
    }

    public function importTrainingData($file, $type)
    {
        try {
            $path = $file->store('temp');
            $import = new TrainingDataImport($type);
            Excel::import($import, $path);
            \Storage::delete($path);
            
            return [
                'success' => true,
                'message' => '导入成功'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => '导入失败：' . $e->getMessage()
            ];
        }
    }
}
