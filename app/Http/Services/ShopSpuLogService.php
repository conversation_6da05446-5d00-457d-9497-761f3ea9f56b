<?php

namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\BigData\ShopSpuPushLogModel;
use App\Http\Models\TaskLogModel;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class ShopSpuLogService
{
    public static function getShopSpuPushLogList($map)
    {
        $query = ShopSpuPushLogModel::orderBy('id', 'desc');
        if (!empty($map['spu_id'])) {
            $query->where('spu_id', (int)$map['spu_id']);
        }
        if (!empty($map['task_code'])) {
            $query->where('task_code', $map['task_code']);
        }
        if (!empty($map['platform'])) {
            $query->whereIn('platform', array_map('intval', explode(',', $map['platform'])));
        }
        if (!empty($map['verify_status'])) {
            $query->where('verify_status', (int)$map['verify_status']);
        }
        if ((!empty($map['push_status']) || (isset($map['push_status']) && $map['push_status'] === '0'))) {
            $query->whereIn('status', array_map('intval', explode(',', $map['push_status'])));
        }

        if (!empty($map['create_time'])) {
            $startTime = (int)strtotime(explode('~', $map['create_time'])[0]);
            $endTime = (int)strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }

        if (!empty($map['push_time'])) {
            $startTime = (int)strtotime(explode('~', $map['push_time'])[0]);
            $endTime = (int)strtotime(explode('~', $map['push_time'])[1]);
            $query->whereBetween('push_time', [$startTime, $endTime]);
        }

        if (!empty($map['update_time'])) {
            $startTime = (int)strtotime(explode('~', $map['update_time'])[0]);
            $endTime = (int)strtotime(explode('~', $map['update_time'])[1]);
            $query->whereBetween('update_time', [$startTime, $endTime]);
        }
        $limit = Arr::get($map, 'limit', 10);
        $result = $query->paginate($limit);
        $result = $result ? $result->toArray() : [];
        if (!empty($result)) {
            $redis = Redis::connection('sku');
            foreach ($result['data'] as $key => &$value) {
                $standardBrandCache = json_decode($redis->hget('standard_brand', $value['brand_id']), true);
                $value['brand_name'] = $standardBrandCache['brand_name'] ?? '';
                $value['platform_name'] = Arr::get(config('field.ShopPlatform'), $value['platform']);
                $value['create_time'] = $value['create_time'] ? date('Y-m-d H:i:s', $value['create_time']) : '';
                $value['push_time'] = $value['push_time'] ? date('Y-m-d H:i:s', $value['push_time']) : '';
                $value['push_status_name'] = Arr::get(config('field.ShopSpuPushStatus'), $value['status']);
                $value['verify_status_name'] = config('field.ShopSpuVerifyStatus')[$value['verify_status']] ?? '无';
                $value['spu_id'] = (string)$value['spu_id'];
            }
            unset($value);
        }
        $isExport = $map['is_export'] ?? 0;
        if ($isExport) {
            //判断如果是导出的数据,那么就要整理数据排好给导出使用
            $result['data'] = array_map(function ($log) {
                if (empty($log['id'])) {
                    return $log;
                }
                return [
                    "序号" => $log['id'] . "\t",
                    "外部平台" => $log['platform_name'] . "\t",
                    "推送任务号" => $log['task_code'] ?? '',
                    "批次号" => '',
                    "推送状态" => $log['push_status_name'],
                    "第三方校验状态" => $log['verify_status_name'],
                    "SPU_ID" => $log['spu_id'] . "\t",
                    "商品型号" => $log['goods_name'],
                    "标准品牌" => $log['brand_name'],
                    "推送时间" => $log['create_time'],
                    "异常原因" => $log['error_msg'],
                ];
            }, $result['data']);
            return [
                'data' => Arr::get($result, 'data', []),
                'count' => Arr::get($result, 'total'),
                'total' => Arr::get($result, 'total'),
            ];
        } else {
            return [
                'data' => Arr::get($result, 'data', []),
                'count' => Arr::get($result, 'total'),
                'total' => Arr::get($result, 'total'),
            ];
        }
    }

    public static function exportShopSpuPushLogList($map)
    {
        $data = self::getShopSpuPushLogList($map);
    }
}
