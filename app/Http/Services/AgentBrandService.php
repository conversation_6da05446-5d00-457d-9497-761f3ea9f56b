<?php

namespace App\Http\Services;

//供应链品牌服务
use App\Exceptions\InvalidRequestException;
use App\Http\Models\AgentBrandSpuModel;
use App\Http\Models\BrandModel;
use App\Http\Models\AgentBrandModel;
use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Models\StandardBrandModel;
use App\Http\Transformers\AgentBrandSpuTransformer;
use App\Http\Transformers\AgentBrandTransformer;
use Illuminate\Support\Facades\Redis;

class AgentBrandService
{

    public function getAgentBrandList($map)
    {
        $model = new AgentBrandModel();
        $query = $model->with(['standard_brand'])->withCount('spu')->orderBy('id', 'desc');
        $query = $this->filter($query, $map);
        $limit = \Arr::get($map, 'limit', 15);

        $result = $query->paginate($limit);

        $result = $result ? $result->toArray() : [];
        $transformer = new AgentBrandTransformer();
        $result['data'] = $transformer->listTransformer($result['data']);
        return [
            'data' => \Arr::get($result, 'data', []),
            'count' => \Arr::get($result, 'total'),
        ];
    }

    public function getAgentBrandSpuList($map)
    {
        $model = new AgentBrandSpuModel();
        $query = $model->orderBy('id', 'desc');

        $query = $query->where('agent_brand_id', $map['agent_brand_id']);

        $limit = \Arr::get($map, 'limit', 15);
        $result = $query->paginate($limit);

        $result = $result ? $result->toArray() : [];
        $transformer = new AgentBrandSpuTransformer();
        $result['data'] = $transformer->listTransformer($result['data']);
        return [
            'data' => \Arr::get($result, 'data', []),
            'count' => \Arr::get($result, 'total'),
        ];
    }

    public function filter($query, $map)
    {

        if (!empty($map['standard_brand_name'])) {
            $query->whereHasIn('standard_brand', function ($q) use ($map) {
                $q->where('brand_name', 'like', "%{$map['standard_brand_name']}%");
            });
        }

        if (!empty($map['pm_user_id'])) {
            $query->where('pm_user_id', $map['pm_user_id']);
        }

        if (!empty($map['status'])) {
            $query->where('status', $map['status']);
        }

        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }

        if (!empty($map['agent_brand_level'])) {
            $map['agent_brand_level'] = trim($map['agent_brand_level'], ',');
            $map['agent_brand_level'] = explode(',', $map['agent_brand_level']);
            $query->whereIn('agent_brand_level', $map['agent_brand_level']);
        }

        if (!empty($map['brand_area'])) {
            $map['brand_area'] = trim($map['brand_area'], ',');
            $map['brand_area'] = explode(',', $map['brand_area']);
            $query->whereHasIn('standard_brand', function ($q) use ($map) {
                $q->whereIn('brand_area', $map['brand_area']);
            });
        }

        if (!empty($map['supplier_code'])) {
            $query->where('supplier_code', 'like', "%{$map['supplier_code']}%");
        }

        if (!empty($map['application_area'])) {
            $regexStr = '(' . str_replace(',', '|', $map['application_area']) . ')';
            $query->whereRaw("application_area REGEXP '${regexStr}'");
        }

        return $query;
    }

    //保存代理品牌
    public function saveAgentBrand($data)
    {
        //先找出pm用户名
        $pmUserName = (new CmsUserInfoModel())->getUserName($data['pm_user_id']);
        $data['pm_user_name'] = $pmUserName;
        if (empty($data['id'])) {
            unset($data['id']);
            $data['create_time'] = time();
            $data['create_name'] = request()->user->name;
            $data['create_uid'] = request()->user->userId;
            return AgentBrandModel::insert($data);
        } else {
            $data['update_time'] = time();
            $data['update_name'] = request()->user->name;
            $data['update_uid'] = request()->user->userId;
            return AgentBrandModel::where('id', $data['id'])->update($data);
        }
    }

    public function saveAgentBrandSpu($data)
    {
        if (empty($data['id'])) {
            unset($data['id']);
            if (AgentBrandSpuModel::where('spu_name', $data['spu_name'])
                ->where('agent_brand_id', $data['agent_brand_id'])->exists()
            ) {
                throw new InvalidRequestException('该产品已经存在,请修改后提交');
            }
            $data['create_time'] = time();
            $data['create_name'] = request()->user->name;
            $data['create_uid'] = request()->user->userId;
            $result = AgentBrandSpuModel::insert($data);
        } else {
            if (AgentBrandSpuModel::where('spu_name', $data['spu_name'])
                ->where('agent_brand_id', $data['agent_brand_id'])->where('id', '!=', $data['id'])->exists()
            ) {
                throw new InvalidRequestException('该产品已经存在,请修改后提交');
            }
            $data['update_time'] = time();
            $data['update_name'] = request()->user->name;
            $data['update_uid'] = request()->user->userId;
            $result = AgentBrandSpuModel::where('id', $data['id'])->update($data);
        }
        return $result;
    }

    public function changeAgentBrandStatus($id, $status)
    {
        return AgentBrandModel::where('id', $id)->update([
            'update_time' => time(),
            'update_name' => request()->user->name,
            'update_uid' => request()->user->userId,
            'status' => $status,
        ]);
    }

    public function getAgentBrandInfo($id)
    {
        $agentBrand = AgentBrandModel::where('id', $id)->first()->toArray();
        $standardBrand = StandardBrandModel::where('standard_brand_id', $agentBrand['standard_brand_id'])->first()->toArray();
        $agentBrand = array_merge($agentBrand, $standardBrand);
        return $agentBrand;
    }

    public function deleteAgentBrandSpu($id)
    {
        return AgentBrandSpuModel::where('id', $id)->delete();
    }

    public function batchDeleteAgentBrandSpu($ids)
    {
        return AgentBrandSpuModel::whereIn('id', $ids)->delete();
    }

    public function exportAgentBrand($map)
    {
        $query = AgentBrandModel::with(['standard_brand'])->withCount('spu')->orderBy('id', 'desc');
        $result = $query->get()->toArray();
        return $result;
    }
}
