<?php

namespace App\Http\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use App\Http\Models\DistributeUserModel;
use App\Exceptions\InvalidRequestException;
use App\Http\Models\DistributeUserPriceModel;
use App\Http\Models\DistributeUserPriceRuleModel;
use App\Http\Transformers\DistributeUserPriceTransformer;

class DistributeUserPriceService
{
    //获取客户价格分发规则列表
    public function getDistributeUserPriceList($map)
    {
        $limit = $map['limit'] ?? 10;
        $query = DistributeUserModel::with('price')->orderBy('id', 'desc');

        if (!empty($map['name'])) {
            $query->where('name', 'like', '%' . $map['name'] . '%');
        }

        if (!empty($map['status'])) {
            $query->where('status', $map['status']);
        }

        if (!empty($map['supplier_id'])) {
            $map['supplier_id'] = trim($map['supplier_id'], ',');
            $map['supplier_id'] = explode(',', $map['supplier_id']);
            // 这里需要连接规则表来查询
            $query->whereHas('price', function ($q) use ($map) {
                $q->whereIn('supplier_id', $map['supplier_id']);
            });
        }

        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }

        $result = $query->paginate($limit)->toArray();

        $transformer = new DistributeUserPriceTransformer();
        $result['data'] = $transformer->listTransformer($result['data']);

        return [
            'data' => \Arr::get($result, 'data', []),
            'count' => \Arr::get($result, 'total'),
        ];
    }

    //保存客户价格分发规则主表
    public function saveDistributeUserPrice($params, $priceItemList, $isCopy)
    {
        try {
            DB::beginTransaction();

            if (!empty($params['id']) && !$isCopy) {
                if (request()->user->userId == 1000) {
                    if (!empty($params['app_key'] && !empty($params['app_secret']))) {
                        $params['app_key'] = $params['app_key'];
                        $params['app_secret'] = $params['app_secret'];
                    }
                }
                $params['update_time'] = time();
                $params['update_name'] = request()->user->name;
                DistributeUserModel::where('id', $params['id'])->update($params);
                $id = $params['id'];
                $newIds = [];
                foreach ($priceItemList as $priceItem) {
                    $exists = DistributeUserPriceModel::where('id', $priceItem['price_id'])->exists();
                    if ($exists) {
                        $itemId = $priceItem['price_id'];
                        unset($priceItem['price_id']);
                        $priceItem['update_time'] = time();
                        $priceItem['update_name'] = request()->user->name;
                        DistributeUserPriceModel::where('id', $itemId)->update($priceItem);
                        $newIds[] = $itemId;
                    } else {
                        unset($priceItem['price_id']);
                        $priceItem['user_id'] = $id;
                        $priceItem['create_time'] = time();
                        $priceItem['create_name'] = request()->user->name;
                        $newIds[] = DistributeUserPriceModel::insertGetId($priceItem);
                    }
                }
                DistributeUserPriceModel::whereNotIn('id', $newIds)->where('user_id', $id)->delete();
            } else {
                // 检查是否已存在相同的用户名
                $exists = DistributeUserModel::where('user_name', $params['user_name'])->exists();

                if ($exists) {
                    throw new InvalidRequestException('该客户已经存在,不能新增');
                }

                unset($params['id']);
                $params['create_time'] = time();
                $params['status'] = $params['status'] ?? 1;
                $params['create_name'] = request()->user->name;
                $params['app_key'] = $this->generateAppKey();
                $params['app_secret'] = $this->generateAppSecret($params['app_key'], $params['user_name']);
                $id = DistributeUserModel::insertGetId($params);

                foreach ($priceItemList as $priceItem) {
                    // 创建价格分发规则
                    $priceParams = [
                        'user_id' => $id,
                        'supplier_id' => $priceItem['supplier_id'],
                        'supplier_code' => $priceItem['supplier_code'],
                        'agreement_price_coefficient' => $priceItem['agreement_price_coefficient'],
                        'guide_price_coefficient' => $priceItem['guide_price_coefficient'],
                        'create_time' => time(),
                        'create_name' => request()->user->name

                    ];
                    DistributeUserPriceModel::insert($priceParams);
                }
            }


            DB::commit();
            return $id;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    //删除客户价格分发规则
    public function deleteDistributeUserPrice($id)
    {
        try {
            DB::beginTransaction();
            DistributeUserModel::where('id', $id)->delete();
            DistributeUserPriceModel::where('user_id', $id)->delete();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    //修改状态
    public function updateStatus($id, $status)
    {
        try {
            DB::beginTransaction();

            // 更新主表状态
            DistributeUserModel::where('id', $id)->update(['status' => $status]);

            DB::commit();

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }


    //生成APP_KEY
    private function generateAppKey()
    {
        $str = "123456789ABCDEFGHIJKLMNPQRSTUVWXYZ";
        $length = 32; // 设定默认长度为32位

        do {
            $result = '';
            for ($i = 0; $i < $length; $i++) {
                $result .= $str[random_int(0, strlen($str) - 1)];
            }

            // 检查是否同时包含数字和字母
            $hasNumber = preg_match('/[1-9]/', $result);
            $hasLetter = preg_match('/[A-Z]/', $result);
        } while (!$hasNumber || !$hasLetter); // 如果不同时包含数字和字母，则重新生成

        return $result;
    }

    //生成APP_SECRET
    private function generateAppSecret($appKey, $userName)
    {
        // 获取当前时间戳
        $timestamp = time();

        // 生成一个安全的随机字符串 (32字符的十六进制字符串)
        $randomString = bin2hex(random_bytes(16));

        // 拼接原始数据
        $rawData = $appKey . $timestamp . $randomString . $userName;

        // 使用SHA-256加密
        $appSecret = hash('sha256', $rawData);

        return $appSecret;
    }
}
