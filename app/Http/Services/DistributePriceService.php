<?php

namespace App\Http\Services;

use Illuminate\Support\Arr;
use App\Http\Models\SupplierModel;
use Illuminate\Support\Facades\Redis;
use App\Http\Models\DistributePriceModel;
use App\Exceptions\InvalidRequestException;
use App\Http\Models\DistributeUserPriceModel;
use App\Http\Models\Supplier\SupplierChannelModel;
use App\Http\Transformers\DistributePriceTransformer;

class DistributePriceService
{
    //获取分发规则列表
    public function getDistributePriceList($map)
    {
        $limit = $map['limit'] ?? 10;
        $query = DistributePriceModel::with('supplier')->orderBy('id', 'desc');
        if (!empty($map['supplier_id'])) {
            $map['supplier_id'] = trim($map['supplier_id'], ',');
            $map['supplier_id'] = explode(',', $map['supplier_id']);
            $query->whereIn('supplier_id', $map['supplier_id']);
        }
        if (!empty($map['status'])) {
            $query->where('status', $map['status']);
        }
        if (!empty($map['canal'])) {
            $query->where('canal', 'like', '%' . $map['canal'] . '%');
        }
        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }
        $result = $query->paginate($limit)->toArray();

        $transformer = new DistributePriceTransformer();
        $result['data'] = $transformer->listTransformer($result['data']);

        return [
            'data' => \Arr::get($result, 'data', []),
            'count' => \Arr::get($result, 'total'),
        ];
    }

    //保存分发规则
    public static function saveDistributePrice($params, $isCopy = 0)
    {
        //判断supplier_id是否存在
        $exists = DistributePriceModel::where('supplier_id', $params['supplier_id'])->where('id', '!=', $params['id'])->exists();
        if ($exists) {
            throw new InvalidRequestException('该渠道已经存在,不能新增');
        }

        if (!empty($params['id']) && $isCopy != 1) {
             //因为更新了专营的编码
             if ($params['supplier_id'] == 17) {
                DistributeUserPriceModel::where('supplier_id', 17)->update([
                    'supplier_code' => $params['canal']
                ]);
            }
            $params['update_time'] = time();
            $params['update_uid'] = request()->user->userId;
            $params['update_name'] = request()->user->name;
            DistributePriceModel::where('id', $params['id'])->update($params);
            $id = $params['id'];
        } else {
            $exists = DistributePriceModel::where('supplier_id', $params['supplier_id'])->exists();
            if ($exists) {
                throw new InvalidRequestException('该渠道已经存在,不能新增');
            }

            unset($params['id']);
            $params['create_time'] = time();
            $params['status'] = 1;
            $params['create_uid'] = request()->user->userId;
            $params['create_name'] = request()->user->name;
            $id = DistributePriceModel::insertGetId($params);
        }
        //存到redis
        $key = 'distribute_price_list';
        $redis = Redis::connection('sku');
        $redis->hset($key, $params['supplier_id'], json_encode($params));
        return $id;
    }

    //删除分发规则
    public static function deleteDistributePrice($id)
    {
        $supplierId = DistributePriceModel::where('id', $id)->value('supplier_id');
        $distributePrice = DistributePriceModel::where('id', $id)->delete();
        $key = 'distribute_price_list';
        $redis = Redis::connection('sku');
        $redis->hdel($key, $supplierId);
        return $distributePrice;
    }

    //修改状态
    public static function updateStatus($id, $status)
    {
        $distributePrice = DistributePriceModel::where('id', $id)->update(['status' => $status]);
        return $distributePrice;
    }

    //获取启用状态下的所有供应商
    public static function getSupplierList()
    {
        $data = DistributePriceModel::where('status', 1)->get()->toArray();
        //整合渠道和供应商
        $result = [];
        foreach ($data as $key => $value) {
            $result[] = [
                'supplier_id' => $value['supplier_id'],
                'supplier_name' => SupplierModel::where('supplier_id', $value['supplier_id'])->value('supplier_name') ?: '猎芯自营',
                'agreement_price_coefficient' => $value['agreement_price_coefficient'],
                'guide_price_coefficient' => $value['guide_price_coefficient'],
            ];
        }
        //供应商
        return $result;
    }
}
