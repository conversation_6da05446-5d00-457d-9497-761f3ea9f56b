<?php

namespace App\Http\Services;

use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Models\SpuExtraModel;
use App\Http\Models\SpuLogModel;
use Arr;
use Illuminate\Support\Facades\DB;

class SpuExtraService
{
    public static function saveSpuExtra($spuId, $spuExtra)
    {
        if (empty($spuId) || !SpuExtraModel::where('spu_id',$spuId)->exists()) {
            $dbRes = SpuExtraModel::insert([
                'spu_id' => $spuId,
                'standard_lead_time' => $spuExtra['standard_lead_time'],
                'mounting_type' => $spuExtra['mounting_type'],
                'length' => $spuExtra['length'],
                'width' => $spuExtra['width'],
                'height' => $spuExtra['height'],
                'weight' => $spuExtra['weight'],
                'reach_status' => $spuExtra['reach_status'],
                'customs_code' => $spuExtra['customs_code'],
                'create_time' => time(),
            ]);
        } else {
            $dbRes = SpuExtraModel::where('spu_id', $spuId)->update([
                'standard_lead_time' => $spuExtra['standard_lead_time'],
                'mounting_type' => $spuExtra['mounting_type'],
                'length' => $spuExtra['length'],
                'width' => $spuExtra['width'],
                'height' => $spuExtra['height'],
                'weight' => $spuExtra['weight'],
                'reach_status' => $spuExtra['reach_status'],
                'customs_code' => $spuExtra['customs_code'],
                'update_time' => time(),
            ]);
        }

        //操作mongo
        if ($dbRes) {
            self::saveSpuExtraToMongo($spuId, $spuExtra);
        }

        return $dbRes;
    }

    //保存一些额外信息到spu,不是很重要的,给mongo用
    public static function saveSpuExtraToMongo($spuId, $spuExtra)
    {
        unset($spuExtra['spu_image_list']);

        if (!empty($spuExtra['image_list']) && !is_array($spuExtra['image_list'])) {
            $spuExtra['image_list'] = json_decode($spuExtra['image_list'], true);
        }
        $spuExtra['spu_id'] = $spuId;
        $data = $spuExtra;
        $mongo = DB::connection('mongodb');
        $exist = $mongo->table('spu_extra')->where('spu_id', $spuId)->exists();
        if ($exist) {
            $mongo->table('spu_extra')->where('spu_id', $spuId)->update($data);
        } else {
            $mongo->table('spu_extra')->insert($data);
        }
    }
}
