<?php

namespace App\Http\Services;

//供应链品牌服务
use App\Http\Models\BrandModel;
use App\Http\Models\ScmBrandModel;
use App\Http\Transformers\ScmBrandTransformer;
use Illuminate\Support\Facades\Redis;

class ScmBrandService
{

    public function getScmBrandList($map, $isExport = false)
    {
        $model = new ScmBrandModel();
        $query = $model->orderBy('scm_brand_id', 'desc');

        if (!empty($map['erp_brand_name'])) {
            $query->where('erp_brand_name', 'like', "${map['erp_brand_name']}%");
        }

        if (!empty($map['add_time'])) {
            $startTime = strtotime(explode('~', $map['add_time'])[0]);
            $endTime = strtotime(explode('~', $map['add_time'])[1]);
            $query->whereBetween('add_time', [$startTime, $endTime]);
        }

        if ($isExport) {
            $result = $query->get()->toArray();
            $transformer = new ScmBrandTransformer();
            $result = $transformer->listTransformer($result);
            return $result;
        }

        $limit = \Arr::get($map, 'limit', 15);
        $result = $query->paginate($limit);



        $result = $result ? $result->toArray() : [];
        $transformer = new ScmBrandTransformer();
        $result['data'] = $transformer->listTransformer($result['data']);
        return [
            'data' => \Arr::get($result, 'data', []),
            'count' => \Arr::get($result, 'total'),
        ];
    }

    public function getScmBrandWithMappingCount($scmBrandId)
    {
        return ScmBrandModel::where('scm_brand_id', '=', $scmBrandId)
            ->select(['erp_brand_name', 'scm_brand_id'])->withCount('mappingCount')->first()->toArray();
    }
}
