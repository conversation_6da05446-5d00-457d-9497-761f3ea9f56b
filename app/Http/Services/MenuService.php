<?php

namespace App\Http\Services;

use App\Http\Models\Cms\ConfigModel;

class MenuService
{
    // 获取菜单
    public static function getMenu($name = '新版基石系统')
    {
        $userMenuList = [];
        $user = request()->user;
        $userId = $user->userId;
        $email = $user->email;
        $roleList = PermService::getUserRoles($userId, $email);
        if (PermService::hasRole(PermService::ROLE_ADMIN, $roleList)) {
            $userMenuList = ConfigModel::getMenusByName($name);
        } else {
            $userPerms = PermService::getUserPerms();
            if ($userPerms) {
                $menuList = ConfigModel::getMenusByName($name);
                if ($menuList) {
                    $userMenuList = self::getUserMenuList($menuList, $userPerms);
                }
            }
        }
        $indexMenu = [
            'title' => '首页',
            'href' => config('website.dashboard_domain'),
            'class' => '',
            'childs' => [],
        ];
        array_unshift($userMenuList, $indexMenu);
        return $userMenuList;
    }

    // 获取用户菜单列表，会获取全部菜单，然后遍历判断用户是否有权限，如果没有权限，那么会删除
    public static function getUserMenuList($menuList, $userPerms)
    {
        foreach ($menuList as $i => &$menuItem) {
            // 如果有子菜单，那么循环遍历判断
            if ($menuItem['childs']) {
                foreach ($menuItem['childs'] as $j => $child) {
                    $permId = PermService::getPermId($child['href']);
                    if (!in_array($permId, $userPerms)) {
                        unset($menuItem['childs'][$j]);
                    }
                }
                if (empty($menuItem['childs'])) {
                    unset($menuList[$i]);
                }
            } else {
                // 如果只是一级菜单，判断用户是否有权限
                $permId = PermService::getPermId($menuItem['href']);
                if (!in_array($permId, $userPerms)) {
                    unset($menuList[$i]);
                }
            }
        }
        return array_values($menuList);//保证索引从0开始
    }
}
