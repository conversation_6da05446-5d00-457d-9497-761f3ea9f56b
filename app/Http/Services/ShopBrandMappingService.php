<?php

namespace App\Http\Services;

use App\Http\Models\TaskLogModel;
use App\Jobs\ImportShopBrandMapping;
use App\Http\Models\StandardBrandModel;
use App\Exceptions\InvalidRequestException;
use App\Http\Models\BigData\ShopBrandModel;
use App\Http\Models\BigData\ShopBrandMappingModel;
use App\Http\Transformers\ShopBrandMappingTransformer;

class ShopBrandMappingService
{
    public static function getShopBrandMappingList($map)
    {
        $limit = request()->get('limit', 10);
        $model = new ShopBrandMappingModel();
        $query = $model->with(['lie_brand', 'shop_brand'])->orderBy('id', 'desc');
        if (!empty($map['lie_brand_name'])) {
            $lieBrandIdList = StandardBrandModel::where('brand_name', 'like', '%' . $map['lie_brand_name'] . '%')->pluck('standard_brand_id')->toArray();
            $query->whereIn('lie_brand_id', $lieBrandIdList);
        }
        if (!empty($map['shop_brand_name'])) {
            $shopBrandIdList = ShopBrandModel::where('brand_name', 'like', '%' . $map['shop_brand_name'] . '%')->pluck('id')->toArray();
            $query->whereIn('shop_brand_id', $shopBrandIdList);
        }
        if (!empty($map['brand_id'])) {
            //先找出对应的所有内部品牌id
            $shopBrandIdList = ShopBrandModel::where('brand_id', $map['brand_id'])->pluck('id')->toArray();
            $query->whereIn('shop_brand_id', $shopBrandIdList);
        }

        if (!empty($map['shop_brand_id'])) {
            $query->where('shop_brand_id', $map['shop_brand_id']);
        }

        if (!empty($map['lie_brand_id'])) {
            $query->where('lie_brand_id', $map['lie_brand_id']);
        }

        if (!empty($map['platform'])) {
            $query->where('platform', $map['platform']);
        }

        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }

        if (!empty($map['is_export'])) {
            $list = $query->get()->toArray();
            $transformer = new ShopBrandMappingTransformer();
            return $transformer->listTransformer($list);
        }

        $list = $query->paginate($limit)->toArray();
        $transformer = new ShopBrandMappingTransformer();
        $list['data'] = $transformer->listTransformer($list['data']);
        return [
            'data' => $list['data'],
            'count' => $list['total'],
        ];
    }

    public static function saveShopBrandMapping($data)
    {
        //判断是否已经存在
        if (empty($data['id'])) {
            if (ShopBrandMappingModel::where('shop_brand_id', $data['shop_brand_id'])
                ->where('lie_brand_id', $data['lie_brand_id'])
                ->where('platform', $data['platform'])->exists()) {
                throw new InvalidRequestException('该标品已经和对应的第三方品牌绑定,不能重复绑定');
            }
        } else {
            if (ShopBrandMappingModel::where('shop_brand_id', $data['shop_brand_id'])
                ->where('lie_brand_id', $data['lie_brand_id'])
                ->where('platform', $data['platform'])->where('id', '!=', $data['id'])->exists()) {
                throw new InvalidRequestException('该标品已经和对应的第三方品牌绑定,不能重复绑定');
            }
        }

        if (empty($data['id'])) {
            return ShopBrandMappingModel::insert([
                'shop_brand_id' => $data['shop_brand_id'],
                'lie_brand_id' => $data['lie_brand_id'],
                'platform' => $data['platform'],
                'create_time' => time(),
                'create_uid' => request()->user->userId,
                'create_name' => request()->user->name,
            ]);
        } else {
            return ShopBrandMappingModel::where('id', $data['id'])->update([
                'shop_brand_id' => $data['shop_brand_id'],
                'lie_brand_id' => $data['lie_brand_id'],
                'update_time' => time(),
                'platform' => $data['platform'],
                'update_uid' => request()->user->userId,
                'update_name' => request()->user->name,
            ]);
        }
    }

    public static function deleteShopBrandMapping($id)
    {
        return ShopBrandMappingModel::where('id', $id)->delete();
    }

    public static function importShopBrandMapping($file)
    {
        if (empty($file)) {
            throw new InvalidRequestException('请上传文件');
        }
        if ($file->isValid()) {
            $clientName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            if ($extension != "xlsx") {
                throw new InvalidRequestException('上传文件非法');
            }
            $newName = 'update_sku_' . md5(date('ymdhis') . $clientName) . "." . $extension;
            $uploadDir = storage_path() . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR;
            $path = $uploadDir . $newName;
            $file->move($uploadDir, $newName);
            $adminId = request()->user->userId;
            $adminName = request()->user->name;
            $logModel = new TaskLogModel();
            $logData = [
                'file_name' => $clientName,
                'log' => date('Y-m-d H:i:s', time()) . " 开始上传处理映射,准备丢入异步队列,文件名为 : " . $clientName,
                'status' => 0,
                'admin_id' => $adminId,
                'admin_name' => $adminName,
                'create_time' => time(),
                'update_time' => time(),
                'type' => TaskLogModel::TYPE_IMPORT_SHOP_BRAND_MAPPING,
            ];
            return \DB::connection('spu')->transaction(function () use ($logModel, $logData, $path) {
                $logId = $logModel->insertGetId($logData);
                $hostParts = explode('.', request()->getHost());
                $mainDomain = implode('.', array_slice($hostParts, -2));
                if ($mainDomain != 'ichunt.net') {
                    (new ImportShopBrandMapping($logId, $path))->handle();
                    $result = true;
                } else {
                    // $result = ImportShopBrandMapping::dispatch($logId, $path);
                    (new ImportShopBrandMapping($logId, $path))->handle();
                    $result = true;
                }
                if (!$result) {
                    throw new InvalidRequestException('推送上传任务失败');
                }
                return $result;
            });

        } else {
            throw new InvalidRequestException('上传文件非法');
        }
    }
}
