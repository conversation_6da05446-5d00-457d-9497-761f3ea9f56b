<?php


namespace App\Http\Services;


use App\Exceptions\InvalidRequestException;
use App\Http\Models\AlikeSpuLogModel;
use App\Http\Models\BrandModel;
use App\Http\Models\PoolClass\ClassAttrValueModel;
use App\Http\Models\PoolClass\PoolClassAttrModel;
use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Models\SampleLogModel;
use App\Http\Models\TaskLogModel;
use App\Http\Queue\RabbitQueueModel;
use App\Imports\AlikeSpuImport;
use App\Imports\UpdateSpuClassImport;
use App\Jobs\UpdateSpu;
use App\Jobs\UpdateSpuAttr;
use App\Jobs\UpdateSpuPDF;
use App\Jobs\UploadAlikeSpu;
use App\Jobs\UploadSample;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class SpuService
{
    /*
     * 获取spu列表
     * @param types 导出类型：1 spu列表导出 2 spu参数导出 3 SPU型号+标准品牌 去重导出
     */
    public function getSpuList($request, $types = 0)
    {
        $list = [];
        $map = $request->input();

        if ((!empty($map['create_time']))) {
            $times = explode('~', $map['create_time']);
            $startTime = strtotime($times[0]) ?: 1420041600;
            $endTime = strtotime($times[1]) ?: time();
            $map['create_time/range'] = $startTime . ',' . $endTime;
        }

        if ((!empty($map['update_time']))) {
            $times = explode('~', $map['update_time']);
            $startTime = strtotime($times[0]) ?: 1420041600;
            $endTime = strtotime($times[1]) ?: time();
            $map['update_time/range'] = $startTime . ',' . $endTime;
        }

        if (!empty($map['standard_encap/eqs'])) {
            $map['standard_encap/eqs'] = explode('€', trim($map['standard_encap/eqs'], '€'));
            $map['standard_encap/eqs'] = json_encode($map['standard_encap/eqs']);
        }

        if (!empty($map['encap/eqs'])) {
            $map['encap/eqs'] = explode('€', trim($map['encap/eqs'], '€'));
            $map['encap/eqs'] = json_encode($map['encap/eqs']);
        }


        foreach ($map as $k => $v) {
            if (empty($v) and $v != '0') {
                unset($map[$k]);
            }
        }
        //去重导出
        if (in_array($types, [3, 4])) {
            $map["mix_sbrand"] = 1;
        }

        $map["p"] = Arr::get($map, "page", 1);
        $map["offset"] = Arr::get($map, "page_size", 10);
        if (!empty($map['p']) && $map['p'] > 1000) {
            $map['p'] = rand(1, 1000);
        }

        unset($map['class_name/condition']);
        unset($map['brand_name/condition']);
        unset($map['create_time']);
        unset($map['brand_id']);
        $map["no_rule"] = "1122";
        $url = config('website.search_domain_new') . '/search/es/searchSpu';
        //    empty($map['brand_id']) && $map['brand_id/sr'] = 'gt,0';
        //dd($map);
        if (request()->get('show_param')) {
            dump($map);
        }
        $return = Http::get($url, $map);
        if (Arr::get($_REQUEST, "debug")) {
            print_r($map);
            print_r($return->body());
            die();
        }
        $data = json_decode($return->body(), true);

        if (isset($data['error_code']) && $data['error_code'] == 0) {
            $redis = Redis::connection('sku');
            $spuRedis = Redis::connection('spu');
            if ($types == 3) { #导出去重
                foreach ($data['data']["list"] as $k => $v) {
                    $info = json_decode($spuRedis->hget('spu', $v["spu_id"]), true);
                    #SPUID、型号、品牌、标准品牌、封装
                    $list[] = [
                        "SPUID" => $v["spu_id"] . "\t",
                        "型号" => $v["spu_name_row"],
                        "品牌" => $v["brand_name"],
                        "标准品牌" => Arr::get($v, "standard_brand_name"),
                        "封装" => Arr::get($info, "encap", ""),
                    ];
                }
            } else {
                //去获取对应的spu有多少sku上架数量
                $spuIds = $data['data']['spu_id'];
                $skuNumMap = $this->getSpuOnSelfSkuNum($spuIds);

                $classModel = new PoolClassModel();
                $confStatus = config('field.SpuStatus');
                foreach ($data['data']['spu_id'] as $k => $v) {
                    $info = json_decode($spuRedis->hget('spu', $v), true);
                    $list[$k] = $info;
                    $standardBrandId = Arr::get($info, 's_brand_id');
                    $list[$k]['standard_brand_name'] = '';
                    if ($standardBrandId) {
                        $standardBrand = $redis->hget('standard_brand', $standardBrandId);
                        $standardBrand = json_decode($standardBrand, true);
                        $list[$k]['standard_brand_name'] = $standardBrand['brand_name'];
                    }
                    $list[$k]['spu_id'] = $v;
                    $list[$k]['sku_num'] = Arr::get($skuNumMap, $v, 0);
                    $spuDb = $this->getSpuFromDb($v);
                    if (isset($spuDb['class_id2']) && $spuDb['class_id2'] != 0) {
                        //获取顶级分类名称
                        $topClass = $classModel->getClassInfo($spuDb['class_id1']);
                        $class = $classModel->getClassInfo($spuDb['class_id2']);
                        $list[$k]['class_name'] = Arr::get($topClass, 'class_name', '其他') . ' || ' . Arr::get($class,
                                'class_name', '其他');
                    } else {
                        $list[$k]['class_name'] = '无';
                    }
                    $list[$k]['eccn'] = $spuDb['eccn'];
                    //制造商处理
                    if (empty($list[$k]['brand_name']) && !empty($info)) {
                        $brand = $redis->hget('brand', $list[$k]['brand_id']);
                        if ($brand) {
                            $list[$k]['brand_name'] = $brand;
                        }
                    }

                    $list[$k]['status_name'] = $confStatus[$list[$k]['status'] ?? 0];
                    $list[$k]['update_time_s'] = date('Y-m-d H:i', $list[$k]['update_time'] ?? 0);
                    if (!empty($list[$k]['pdf'])) {
                        if ((strpos($list[$k]['pdf'], "files.ichunt.net") !== false ||
                                strpos($list[$k]['pdf'], "file.liexindev.net") !== false) &&
                            strpos($list[$k]['pdf'], 'fileType=pdf') === false) {
                            $list[$k]['pdf'] = $list[$k]['pdf'] . '?fileType=pdf';
                        }

                    }
                }
            }
        } else {
            return ["total" => 0, "msg" => $data["error_msg"], 'list' => []];
        }
        if (!isset($map['status/condition'])) {
            $_GET['status/condition'] = '';
        }
        if (in_array($types, [1, 2])) {
            $list = $this->clearExport($types, $list);
        }
        return [
            'list' => $list,
            'total' => !empty($data['data']['total']) ? $data['data']['total'] : 0,
            "msg" => "符合总条数为0"
        ];
    }

    public function getSpuOnSelfSkuNum($spuIds = [])
    {
        $spuIds = implode(',', $spuIds);
        $url = config('website.search_domain_new') . '/es/countSpuCanBuy?spu_ids=' . $spuIds;
        $response = Http::get($url)->json();
        $skuNumMap = [];
        if (isset($response['code']) && $response['code'] == 0) {
            $skuNumMap = $response['data'];
        }
        return $skuNumMap;
    }

    //导出格式化数据
    public function clearExport($types = "spu_list", $data)
    {
        $temp = [];
        switch ($types) {
            case 1: //spu列表

                foreach ($data as $k => $v) {
                    if (array_key_exists("spu_id", $v) == false) {
                        continue;
                    }
                    $temp[] = [
                        "spu_id" => Arr::get($v, "spu_id") . "\t",
                        "class_name" => Arr::get($v, "class_name"),
                        "spu_name" => Arr::get($v, "spu_name"),
                        "brand_name" => Arr::get($v, "brand_name"),
                        "standard_brand_name" => Arr::get($v, "standard_brand_name"),
                        "encap" => Arr::get($v, "encap"),
                        "status_name" => Arr::get($v, "status_name"),
                        "update_time_s" => $v["update_time_s"],
                    ];
                }
                break;
            case 2: //spu参数
                $last_class_id = $data[0]["class_id2"];

                $classAttr = PoolClassAttrModel::where(["class_id" => $last_class_id, "status" => 1])->get()->toArray();
                $headerb = array_column($classAttr, "attr_name");

                $mongo = \DB::connection('mongodb');
                foreach ($data as $k => $v) {
                    $spuId = $v["spu_id"];
                    $spuAttr = $mongo->table('spu_attrs2')->where('spu_id', (int)$spuId)->first();

                    $t = [
                        "spu_id" => $v["spu_id"] . "\t",
                        "spu_name" => $v["spu_name"],
                        "brand_name" => $v["brand_name"],
                        "encap" => Arr::get($v, "encap"),
                    ];
                    $attrOrg = Arr::get($spuAttr, "attrs_extend");
                    if (!$attrOrg) {
                        continue;
                    }
                    $attr = array_column($attrOrg, "attr_value", "attr_name");
                    foreach ($headerb as $b) {
                        if (!$attr) {
                            $t[$b] = "";
                        } else {
                            $t[$b] = array_key_exists($b, $attr) ? $attr[$b] . "\t" : "";
                        }
                    }
                    $temp[] = $t;
                }

                break;

        }
        return $temp;
    }

    //根据品牌名和spu名称获取spuid
    public function getSpuId($brandName, $spuName)
    {
        $spuService = new SpuService();
        $brandModel = new BrandModel();
        $brandId = $brandModel->BrandInfoName(trim($brandName));
        if (!$brandId) {
            return false;
        }
        $spuId = $spuService->checkSpuUnique($brandId, trim($spuName));
        return $spuId ?: false;
    }

    //获取spu信息
    public function getSpuInfo($spuId)
    {
        $redis = Redis::connection('sku');
        $spuRedis = Redis::connection('spu');
        //先去redis获取spu基本信息
        $spuInfo = $spuRedis->hget('spu', $spuId);
        if (!empty($spuInfo)) {
            $spuInfo = json_decode($spuInfo, true);
            //还要获取属性列表
            $attrService = new AttrService();
            $spuAttrs = $attrService->getAttrsExtendBySpuIds([$spuId]);
            $spuInfo['attrs_extend'] = (!count($spuAttrs)) > 0 ? $spuAttrs : [];
            $spuInfo['brand_name'] = $redis->hget('brand', $spuInfo['brand_id']);
            return $spuInfo;
        } else {
            return [];
        }
    }

    //判断SPU是否唯一
    public function checkSpuUnique($brandId, $spuName)
    {
        $mongodb = DB::connection('mongodb')->collection('spu');
        $result = $mongodb->where(['brand_id' => (int)$brandId, 'spu_name' => $spuName])->first();
        if (!$result) {
            return false;
        } else {
            return $result['spu_id'];
        }
    }

    //新增和修改mongo spu判断唯一数据
    public function saveSpuToMongo($brandId, $spuName, $spuId, $type = '', $mongodb = '')
    {
        if (empty($brandId) || empty($spuName) || empty($spuId)) {
            return false;
        }
        if (empty($mongodb)) {
            $mongodb = DB::connection('mongodb')->collection('spu');
        }
        if (empty($type)) {
            $result = $mongodb->where(['spu_id' => (int)$spuId])->first();
        } else {
            $result = null;
        }
        $data['spu_id'] = (int)$spuId;
        $data['spu_name'] = $spuName;
        $data['brand_id'] = (int)$brandId;
        if ($result) {
            $map['spu_id'] = (int)$spuId;
            $save = $mongodb->update($map, $data);
        } else {
            $save = $mongodb->insert($data);
        }
        if ($save) {
            return true;
        } else {
            return false;
        }
    }

    public static function handleSpu($info)
    {
        $info = (array)$info;
        $config = config('field.SpuField');
        $spu = $info;
        if (empty($info['spu_id'])) {
            return false;
        }
        $redis = Redis::connection('sku');
        $spuRedis = Redis::connection('spu');
        $redisInfo = json_decode($spuRedis->hget('spu', $info['spu_id']), true);
        if (!empty($redisInfo) || is_array($redisInfo)) {
            foreach ($redisInfo as $k => $v) {
                if (!isset($info[$k])) {
                    $info[$k] = $v;
                }
            }
        }
        foreach ($info as $k => $v) {
            if (!isset($config[$k]) || $config[$k] !== 1) {
                unset($info[$k]);
            }
        }
        $spuRedis->hset('spu', $spu['spu_id'], json_encode($info));
        return $spu;
    }

    //推送redis更新spu
    public function pushSpuUpdate($spuIds, $isSingleUpdate = false)
    {
        if ($isSingleUpdate) {
            //也要推送的mq
            (new RabbitQueueModel('trading'))->insertRawQueue('', [
                'spu_id' => (int)$spuIds,
            ], 'update_spu_attr_es');
        }

        $redis = Redis::connection('sku');
        $spuIds = explode(',', $spuIds);
        $pushName = 'update_list_spu';
        if (!empty($spuIds) && is_array($spuIds)) {
            foreach ($spuIds as $k => $v) {
                $redis->rpush($pushName, $v);
            }
            return true;
        } else {
            return false;
        }


    }

    //保存spu
    public function saveSpu($spuData)
    {
        $redis = Redis::connection('sku');
        $spuRedis = Redis::connection('spu');
        $spuService = new SpuService();
        $brandModel = new BrandModel();
        $spuId = $spuData['spu_id'];

        if ($spuId) {
            SpuLogService::saveSpuLog($spuData);
        }

        $attrData = !empty($spuData['attrs']) ? $spuData['attrs'] : [];
        $spuData = Arr::except($spuData, ['attrs']);

        $spuImageList = $spuData['spu_image_list'];
        $spuImageList = array_filter($spuImageList, function ($value) {
            return !empty($value['thumbnail']);
        });

        $spuExtraColumn = [
            'standard_lead_time',
            'mounting_type',
            'length',
            'width',
            'height',
            'weight',
            'reach_status',
            'customs_code',
            'en_pdf_url',
            'spu_image_list',
        ];
        $spuExtra = Arr::only($spuData, $spuExtraColumn);
        $spuExtra['spu_detail'] = $spuData['spu_detail'];
        $spuImageList = array_values($spuImageList);
        $spuExtra['image_list'] = $spuImageList;
        unset($spuExtra['spu_image_list']);


        $spuData = Arr::except($spuData, $spuExtraColumn);
        //去获取标准品牌id
        $brandId = $spuData['brand_id'];
        $standardBrandId = (new StandardBrandService())->getStandardBrandIdByBrandId($brandId);
        if ($standardBrandId) {
            $spuData['s_brand_id'] = $standardBrandId;
        }


        $originSpu = [];
        if ($spuId) {//编辑
            $dbInfo = resolveDB($spuId);
            //一开始的spu数据
            $originSpu = (array)DB::connection($dbInfo['db'])->table($dbInfo['table'])
                ->where('spu_id', $spuData['spu_id'])->first();
            $spuData['update_time'] = time();
            $result = DB::connection($dbInfo['db'])->table($dbInfo['table'])
                ->where('spu_id', $spuData['spu_id'])->update($spuData);
            if (!$result) {
                throw new InvalidRequestException('编辑SPU失败');
            }
            //编辑成功，写入redis
            $spu = (array)DB::connection($dbInfo['db'])->table($dbInfo['table'])->where('spu_id',
                $spuData['spu_id'])->first();
            if ($originSpu['spu_name'] != $spu['spu_name'] || $originSpu['brand_id'] != $spu['brand_id']) {
                $brandName = $brandModel->HDBrandName($spuData['brand_id'], $redis);
                $BrandNames = $brandModel->HDBrandName($originSpu['brand_id'], $redis);
                //识别spu是否唯一
                $spuRedis->hdel('spu_unique_judge', md5(strtolower($originSpu['spu_name'] . '_' . $BrandNames)));//删除原来的
                $spuRedis->hset('spu_unique_judge', md5(strtolower($spu['spu_name'] . '_' . $brandName)),
                    $spu['spu_id']);
            }
            SpuService::handleSpu($spu);
            //写入mongo
            $spuService->saveSpuToMongo($spu['brand_id'], $spu['spu_name'], $spu['spu_id']);
            //写入spu的多图,目前只是工艺品用到,所以简单存到mongo


        } else {//新增
            //在同个品牌下不存在spu才能新增
            if (!$spuService->checkSpuUnique($spuData['brand_id'], $spuData['spu_name'])) {
                //添加到哪个库
                $spuId = (new IdService())->structure();
                $dbInfo = resolveDB($spuId);
                $spuData['create_time'] = time();
                $spuData['update_time'] = time();
                $spuData['sale_time'] = time();
                $spuData['spu_id'] = $spuId;
                $spuData['s_brand_id'] = (new StandardBrandService())->getStandardBrandIdByBrandId($spuData['brand_id']);
                $result = DB::connection($dbInfo['db'])->table($dbInfo['table'])->insert($spuData);
                if (!$result) {
                    throw new InvalidRequestException('新增SPU失败');
                }
                //新增spu成功，写入Redis
                $spu = (array)DB::connection($dbInfo['db'])->table($dbInfo['table'])->where('spu_id',
                    $spuId)->first();
                $brandName = $brandModel->HDBrandName($spuData['brand_id'], $redis);
                $spuRedis->hset('spu_unique_judge', md5(strtolower($spu['spu_name'] . '_' . $brandName)),
                    $spu['spu_id']);
                SpuService::handleSpu($spu);
                //写入mongo
                $spuService->saveSpuToMongo($spu['brand_id'], $spu['spu_name'], $spu['spu_id']);
            } else {
                throw new InvalidRequestException('新增SPU失败，该品牌中已存在同名SPU');
            }

        }
        SpuExtraService::saveSpuExtra($spuData['spu_id'], $spuExtra);

        //推送通知ES
        $spuService->pushSpuUpdate($spuData['spu_id'], true);

        //推送rabbitMQ
        if (!empty($originSpu)) {
            //已经存在的spu,要判断图片或者pdf是否有修改,有修改才去丢队列
            if ($spuData['images_l'] && $originSpu['images_l'] != $spuData['images_l']) {
                (new RabbitQueueModel('trading'))->insertRawQueue('', [
                    'file_type' => 1,
                    'spu_id' => $spuId,
                    'url' => $spuData['images_l']
                ], 'spu_update_image_pdf');
            }
            if ($spuData['pdf'] && $originSpu['pdf'] != $spuData['pdf']) {
                (new RabbitQueueModel('trading'))->insertRawQueue('', [
                    'file_type' => 2,
                    'spu_id' => $spuId,
                    'url' => $spuData['pdf']
                ], 'spu_update_image_pdf');
            }
        } else {
            if ($spuData['images_l']) {
                (new RabbitQueueModel('trading'))->insertRawQueue('', [
                    'file_type' => 1,
                    'spu_id' => $spuId,
                    'url' => $spuData['images_l']
                ], 'spu_update_image_pdf');
            }

            if ($spuData['pdf']) {
                (new RabbitQueueModel('trading'))->insertRawQueue('', [
                    'file_type' => 2,
                    'spu_id' => $spuId,
                    'url' => $spuData['pdf']
                ], 'spu_update_image_pdf');
            }
        }
        //保存spu参数
        $spuAttr = (new SpuAttrService())->saveSpuAttr($spuId, $attrData);
        if (!empty($originSpu)) {
            //去比对原始spu和新增spu不同的字段
            $diffData = $this->getSpuDiffData($originSpu, $spuData);
            //找到不同的地方,然后去推送给辐射spu服务
            $diffData['extra'] = $spuExtra;
            $extraDbColumn = [
                'standard_lead_time',
                'mounting_type',
                'length',
                'width',
                'height',
                'weight',
                'reach_status',
                'customs_code',
            ];
            $diffData['extra']['common'] = Arr::only($spuExtra, $extraDbColumn);
            $diffData['extra'] = Arr::except($diffData['extra'], $extraDbColumn);
            $diffData['attrs_mix'] = $spuAttr['attrs_mix']??'';
            $diffData['attrs_extend'] = $spuAttr['attrs_extend']??[];
            $diffData['attrs_format'] = $spuAttr['attrs_format']??[];
            (new RabbitQueueModel('trading'))->insertRawQueue('', [
                'spu_id' => (string)$spuId,
                'spu_data_info' => $diffData,
                'type' => 3,
            ], 'spu_update_Info');
        }


        return $spuData['spu_id'];
    }

    public function getSpuDiffData($originSpu, $spuData)
    {
        $columns = array_keys($spuData);
        $originSpu = Arr::only($originSpu, $columns);
        ksort($spuData);
        ksort($originSpu);
        $diffData = [];
        $ignoreColumns = ['update_time', 'status', 'spu_id', 'brand_id', 'spu_detail'];
        foreach ($spuData as $key => $value1) {
            if (in_array($key, $ignoreColumns)) {
                continue;
            }
            if ($key == 'class_id1' || $key == 'class_id2') {
                $value1 = (int)$value1;
            }
            $diffData[$key] = $value1;
        }
        return $diffData;
    }

    public static function updateSpuClassTask($file)
    {
        if (empty($file)) {
            throw new InvalidRequestException('请上传文件');
        }
        if ($file->isValid()) {
            $clientName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            $size = $file->getSize();
            if ($extension != "xlsx") {
                throw new InvalidRequestException('上传文件非法');
            }
            $newName = 'update_spu_class_' . md5(date('ymdhis') . $clientName) . "." . $extension;
            $uploadDir = storage_path() . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR;
            $path = $uploadDir . $newName;
            $file->move($uploadDir, $newName);
            $adminId = request()->user->userId;
            $adminName = request()->user->name;
            $logModel = new TaskLogModel();
            $logData = [
                'file_name' => $clientName,
                'log' => date('Y-m-d H:i:s', time()) . " 开始上传处理SPU,文件名为 : " . $clientName . " ; 大小为 : " . ($size / 1000) . "kb",
                'status' => 0,
                'admin_id' => $adminId,
                'admin_name' => $adminName,
                'create_time' => time(),
                'update_time' => time(),
                'type' => TaskLogModel::TYPE_UPDATE_SPU,
            ];
            return \DB::connection('spu')->transaction(function () use ($logModel, $logData, $path) {
                $logId = $logModel->insertGetId($logData);

                $hostParts = explode('.', request()->getHost());
                $mainDomain = implode('.', array_slice($hostParts, -2));
                if ($mainDomain != 'ichunt.net') {
                    (new UpdateSpu($logId, $path))->handle();
                    $result = true;
                } else {
                    $result = UpdateSpu::dispatch($logId, $path);
                }
                if (!$result) {
                    throw new InvalidRequestException('推送上传任务失败');
                }
                return $result;
            });

        } else {
            throw new InvalidRequestException('上传文件非法');
        }
    }

    //更新spu属性
    public static function updateSpuAttrTask($file)
    {
        if (empty($file)) {
            throw new InvalidRequestException('请上传文件');
        }
        if ($file->isValid()) {
            $clientName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            $size = $file->getSize();
            if ($extension != "xlsx") {
                throw new InvalidRequestException('上传文件非法');
            }
            $newName = 'update_spu_attr_' . md5(date('ymdhis') . $clientName) . "." . $extension;
            $uploadDir = storage_path() . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR;
            $path = $uploadDir . $newName;
            $file->move($uploadDir, $newName);
            $adminId = request()->user->userId;
            $adminName = request()->user->name;
            $logModel = new TaskLogModel();
            $logData = [
                'file_name' => $clientName,
                'log' => date('Y-m-d H:i:s', time()) . " 开始上传处理SPU,文件名为 : " . $clientName . " ; 大小为 : " . ($size / 1000) . "kb",
                'status' => 0,
                'admin_id' => $adminId,
                'admin_name' => $adminName,
                'create_time' => time(),
                'update_time' => time(),
                'type' => TaskLogModel::TYPE_UPDATE_SPU_ATTR,
            ];
            //return \DB::connection('spu')->transaction(function () use ($logModel, $logData, $path) {
            $logId = $logModel->insertGetId($logData);
            $hostParts = explode('.', request()->getHost());
            $mainDomain = implode('.', array_slice($hostParts, -2));
            if ($mainDomain != 'ichunt.net') {
                $result = UpdateSpuAttr::dispatchNow($logId, $path);
                $result = true;
            } else {
                $result = UpdateSpuAttr::dispatch($logId, $path);
            }

            //$result = true;
            if (!$result) {
                throw new InvalidRequestException('推送上传任务失败');
            }
            return $result;
            //});

        } else {
            throw new InvalidRequestException('上传文件非法');
        }
    }

    public static function updateSpuPDFTask($spuPDFData, $lang)
    {
        if (empty($spuPDFData)) {
            throw new InvalidRequestException('PDF和SPU对应的数据不能为空');
        }

        $adminId = request()->user->userId;
        $adminName = request()->user->name;
        $logModel = new TaskLogModel();
        $logData = [
            'file_name' => 'spu_PDF_data_' . date('Y_m_d_H_i_s', time()),
            'log' => date('Y-m-d H:i:s', time()) . " 开始上传处理SPU",
            'status' => 0,
            'admin_id' => $adminId,
            'admin_name' => $adminName,
            'create_time' => time(),
            'update_time' => time(),
            'type' => TaskLogModel::TYPE_UPDATE_SPU_PDF,
        ];
        return \DB::connection('spu')->transaction(function () use ($logModel, $logData, $spuPDFData, $lang) {
            $logId = $logModel->insertGetId($logData);
            $result = UpdateSpuPDF::dispatch($logId, $spuPDFData, $lang);
            if (!$result) {
                throw new InvalidRequestException('推送上传任务失败');
            }
            return $result;
        });
    }

    public function getSpuBySpuNameAndStandardBrandId($spuName, $standardBrandId)
    {
        //直接去mongo获取一个spu_id即可
        $mongo = DB::connection('mongodb');
        $spu = $mongo->table('spu')->where('s_brand_id', (int)$standardBrandId)->where('spu_name', (string)$spuName)->first();
        if (empty($spu)) {
            return $spu;
        }
        $spuId = $spu['spu_id'];
        //再去spu的redis获取spu信息
        $spuRedis = Redis::connection('spu');
        $spu = $spuRedis->hget('spu', $spuId);
        $spu = json_decode($spu, true);
        $spu['spu_id'] = (string)$spuId;
        if (!empty($spu['bussiness_area'])) {
            $spu['bussiness_area'] = implode(' | ', array_map(function ($value) {
                return Arr::get(config('field.SpuApplicationArea'), $value, '');
            }, explode('|', $spu['bussiness_area'])));
        }
        return $spu;
    }


    //获取spu额外数据
    public function getSpuExtra($spuId)
    {
        $attr = DB::connection('mongodb')->collection('spu_extra')->where('spu_id', $spuId)->first();
        if (!empty($attr)) {
            $attr = Arr::except($attr, ['_id']);
        }
        return $attr;
    }

    public function getSpuFromDb($spuId)
    {
        $dbInfo = resolveDB($spuId);
        //一开始的spu数据
        $spu = (array)DB::connection($dbInfo['db'])->table($dbInfo['table'])
            ->where('spu_id', $spuId)->first();

        return $spu ?: [];
    }


}
