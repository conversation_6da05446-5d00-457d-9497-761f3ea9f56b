<?php

namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\SampleLogModel;
use App\Http\Models\SampleModel;
use App\Imports\SampleImport;
use App\Jobs\UploadSample;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class SampleService
{
    public static function uploadSample($file)
    {
        if (empty($file)) {
            throw new InvalidRequestException('请上传文件');
        }
        if ($file->isValid()) {
            $clientName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            if ($extension != "xlsx") {
                throw new InvalidRequestException('上传文件非法');
            }
            $newName = md5(date('ymdhis') . $clientName) . "." . $extension;
            $uploadDir = storage_path() . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR;
            $path = $uploadDir . $newName;
            $file->move($uploadDir, $newName);
//            dd($uploadDir);
//            unlink($path);
            $adminId = request()->user->userId;
            $adminName = request()->user->name;
            $logModel = new SampleLogModel();
            $logData = [
                'file_name' => $clientName,
                'log' => date('Y-m-d H:i:s', time()) . " 开始上传样品",
                'status' => 0,
                'admin_id' => $adminId,
                'admin_name' => $adminName,
                'create_time' => time(),
                'update_time' => time(),
            ];
            //校验是否都有标准品牌

            return \DB::connection('spu')->transaction(function () use ($logModel, $logData, $path) {
                $logId = $logModel->insertGetId($logData);
//            $result = dispatch(new UploadSample($logId, $sampleList));
                $result = UploadSample::dispatch($logId, $path);
                if (!$result) {
                    throw new InvalidRequestException('推送上传任务失败');
                }
                return $result;
            });

        } else {
            throw new InvalidRequestException('上传文件非法');
        }
    }


    public static function getSampleList($map)
    {
        $query = SampleModel::with('sample_class')->where('status', '!=', SampleModel::STATUS_DELETED);
        if (!empty($map['goods_name'])) {
            $query->where('goods_name', 'like', '%' . $map['goods_name'] . '%');
        }
        if (!empty($map['goods_id'])) {
            $query->where('goods_id', 'like', '%' . $map['goods_id'] . '%');
        }
        if (!empty($map['brand_name'])) {
            $query->where('brand_name', 'like', '%' . $map['brand_name'] . '%');
        }
        if (!empty($map['class_id'])) {
            $query->where('class_id', $map['class_id']);
        }
        if (!empty($map['sku_id'])) {
            $query->where('goods_id', $map['sku_id']);
        }
        if (!empty($map['add_time'])) {
            $startTime = strtotime(explode('~', $map['add_time'])[0]);
            $endTime = strtotime(explode('~', $map['add_time'])[1]);
            $query->whereBetween('add_time', [$startTime, $endTime]);
        }
        $limit = Arr::get($map, 'limit', 10);
        $result = $query->orderBy('add_time', 'desc')->paginate($limit);
        $result = $result ? $result->toArray() : [];
        $redis = Redis::connection('sku');
        if (!empty($result)) {
            foreach ($result['data'] as $key => &$value) {
                $value['no'] = $key + 1;
                $value['add_time'] = date('Y-m-d H:i:s', $value['add_time']);
                $value['sample_type_name'] = Arr::get(config('field.SampleType'), $value['sample_type']);
                if ($value['sample_type'] == 1) {
                    $sku = $redis->hget('Self_SelfGoods', $value['goods_id']);
                    $sku = json_decode($sku, true);
                    $value['stock'] = $sku['stock'];
                } else {
                    $sku = $redis->hget('sku', $value['goods_id']);
                    $sku = json_decode($sku, true);
                    $value['stock'] = $sku['stock'];
                    $value['canal'] = $sku['canal'];
                }
                $value['goods_id'] = (string)$value['goods_id'];
            }
            unset($value);
        }
        return [
            'data' => Arr::get($result, 'data', []),
            'count' => Arr::get($result, 'total'),
            'total' => Arr::get($result, 'total'),
        ];
    }

    public static function deleteSample($id)
    {
        $goodsId = SampleModel::where('id', $id)->value('goods_id');
        $redis = Redis::connection('sku');
        $redis->hdel('lie_sample_list', $goodsId);
        return SampleModel::where('id', $id)->update([
            'status' => SampleModel::STATUS_DELETED,
            'update_time' => time(),
        ]);
    }


    public static function batchDeleteSample($ids = [])
    {
        $redis = Redis::connection('sku');
        $goodsIds = SampleModel::whereIn('id', $ids)->pluck('goods_id')->toArray();
        foreach ($goodsIds as $goodsId) {
            $redis->hdel('lie_sample_list', $goodsId);
        }
        return SampleModel::whereIn('id', $ids)->update([
            'status' => SampleModel::STATUS_DELETED,
            'update_time' => time(),
        ]);
    }

    public static function saveSample($id, $maxNumber, $sampleStock,$classId)
    {
        $redis = Redis::connection('sku');
        $result = SampleModel::where('id', $id)->update([
            'max_number' => $maxNumber,
            'sample_stock' => $sampleStock,
            'update_time' => time(),
            'class_id' => $classId,
        ]);
        $sample = SampleModel::where('id', $id)->first()->toArray();
        $redis->hset('lie_sample_list', $sample['goods_id'], json_encode($sample));

        return $result;
    }

    public static function getSampleUploadList($map)
    {
        $query = SampleLogModel::orderBy('create_time', 'desc');

        if (!empty($map['status']) || (isset($map['status']) && $map['status'] === '0')) {
            $query->where('status', $map['status']);
        }
        if (!empty($map['admin_id'])) {
            $query->where('admin_id', $map['admin_id']);
        }
        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }

        $limit = Arr::get($map, 'limit', 10);
        $result = $query->paginate($limit);
        $result = $result ? $result->toArray() : [];
        foreach ($result['data'] as $key => &$value) {
            $value['create_time'] = date('Y-m-d H:i:s', $value['create_time']);
            $value['status_name'] = Arr::get(config('field.SampleUploadStatus'), $value['status']);
        }
        unset($value);

        return [
            'data' => Arr::get($result, 'data', []),
            'count' => Arr::get($result, 'total'),
            'total' => Arr::get($result, 'total'),
        ];
    }

}
