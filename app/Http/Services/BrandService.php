<?php

namespace App\Http\Services;

//标准品牌服务

use App\Exceptions\InvalidRequestException;
use App\Exports\BrandExport;
use App\Http\Models\BrandModel;
use App\Http\Models\SeoElementModel;
use App\Http\Transformers\BrandTransformer;
use App\Http\Transformers\StandardBrandTransformer;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class BrandService
{

    public function getBrand($brandId)
    {
        $brand = BrandModel::where('brand_id', $brandId)->first()->toArray();
        $brand['seo'] = SeoElementService::getSeoElement($brand['brand_id'], SeoElementModel::TYPE_BRAND);
        return $brand;
    }

    public function getBrandList($map)
    {
        $model = new BrandModel();
        $query = $model->orderBy('brand_id', 'desc');
        $query = $this->filter($query, $map);
        $limit = \Arr::get($map, 'limit', 15);
        $result = $query->with('standard_brand_mapping')->paginate($limit);

        $result = $result ? $result->toArray() : [];
        $transformer = new BrandTransformer();
        $result['data'] = $transformer->listTransformer($result['data']);
        return [
            'data' => \Arr::get($result, 'data', []),
            'count' => \Arr::get($result, 'total'),
        ];
    }


    public function filter($query, $map)
    {

        if (!empty($map['standard_brand_name'])) {
            $query->whereHas('standard_brand', function ($q) use ($map) {
                $q->where('brand_name', 'like', "${map['brand_name']}%")
                    ->orWhere('show_name', 'like', "${map['brand_name']}%")
                    ->orWhere('brand_name_en', 'like', "${map['brand_name']}%")
                    ->orWhere('brand_short_name_en', 'like', "${map['brand_name']}%")
                    ->orWhere('brand_name_cn', 'like', "${map['brand_name']}%")
                    ->orWhere('brand_short_name_cn', 'like', "${map['brand_name']}%");
            });
        }

        if (!empty($map['brand_name_raw'])) {
            $query->where('brand_name', $map['brand_name_raw']);
        }

        if (!empty($map['first_letter'])) {
            $query->where('first_letter', "${map['first_letter']}%");
        }
        if (!empty($map['brand_id'])) {
            $query->where('brand_id', $map['brand_id']);
        }

        if (!empty($map['has_brand_brief'])) {
            $query->where('brand_brief', '!=', '');
        }
        if (!empty($map['has_brand_desc'])) {
            $query->where('brand_desc', '!=', '');
        }

        if (!empty($map['has_order'])) {
            $query->where('has_order', $map['has_order']);
        }

        if (!empty($map['ignore_handle'])) {
            $query->where('ignore_handle', $map['ignore_handle']);
        }

        if (!empty($map['has_put_away_sku'])) {
            if ($map['has_put_away_sku'] == 1) {
                $query->where('sku_number', '>', 0);
            }else{
                $query->where('sku_number', 0);
            }
        }

        if (!empty($map['brand_name'])) {
            $query->where('brand_name', 'like', "${map['brand_name']}%");
        }
        if (!empty($map['is_unusual']) || (isset($map['is_unusual']) && $map['is_unusual'] == '0')) {
            $query->where('is_unusual', $map['is_unusual']);
        }
        if (!empty($map['brand_area']) || (isset($map['brand_area']) && $map['brand_area'] == '0')) {
            $query->where('brand_area', $map['brand_area']);
        }
        if (!empty($map['has_picture']) || (isset($map['has_picture']) && $map['has_picture'] === '0')) {
            if ($map['has_picture']) {
                $query->where('brand_logo', '!=', '');
            } else {
                $query->where('brand_logo', '=', '');
            }
        }
        if (!empty($map['insert_type']) || (isset($map['insert_type']) && $map['insert_type'] == '0')) {
            $query->where('insert_type', $map['insert_type']);
        }

        if (!empty($map['sku_upload_need_deal']) || (isset($map['sku_upload_need_deal']) && $map['sku_upload_need_deal'] == '0')) {
            $query->where('sku_upload_need_deal', $map['sku_upload_need_deal']);
        }
        if (!empty($map['has_mapping']) || (isset($map['has_mapping']) && $map['has_mapping'] === '0')) {
            if ($map['has_mapping'] == -1) {
                $query->where('ignore_handle', 1);
            } else {
                if ($map['has_mapping']) {
                    $query->whereHas('standard_brand_mapping', function ($q) use ($map) {
                        $q->where('brand_id', '!=', '');
                    });
                } else {
                    $query->whereNotIn('brand_id', function ($q) {
                        $q->select('brand_id')->from('lie_brand_standard_mapping');
                    });
                }
            }

        }
        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }

        return $query;
    }

    public function changeBrandStatus($brandId, $status)
    {
        $result = BrandModel::where('brand_id', $brandId)->update([
            'brand_id' => $brandId,
            'status' => $status
        ]);
        //同时还要将修改的东西放到缓存里面去
        if ($result) {
            $redis = Redis::connection('sku');
            $brand = BrandModel::where('brand_id', $brandId)->first();
            if (!empty($brand)) {
                $brand = $brand->toArray();
                $redis->hset('brand_info', $brandId, $brand);
                return true;
            }
        }
        return false;
    }

    public function searchBrand($brandName)
    {
        $query = BrandModel::where('brand_name', 'like', '%' . $brandName . '%');
        $count = $query->count();
        $brandList = $query->select('brand_name', 'brand_id')->paginate(17);
        $brandList = $brandList ? $brandList->toArray() : [];
        $data = Arr::get($brandList, 'data');
        $lastPage = Arr::get($brandList, 'last_page');
        return [
            'errcode' => 0,
            'errmsg' => 'ok',
            'total' => $count,
            'count' => $count,
            'data' => $data,
            'last_page' => $lastPage
        ];
    }

    public function saveBrand($data)
    {
        $seoData = \Arr::only($data, ['title', 'keywords', 'description']);
        unset($data['title'], $data['keywords'], $data['description']);
        $data['brand_name'] = trim($data['brand_name']);
        if (!empty($data['brand_id'])) {
            $existBrandName = BrandModel::where('brand_id', '!=', $data['brand_id'])
//            $existBrandName = BrandModel::where('brand_id', '>', 0)
                ->whereRaw('BINARY brand_name = ?', $data['brand_name'])->count();
        } else {
            $existBrandName = BrandModel::where('brand_name', $data['brand_name'])->count();
        }
        if ($existBrandName) {
            throw new InvalidRequestException('添加的品牌已经存在,请确认品牌名称');
        }
        if (!empty($data['brand_id'])) {
            $brandId = $data['brand_id'];
            $seoData['key_id'] = $data['brand_id'];
            $data['update_time'] = time();
            $data['update_uid'] = request()->user->userId;
            $data['update_name'] = request()->user->name;
            BrandModel::where('brand_id', $data['brand_id'])->update($data);
        } else {
            $data['create_time'] = time();
            $brandId = BrandModel::insertGetId($data);
            $seoData['key_id'] = $brandId;
        }
        $this->saveBrandNameToRedis($brandId, true);
        $seoService = new SeoElementService();
        $seoData['type'] = SeoElementModel::TYPE_BRAND;
        return $seoService->saveSeoElement($seoData);

    }

    public function saveBrandNameToRedis($brandId = '', $isUpload = false)
    {
        if (empty($brandId)) {
            return false;
        }
        $redis = Redis::connection('sku');
        $brandName = $redis->hget('brand', $brandId);
        if (!$brandName || $isUpload === true) {
            $brand = BrandModel::where('brand_id', '=', $brandId)->select('brand_name')->first();
            if (!$brand) {
                return false;
            }
            $brand = $brand->toArray();
            $redis->hset('brand', $brandId, $brand['brand_name']);
            $key = md5(strtolower($brand['brand_name']));
            if (!$redis->hget('brand_name_all', $key)) {
                $redis->hset('brand_name_all', $key, $brandId);
            }
            return $brand['brand_name'];
        } else {
            return $brandName;
        }
    }

    public function getBrandListForSelect($brandName)
    {
        $query = BrandModel::select(['brand_id', 'brand_name']);
        if ($brandName) {
            $query->where('brand_name', 'like', "%${brandName}");
        }
        $result = $query->paginate(10)->toArray();
        return $result;
    }

    public function exportBrands($map)
    {
        return Excel::download(new BrandExport($map), '品牌导出.csv');
    }

    public function updateIgnoreHandle($brandId, $ignoreHandle, $ignoreHandleReason = '')
    {
        return BrandModel::where('brand_id', $brandId)->update([
            'update_time' => time(),
            'update_name' => request()->user->name,
            'update_uid' => request()->user->userId,
            'ignore_handle' => $ignoreHandle,
            'ignore_handle_reason' => $ignoreHandleReason
        ]);
    }
    public function batchUpdateIgnoreHandle($brandIds, $ignoreHandle, $ignoreHandleReason = '')
    {
        return BrandModel::whereIn('brand_id', $brandIds)->update([
            'update_time' => time(),
            'update_name' => request()->user->name,
            'update_uid' => request()->user->userId,
            'ignore_handle' => $ignoreHandle,
            'ignore_handle_reason' => $ignoreHandleReason
        ]);
    }

}
