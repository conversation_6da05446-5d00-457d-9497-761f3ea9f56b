<?php


namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Models\Cms\CmsUserIntraCodeModel;
use App\Http\Models\SeoElementModel;
use App\Http\Models\Supplier\SupplierChannelModel;
use App\Http\Models\SupplierExtraModel;
use App\Http\Models\SupplierModel;
use App\Http\Models\SupplierRatioModel;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

class SupplierService
{

    public function getSupplier($supplierId)
    {
        return SupplierModel::where('supplier_id', $supplierId)->first()->toArray();
    }

    public function getSupplierByCanal($supplier_code)
    {
        $data =  SupplierChannelModel::where('supplier_code', $supplier_code)->first();
        return $data ? $data->toArray() : [];
    }


    //获取真实的供应商
    public function getBaseSupplier($supplierName = '')
    {
        $query = SupplierModel::where('is_type', 0);
        if ($supplierName) {
            $query = $query->where('supplier_name', $supplierName);
        }
        $result = $query->select(['supplier_name', 'supplier_id', 'status'])->get()->toArray();
        return $result;
    }

    public function getSupplierList($map)
    {
        $limit = !empty($map['limit']) ? $map['limit'] : 10;
        $p = !empty($map['p']) ? $map['p'] : 1;
        $list = SupplierModel::where('is_type', 0)->where(function ($query) use ($map) {
            if (!empty($map['supplier_id'])) {
                $query->where('supplier_id', $map['supplier_id']);
            }
            if (!empty($map['supplier_name'])) {
                $query->where('supplier_name', $map['supplier_name']);
            }
            if (!empty($map['status']) || (isset($map['status']) && $map['status'] == '0')) {
                $query->where('status', $map['status']);
            }
            if (!empty($map['type_id'])) {
                $query->where('type_id', $map['type_id']);
            }
        })->select(['supplier_id', 'supplier_name', 'type_id', 'status'])
            ->paginate($limit, ['*'], 'p', $p)->toArray();
        return $list;
    }

    //获取供应商修改页面要的数据
    public function getSaveSupplierData($supplierId)
    {
        $supplier = [];
        if (!empty($supplierId)) {
            $supplier = SupplierModel::where('supplier_id', $supplierId)->first()->toArray();
            $extInfo = SupplierExtraModel::where('supplier_id', $supplierId)->first();
            $extInfo = !empty($extInfo) ? $extInfo->toArray() : [];
            $supplier = array_merge($supplier, $extInfo);
        }

        if (!empty($supplier['code'])) {
            $admin = CmsUserIntraCodeModel::where('code_id', $supplier['code'])
                ->select('admin_id')
                ->first()->toArray();
            $codeName = CmsUserInfoModel::where('userId', $admin['admin_id'])
                ->select(['name', 'email', 'userId'])
                ->first();
            $codeName = (array)$codeName;
            $codeName['email'] = !empty($codeName['email']) ? $codeName['email'] : '';
            $supplier['code_name'] = !empty($codeName['name']) ? $codeName['name'] : $codeName['email'];
        }
        $data['supplier'] = $supplier;

        $data['type_id'] = config('field.SupplierTypeID');
        $data['status'] = config('field.SupplierStatus');
        $seo = [
            'type' => 2,
            'key_id' => $supplierId,
            'field' => '供应商名字',
            'value' => Arr::get($supplier, 'supplier_name')
        ];

        //获取三要素
        if (!empty($supplierId)) {
            $SeoModel = new SeoElementModel();
            $seoInfo = $SeoModel->getSeoElementCache($seo);
            if ($seoInfo) {
                $seo = array_merge($seo, $seoInfo);
            }
        }

        $data['seo'] = $seo;

        $data['purchaseUsers'] = (new CmsUserIntraCodeModel())->getCodeUsersForSelect();
        $supplierChannelCodes = (new SupplierChannelModel())->where('is_type', 0)
            ->where('status', SupplierChannelModel::STATUS_PASSED)->pluck('supplier_name', 'supplier_code')->toArray();
        foreach ($supplierChannelCodes as $key => &$value) {
            $value .= '(' . $key . ')';
        }
        unset($value);
        $data['supplierChannelCodes'] = $supplierChannelCodes;
        return $data;
    }

    public function saveSupplier($data)
    {
        $data['update_time'] = time();
        $supplier = SupplierModel::where('supplier_name', $data['supplier_name'])->select('supplier_id')->first();
        if ($supplier) {
            $supplier = $supplier->toArray();
        }
        if ($supplier['supplier_id'] != $data['supplier_id'] && request()->user->userId != 1000) {
            throw new InvalidRequestException('当前供应商名字已经存在');
        }
        $redis = Redis::connection('sku');
        $redis->hset('supplier', $data['supplier_id'], $data['supplier_name']);

        DB::connection('spu')->transaction(function () use ($data) {
            $result = SupplierModel::where('supplier_id', $data['supplier_id'])->update($data);
            if (!$result) {
                return false;
            }
            $this->saveSupplierExtra();
            return true;
        });

        //(new NoticeService)->sendNotice(1, $data['supplier_id'], request()->user->userId);
        return true;
    }

    public function saveSupplierExtra()
    {
        $field = [
            'supplier_id',
            'supplier_nickname',
            'ad_text',
            'cn_delivery',
            'hk_delivery',
            'ad_url',
            'sort',
            'supplier_logo',
            'status',
            'describe',
            'fee_des',
        ];
        $data = TrimX('', false, $field);
        $supplierExtraModel = new SupplierExtraModel();
        $extFind = $supplierExtraModel->where('supplier_id', $data['supplier_id'])
            ->select(['supplier_id', 'price_json'])->first();
        if ($extFind) {
            $extFind = $extFind->toArray();
            $result = $supplierExtraModel->where('supplier_id', $data['supplier_id'])->update($data);
            if ($result === 0) {
                return true;
            }
            if (!empty($extFind['price_json'])) {
                $data['price_json'] = json_decode($extFind['price_json'], true);
            }
        } else {
            $result = $supplierExtraModel->insert($data);
        }
        if (!$result) {
            throw new InvalidRequestException('修改供应商文案失败');
        }
        $redis = Redis::connection('sku');
        // 更新redis
        $extraInfo = json_decode($redis->hget('SUPPLIER_REDIS_INFO_', $data['supplier_id']), true);
        if (!empty($extraInfo['price_json'])) {
            $data['price_json'] = $extraInfo['price_json'];
        }//获取原有的系数

        //所有字段转成字符串
        foreach ($data as $a => &$b) {
            if ($a != "price_json") {
                $b = (string)$b;
            }
        }

        $redis->hset('SUPPLIER_REDIS_INFO_', $data['supplier_id'], json_encode($data));
        //排序
        $res = $supplierExtraModel->where('status', 1)->select('supplier_id')->orderBy('sort', 'DESC')->get();
        // 根据 redis中supplier_id 获取 supplier_nickname
        $supplierListShowArr = [];
        foreach ($res as $key => $value) {
            $supplierInfoJson = $redis->hget('SUPPLIER_REDIS_INFO_', $value['supplier_id']);
            $supplierInfoArr = json_decode($supplierInfoJson, true);
            $supplierListShowArr[$value['supplier_id']] = !empty($supplierInfoArr['supplier_nickname']) ? $supplierInfoArr['supplier_nickname'] : '';
        }
        $redis->set('SUPPLIER_REDIS_LIST_INFO_', json_encode($supplierListShowArr));
        return true;
    }

    //修改附加费，兼容美金
    public function saveSuppExtendFee($supplierId, $suppExtendFee, $userId = 0)
    {
        DB::connection('spu')->beginTransaction();
        $result = SupplierModel::where('supplier_id', '=', $supplierId)->update([
            'supp_extend_fee' => $suppExtendFee,
            'update_time' => time()
        ]);
        if (!$result) {
            return false;
        }
        $redis = Redis::connection('sku');
        $redis->hset('supp_extend_fee', $supplierId, $suppExtendFee);
        DB::connection('spu')->commit();
        //(new NoticeService)->sendNotice(3, $supplierId, $userId);
        return true;
    }

    //保存供应商seo信息
    public function saveSupplierSeo($collert)
    {
        $seoModel = new SeoElementModel();
        $cache = $seoModel->getSeoElementCache($collert);

        if ($cache) {//更新
            $collert['id'] = $cache['id'];
            $result = $seoModel->saveSeoElement($collert);
        } else {//新增
            $result = $seoModel->addSeoElement($collert);
        }

        return $result;
    }

    //保存供应商系数
    public function saveSupplierRatio($id, $data)
    {
        $supplierRatioModel = new SupplierRatioModel();
        if (!empty($data['id'])) {
            $findInfo = $supplierRatioModel->where('id', '=', $data['id'])->select('supplier_id',
                'is_default')->first()->toArray();
            if (!$findInfo) {
                return false;
            }
            $data['supplier_id'] = $findInfo['supplier_id'];
            if ($findInfo['is_default'] == 2 && $data['status'] == 2) {
                throw new InvalidRequestException('无法禁用默认系数');
            }
        }

        if (!empty($data['is_default']) && $data['is_default'] != 2) {
            $find = $supplierRatioModel->where('supplier_id', '=', $data['supplier_id'])
                ->where('is_default', '=', 2)->first();
            if (!$find) {
                throw new InvalidRequestException('必须设置为默认系数');
            }
        }

        if (empty($data['id'])) {//新增
            $result = $supplierRatioModel->addSupplierRatio($data, $id, request()->user->userId);
        } else {
            $result = $supplierRatioModel->saveSupplierRatio($data, $id, request()->user->userId);
        }

        return $result;
    }

    //保存供应商系数状态
    public function saveSupplierRatioStatus($id, $data)
    {
        return $this->saveSupplierRatio($id, $data);
    }

    public function getSupplierNameByCanal($canal)
    {
        return SupplierChannelModel::where('supplier_code', $canal)->value('supplier_name');
    }

    //获取供应商名称
    public static function getSupplierNamesBySupplierIds($supplierIds)
    {
        if (!is_array($supplierIds)) {
            $supplierIds = explode(',', $supplierIds);
        }

        return SupplierModel::whereIn('supplier_id', $supplierIds)->pluck('supplier_name')->join(',');
    }

    public static function getSupplierListForXmSelect()
    {
        $supplierList = SupplierModel::where('is_type', 0)->where('status', 1)->select(['supplier_id', 'supplier_name'])->get()->toArray();
        $supplierListForXmSelect = [];
        foreach ($supplierList as $supplier) {
            $supplierListForXmSelect[] = [
                'value' => $supplier['supplier_id'],
                'name' => $supplier['supplier_name']
            ];
        }
        return $supplierListForXmSelect;
    }
}
