<?php

namespace App\Http\Services;

use App\Http\Queue\RabbitQueueModel;
use App\Map\GoodsMap;
use DB;
use Illuminate\Support\Arr;
use Log;
use Illuminate\Support\Facades\Redis;

class ThirdApi
{
    /*
     * 推送自营型号到新wms
     */
    public function pushNewWms($goods_id = 0,$is_debug = 0){
        $rabbit_queue_model = new RabbitQueueModel("trading");
        $GoodsModel = new \App\Http\Models\LiexinData\GoodsModel();
        if ($goods_id){
            $arr=$GoodsModel->where("goods_id",$goods_id)->orderby("goods_id","desc")->limit(1)->get()->toArray();
        }else{
            $arr=$GoodsModel->orderby("goods_id","desc")->limit(1000)->get()->toArray();
        }


        $redis = Redis::connection('sku');
        $redisSpu = Redis::connection("spu");

        $s = 0;

        #查询制造商
        foreach ($arr as $a=>$b){
            $brandInfo = $redis->hget("Self_Brand",$b["brand_id"]);
            if (!$brandInfo){
                print_r("不存在brand_id跳过.".$b["brand_id"]);
                continue;
            }

            $brandInfoArr = json_decode($brandInfo,true);
            $spu_id = $b["spu_id"];
            $spustr = $redisSpu->hget("spu",$spu_id);

            $spuArr = json_decode($spustr,true);
            $standard_brand_id = Arr::get($spuArr,"s_brand_id",0);
            $class_id1 = Arr::get($spuArr,"class_id1",0);
            $standard_brand_name = "";
            if ($standard_brand_id>0){
               $standard_brand_info = $redis->hget("standard_brand",$standard_brand_id);
               $standard_brand_arr = json_decode($standard_brand_info,true);
               $standard_brand_name = $standard_brand_arr["brand_name"];
            }

            #获取分类
            $class_id1_name = "";
            if ($class_id1){
                $classInfo = $redis->hget("pool_class_info",$class_id1);
                if ($classInfo){
                    $class_id1_name = Arr::get(json_decode($classInfo,true),"class_name");
                }
            }

            //拦截必填，没有erp编码不要推送wms
            if (!$b["erp_number"]){
                continue;
            }

            $attrArr = json_decode($b["other_attrs"],true); #{"length":"100","gross_wegiht":"80"}
            $queue_data = [
                'sku_id' => $b["goods_id"],                         //自营货品ID
                'erp_material_number' => $b["erp_number"],      // 金蝶物料编码
                'goods_name' => $b["goods_name"],          // 货品名称
                'sup_brand_id' => $b["brand_id"],                      // 制造商ID
                'standard_brand_id' => $standard_brand_id,            //标准品牌id
                'standard_brand_name' => $standard_brand_name,            //标准品牌名称
                'class_id1' => $class_id1,                            // 一级分类
                'class_name1' => $class_id1_name,                  // 一级分类名称
                'sup_brand_name' => $brandInfoArr["brand_name"],       // 制造商
                'packing' => $b["packing"],                            // 包装方式
                "picking_mode" => $b["pick_type"],                       // 拣货方式
                "encap" =>$b["encap"],                          // 封装/规格
                "goods_unit" => GoodsMap::$packing[$b["goods_unit"]],     // 商品单位
                "mpq" => $b["mpq"],                          // 标准包装量
                "standard_length" => Arr::get($attrArr,"length",0),                          //  标准包装长
                "standard_gross_weight" => Arr::get($attrArr,"gross_wegiht",0),                          //  标准包装长
                "create_name" => "",                  // 创建人
                "create_time" => $b["add_time"],               // 创建时间
                "update_name" => "",                  // 更新人
                "update_time" => $b["update_time"],               // 更新时间
            ];

            $res = $rabbit_queue_model->insertQueue('/queue/zyGoods/addGoods', $queue_data, "lie_queue_wms");
            if ($is_debug){
                print_r(json_encode($queue_data));
                print_r("   ");
                print_r("  推入队列:".'/queue/zyGoods/addGoods');
            }
            write_log("pushNewWms-推送wms:" . json_encode($queue_data) . "\n","footstone");
            $s = $s+1;
        }
    }


    /*
     * 推送联营型号到mws
     */
    public function pushLyWms($goods_id = 0,$is_debug = 0){
        $msg = "";
        $rabbit_queue_model = new RabbitQueueModel("trading");

        $redisSku = Redis::connection('sku');
        $redisSpu = Redis::connection("spu");

        #查询sku
        $skuInfo = $redisSku->hget("sku",$goods_id);
//        print_r($goods_id);
      #  die();
        if (!$skuInfo){
            $msg = "pushLyWms-redis不存在此sku:" . $goods_id ;
            write_log($msg,"footstone");
            return $msg;
        }
        $skuInfoArr = json_decode($skuInfo,true);

        #查询spu
        $spustr = $redisSpu->hget("spu",$skuInfoArr["spu_id"]);
        if (!$spustr){
            $msg = "pushLyWms-redis不存在此spu:" . $goods_id ;
            write_log($msg,"footstone");
            return $msg;
        }
        $spuInfoArr = json_decode($spustr,true);
        $standard_brand_id = Arr::get($spuInfoArr,"s_brand_id",0);
        $class_id1 = Arr::get($spuInfoArr,"class_id1",0);
        $standard_brand_name = "";
        if ($standard_brand_id>0){
            $standard_brand_info = $redisSku->hget("standard_brand",$standard_brand_id);
            $standard_brand_arr = json_decode($standard_brand_info,true);
            $standard_brand_name = $standard_brand_arr["brand_name"];
        }
        $brand_pack = Arr::get($spuInfoArr,"brand_pack","");

        //增加获取上传的包装
        $orgInfo = json_decode($redisSku->hget('sku_raw_map', $goods_id), true);
        if (Arr::get($orgInfo,"pack")){
            $brand_pack = $orgInfo["pack"];
        }

        #获取分类
        $class_id1_name = "";
        if ($class_id1){
           $classInfo = $redisSku->hget("pool_class_info",$class_id1);
           if ($classInfo){
               $class_id1_name = Arr::get(json_decode($classInfo,true),"class_name");
           }
        }

        $packing = ""; //包装方式
        $picking_mode = ""; //拣货方式
        //包装方式 5编带 6袋 7盒 8管 9托盘 10卷 11其他   12剪切带 13散装  ？,
        //'拣货方式:1:裁剪 2：称重 3：计数
        $packingInt = [
            "编带"=>5,
            "袋"=>6,
            "盒"=>7,
            "管"=>8,
            "托盘"=>9,
            "卷"=>10,
            "其他"=>11,
            "剪切带"=>12,
            "散装"=>13,
        ];

        $packingToMode = [
            "编带"=>1,
            "袋"=>3,
            "盒"=>3,
            "管"=>3,
            "托盘"=>3,
            "卷"=>1,
            "散装"=>3,
            "其他"=>3,
        ];

        if (in_array($brand_pack,array_keys($packingInt))){
            $packing = $packingInt[$brand_pack];
            $picking_mode = $packingToMode[$brand_pack];
        }

        $queue_data = [
            'sku_id' => (string)$goods_id,                         //自营货品ID
            'erp_material_number' => 0,      // 金蝶物料编码
            'goods_name' => $spuInfoArr["spu_name"],          // 货品名称
            'sup_brand_id' => $standard_brand_id,                      // 制造商ID
            'standard_brand_id' => $standard_brand_id,            //标准品牌id
            'standard_brand_name' => $standard_brand_name,            //标准品牌名称
            'sup_brand_name' => $standard_brand_name,       // 制造商
            'packing' => $packing,                            // 包装方式
            'class_id1' => $class_id1,                            // 一级分类
            'class_name1' => $class_id1_name,                  // 一级分类名称
            "picking_mode" => $picking_mode,                       // 拣货方式
            "encap" =>Arr::get($spuInfoArr,"encap",""),                          // 封装/规格
            "goods_unit" => "个",     // 商品单位
            "mpq" => $skuInfoArr["mpq"],                          // 标准包装量
            "standard_length" => 0,                          //  标准包装长
            "standard_gross_weight" => 0,                          //  标准包装长
            "create_name" => "",                  // 创建人
            "create_time" => time(),               // 创建时间
            "update_name" => "",                  // 更新人
            "update_time" => time(),               // 更新时间

        ];
        $res = $rabbit_queue_model->insertQueue('/queue/zyGoods/addGoods', $queue_data, "lie_queue_wms");
        if ($is_debug){
            print_r(json_encode($queue_data));
            print_r("   ");
            print_r("  推入队列:".'/queue/zyGoods/addGoods');
            die();
        }
        write_log("pushLyWms-推送wms:" . json_encode($queue_data) . "\n","footstone");
        return $msg;
    }

}
