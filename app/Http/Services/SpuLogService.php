<?php

namespace App\Http\Services;

use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Models\SpuLogModel;
use Arr;

class SpuLogService
{
    //获取spu日志列表
    public static function getSpuLogList($map)
    {
        $query = SpuLogModel::orderBy('id', 'desc');
        if (!empty($map['spu_id'])) {
            $query->where('spu_id', $map['spu_id']);
        }
        $data = $query->paginate($map['limit'] ?? 10)->toArray();

        foreach ($data['data'] as &$item) {
            $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
            $item['content'] = $item['content'] ? json_decode($item['content']) : '';
        }
        unset($item);

        return [
            'list' => $data['data'],
            'total' => $data['total'] ?? 0,
        ];
    }

    //保存spu操作日志
    public static function saveSpuLog($data)
    {
        $spuColumnMap = [
            'spu_name' => 'SPU名称',
            'class_id1' => '一级分类',
            'class_id2' => '二级分类',
            'status' => '状态',
            'images_l' => 'SPU大图',
            'images_s' => 'SPU小图',
            'encap' => '封装',
            'pdf' => '中文PDF资料',
            'en_pdf_url' => '英文PDF资料',
            'spu_brief' => 'SPU简单描述',
            'has_rohs' => '是否有铅',
            'spu_detail' => 'spu详情',
            'remark' => '备注',
            'bussiness_area' => '运用领域',
            'application_level' => '应用级别',
            'eccn' => 'eccn',
            'rohs' => 'rohs',
            'series' => '系列',
            'lifecycle' => '生命周期',
            'brand_pack' => '制造商包装',
            'mpq' => '标准包装量',
            'humistor' => '湿敏电阻',
            'spu_title' => '商品标题名称',
            'standard_lead_time' => '原厂标准交货期',
            'mounting_type' => '安装类型',
            'length' => '长度',
            'width' => '宽度',
            'height' => '高度',
            'weight' => '商品重量',
            'reach_status' => 'REACH状态',
            'customs_code' => '海关编码',
        ];

        $dataAttrs = [];
        $attrs = $data['attrs'] ?? [];
        unset($data['attrs']);
        $spuId = $data['spu_id'];

        if (!empty($attrs)) {
            foreach ($attrs as $attr) {
                $dataAttrs[$attr['attr_name']] = $attr['origin_value'] ?? '';
            }
        }

        //再找出当前mongo的参数
        $originAttrs = [];
        $originAttrsExtend = (new AttrService())->getAttrsExtend($spuId);
        foreach ($originAttrsExtend as $item) {
            $originAttrs[$item['attr_name']] = $item['attr_value'];
        }

        //先去找出spu原来的信息,包括属性
        $originSpu = (new SpuService())->getSpuFromDb($spuId) ?? [];
        $spuExtra = (new SpuService())->getSpuExtra($spuId) ?? [];
        $originSpu = array_merge($originSpu, $spuExtra);

        $data['image_list'] = json_encode($data['spu_image_list']);
        unset($data['spu_image_list']);
        $originSpu['image_list'] = !empty($originSpu['image_list']) ? json_encode($originSpu['image_list']) : '';
        $originSpu['en_pdf_url'] = !empty($originSpu['en_pdf_url']) ? json_encode($originSpu['en_pdf_url']) : '';

        //比对spu基础信息
        $spuDiff = array_diff_assoc($data, $originSpu);
        //进行各个字段的判断
        $contentList = [];
        foreach ($spuDiff as $key => $item) {
            $columnName = \Arr::get($spuColumnMap, $key);
            switch ($key) {
                case 'class_id1':
                    $originClassName1 = PoolClassModel::where('class_id', $originSpu['class_id1'])->value('class_name');
                    $dataClassName1 = PoolClassModel::where('class_id', $data['class_id1'])->value('class_name');
                    $contentList[] = "更新" . $columnName . ",修改前 $columnName: $originClassName1 改成 $dataClassName1";
                    break;
                case 'class_id2':
                    $originClassName2 = PoolClassModel::where('class_id', $originSpu['class_id2'])->value('class_name');
                    $dataClassName2 = PoolClassModel::where('class_id', $data['class_id2'])->value('class_name');
                    $contentList[] = "更新" . $columnName . ",修改前 $columnName: $originClassName2 改成 $dataClassName2";
                    break;
                case 'status':
                    $originStatus = config('field.SpuStatus')[$originSpu['status']] ?? '';
                    $dataStatus = config('field.SpuStatus')[$data['status']] ?? '';
                    $contentList[] = "更新" . $columnName . ",修改前 $columnName: $originStatus 改成 $dataStatus";
                    break;
                case 'has_rohs':
                    $originHasRohsStatus = $originSpu['has_rohs'] > 0 ? '是' : '否';
                    $dataHasRohsStatus = $data['has_rohs'] > 0 ? '是' : '否';
                    $contentList[] = "更新" . $columnName . ",修改前 $columnName: $originHasRohsStatus 改成 $dataHasRohsStatus";
                    break;
                case 'bussiness_area':
                    $originSpuBussinessArea = implode(',', array_map(function ($area) {
                        return Arr::get(config('field.SpuApplicationArea'), $area, '');
                    }, explode('|', $originSpu['bussiness_area'])));
                    $dataBussinessArea = implode(',', array_map(function ($area) {
                        return Arr::get(config('field.SpuApplicationArea'), $area, '');
                    }, explode('|', $data['bussiness_area'])));
                    $contentList[] = "更新" . $columnName . ",修改前 $columnName: $originSpuBussinessArea 改成 $dataBussinessArea";
                    break;
                case 'spu_id':
                    break;
                case 'image_list':
                case 'spu_brief':
                case 'spu_detail':
                    $contentList[] = "更新" . $columnName;
                    break;
                default:
                    if (isset($originSpu[$key])) {
                        $originContent = $originSpu[$key];
                    } else {
                        $originContent = '空';
                    }
                    $contentList[] = "更新" . $columnName . ",修改前 $columnName: $originContent 改成 $data[$key]";
                    break;
            }
        }

        //比对spu参数
        $attrDiff = array_diff_assoc($dataAttrs, $originAttrs);
        foreach ($attrDiff as $key => $item) {
            $contentList[] = "更新分类参数值, 修改前 $key :" . (Arr::get($originAttrs, $key)) . " 改成 $item";
        }

        $contentList = array_map(function ($item) {
            $item .= ";   | ";
            return $item;
        }, $contentList);
        $data['content'] = json_encode($contentList);

        return SpuLogModel::insertGetId([
            'spu_id' => $data['spu_id'],
            'content' => $data['content'],
            'create_time' => time(),
            'create_uid' => request()->user->userId ?? 1000,
            'create_name' => request()->user->name ?? 'admin',
        ]);
    }

    public static function addSpuLog($spuId, $content)
    {
        if (!is_array($content)) {
            $content = json_encode([$content]);
        }
        return SpuLogModel::insertGetId([
            'spu_id' => $spuId,
            'content' => $content,
            'create_time' => time(),
            'create_uid' => request()->user->userId ?? 1000,
            'create_name' => request()->user->name ?? 'admin',
        ]);
    }


}
