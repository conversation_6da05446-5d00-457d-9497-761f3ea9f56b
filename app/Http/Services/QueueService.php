<?php

namespace App\Http\Services;

use App\Http\Models\Cms\ConfigModel;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;

class QueueService
{
    public static function publishQueue($queueName, $data)
    {
        $conn = new AMQPStreamConnection(config('database.connections.rabbitmq.host'),
            config('database.connections.rabbitmq.port'),
            config('database.connections.rabbitmq.login'),
            config('database.connections.rabbitmq.password'));
        $channel = $conn->channel();
        $channel->queue_declare($queueName, false, true, false, false);

        $msg = new AMQPMessage(json_encode($data),
            array('content_type' => 'text/plain'));
        $channel->basic_publish($msg, '', $queueName);
    }
}
