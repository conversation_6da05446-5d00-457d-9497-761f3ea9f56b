<?php

namespace App\Http\Services;

use Illuminate\Support\Arr;
use App\Http\Models\TaskLogModel;
use App\Http\Queue\RabbitQueueModel;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Storage;
use App\Exports\ShopDistributeSkuExport;
use App\Jobs\ImportShopDistributeSkuJob;
use App\Http\Models\BigData\ShopInfoModel;
use App\Exceptions\InvalidRequestException;
use App\Http\Models\BigData\ShopDistributeSkuModel;
use App\Http\Transformers\ShopDistributeSkuTransformer;
use DB;

class ShopDistributeSkuService
{
    public static function getShopDistributeSkuList($map)
    {
        $query = ShopDistributeSkuModel::with(['shop', 'channel', 'supplier_channel'])->orderBy('id', 'desc');

        if (!empty($map['id'])) {
            $query->where('id', $map['id']);
        }

        if (!empty($map['shop_id'])) {
            $query->where('shop_id', $map['shop_id']);
        }


        if (isset($map['status']) && $map['status'] !== '') {
            $query->where('status', $map['status']);
        }

        if (!empty($map['supplier_id'])) {
            $supplierId = explode(',', $map['supplier_id']);
            $query->whereIn('supplier_id', $supplierId);
        }

        if (!empty($map['standard_brand_id'])) {
            $standardBrandId = explode(',', $map['standard_brand_id']);
            $query->whereIn('standard_brand_id', $standardBrandId);
        }

        if (!empty($map['spu_name'])) {
            $query->where('spu_name', 'like', '%' . $map['spu_name'] . '%');
        }

        if (!empty($map['class_id2'])) {
            $classId = explode(',', $map['class_id2']);
            $query->whereIn('class_id2', $classId);
        }

        if (!empty($map['is_invalid_title'])) {
            if ($map['is_invalid_title'] == 1) {
                $query->whereRaw('CHAR_LENGTH(goods_title) > 50');
            } else {
                $query->whereRaw('CHAR_LENGTH(goods_title) <= 50');
            }
        }

        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }

        if (!empty($map['supplier_code'])) {
            $query->where('supplier_code', $map['supplier_code']);
        }


        if (!empty($map['stock_compare_type']) && !empty($map['stock_num'])) {
            $compareMap = [
                'eq' => '=',
                'neq' => '!=',
                'lt' => '<',
                'lte' => '<=',
                'gt' => '>',
                'gte' => '>=',
            ];
            $query->where('stock', $compareMap[$map['stock_compare_type']], $map['stock_num']);
        }

        if (!empty($map['sku_id'])) {
            $query->where('sku_id', $map['sku_id']);
        }

        if (isset($map['is_export']) && $map['is_export'] == 1) {
            $shopList = ShopInfoModel::select(['shop_id', 'shop_name'])->pluck('shop_name', 'shop_id')->toArray();
            $result = $query->get()->toArray();
            foreach ($result as &$item) {
                $item['shop_name'] = $shopList[$item['shop_id']] ?? '';
            }
            unset($item);
            $result = (new ShopDistributeSkuTransformer())->listTransformer($result);
            return $result;
        } else {
            $query->with(['shop']);
            $limit = Arr::get($map, 'limit', 10);
            $result = $query->paginate($limit);
            $result = $result ? $result->toArray() : [];
            $result['data'] = (new ShopDistributeSkuTransformer())->listTransformer($result['data']);
        }


        return [
            'data' => Arr::get($result, 'data', []),
            'count' => Arr::get($result, 'total'),
            'total' => Arr::get($result, 'total'),
        ];
    }

    public static function saveShopDistributeSku($skuIds, $shopIds)
    {
        $skuIds = explode(',', trim($skuIds, ','));
        $shopIds = explode(',', trim($shopIds, ','));
        foreach ($skuIds as $skuId) {
            foreach ($shopIds as $shopId) {
                ShopDistributeSkuModel::create([
                    'sku_id' => $skuId,
                    'shop_id' => $shopId,
                    'create_time' => time(),
                    'create_name' => request()->user->name,
                    'create_uid' => request()->user->userId,
                ]);
            }
        }
    }

    public static function updateDistributeSkuStatus($id, $status)
    {
        $result = ShopDistributeSkuModel::where('id', $id)->update([
            'status' => $status,
            'update_time' => time(),
            'update_name' => request()->user->name,
            'update_uid' => request()->user->userId,
        ]);
        return $result;
    }

    public static function deleteShopDistributeSku($id)
    {
        $result = ShopDistributeSkuModel::where('id', $id)->delete();
        return $result;
    }

    public static function batchSaveShopDistributeSku($skuIds, $shopIds)
    {
        $skuIds = explode(',', trim($skuIds, ','));
        $shopIds = explode(',', trim($shopIds, ','));
        foreach ($skuIds as $skuId) {
            foreach ($shopIds as $shopId) {
                $shopPlatform = ShopInfoModel::where('shop_id', $shopId)->value('platform');
                $sku = (new SkuService())->getSkuCacheInfo($skuId);
                $exit = ShopDistributeSkuModel::where('sku_id', $skuId)->where('shop_id', $shopId)->first();
                if ($exit) {
                    //判断是否被删除
                    if ($exit->is_deleted == 1) {
                        ShopDistributeSkuModel::where('id', $exit->id)->update([
                            'is_deleted' => 0,
                            'update_time' => time(),
                            'update_name' => request()->user->name,
                        ]);
                    }
                    continue;
                }
                $sku = (new SkuService())->getSkuCacheInfo($skuId);
                if (empty($sku['standard_brand_id'])) {
                    throw new InvalidRequestException('商品 : ' . $sku['goods_name'] . ' 标准品牌为空');
                }
                ShopDistributeSkuModel::insert([
                    'sku_id' => $skuId,
                    'shop_id' => $shopId,
                    'spu_id' => $sku['spu_id'],
                    'spu_name' => $sku['spu_name'],
                    'standard_brand_id' => $sku['standard_brand_id'],
                    'class_id2' => $sku['class_id2'],
                    'ladder_price' => $sku['ladder_price'] ? json_encode($sku['ladder_price']) : '',
                    'mpq' => $sku['mpq'],
                    'moq' => $sku['moq'],
                    'stock' => $sku['stock'],
                    'multiple' => $sku['multiple'],
                    'supplier_id' => $sku['supplier_id'],
                    'supplier_code' => $sku['canal'],
                    'platform' => $shopPlatform,
                    'create_time' => time(),
                    'create_name' => request()->user->name,
                ]);
            }
        }
    }

    public static function exportShopDistributeSku($map)
    {
        ini_set('memory_limit', -1);
        $map['is_export'] = 1;
        $result = self::getShopDistributeSkuList($map);
        $data = array_map(function ($item) {
            return [
                $item['sku_id'] . "\t",
                $item['sku']['spu_name'] ?? '',
                isset($item['sku']['class_name1']) && isset($item['sku']['class_name2']) ? $item['sku']['class_name1'] . '/' . $item['sku']['class_name2'] : '',
                $item['sku']['standard_brand_name'] ?? '',
                $item['purchase_coefficient'],
                $item['purchase_price'],
                $item['sale_coefficient'],
                $item['sale_price'],
                $item['shop_id'],
                $item['shop_name'],
                $item['status_name'],
                $item['push_fail_reason'],
                $item['create_time'],
                $item['update_time'],
            ];
        }, $result);
        $shopName = ShopInfoModel::where('shop_id', $map['shop_id'])->value('shop_name');
        $fileName = '店铺分发SKU列表_' . $shopName . '_' . date('Y-m-d_H-i-s') . '.xlsx';
        return Excel::download(new ShopDistributeSkuExport($data), $fileName);
    }

    /**
     * 导入SKU
     *
     * @param array $skuIds SKU ID数组
     * @param int $shopId 店铺 ID
     * @param int|null $userId 用户ID
     * @param string|null $userName 用户名称
     * @return array 处理结果
     */
    public static function importShopDistributeSku($originalName,$filePath, $shopId)
    {
        $userId = request()->user->userId;
        $userName = request()->user->name;
        $logModel = new TaskLogModel();
        $logData = [
            'file_name' => $originalName,
            'log' => date('Y-m-d H:i:s', time()) . " 开始上传处理分发数据,准备丢入异步队列,文件名为 : " . $originalName,
            'status' => 0,
            'admin_id' => $userId,
            'admin_name' => $userName,
            'create_time' => time(),
            'update_time' => time(),
            'type' => TaskLogModel::TYPE_IMPORT_SHOP_DISTRIBUTE_SKU,
        ];
        $logId = $logModel->insertGetId($logData);
         //(new ImportShopDistributeSkuJob($logId,$filePath, $shopId, $userId, $userName))->handle();
        //sleep(1);
        dispatch(new ImportShopDistributeSkuJob($logId,$filePath, $shopId, $userId, $userName));
    }

    public static function updateShopDistributeSku($map)
    {
        $map['update_time'] = time();
        $map['update_name'] = request()->user->name;
        $result = ShopDistributeSkuModel::where('id', $map['id'])->update($map);
        if ($result) {
            //投队列
            (new RabbitQueueModel('trading'))->insertRawQueue('', (int)$map['id'], 'liexin_nplm_update_push_pool_sku');
        }
        return $result;
    }
}
