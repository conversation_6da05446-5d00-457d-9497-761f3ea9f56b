<?php

namespace App\Http\Services;

//标准品牌服务
use App\Http\Models\BrandModel;
use App\Http\Models\StandardBrandMappingModel;
use App\Http\Models\StandardBrandModel;
use App\Http\Models\StandardBrandScmMappingModel;
use App\Http\Models\StandardBrandSpuModel;
use App\Http\Transformers\StandardBrandTransformer;
use App\Imports\StandardBrandSpuImport;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class StandardBrandService
{
    public function getStandardBrandList($map)
    {
        $model = new StandardBrandModel();
        $query = $model->orderBy('standard_brand_id', 'desc');
        $query = $this->filter($query, $map);
        $limit = \Arr::get($map, 'limit', 15);
        $result = $query->paginate($limit);

        $result = $result ? $result->toArray() : [];
        $transformer = new StandardBrandTransformer();
        $result['data'] = $transformer->listTransformer($result['data']);
        return [
            'data' => \Arr::get($result, 'data', []),
            'count' => \Arr::get($result, 'total'),
        ];
    }

    public function filter($query, $map)
    {
        if (!empty($map['brand_name'])) {
            $query->where(function ($q) use ($map) {
                $q->where('brand_name', 'like', "${map['brand_name']}%")
                    ->orWhere('show_name', 'like', "${map['brand_name']}%")
                    ->orWhere('brand_name_en', 'like', "${map['brand_name']}%")
                    ->orWhere('brand_short_name_en', 'like', "${map['brand_name']}%")
                    ->orWhere('brand_name_cn', 'like', "${map['brand_name']}%")
                    ->orWhere('brand_short_name_cn', 'like', "${map['brand_name']}%");
            });
        }

        if (!empty($map['brand_name_raw'])) {
            $query->where('brand_name',  $map['brand_name_raw']);
        }

        if (!empty($map['brand_name_en'])) {
            $query->where('brand_name_en', 'like', "${map['brand_name_en']}%");
        }
        if (!empty($map['standard_brand_id'])) {
            $query->where('standard_brand_id', '=', $map['standard_brand_id']);
        }
        if (!empty($map['brand_name_cn'])) {
            $query->where('brand_name_cn', 'like', "${map['brand_name_cn']}%");
        }
        if ((isset($map['brand_area']) && $map['brand_area'] == '0') || !empty($map['brand_area'])) {
            $query->where('brand_area', $map['brand_area']);
        }
        if (!empty($map['status']) || (isset($map['status']) && $map['status'] == '0')) {
            $query->where('status', $map['status']);
        }
        if (!empty($map['has_picture'])) {
            $map['has_picture'] == 1 ? $query->where('brand_logo', '!=', '') : $query->where('brand_logo', '');
        }
        if (!empty($map['first_letter'])) {
            $query->where('first_letter', $map['first_letter']);
        }
        if (!empty($map['is_show'])) {
            $query->where('is_show', $map['is_show']);
        }

        if (!empty($map['has_brand_brief']) && $map['has_brand_brief'] == -1) {
            $query->where('brand_brief', '');
        }
        if (!empty($map['has_brand_brief']) && $map['has_brand_brief'] == 1) {
            $query->where('brand_brief', '!=', '');
        }
        if (!empty($map['has_brand_desc']) && $map['has_brand_desc'] == -1) {
            $query->where(function ($q) {
                $q->where('brand_desc', '')->orWhere('brand_desc', null);
            });
        }
        if (!empty($map['has_brand_desc']) && $map['has_brand_desc'] == 1) {
            $query->where('brand_desc', '!=', '');
        }
        if (!empty($map['offset_time'])) {
            $query->where(function ($q) use ($map) {
                $q->where('create_time', '>=', $map['offset_time'])->orWhere('update_time', '>=', $map['offset_time']);
            });
        }

        return $query;
    }

    //获取标准品牌主要spu列表
    public function getMainSpuList($standardBrandId, $map)
    {
        $limit = \Arr::get($map, 'limit', 15);
        $result = StandardBrandSpuModel::where('standard_brand_id', $standardBrandId)->paginate($limit);
        $result = !empty($result) ? $result->toArray() : [];
        return [
            'data' => \Arr::get($result, 'data', []),
            'count' => \Arr::get($result, 'total'),
        ];
    }

    public function saveStandardBrand($map)
    {
        $map = \Arr::only($map, [
            'standard_brand_id',
            'brand_name',
            'brand_logo',
            'brand_desc',
            'brand_area',
            'brand_brief',
            'main_product',
            'show_name',
            'brand_name_en',
            'brand_short_name_en',
            'brand_name_cn',
            'brand_short_name_cn',
            'seo_brand_name',
            'seo_title',
            'seo_keywords',
            'seo_description',
            'is_show',
            'status',
            'first_letter',
            'brand_positioning',
            'brand_authorization'
        ]);
        $model = new StandardBrandModel();
        //新增
        if (empty($map['standard_brand_id'])) {
            $data = $map;
            unset($map['brand_id']);
            $data['create_time'] = time();
            $data['create_name'] = request()->user->name;
            $data['create_uid'] = request()->user->userId;
            $result = $model->insertGetId($data);
            $standardBrandId = $result;
        } else {
            $data = $map;
            $data['update_time'] = time();
            $data['update_name'] = request()->user->name;
            $data['update_uid'] = request()->user->userId;
            $str = "\n";
            $str2 = "<div>                                                                                    </div>";
            $data['brand_desc'] = str_replace($str, '', $data['brand_desc']);
            $data['brand_desc'] = str_replace($str2, '', $data['brand_desc']);
            $data['brand_desc'] = trim($data['brand_desc']);
            if (trim(str_replace("&nbsp;", "", strip_tags($data['brand_desc']))) == '') {
                $data['brand_desc'] = '';
            }
            $result = $model->where('standard_brand_id', $map['standard_brand_id'])->update($data);
            $standardBrandId = $data['standard_brand_id'];
        }
        //如果scm_brand_id不为空,那么就要去补充映射
        $scmBrandId = request()->get('scm_brand_id');
        if ($scmBrandId) {
            (new StandardBrandScmMappingModel())->saveMapping($standardBrandId, $scmBrandId);
        }
        $brand = $model->where('standard_brand_id', $standardBrandId)->first()->toArray();
        $this->saveStandardBrandToRedis($standardBrandId, $brand);
        return $result;
    }

    public function saveStandardBrandToRedis($standardBrandId, $brand)
    {
        $redis = Redis::connection('sku');
        $redis->hset('standard_brand', $standardBrandId, json_encode($brand));
    }

    //检查是否有映射品牌
    public function checkHasMappingBrand($standardBrandId)
    {
        return (bool)StandardBrandMappingModel::where('standard_brand_id', $standardBrandId)->exists();
    }

    public function disableStandardBrand($brandId)
    {
        $standardBrandModel = new StandardBrandModel();
        $result = $standardBrandModel->where('standard_brand_id', $brandId)->update([
            'update_time' => time(),
            'status' => $standardBrandModel::STATUS_DISABLE,
        ]);
        $brand = $standardBrandModel->where('standard_brand_id', $brandId)->first()->toArray();
        if ($result) {
            $this->saveStandardBrandToRedis($brandId, $brand);
        }
        return $result;
    }

    public function enableStandardBrand($brandId)
    {
        $standardBrandModel = new StandardBrandModel();
        $result = $standardBrandModel->where('standard_brand_id', $brandId)->update([
            'update_time' => time(),
            'status' => $standardBrandModel::STATUS_OK,
        ]);
        $brand = $standardBrandModel->where('standard_brand_id', $brandId)->first()->toArray();
        if ($result) {
            $this->saveStandardBrandToRedis($brandId, $brand);
        }
        return $result;
    }

    //添加标准品牌的主要产品
    public function addStandardBrandSpu($spuId, $data)
    {
        unset($data['spu_name'], $data['brand_name']);
        if (StandardBrandSpuModel::where('spu_id', $spuId)->exists()) {
            $data['update_time'] = time();
            $result = StandardBrandSpuModel::where('standard_brand_id', $data['standard_brand_id'])
                ->where('spu_id', $spuId)->update($data);
        } else {
            $data['create_time'] = time();
            $result = StandardBrandSpuModel::insert($data);
        }

        return $result;
    }

    public function updateStandardBrandSpu($id, $data)
    {
        $data['update_time'] = time();
        return StandardBrandSpuModel::where('id', $id)->update($data);
    }

    public function importStandardBrandSpu($standardBrandId, $file)
    {
        $spuList = Excel::toArray(new StandardBrandSpuImport, $file)[0];
        $redis = Redis::connection('sku');
        $spuRedis = Redis::connection('spu');
        foreach ($spuList as $key => $row) {
            if ($key == 0) {
                continue;
            }
            //先去校验spuid是否正确
            $spuId = $row[0];
            $typeName = $row[1];
            $sort = (int)$row[2];
            $spuCache = $spuRedis->hget('spu', $spuId);
            if (empty($spuCache)) {
                continue;
            }
            $exist = StandardBrandSpuModel::where('standard_brand_id', $standardBrandId)
                ->where('spu_id', $spuId)->exists();
            $data = [
                'standard_brand_id' => $standardBrandId,
                'spu_id' => $spuId,
                'type' => \Arr::get(array_flip(config('field.StandardBrandSpuType')), $typeName, 0),
                'create_time' => time(),
                'sort' => $sort,
            ];
            if ($exist) {
                StandardBrandSpuModel::where('standard_brand_id', $standardBrandId)
                    ->where('spu_id', $spuId)->update($data);
            } else {
                StandardBrandSpuModel::insert($data);
            }
        }
        return true;
    }

    public function deleteStandardBrandSpu($ids)
    {
        return StandardBrandSpuModel::whereIn('id', $ids)->delete();
    }

    //根据品牌id获取标准品牌名字列表
    public function getStandardBrandNameListByBrandIds($brandIds)
    {
        $brandIds = explode(',', trim($brandIds, ','));
        if (empty($brandIds)) {
            return '';
        }
        $redis = Redis::connection('sku');
        $brands = $redis->hmget('standard_brand', $brandIds);
        $standardBrandNameList = [];
        foreach ($brands as $brand) {
            $brand = json_decode($brand, true);
            $standardBrandNameList[] = $brand['brand_name'];
        }
        return $standardBrandNameList ? implode(',', $standardBrandNameList) : '';
    }

    public function checkStandardBrandNameList($standardBrandNameList = [])
    {
        //去数据库查询
        $validStandardBrandList = StandardBrandModel::whereIn('brand_name',
            $standardBrandNameList)
            ->pluck('brand_name', 'standard_brand_id')->toArray();
        $validStandardBrandMap = array_flip($validStandardBrandList);
        $invalidBrandNameList = [];
        $validStandardBrandNameList = [];
        $validStandardBrandIds = [];
        //判断哪些是错误的,哪些是正确的
        $redis = Redis::connection('sku');
        foreach ($standardBrandNameList as $key => $standardBrandName) {
            if (in_array($standardBrandName, array_values($validStandardBrandList))) {
                $validStandardBrandIds[] = $validStandardBrandMap[$standardBrandName];
                $validStandardBrandNameList[] = $standardBrandName;
                unset($standardBrandNameList[$key]);
            }
        }
        //剩下没有能直接找到标准品牌的,先当作普通品牌处理,然后去找出标准品牌,如果还是找不到,那就是无效品牌了
        foreach ($standardBrandNameList as $key => $checkStandardBrandName) {
            //先去找对应的标准品牌
            $brandId = BrandModel::where('brand_name', $checkStandardBrandName)->value('brand_id');
            $standardBrandId = $redis->hget('standard_brand_mapping', $brandId);
            if (empty($standardBrandId)) {
                $invalidBrandNameList[] = $checkStandardBrandName;
                continue;
            }
            $standardBrand = $redis->hget('standard_brand', $standardBrandId);
            $standardBrand = json_decode($standardBrand, true);
            $standardBrandName = Arr::get($standardBrand, 'brand_name', '');
            $standardBrandId = Arr::get($standardBrand, 'standard_brand_id', '');
            if (empty($standardBrandName)) {
                $invalidBrandNameList[] = $checkStandardBrandName;
                continue;
            }
            $validStandardBrandIds[] = $standardBrandId;
            $validStandardBrandNameList[] = $standardBrandName;
        }
        //整合数据,返回标准的数据和无效的数据
        return [
            'invalid_brand_name_list' => $invalidBrandNameList,
            'valid_brand_name_list' => $validStandardBrandNameList,
            'valid_brand_ids' => $validStandardBrandIds,
        ];
    }

    public function searchStandardBrand($brandName)
    {
        $query = StandardBrandModel::where('brand_name', 'like', $brandName . '%');
        $count = $query->count();
        $brandList = $query->select(['brand_name', 'standard_brand_id'])->paginate(15);
        $brandList = $brandList ? $brandList->toArray() : [];
        $data = Arr::get($brandList, 'data');
        $lastPage = Arr::get($brandList, 'last_page');
        return [
            'errcode' => 0,
            'errmsg' => 'ok',
            'total' => $count,
            'count' => $count,
            'data' => $data,
            'last_page' => $lastPage
        ];
    }

    public function getStandardBrandById($standardBrandId)
    {
        $redis = Redis::connection('sku');
        $standardBrand = $redis->hget('standard_brand', $standardBrandId);
        $standardBrand = json_decode($standardBrand, true);
        if (empty($standardBrand)) {
            return [];
        }
        return $standardBrand;
    }

    public function getStandardBrandByBrandName($brandName)
    {
        $standardBrand = StandardBrandModel::where('brand_name', $brandName)->first();
        return $standardBrand ? $standardBrand->toArray() : [];
    }

    public function getRelatedBrandsByStandardBrandId($standardBrandId)
    {
        $mappingBrandIds = StandardBrandMappingModel::where('standard_brand_id', $standardBrandId)->pluck('brand_id')->toArray();
        $relatedBrands = BrandModel::whereIn('brand_id', $mappingBrandIds)->select([
            'brand_id',
            'brand_name',
            'brand_logo',
        ])->get()->toArray();
        return $relatedBrands ? $relatedBrands : [];
    }

    public function getStandardBrandIdByBrandId($brandId)
    {
        $redis = Redis::connection('sku');
        $standardBrandId = $redis->hget('standard_brand_mapping', $brandId);
        return (int)$standardBrandId;
    }

    public function getStandardBrandMap()
    {
        return StandardBrandModel::pluck('brand_name', 'standard_brand_id')->toArray();
    }

    //获取品牌名称
    public static function getStandardBrandNamesByBrandIds($brandIds)
    {
        if (!is_array($brandIds)) {
            $brandIds = explode(',', $brandIds);
        }

        return StandardBrandModel::whereIn('standard_brand_id', $brandIds)->pluck('brand_name')->join(',');
    }

    //获取标准品牌列表给xm-select
    public static function getStandardBrandListForXmSelect()
    {
        $list = StandardBrandModel::select('brand_name', 'standard_brand_id', 'brand_area')->get()->toArray();
        $list = array_map(function ($item) {
            return [
                'value' => $item['standard_brand_id'],
                'name' => $item['brand_name'],
                'brand_area' => $item['brand_area'],
            ];
        }, $list);
        return $list;
    }

}
