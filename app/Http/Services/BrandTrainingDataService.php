<?php

namespace App\Http\Services;

use App\Http\Models\TrainingDataModel;
use App\Http\Transformers\BrandTrainingDataTransformer;
use App\Imports\BrandTrainingDataImport;
use Illuminate\Support\Arr;
use Maatwebsite\Excel\Facades\Excel;

class BrandTrainingDataService
{
    public function getBrandTrainingDataList($map)
    {
        $model = new TrainingDataModel();
        $query = $model->where('type', TrainingDataModel::TYPE_BRAND)->orderBy('id', 'desc');
        $query = $this->filter($query, $map);
        $limit = Arr::get($map, 'limit', 15);
        $result = $query->paginate($limit);

        $result = $result ? $result->toArray() : [];
        $transformer = new BrandTrainingDataTransformer();
        $result['data'] = $transformer->listTransformer($result['data']);
        return [
            'data' => Arr::get($result, 'data', []),
            'count' => Arr::get($result, 'total'),
        ];
    }

    public function filter($query, $map)
    {
        if (!empty($map['content'])) {
            $query->where('content', 'like', '%' . $map['content'] . '%');
        }
        if (!empty($map['create_name'])) {
            $query->where('create_name', 'like', '%' . $map['create_name'] . '%');
        }
        return $query;
    }

    public function saveBrandTrainingData($map)
    {
        $model = new TrainingDataModel();
        $id = Arr::get($map, 'id');
        $data = [
            'content' => $map['content'],
            'type' => TrainingDataModel::TYPE_BRAND,
            'update_time' => time(),
            'update_name' => request()->user->name ?? 'admin',
        ];

        if ($id) {
            // 更新
            $result = $model->where('id', $id)->update($data);
        } else {
            // 新增
            $data['create_time'] = time();
            $data['create_name'] = request()->user->name ?? 'admin';
            $result = $model->insert($data);
        }

        return $result;
    }

    public function deleteBrandTrainingData($id)
    {
        $model = new TrainingDataModel();
        return $model->where('id', $id)->where('type', TrainingDataModel::TYPE_BRAND)->delete();
    }

    public function getBrandTrainingDataById($id)
    {
        $model = new TrainingDataModel();
        $trainingData = $model->where('id', $id)->where('type', TrainingDataModel::TYPE_BRAND)->first();
        return $trainingData ? $trainingData->toArray() : [];
    }

    public function importBrandTrainingData($file)
    {
        try {
            $path = $file->store('temp');
            $import = new BrandTrainingDataImport();
            Excel::import($import, $path);
            \Storage::delete($path);
            
            return [
                'success' => true,
                'message' => '导入成功'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => '导入失败：' . $e->getMessage()
            ];
        }
    }
}
