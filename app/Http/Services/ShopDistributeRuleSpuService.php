<?php

namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\StandardBrandModel;
use App\Http\Transformers\ShopDistributeRuleSpuTransformer;
use App\Http\Models\BigData\ShopBrandModel;
use App\Http\Models\BigData\ShopDistributeRuleSpuModel;
use Illuminate\Support\Facades\Redis;

class ShopDistributeRuleSpuService
{
    public static function saveShopDistributeRuleSpu($data)
    {

        $eccnList = explode(',', $data['eccn']);
        if (!empty($eccnList)) {
            foreach ($eccnList as $eccn) {
                if (substr_count($eccn, '%') == 1) {
                    throw new InvalidRequestException('eccn模糊匹配只支持双向模糊匹配,比如%A99%');
                }
            }
        }

        if (empty($data['id'])) {
            //判断是否存在
            $id = ShopDistributeRuleSpuModel::insertGetId([
                'brand_id_list' => $data['brand_id_list'],
                'class_id_list' => $data['class_id_list'],
                'platform' => $data['platform'],
                'status' => 1,
                'brand_type' => $data['brand_type'] ?? 1,
                'eccn' => $data['eccn'] ?? '',
                'eccn_file_url' => $data['eccn_file_url'] ?? '',
                'eccn_file_name' => $data['eccn_file_name'] ?? '',
                'create_time' => time(),
                'create_uid' => request()->user->userId,
                'create_name' => request()->user->name,
            ]);
        } else {
            //判断是否存在
            $id = $data['id'];
            ShopDistributeRuleSpuModel::where('id', $data['id'])->update([
                'brand_id_list' => $data['brand_id_list'],
                'class_id_list' => $data['class_id_list'],
                'brand_type' => $data['brand_type'] ?? 1,
                'eccn' => $data['eccn'] ?? '',
                'eccn_file_url' => $data['eccn_file_url'] ?? '',
                'eccn_file_name' => $data['eccn_file_name'] ?? '',
                'update_time' => time(),
                'update_uid' => request()->user->userId,
                'update_name' => request()->user->name,
            ]);
        }
        self::saveShopDistributeRuleSpuToRedis($id);
        return $id;
    }

    public static function saveShopDistributeRuleSpuToRedis($id)
    {
        $distributeRule = ShopDistributeRuleSpuModel::find($id);
        $rule = [
            'platform' => $distributeRule->platform,
            'brand_id_list' => $distributeRule->brand_id_list ? array_map('intval', explode(',', $distributeRule->brand_id_list)) : [],
            'class_id_list' => $distributeRule->class_id_list ? array_map('intval', explode(',', $distributeRule->class_id_list)) : [],
            'brand_type' => $distributeRule->brand_type ?? 1,
            'create_time' => $distributeRule->create_time,
            'update_time' => $distributeRule->update_time,
            'eccn_list' => $distributeRule->eccn ? explode(',', trim($distributeRule->eccn, ',')) : [],
        ];
        $redis = Redis::connection('sku');
        return $redis->hset('shop_distribution_spu', $id, json_encode($rule));
    }

    public static function getShopDistributeRuleSpuList($map)
    {
        $limit = request()->get('limit', 10);
        $model = new ShopDistributeRuleSpuModel();
        $query = $model->orderBy('id', 'desc');

        if (!empty($map['status'])) {
            $query->where('status', $map['status']);
        }

        if (!empty($map['platform'])) {
            $query->where('platform', $map['platform']);
        }

        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }
        $list = $query->paginate($limit)->toArray();
        $transformer = new ShopDistributeRuleSpuTransformer();
        $list['data'] = $transformer->listTransformer($list['data']);
        return [
            'data' => $list['data'],
            'count' => $list['total'],
        ];
    }

    public static function deleteShopDistributeRuleSpu($id)
    {
        $result = ShopDistributeRuleSpuModel::where('id', $id)->delete();
        if ($result) {
            $redis = Redis::connection('sku');
            $redis->hdel('shop_distribution', $id);
        }
        return $result;
    }


    public static function updateDistributeRuleSpuStatus($id, $status)
    {
        $result = ShopDistributeRuleSpuModel::where('id', $id)->update([
            'status' => $status,
            'update_time' => time(),
            'update_name' => request()->user->name,
            'update_uid' => request()->user->userId,
        ]);
        if ($result && $status == -1) {
            $redis = Redis::connection('sku');
            $redis->hdel('shop_distribution', $id);
        }

        if ($result && $status == 1) {
            self::saveShopDistributeRuleSpuToRedis($id);
        }

        return $result;
    }
}
