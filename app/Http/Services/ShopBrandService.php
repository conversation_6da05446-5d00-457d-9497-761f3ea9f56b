<?php

namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\BigData\ShopBrandMappingModel;
use App\Http\Models\BigData\ShopBrandModel;

class ShopBrandService
{

    public static function getShopBrandList($map)
    {
        $query = ShopBrandModel::orderBy('id', 'desc');

        if (!empty($map['brand_name'])) {
            $query->where('brand_name', 'like', '%' . $map['brand_name'] . '%');
        }

        if (!empty($map['brand_id'])) {
            $query->where('brand_id', $map['brand_id']);
        }

        if (!empty($map['platform'])) {
            $query->where('platform', $map['platform']);
        }

        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }

        //export
        if (!empty($map['is_export'])) {
            return self::transformData($query->get()->toArray());
        }

        $total = $query->count();
        $list = $query->paginate($map['limit'] ?? 10)->toArray();
        $list['data'] = self::transformData($list['data']);
        return [
            'data' => $list['data'],
            'count' => $total
        ];
    }

    protected static function transformData($data)
    {
        foreach ($data as $key => $item) {
            $item['platform_name'] = config('field.ShopPlatform')[$item['platform']];
            $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
            $item['status_name'] = $item['status'] ? '启用' : '禁用';
            $data[$key] = $item;
        }
        return $data;
    }

    public static function getShopBrandByPlatform($platform = 1)
    {
        $brandList = ShopBrandModel::where('platform', $platform)->get()->toArray();
        $data = [];
        foreach ($brandList as $brand) {
            $data[$brand['id']] = $brand['brand_name'] . " ({$brand['brand_id']})";
        }

        return $data;
    }


    public static function saveShopBrand($data)
    {
        $platFormList = explode(',', trim($data['platform_list']));
        foreach ($platFormList as $platform) {
            $platformName = config('field.ShopPlatform')[$platform] ?? '未知';
            //判断是否已经存在
            if (empty($data['id'])) {
                if (ShopBrandModel::where('brand_id', $data['brand_id'])->where('platform', $platform)->exists()) {
                    throw new InvalidRequestException('平台 [ ' . $platformName . ' ] 对应的品牌id已经存在');
                }
            }
            if (empty($data['id'])) {
                ShopBrandModel::insert([
                    'brand_name' => $data['brand_name'],
                    'brand_id' => $data['brand_id'],
                    'platform' => $platform,
                    'create_time' => time(),
                ]);
            } else {
                ShopBrandModel::where('id', $data['id'])->update([
                    'brand_name' => $data['brand_name'],
                    'brand_id' => $data['brand_id'],
                    'platform' => $platform,
                    'update_time' => time(),
                ]);
            }
        }

    }
}
