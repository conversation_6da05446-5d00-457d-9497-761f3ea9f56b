<?php

namespace App\Http\Services;

use App\Http\Models\BigData\ShopAttrMappingModel;
use App\Http\Models\BigData\ShopAttrModel;
use App\Http\Models\BigData\ShopBrandModel;
use App\Http\Models\BigData\ShopClassMappingModel;
use App\Http\Models\BigData\ShopClassModel;
use App\Http\Models\PoolClass\PoolClassAttrModel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Redis;

class ShopAttrService
{

    public static function getShopAttrList($map)
    {
        $query = ShopAttrModel::orderBy('id', 'desc');

        if (!empty($map['attr_name'])) {
            $query->where('attr_name', 'like', '%' . $map['attr_name'] . '%');
        }

        if (!empty($map['platform'])) {
            $query->where('platform', $map['platform']);
        }

        if (!empty($map['class_id'])) {
            $query->where('class_id', $map['class_id']);
        }

        if (!empty($map['attr_id'])) {
            $query->where('attr_id', $map['attr_id']);
        }

        if (!empty($map['default_unit'])) {
            $query->where('default_unit', 'like', '%' . $map['default_unit'] . '%');
        }

        if (!empty($map['is_required'])) {
            $query->where('is_required', $map['is_required']);
        }

        if (!empty($map['status'])) {
            $query->where('status', $map['status']);
        }

        if (!empty($map['input_type'])) {
            $query->where('input_type', $map['input_type']);
        }

        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }

        // 处理导出请求
        if (!empty($map['is_export'])) {
            $result = $query->get()->toArray();
            return self::transformData($result);
        }

        $list = $query->paginate($map['limit'] ?? 10)->toArray();

        $list['data'] = array_map(function ($item) {
            $item['input_type_name'] = config('field.ShopAttrInputType')[$item['input_type']] ?? '';
            $item['status_name'] = $item['status'] == 1 ? '正常' : '删除';
            $item['is_required_name'] = $item['is_required'] == 1 ? '必选' : '非必选';
            $item['platform_name'] = config('field.ShopPlatform')[$item['platform']] ?? '';
            $item['is_mapping_name'] = $item['is_mapping'] == 1 ? '已映射' : '未映射';
            $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
            return $item;
        }, $list['data']);

        return $list;
    }

    /**
     * 转换数据用于导出
     *
     * @param array $data
     * @return array
     */
    public static function transformData($data)
    {
        return array_map(function ($item) {
            $item['input_type_name'] = config('field.ShopAttrInputType')[$item['input_type']] ?? '';
            $item['status_name'] = $item['status'] == 1 ? '正常' : '删除';
            $item['is_required_name'] = $item['is_required'] == 1 ? '必选' : '非必选';
            $item['platform_name'] = config('field.ShopPlatform')[$item['platform']] ?? '';
            $item['is_mapping_name'] = $item['is_mapping'] == 1 ? '已映射' : '未映射';
            $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
            return $item;
        }, $data);
    }

    public static function getShopAttrByPlatform($platform = 1)
    {
        return ShopAttrModel::where('platform', $platform)->pluck('attr_name', 'id')->toArray();
    }


    //选择到分类id以后,拉取所有的参数出来,并且也要把对应猎芯分类的参数给拉取出来
    public static function getAttrsByShopClassId($classId, $platform = 1)
    {
        $shopClassId = ShopClassModel::where('id', $classId)->where('platform', $platform)->value('class_id');
        //先去找到第三方的参数列表
        $shopAttrList = ShopAttrModel::where('class_id', $shopClassId)->where('platform', $platform)
            ->orderBy('is_required', 'desc')->get()->toArray();
        $shopAttrList = array_filter($shopAttrList, function ($item) {
            //如果状态为删除,并且is_mapping是否处理过为1的话,就不要这个参数了
            return !($item['status'] == 0 && $item['is_mapping'] == 1);
        });
        $shopAttrList = array_values($shopAttrList);
        $shopAttrList = array_map(function ($item) {
            $item['enum_value'] = $item['enum_value'] ? json_decode($item['enum_value'], true) : [];
            $item['unit'] = $item['unit'] ? json_decode($item['unit'], true) : [];
            $item['input_type_name'] = config('field.ShopAttrInputType')[$item['input_type']] ?? '';
            $item['origin_shop_attr_id'] = $item['id'];
            return $item;
        }, $shopAttrList);
        //再去找出对应的猎芯分类id
        $lieClassId = ShopClassMappingModel::where('shop_class_id', $classId)->value('lie_class_id');
        //最后找出猎芯这个分类的所有参数
        $lieAttrList = PoolClassAttrModel::where('class_id', $lieClassId)->get()->toArray();
        foreach (config('field.LieSpuColumnAttr') as $key => $column) {
            //如果是墨卡托,那么就不展示sku_eccn
            if ($platform == 2 && $key == -7) {
                continue;
            }
            array_unshift($lieAttrList, [
                'attr_id' => $key,
                'attr_name' => $column,
            ]);
        }


        //还要返回草稿,如果有的话
        $redis = Redis::connection('sku');
        $draft = $redis->hget('lie_shop_attr_mapping_draft', $classId . '_' . $platform);
        if (!empty($draft)) {
            $draft = json_decode($draft, true);
            foreach ($draft as $key => &$item) {
                if (isJsonDeserializable($item['value'])) {
                    $item['value'] = json_decode($item['value'], true);
                }
            }
        }
        // $shopAttrList = array_reduce($shopAttrList, function ($carry, $item) {
        //     if (!isset($carry[$item['attr_id']]) || $carry[$item['attr_id']]['platform'] !== $item['platform']) {
        //         $carry[$item['attr_id']] = $item;
        //     }
        //     return $carry;
        // }, []);
        // $shopAttrList = array_values($shopAttrList);

        return [
            'shop_attr_list' => $shopAttrList,
            'lie_attr_list' => $lieAttrList,
            'draft' => $draft ?? '',
        ];
    }

    //根据分类id批量判断对应的第三方分类的属性有没有变动,通过shop_attr表的status(是否被删除),is_mapping(为0的话就要重新映射)
    public static function checkShopAttrChangeByClassMappingIds($classMappingIds)
    {
        //先去找到对应的第三方分类id,通过分类映射表
        $shopClassIds = ShopClassMappingModel::whereIn('id', $classMappingIds)->pluck('shop_class_id')->toArray();
        //通过上面的分类,获取原始分类id
        $classIds = ShopClassModel::whereIn('id', $shopClassIds)->pluck('class_id')->toArray();
        //然后找出对应的所有属性
        $shopAttrList = ShopAttrModel::whereIn('class_id', $classIds)->where(function ($query) {
            $query->where('is_mapping', 0);
        })->where('is_required', 1)->get()->toArray();
        //分配回给原来的分类
        $shopAttrGroupList = collect($shopAttrList)->groupBy(['class_id'])->toArray();
        //然后分配到对应的分类映射上面去
        $shopClassIdList = [];
        foreach ($shopAttrGroupList as $classId => $shopAttrList) {
            foreach ($shopAttrList as $shopAttr) {
                $platform = $shopAttr['platform'];
                //找到对应的shop_class_id
                $shopClassId = ShopClassModel::where('class_id', $classId)->where('platform', $platform)->value('id');
                //dump($shopClassId);
                $shopClassIdList[] = $shopClassId;
            }
        }
        //然后返回一个有问题的数组,和shop_class_id判断即可
        return array_unique($shopClassIdList);
    }
}
