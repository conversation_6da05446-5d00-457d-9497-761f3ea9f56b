<?php

namespace App\Http\Services;

use App\Http\Models\BigData\ShopAttrModel;
use App\Http\Models\BigData\ShopClassMappingModel;
use App\Http\Models\BigData\ShopClassModel;
use App\Http\Models\BigData\ShopBrandModel;

class ShopClassService
{

    public function getShopClassList($map)
    {
        $query = ShopClassModel::query();

        if (!empty($map['class_name'])) {
            $query->where('class_name', 'like', '%' . $map['class_name'] . '%');
        }

        if (!empty($map['platform'])) {
            $query->where('platform', $map['platform']);
        }

        if (!empty($map['class_id'])) {
            $query->where('class_id', $map['class_id']);
        }

        if (!empty($map['create_time'])) {
            $startime = strtotime(explode('~', $map['create_time'])[0]);
            $endtime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startime, $endtime]);
        }

        if (!empty($map['is_export'])) {
            $list = $query->orderBy('id', 'desc')->get()->toArray();
            $list = self::transformData($list);
            return $list;
        }

        $total = $query->count();
        $list = $query->orderBy('id', 'desc')
            ->paginate($map['limit'] ?? 10)
            ->toArray();
        $list['data'] = self::transformData($list['data']);
        return [
            'data' => $list['data'],
            'count' => $total
        ];
    }

    protected static function transformData($data)
    {
        foreach ($data as $key => $item) {
            $data[$key]['platform_name'] = config('field.ShopPlatform')[$item['platform']] ?? '';
            $data[$key]['status_name'] = $item['status'] ? '启用' : '禁用';
            $data[$key]['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
        }
        return $data;
    }

    public static function getShopClassByPlatform($platform = 1, $hasMapping = false)
    {
        //判断是否是要包含映射的
        if (!$hasMapping) {
            $classList = ShopClassModel::where('platform', $platform)->get()->toArray();
        } else {
            //$classList = ShopClassModel::has('shop_class_mapping')->where('platform', $platform)->get()->toArray();
            //暂时放开限制
            $classList = ShopClassModel::where('platform', $platform)->get()->toArray();
        }
        $topClassList = $classList;
        $topClassList = array_map(function ($class) {
            $class['name'] = "{$class['class_name']} ({$class['class_id']})";
            $class['class_name'] = "{$class['class_name']} ({$class['class_id']})";
            $class['value'] = $class['id'];
            return $class;
        }, $topClassList);
        return $topClassList;
    }

    public static function getShopClassMappingInitValue($mappingId)
    {
        $shopClassId = ShopClassMappingModel::where('id', $mappingId)->value('shop_class_id');
        //构建需要的数据
        return [[
            'id' => $shopClassId,
            'class_name' => ShopClassModel::where('id', $shopClassId)->value('class_name'),
        ]];
    }


    //转移VC分类数据到这边新的表
    public static function transferVCClassData()
    {
        $jdClass = \DB::connection('distribution')->table('lie_jd_vc_categories')->get()->toArray();

        foreach ($jdClass as $key => $value) {
            $value = (array)$value;
            if (ShopClassModel::where('class_name', $value['name'])->where('platform', 1)->exists()) {
                continue;
            }
            //先去判断深度,深度为3,那么就是父级分类
            if ($value['depth'] == 3) {
                ShopClassModel::insert([
                    'parent_id' => 0,
                    'class_id' => $value['id'],
                    'class_name' => $value['name'],
                    'platform' => 1,
                    'create_time' => time(),
                    'depth' => 3,
                ]);
            } else {
                //深度4的情况,还会存在对应父分类找不到的情况,因为上面深度3的分类没有完整所有的父分类
                //先检查父分类是否存在
                if (!ShopClassModel::where('class_name', $value['cid3_name'])->where('platform', 1)->exists()) {
                    //不存在的话,先插入
                    ShopClassModel::insert([
                        'parent_id' => 0,
                        'class_id' => $value['cid3'],
                        'class_name' => $value['cid3_name'],
                        'platform' => 1,
                        'create_time' => time(),
                        'depth' => 3,
                    ]);
                }
                //然后插入子级分类
                ShopClassModel::insert([
                    'parent_id' => $value['cid3'],
                    'class_id' => $value['id'],
                    'class_name' => $value['name'],
                    'platform' => 1,
                    'create_time' => time(),
                    'depth' => 4,
                ]);
            }
        }
    }

    //转移授权分类数据到这边新的表
    public static function transferClassData()
    {
        $jdClass = \DB::connection('distribution')->table('lie_jd_authorized_categories')->get()->toArray();

        foreach ($jdClass as $key => $value) {
            $value = (array)$value;
            if (ShopClassModel::where('class_name', $value['category_name'])->where('platform', 1)->exists()) {
                continue;
            }
            //先去判fid,如果不存在,则先插入
            if (!ShopClassModel::where('class_id', $value['fid'])->exists()) {
                ShopClassModel::insert([
                    'parent_id' => 0,
                    'class_id' => $value['category_id'],
                    'class_name' => $value['category_name'],
                    'platform' => 1,
                    'create_time' => time(),
                    'depth' => 3,
                ]);
            }

            //然后插入子级分类
            ShopClassModel::insert([
                'parent_id' => $value['fid'],
                'class_id' => $value['category_id'],
                'class_name' => $value['category_name'],
                'platform' => 1,
                'create_time' => time(),
                'depth' => 4,
            ]);
        }
    }

    public static function transferBrandData()
    {
        //转移数据到品牌表
        $jdBrandList = \DB::connection('distribution')->table('lie_jd_vc_bind_brand')->get()->toArray();
        //插入到新的表
        foreach ($jdBrandList as $jdBrand) {
            $jdBrand = (array)$jdBrand;
            ShopBrandModel::insert([
                'brand_name' => $jdBrand['name'],
                'brand_id' => $jdBrand['id'],
                'platform' => 1,
                'create_time' => time(),
                'update_time' => time(),
            ]);
        }
    }

    public static function transferAttrData()
    {
        //转移数据到品牌表
        $jdAttrList = \DB::connection('distribution')->table('lie_jd_category_attr')->get()->toArray();
        //插入到新的表
        foreach ($jdAttrList as $jdAttr) {
            $jdAttr = (array)$jdAttr;
            ShopAttrModel::insert([
                'attr_name' => $jdAttr['attr_name'],
                'attr_id' => $jdAttr['attr_id'],
                'class_id' => $jdAttr['category_id'],
                'is_required' => 1,
                'create_time' => time(),
                'update_time' => time(),
            ]);
        }
    }
}
