<?php

namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\BigData\ShopAttrModel;
use App\Http\Models\PoolClass\ClassAttrUnitConvertModel;
use App\Http\Models\StandardBrandModel;
use App\Http\Transformers\ShopUnitMappingTransformer;
use App\Http\Models\BigData\ShopBrandModel;
use App\Http\Models\BigData\ShopUnitMappingModel;
use Illuminate\Support\Facades\Redis;

class ShopUnitMappingService
{
    public static function saveShopUnitMapping($data)
    {
        if (empty($data['id'])) {
            //判断是否存在
            if (ShopUnitMappingModel::where('unit_name', $data['unit_name'])->where('platform', $data['platform'])->exists()) {
                throw new InvalidRequestException('该单位映射已经存在,不能新增');
            }
            $data['unit_name'] = str_replace("'", '"', $data['unit_name']);
            $id = ShopUnitMappingModel::insertGetId([
                'standard_unit_name' => $data['standard_unit_name'],
                'attr_name' => $data['attr_name'],
                'platform' => $data['platform'],
                'unit_name' => $data['unit_name'],
                'ratio' => $data['ratio'],
                'create_time' => time(),
                'create_uid' => request()->user->userId,
                'create_name' => request()->user->name,
            ]);
        } else {
            $id = $data['id'];
            ShopUnitMappingModel::where('id', $data['id'])->update([
                'ratio' => $data['ratio'],
                'update_time' => time(),
                'update_uid' => request()->user->userId,
                'update_name' => request()->user->name,
            ]);
        }
        return $id;
    }


    public static function getShopUnitMappingList($map)
    {
        $limit = request()->get('limit', 10);
        $model = new ShopUnitMappingModel();
        $query = $model->with(['lie_unit_convert'])->orderBy('id', 'desc');

        if (!empty($map['platform'])) {
            $query->where('platform', $map['platform']);
        }

        if (!empty($map['standard_unit_name'])) {
            $query->where('standard_unit_name', 'like', "%{$map['standard_unit_name']}%");
        }

        if (!empty($map['convert_name'])) {
            $standardUnitNameList = ClassAttrUnitConvertModel::where('convert_name', 'like', "%{$map['convert_name']}%")->pluck('standard_unit_name')->toArray();
            $query->whereIn('standard_unit_name', $standardUnitNameList);
        }

        if (!empty($map['unit_name'])) {
            $query->where('unit_name', 'like', "%{$map['unit_name']}%");
        }

        if (!empty($map['attr_name'])) {
            $query->where('attr_name', $map['attr_name']);
        }

        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }
        $list = $query->paginate($limit)->toArray();
        $transformer = new ShopUnitMappingTransformer();
        $list['data'] = $transformer->listTransformer($list['data']);
        return [
            'data' => $list['data'],
            'count' => $list['total'],
        ];
    }

    public static function deleteShopUnitMapping($id)
    {
        $result = ShopUnitMappingModel::where('id', $id)->delete();
        return $result;
    }


    public static function updateUnitMappingStatus($id, $status)
    {
        $result = ShopUnitMappingModel::where('id', $id)->update([
            'status' => $status,
            'update_time' => time(),
            'update_name' => request()->user->name,
            'update_uid' => request()->user->userId,
        ]);
        if ($result && $status == -1) {
            $redis = Redis::connection('sku');
            $redis->hdel('shop_distribution', $id);
        }

        if ($result && $status == 1) {
            self::saveShopUnitMappingToRedis($id);
        }

        return $result;
    }

    public static function getShopUnitListByPlatform($platform = 1, $notMapping = false)
    {
        if (!$notMapping) {
            $shopUnitNameList = ShopAttrModel::where('default_unit', '!=', '')->where('platform', $platform)
                ->select('default_unit')->distinct()->pluck('default_unit', 'default_unit')->toArray();
        } else {
            $shopUnitNameList = ShopAttrModel::leftjoin('lie_shop_unit_mapping', function ($join) use ($platform) {
                $join->on('lie_shop_attr.default_unit', '=', 'lie_shop_unit_mapping.unit_name')->where('lie_shop_unit_mapping.platform', $platform);
            })
                ->where('default_unit', '!=', '')->where('lie_shop_attr.platform', $platform)
                ->whereNull('lie_shop_unit_mapping.id')
                ->select('default_unit')->distinct()->pluck('default_unit', 'default_unit')->toArray();
        }

        return $shopUnitNameList;
    }

    //找出没有映射的单位
    public static function getNotMappingUnit($mappingId)
    {
        $mapping = ShopUnitMappingModel::find($mappingId)->toArray();
        $result = ShopAttrModel::leftjoin('lie_shop_unit_mapping', 'lie_shop_attr.default_unit', '=', 'lie_shop_unit_mapping.unit_name')
            ->where('default_unit', '!=', '')->where('lie_shop_attr.platform', $mapping['platform'])
            ->select('default_unit')->distinct()->pluck('default_unit', 'default_unit')->toArray();
        $selfAttr = ShopAttrModel::where('default_unit', $mapping['unit_name'])->pluck('default_unit', 'default_unit')->toArray();
        $result = array_merge($selfAttr, $result);
        return $result;
    }

    public static function getNotMappingShopUnitList($map)
    {
        $limit = request()->get('limit', 10);
        $model = new ShopAttrModel();
        $query = $model->select([
            'lie_shop_attr.*',
        ])->distinct()->leftJoin('lie_shop_unit_mapping', function ($join) use ($map) {
            $join->on('lie_shop_attr.default_unit', '=', 'lie_shop_unit_mapping.unit_name');
            if (!empty($map['platform'])) {
                $join->where('lie_shop_unit_mapping.platform', $map['platform']);
            }

        })->where('default_unit', '!=', '')->whereNull('lie_shop_unit_mapping.id')->groupBy('default_unit');
        $query = $query->orderBy('lie_shop_attr.id', 'desc');

        if (!empty($map['platform'])) {
            $query->where('lie_shop_attr.platform', $map['platform']);
        }

        if (!empty($map['unit_name'])) {
            $query->where('lie_shop_attr.default_unit', 'like', "%{$map['unit_name']}%");
        }

        if (!empty($map['attr_name'])) {
            $query->where('lie_shop_attr.attr_name', $map['attr_name']);
        }

        if ((isset($map['is_required']) && $map['is_required'] === '0') || !empty($map['is_required'])) {
            $query->where('is_required', $map['is_required']);
        }

        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }


        $list = $query->paginate($limit)->toArray();
        $transformer = new ShopUnitMappingTransformer();
        $list['data'] = $transformer->listTransformer($list['data']);
        return [
            'data' => $list['data'],
            'count' => $list['total'],
        ];
    }

}
