<?php

namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use App\Exports\AlikeSpuUploadResultExport;
use App\Http\Models\AgentBrandModel;
use App\Http\Models\AgentBrandSpuModel;
use App\Http\Models\AlikeSpuCenterModel;
use App\Http\Models\AlikeSpuLogModel;
use App\Http\Models\AlikeSpuModel;
use App\Http\Models\Supplier\SupplierChannelModel;
use App\Http\Models\UploadLogModel;
use App\Http\Transformers\SkuTransformer;
use App\Imports\AlikeSpuImport;
use App\Imports\AlikeSpuUploadResultImport;
use App\Jobs\UploadAlikeSpu;
use http\Exception\InvalidArgumentException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Response;
use Maatwebsite\Excel\Facades\Excel;

class AlikeSpuService
{

    public static function uploadAlikeSpu($file)
    {
        if (empty($file)) {
            throw new InvalidRequestException('请上传文件');
        }
        if ($file->isValid()) {
            $clientName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            if ($extension != "xlsx") {
                throw new InvalidRequestException('上传文件非法');
            }
            $newName = md5(date('ymdhis') . $clientName) . "." . $extension;
            $uploadDir = storage_path() . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR;
            $path = $uploadDir . $newName;
            $file->move($uploadDir, $newName);

            $adminId = request()->user->userId;
            $adminName = request()->user->name;
            $logModel = new AlikeSpuLogModel();
            $logData = [
                'file_name' => $clientName,
                'log' => date('Y-m-d H:i:s', time()) . (" 开始上传映射商品 "),
                'status' => 0,
                'admin_id' => $adminId,
                'admin_name' => $adminName,
                'create_time' => time(),
                'update_time' => time(),
            ];
            $logId = $logModel->insertGetId($logData);

            $host = request()->getHost();
            if (strpos($host, 'ichunt.net') !== false) {
                $result = dispatch(new UploadAlikeSpu($logId, $path));
            } else {
                (new UploadAlikeSpu($logId, $path))->handle();
                $result = true;
            }
            if (!$result) {
                throw new InvalidRequestException('推送上传任务失败');
            }
            return $result;
        } else {
            throw new InvalidRequestException('上传文件非法');
        }
    }


    public static function getAlikeSpuList($map)
    {
        $query = AlikeSpuModel::where('is_deleted', 0)->orderBy('id', 'desc');
        if (!empty($map['spu_name'])) {
            $query->where('spu_name', 'like', '%' . $map['spu_name'] . '%');
        }
        if (!empty($map['alike_spu_name'])) {
            $query->where('alike_spu_name', 'like', '%' . $map['alike_spu_name'] . '%');
        }
        if (!empty($map['brand_name'])) {
            $query->where('brand_name', 'like', '%' . $map['brand_name'] . '%');
        }
        if (!empty($map['alike_brand_name'])) {
            $query->where('alike_brand_name', 'like', '%' . $map['alike_brand_name'] . '%');
        }
        if (!empty($map['pin_to_pin']) || (isset($map['pin_to_pin']) && $map['pin_to_pin'] === '0')) {
            $query->where('pin_to_pin', $map['pin_to_pin']);
        }

        if (!empty($map['match_rate'])) {
            $map['match_rate'] = $map['match_rate'] / 100;
            switch ($map['match_rate_compare_type']) {
                case 'gte':
                    $query->where('match_rate', '>=', $map['match_rate']);
                    break;
                case 'gt':
                    $query->where('match_rate', '>', $map['match_rate']);
                    break;
                case 'lte':
                    $query->where('match_rate', '<=', $map['match_rate']);
                    break;
                case 'lt':
                    $query->where('match_rate', '<', $map['match_rate']);
                    break;
                case 'eq':
                    $query->where('match_rate', '=', $map['match_rate']);
                    break;
                case 'neq':
                    $query->where('match_rate', '!=', $map['match_rate']);
                    break;
            }
        }

        if (!empty($map['add_time'])) {
            $startTime = strtotime(explode('~', $map['add_time'])[0]);
            $endTime = strtotime(explode('~', $map['add_time'])[1]);
            $query->whereBetween('add_time', [$startTime, $endTime]);
        }
        if (!empty($map['admin_id'])) {
            $query->where('admin_id', $map['admin_id']);
        }
        $limit = Arr::get($map, 'limit', 10);

        // 预加载标准品牌关系
        $query->with(['standard_brand', 'alike_standard_brand']);

        $result = $query->orderBy('add_time', 'desc')->paginate($limit);
        $result = $result ? $result->toArray() : [];
        $userIds = array_unique(array_column($result['data'], 'admin_id'));
        $usersInfo = AlikeSpuModel::getCreateUsersInfo($userIds);

        // 构建skuGroupCount请求参数
        $keywordParams = [];
        foreach ($result['data'] as $value) {
            // 获取被替代型号的标准品牌ID
            $standardBrandId = isset($value['standard_brand']) ? $value['standard_brand']['standard_brand_id'] : null;
            if (!empty($value['spu_name']) && !empty($standardBrandId)) {
                $keywordParams[] = [
                    'spu_name' => $value['spu_name'],
                    'standard_brand_id' => $standardBrandId
                ];
            }

            // 获取替代型号的标准品牌ID
            $alikeStandardBrandId = isset($value['alike_standard_brand']) ? $value['alike_standard_brand']['standard_brand_id'] : null;
            if (!empty($value['alike_spu_name']) && !empty($alikeStandardBrandId)) {
                $keywordParams[] = [
                    'spu_name' => $value['alike_spu_name'],
                    'standard_brand_id' => $alikeStandardBrandId
                ];
            }
        }

        // 调用skuGroupCount接口
        $skuCounts = [];
        if (!empty($keywordParams)) {
            $url = config('website.search_domain_new') . '/sku/skuGroupCount';
            $response = Http::asForm()->post($url, [
                'keyword' => json_encode($keywordParams),
                'no_rule' => "1122"
            ]);

            $responseData = $response->json();
            if (isset($responseData['error_code']) && $responseData['error_code'] == 0) {
                $skuCounts = $responseData['data'] ?? [];
            }
        }
        foreach ($result['data'] as $key => &$value) {
            $value['no'] = $value['id'];
            $value['admin_name'] = Arr::get($usersInfo, $value['admin_id']);
            $value['pin_to_pin'] = config('field.PinToPinMap')[$value['pin_to_pin']];
            $value['add_time'] = date('Y-m-d H:i:s', $value['add_time']);
            $value['match_rate'] = $value['match_rate'] * 100 . '%';

            // 添加SKU数量信息
            $standardBrandId = isset($value['standard_brand']) ? $value['standard_brand']['standard_brand_id'] : '';
            $alikeStandardBrandId = isset($value['alike_standard_brand']) ? $value['alike_standard_brand']['standard_brand_id'] : '';

            $spuKey = $value['spu_name'] . '|' . $standardBrandId;
            $alikeSpuKey = $value['alike_spu_name'] . '|' . $alikeStandardBrandId;
            $value['effective_spu_num'] = $skuCounts[$spuKey] ?? 0;
            $value['effective_alike_spu_num'] = $skuCounts[$alikeSpuKey] ?? 0;
        }
        unset($value);

        return [
            'data' => Arr::get($result, 'data', []),
            'count' => Arr::get($result, 'total'),
            'total' => Arr::get($result, 'total'),
        ];
    }

    public static function getAlikeSpuUploadList($map)
    {
        $query = AlikeSpuLogModel::orderBy('create_time', 'desc');

        if (!empty($map['status']) || (isset($map['status']) && $map['status'] === '0')) {
            $query->where('status', $map['status']);
        }
        if (!empty($map['admin_id'])) {
            $query->where('admin_id', $map['admin_id']);
        }
        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }

        $limit = Arr::get($map, 'limit', 10);
        $result = $query->paginate($limit);
        $result = $result ? $result->toArray() : [];
        foreach ($result['data'] as $key => &$value) {
            $value['create_time'] = date('Y-m-d H:i:s', $value['create_time']);
            $value['status_name'] = Arr::get(config('field.AlikeSpuUploadStatus'), $value['status']);
        }
        unset($value);

        return [
            'data' => Arr::get($result, 'data', []),
            'count' => Arr::get($result, 'total'),
            'total' => Arr::get($result, 'total'),
        ];
    }

    public static function getAlikeSpuItems($map)
    {
        foreach ($map as $k => $v) {
            if (empty($v) and $v != '0') {
                unset($map[$k]);
            }
        }
        $page = Arr::get($map, 'page', 1);
        $pageSize = Arr::get($map, 'limit', 10);

        $searchMap = Arr::only($map, [
            'goods_id/condition',
            'brand_name/condition',
            'goods_status/condition',
            'canal/condition',
        ]);

        $searchMap['p'] = $page;
        $searchMap['offset'] = $pageSize;

        $url = config('website.search_domain_new') . '/search/es/searchSku';
        if (!empty($map['p']) && $map['p'] > 1000) {
            $map['p'] = rand(5, 1000);
        }
        if (!empty($map['spu_name'])) {
            $searchMap['goods_name/eq'] = $map['spu_name'];
            $searchMap['alike_spu_name/exist'] = 1;
        }
        if (!empty($map['alike_spu_name'])) {
            //            $searchMap['alike_spu_name/condition'] = $map['alike_spu_name'];
            $searchMap['goods_name/eq'] = $map['alike_spu_name'];
            $searchMap['alike_spu_name/exist'] = 1;
        }

        if (!empty($map['standard_brand_id'])) {
            $searchMap['standard_brand_id/eq'] = $map['standard_brand_id'];
        }
        if (!empty($map['alike_standard_brand_id'])) {
            $searchMap['standard_brand_id/eq'] = $map['alike_standard_brand_id'];
        }

        if (!empty($map['p']) && $map['p'] > 1000) {
            $searchMap['p'] = rand(5, 1000);
        }
        $searchMap['show_status'] = 1;
        $searchMap['no_rule'] = "1122"; //后台搜索

        $res = Http::get($url, $searchMap)->body();
        $data = json_decode($res, true);
        if (Arr::get($map, "debug") == 1) {

            print_r($url);
            print_r("<br/>");
            print_r(json_encode($map));
            print_r("<br/>");
            print_r($res);
            die();
        }

        $skuList = [];
        if (isset($data['error_code']) && $data['error_code'] == 0) {
            //这里是处理后的sku列表数据
            $skuList = SkuTransformer::listTransformer($data);
        }

        //去判断找出是否是代理品牌的商品
        foreach ($skuList as &$sku) {
            $agentBrandId = AgentBrandModel::where('standard_brand_id', Arr::get($sku, 'standard_brand_id'))
                ->where('status', AgentBrandModel::STATUS_ENABLE)->value('id');
            $sku['is_agent_brand_spu'] = '否';
            $sku['agent_brand_spu_type_name'] = '';
            if (!empty($agentBrandId)) {
                $agentBrandSpu = AgentBrandSpuModel::where('spu_name', $sku['spu_name'])->where('agent_brand_id', $agentBrandId)->first();
                $sku['is_agent_brand_spu'] = $agentBrandSpu ? '是' : '否';
                $sku['agent_brand_spu_type_name'] = $agentBrandSpu['type_name'] ?? '';
            }
        }
        unset($sku);
        unset($value);
        return [
            'data' => $skuList,
            'count' => !empty($data['data']['total']) ? $data['data']['total'] : 0,
            'total' => !empty($data['data']['total']) ? $data['data']['total'] : 0
        ];
    }

    //删除被替代型号
    public static function deleteSpuSku($spuName, $skuIds, $mainId)
    {
        //先去记录这个删除是对应哪个main_id的
        $redis = Redis::connection('sku');
        $mainIdSkuData = $redis->hget('footstone_alike_spu_delete_sku', $mainId);
        if (empty($mainIdSkuData)) {
            $skuData = [
                'delete_sku_ids' => (array)$skuIds,
                'delete_alike_sku_ids' => [],
            ];
        } else {
            $mainIdSkuData = json_decode($mainIdSkuData, true);
            $mainIdSkuData['delete_sku_ids'] = array_unique(array_merge(
                $mainIdSkuData['delete_sku_ids'],
                (array)$skuIds
            ));
            $skuData = $mainIdSkuData;
        }
        $redis->hset('footstone_alike_spu_delete_sku', $mainId, json_encode($skuData));

        $deleteSkuIds = AlikeSpuCenterModel::where('spu_name', $spuName)->value('delete_sku_ids');
        $deleteSkuIds = $deleteSkuIds ? explode(',', $deleteSkuIds) : [];
        $deleteSkuIds = array_merge($deleteSkuIds, explode(',', $skuIds));
        $deleteSkuIds = array_unique($deleteSkuIds);
        $deleteSkuIds = trim(implode(',', $deleteSkuIds), ',');
        return AlikeSpuCenterModel::where('spu_name', $spuName)->update([
            'delete_sku_ids' => $deleteSkuIds,
            'is_sync' => 0,
            'update_time' => time(),
        ]);
    }

    //删除国产替代型号
    public static function deleteAlikeSpuSku($spuName, $skuIds, $mainId)
    {
        //先去记录这个删除是对应哪个main_id的
        $redis = Redis::connection('sku');
        $mainIdSkuData = $redis->hget('footstone_alike_spu_delete_sku', $mainId);
        if (empty($mainIdSkuData)) {
            $skuData = [
                'delete_sku_ids' => [],
                'delete_alike_sku_ids' => (array)$skuIds,
            ];
        } else {
            $mainIdSkuData = json_decode($mainIdSkuData, true);
            $mainIdSkuData['delete_alike_sku_ids'] = array_unique(array_merge(
                $mainIdSkuData['delete_alike_sku_ids'],
                (array)$skuIds
            ));
            $skuData = $mainIdSkuData;
        }
        $redis->hset('footstone_alike_spu_delete_sku', $mainId, json_encode($skuData));

        $deleteSkuIds = AlikeSpuCenterModel::where('spu_name', $spuName)->value('delete_alike_sku_ids');
        $deleteSkuIds = $deleteSkuIds ? explode(',', $deleteSkuIds) : [];
        $deleteSkuIds = array_merge($deleteSkuIds, explode(',', $skuIds));
        $deleteSkuIds = array_unique($deleteSkuIds);
        $deleteSkuIds = trim(implode(',', $deleteSkuIds), ',');
        return AlikeSpuCenterModel::where('spu_name', $spuName)->update([
            'delete_alike_sku_ids' => $deleteSkuIds,
            'is_sync' => 0,
            'update_time' => time(),
        ]);
    }

    public static function downloadUploadResult($id)
    {
        ini_set('memory_limit', -1);
        $log = AlikeSpuLogModel::where('id', $id)->first()->toArray();
        $resultFileUrl = $log['result_file_url'];
        $filePath = $resultFileUrl;
        $path = storage_path() . '/' . 'app/' . $filePath;
        //如果任务状态是处理中,那么直接读取存储的文件就行
        if ($log['status'] == AlikeSpuLogModel::STATUS_IN_PROGRESS) {
            return $path;
        } else {
            if ($log['status'] == AlikeSpuLogModel::STATUS_OK) {
                //如果处理状态是已完成
                //读取已经存储的excel,然后判断是否已经处理完毕
                $result = Excel::toArray(new AlikeSpuUploadResultImport, $path);
                if (empty($result[0])) {
                    return false;
                }
                //重新构建excel并且存储覆盖
                $resultData = [];
                foreach ($result[0] as $key => &$item) {
                    if ($key == 0) {
                        continue;
                    }
                    $spuName = $item[1];
                    $isSync = AlikeSpuCenterModel::where('spu_name', $spuName)->value('is_sync');
                    if ($isSync) {
                        $item[6] = '处理成功';
                    }
                    $resultData[] = [$item[0], $item[1], $item[2], $item[3], $item[4], $item[5], $item[6]];
                }
                unset($item);
                $header = ['国产替代型号', '国产替代品牌', '被替代型号', '被替代品牌', 'pin to pin', '备注', '处理结果'];
                array_unshift($resultData, $header);
                // unlink($path);
                Excel::store(new AlikeSpuUploadResultExport($resultData), $path, 'local', \Maatwebsite\Excel\Excel::CSV);
                return $path;
            }
        }
        return false;
    }

    //重新跑数据
    public static function reloadAlikeSpu($id)
    {
        $spuName = AlikeSpuModel::where('id', $id)->value('spu_name');
        $redis = Redis::connection('sku');
        $mainIdDeleteSku = $redis->hget('footstone_alike_spu_delete_sku', $id);
        $mainIdDeleteSku = !empty($mainIdDeleteSku) ? json_decode($mainIdDeleteSku, true) : [];

        $centerData = AlikeSpuCenterModel::where('spu_name', $spuName)->first()->toArray();
        $centerDeleteSkuIds = $centerData['delete_sku_ids'];
        $centerDeleteAlikeSkuIds = $centerData['delete_alike_sku_ids'];
        if ($mainIdDeleteSku) {
            $centerDeleteSkuIds = explode(',', $centerDeleteSkuIds);
            $centerDeleteAlikeSkuIds = explode(',', $centerDeleteAlikeSkuIds);

            $centerDeleteSkuIds = array_diff($centerDeleteSkuIds, $mainIdDeleteSku['delete_sku_ids']);
            $centerDeleteAlikeSkuIds = array_diff($centerDeleteAlikeSkuIds, $mainIdDeleteSku['delete_alike_sku_ids']);
            $centerModel = new AlikeSpuCenterModel();
            $centerModel->where('spu_name', $spuName)->update([
                'update_time' => time(),
                'delete_sku_ids' => $centerDeleteSkuIds ? implode(',', $centerDeleteSkuIds) : '',
                'delete_alike_sku_ids' => $centerDeleteAlikeSkuIds ? implode(',', $centerDeleteAlikeSkuIds) : '',
                'is_sync' => 0,
            ]);
        } else {
            $centerModel = new AlikeSpuCenterModel();
            $centerModel->where('spu_name', $spuName)->update([
                'update_time' => time(),
                'is_sync' => 0,
            ]);
        }

        return true;
    }
}
