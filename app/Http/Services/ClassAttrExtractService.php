<?php

namespace App\Http\Services;

use App\Http\Models\Cms\ConfigModel;
use App\Http\Models\PoolClass\ClassAttrUnitConvertItemModel;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;
use Str;

class ClassAttrExtractService
{
    //处理正负号逻辑
    public static function handlePlusAndMinus($value, $attrNameFormat, $standardUnitName, $unitConvertId, $insertTypeName = '')
    {
        if (Str::contains($value, '±')) {
            $purNumber = AttrPregService::extractPurNumber($value);
            $valueMin = $valueMax = $purNumber[0];
            //然后去匹配单位是否存在
            $unitName = AttrPregService::extractUnit($value);
            $ratio = ClassAttrUnitConvertItemModel::where('convert_id', $unitConvertId)->where('unit_name', $unitName)->value('ratio');
            if (!$ratio) {
                return '值对应属性' . $attrNameFormat . '录入方式为' . $insertTypeName . ',单值对应的属性单位不属于该属性的默认单位列表中';
            }
            return [
                'value_min' => sprintf('%.10F', bcdiv(-($valueMin / $ratio), 10)),
                'value_min_unit' => $unitName,
                'value_max' => sprintf('%.10F', bcdiv($valueMax / $ratio, 10)),
                'value_max_unit' => $unitName,
                'standard_unit' => $standardUnitName,
                'standard_unit_id' => $unitConvertId,
                'origin_value' => $value,
            ];
        } else {
            return [];
        }
    }


    public static function handleSingle($value, $attrNameFormat, $standardUnitName, $unitConvertId, $insertTypeName = '')
    {
        // 使用正则表达式匹配所有数字（包括负数）
        $purNumber = AttrPregService::extractPurNumber($value);
        if (count($purNumber) == 0) {
            return '值对应属性' . $attrNameFormat . '录入方式为' . $insertTypeName . ',检测不到数值';
        }
        if (count($purNumber) == 1 && !is_numeric($purNumber[0])) {
            return '值对应属性' . $attrNameFormat . '录入方式为' . $insertTypeName . ',该属性值不是单数值';
        }
        if (count($purNumber) == 2) {
            return '值对应属性' . $attrNameFormat . '录入方式为' . $insertTypeName . ',但是接收的值为范围';
        }
        //然后去匹配单位是否存在
        $unitName = AttrPregService::extractUnit($value);
        $ratio = ClassAttrUnitConvertItemModel::where('convert_id', $unitConvertId)->where('unit_name', $unitName)->value('ratio');
        if (!$ratio) {
            return '值对应属性' . $attrNameFormat . '录入方式为' . $insertTypeName . ',单值对应的属性单位不属于该属性的默认单位列表中';
        }
        return [
            'value' => sprintf('%.10F', bcdiv($purNumber[0], $ratio, 10)),
            'value_unit' => $unitName,
            'standard_unit' => $standardUnitName,
            'standard_unit_id' => $unitConvertId,
            'origin_value' => $value,
            'real_insert_type' => 1,
        ];
    }

    public static function handleSingleAndText($value, $attrNameFormat, $standardUnitName, $unitConvertId, $insertTypeName = '')
    {
        if (Str::contains($value, '~')) {
            return '值对应属性' . $attrNameFormat . '录入方式为' . $insertTypeName . ',但是输入的是范围值';
        }

        if (!Str::contains($value, '@')) {
            $singleAttrValue = $value;
            $attrText = '';
        } else {
            $data = explode('@', $value);
            $singleAttrValue = $data[0];
            $attrText = '@' . $data[1];
        }
        $result = self::handleSingle($singleAttrValue, $attrNameFormat, $standardUnitName, $unitConvertId, $insertTypeName);
        if (is_array($result)) {
            $result['attr_text'] = $attrText;
            if (!empty($attrText)) {
                $result['origin_value'] = $value ;
            }
        }
        return $result;
    }

    //处理范围值
    public static function handleRange($value, $attrNameFormat, $standardUnitName, $unitConvertId, $insertTypeName = '')
    {
        if (Str::contains($value, '~')) {
            $valueMinUnit = $valueMaxUnit = '';
            $values = explode('~', $value);
            $valueMin = $valueMax = 0;
            foreach ($values as $key => $itemValue) {
                $itemValue = trim($itemValue);
                $purNumber = AttrPregService::extractPurNumber($itemValue);
                if (count($purNumber) == 0) {
                    return '值对应属性' . $attrNameFormat . '录入方式为' . $insertTypeName . ',检测不到数值';
                }
                //然后去匹配单位是否存在
                $unitName = AttrPregService::extractUnit($itemValue);
                if ($key == 0) {
                    $valueMin = $purNumber[0];
                    $valueMinUnit = $unitName;
                    $ratioMin = ClassAttrUnitConvertItemModel::where('convert_id', $unitConvertId)->where('unit_name', $unitName)->value('ratio');
                    if (!$ratioMin) {
                        return '值对应属性' . $attrNameFormat . '录入方式为' . $insertTypeName . ',但' . ($key == 0 ? '左边值' : '右边值') . '对应的属性单位不属于该属性的默认单位列表中';
                    }
                } else {
                    $valueMax = $purNumber[0];
                    $valueMaxUnit = $unitName;
                    $ratioMax = ClassAttrUnitConvertItemModel::where('convert_id', $unitConvertId)->where('unit_name', $unitName)->value('ratio');
                    if (!$ratioMax) {
                        return '值对应属性' . $attrNameFormat . '录入方式为' . $insertTypeName . ',但' . ($key == 0 ? '左边值' : '右边值') . '对应的属性单位不属于该属性的默认单位列表中';
                    }
                }

            }
            return [
                'value_min' => sprintf('%.10F', bcdiv($valueMin, $ratioMin, 10)),
                'value_min_unit' => $valueMinUnit,
                'value_max' => sprintf('%.10F', bcdiv($valueMax, $ratioMax, 10)),
                'value_max_unit' => $valueMaxUnit,
                'standard_unit' => $standardUnitName,
                'standard_unit_id' => $unitConvertId,
                'origin_value' => $value,
            ];
        }
        if (Str::contains($value, '±')) {
            $purNumber = AttrPregService::extractPurNumber($value);
            if (count($purNumber) == 0) {
                return '值对应属性' . $attrNameFormat . '录入方式为' . $insertTypeName . ',检测不到数值';
            }
            if (!is_numeric($purNumber[0])) {
                return '值对应属性' . $attrNameFormat . '录入方式为' . $insertTypeName . ',但该属性值也不是单数值';
            }
            $valueMin = $valueMax = $purNumber[0];
            //然后去匹配单位是否存在
            $unitName = AttrPregService::extractUnit($value);
            $ratio = ClassAttrUnitConvertItemModel::where('convert_id', $unitConvertId)->where('unit_name', $unitName)->value('ratio');
            if (!$ratio) {
                return '值对应属性' . $attrNameFormat . '录入方式为' . $insertTypeName . ',但值对应的单位不属于该属性的默认单位列表中';
            }
            return [
                'value_min' => - (bcdiv($valueMin, $ratio, 10)),
                'value_min_unit' => $unitName,
                'value_max' => bcdiv($valueMax, $ratio, 10),
                'value_max_unit' => $unitName,
                'standard_unit' => $standardUnitName,
                'standard_unit_id' => $unitConvertId,
                'origin_value' => $value,
            ];
        }

        //这个就是单数值
        if (!Str::contains($value, '~')) {
            // 使用正则表达式匹配所有数字（包括负数）
            $purNumber = AttrPregService::extractPurNumber($value);
            if (count($purNumber) == 0) {
                return '值对应属性' . $attrNameFormat . '录入方式为' . $insertTypeName . ',检测不到数值';
            }
            if (count($purNumber) == 1 && !is_numeric($purNumber[0])) {
                return '值对应属性' . $attrNameFormat . '录入方式为' . $insertTypeName . ',该属性值不是单数值';
            }
            //然后去匹配单位是否存在
            $unitName = AttrPregService::extractUnit($value);
            $ratio = ClassAttrUnitConvertItemModel::where('convert_id', $unitConvertId)->where('unit_name', $unitName)->value('ratio');
            if (!$ratio) {
                return '值对应属性' . $attrNameFormat . '录入方式为' . $insertTypeName . ',单数值对应的属性单位不属于该属性的默认单位列表中';
            }
            return [
                'value' => sprintf('%.10F', bcdiv($purNumber[0], $ratio, 10)),
                'value_unit' => $unitName,
                'standard_unit' => $standardUnitName,
                'standard_unit_id' => $unitConvertId,
                'origin_value' => $value,
                'real_insert_type' => 1,
            ];
        }
    }

    public static function handleRangeAndText($value, $attrNameFormat, $standardUnitName, $unitConvertId, $insertTypeName = '')
    {
        $valueMinUnit = $valueMaxUnit = '';
        if (Str::contains($value, '@')) {
            $data = explode('@', $value);
            $rangeAttrValue = $data[0];
            $attrText = '@' . $data[1];
        } else {
            $rangeAttrValue = $value;
            $attrText = '';
        }

        $result = self::handleRange($rangeAttrValue, $attrNameFormat, $standardUnitName, $unitConvertId, $insertTypeName);
        if (is_array($result)) {
            $result['attr_text'] = $attrText;
            if (!empty($attrText)) {
                $result['origin_value'] = $value;
            }
        }
        return $result;
    }

    public static function handleMultiSingle($value, $attrNameFormat, $standardUnitName, $unitConvertId, $insertTypeName)
    {
        $data = [];
        //先去分割
        if (!str_contains($value, '€')) {
            $items = [$value];
        } else {
            $items = explode('€', $value);
        }
        foreach ($items as $attrValue) {
            $result = ClassAttrExtractService::handleSingle($attrValue, $attrNameFormat, $standardUnitName, $unitConvertId, $insertTypeName);
            if (!is_array($result)) {
                return $result;
            }
            $data[] = $result;
        }
        return $data;
    }

    public static function handleMultiRange($value, $attrNameFormat, $standardUnitName, $unitConvertId, $insertTypeName)
    {
        //没有分割符号的
        if (!str_contains($value, '€')) {
            //判断是不是单数值
            if (!Str::contains($value, '~') && !Str::contains($value, '±')) {
                return self::handleSingle($value, $attrNameFormat, $standardUnitName, $unitConvertId, $insertTypeName);
            } else {
                //否则是范围值
                return self::handleRange($value, $attrNameFormat, $standardUnitName, $unitConvertId, $insertTypeName);
            }
        } else {
            //有分割符号的
            $data = [];
            $items = explode('€', $value);
            $hasSingle = $hasRange = false;
            foreach ($items as $itemValue) {
                //判断是不是单数值
                if (!Str::contains($itemValue, '~') && !Str::contains($itemValue, '±')) {
                    $result = self::handleSingle($itemValue, $attrNameFormat, $standardUnitName, $unitConvertId, $insertTypeName);
                    $hasSingle = true;
                } else {
                    $hasRange = true;
                    //否则是范围值
                    $result = self::handleRange($itemValue, $attrNameFormat, $standardUnitName, $unitConvertId, $insertTypeName);
                }
                if (!is_array($result)) {
                    return $result;
                }
                $data[] = $result;
            }
            if ($hasSingle && $hasRange) {
                return '值对应属性' . $attrNameFormat . '录入方式为' . $insertTypeName . ',不允许同时存在单数值和范围值,只能统一单数值或者范围值';
            }
            return $data;
        }
    }

    public static function handleMultiSingleAndText($value, $attrNameFormat, $standardUnitName, $unitConvertId, $insertTypeName)
    {
        $data = [];
        //先去分割
        if (!str_contains($value, '€')) {
            $items = [$value];
        } else {
            $items = explode('€', $value);
        }
        foreach ($items as $itemValue) {
            $result = self::handleSingleAndText($itemValue, $attrNameFormat, $standardUnitName, $unitConvertId, $insertTypeName);
            if (!is_array($result)) {
                return $result;
            }
            $data[] = $result;
        }

        return $data;
    }
}
