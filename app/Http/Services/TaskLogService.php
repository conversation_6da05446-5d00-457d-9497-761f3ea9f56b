<?php

namespace App\Http\Services;

use App\Http\Models\AlikeSpuLogModel;
use App\Http\Models\TaskLogModel;
use Illuminate\Support\Arr;

class TaskLogService
{
    public static function getTaskLogList($type, $map)
    {
        $query = TaskLogModel::where('type', $type)->orderBy('create_time', 'desc');

        if (!empty($map['status']) || (isset($map['status']) && $map['status'] === '0')) {
            $query->where('status', $map['status']);
        }
        if (!empty($map['admin_id'])) {
            $query->where('admin_id', $map['admin_id']);
        }
        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }

        $limit = Arr::get($map, 'limit', 10);
        $result = $query->paginate($limit);
        $result = $result ? $result->toArray() : [];
        foreach ($result['data'] as $key => &$value) {
            $value['create_time'] = date('Y-m-d H:i:s', $value['create_time']);
            $value['status_name'] = Arr::get(config('field.TaskLogStatus'), $value['status']);
        }
        unset($value);

        return [
            'data' => Arr::get($result, 'data', []),
            'count' => Arr::get($result, 'total'),
            'total' => Arr::get($result, 'total'),
        ];
    }
}
