<?php

namespace App\Http\Services;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use App\Http\Models\OperationLogModel;
use App\Http\Queue\BoardcastQueueModel;
use App\Http\Models\ClassManagementModel;
use App\Exceptions\InvalidRequestException;
use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Transformers\ClassManagementTransformer;

class ClassManagementService
{

    //供应链scm,crm,采购,供应商
    const SYSTEM_SCM = "scm";
    const SYSTEM_CRM = "crm";
    const SYSTEM_PURCHASE = "purchase";
    const SYSTEM_SUPPLIER = "supplier";
    const SYSTEM_ORDER = "order";
    const SYSTEM_FRQ = "frq";
    const SYSTEM_ERP = "erp";


    /**
     * 获取分类管理列表
     *
     * @param array $map 查询条件
     * @return array
     */
    public function getClassManagementList($map)
    {
        $limit = Arr::get($map, 'limit', 10);
        $query = ClassManagementModel::with('class')->orderBy('id', 'desc');

        if (!empty($map['id'])) {
            $query->where('id', $map['id']);
        }

        if (!empty($map['goods_name'])) {
            $query->where('goods_name', 'like', '%' . $map['goods_name'] . '%');
        }

        if (!empty($map['brand_name'])) {
            $query->where('brand_name', 'like', '%' . $map['brand_name'] . '%');
        }

        if (!empty($map['order_sn'])) {
            $query->where('order_sn', 'like', '%' . $map['order_sn'] . '%');
        }

        if (!empty($map['spu_id'])) {
            $query->where('spu_id', 'like', '%' . $map['spu_id'] . '%');
        }

        if (!empty($map['sku_id'])) {
            $query->where('sku_id', 'like', '%' . $map['sku_id'] . '%');
        }

        if (isset($map['status']) && $map['status'] !== '') {
            $query->where('status', $map['status']);
        }

        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }

        $result = $query->paginate($limit)->toArray();

        $transformer = new ClassManagementTransformer();
        $result['data'] = $transformer->listTransformer($result['data']);

        return [
            'data' => Arr::get($result, 'data', []),
            'count' => Arr::get($result, 'total'),
        ];
    }

    /**
     * 保存分类管理
     *
     * @param array $params 参数
     * @return int
     */
    public function saveClassManagement($params)
    {
        if (empty($params['id'])) {
            // 新增
            $id = ClassManagementModel::insertGetId([
                'goods_name' => $params['goods_name'],
                'brand_name' => $params['brand_name'],
                'class_id' => $params['class_id'],
                'status' => ClassManagementModel::STATUS_PENDING,
                'order_sn' => $params['order_sn'],
                'sales_name' => $params['sales_name'],
                'sales_id' => $params['sales_id'],
                'spu_id' => $params['spu_id'],
                'sku_id' => $params['sku_id'],
                'create_time' => time(),
                'update_time' => time(),
                'update_name' => request()->user->name,
                'update_uid' => request()->user->userId,
                'remark' => $params['remark'] ?? '',
            ]);
            OperationLogService::addOperationLog([
                'obj_name' => 'class_management',
                'obj_id' => $id,
                'content' => '新增分类管理',
                'create_time' => time(),
                'create_name' => request()->user->name,
            ]);
        } else {
            // 更新
            $id = $params['id'];
            $oldData = ClassManagementModel::find($id);

            if (!$oldData) {
                throw new InvalidRequestException('数据不存在');
            }

            $updateData = [
                'goods_name' => $params['goods_name'],
                'brand_name' => $params['brand_name'],
                'class_id' => $params['class_id'],
                'order_sn' => $params['order_sn'],
                'sales_name' => $params['sales_name'],
                'sales_id' => $params['sales_id'],
                'spu_id' => $params['spu_id'],
                'sku_id' => $params['sku_id'],
                'update_time' => time(),
                'update_name' => request()->user->name,
                'update_uid' => request()->user->userId,
                'remark' => $params['remark'] ?? '',
            ];

            // 如果状态有变更，记录状态变更
            if (isset($params['status']) && $params['status'] != $oldData->status) {
                $updateData['status'] = $params['status'];
                $statusText = $params['status'] == ClassManagementModel::STATUS_PROCESSED ? '已处理' : '待处理';
                $oldStatusText = $oldData->status == ClassManagementModel::STATUS_PROCESSED ? '已处理' : '待处理';

                OperationLogService::addOperationLog([
                    'obj_name' => 'class_management',
                    'obj_id' => $id,
                    'content' => '修改分类管理状态 : 从' . $oldStatusText . '改为' . $statusText,
                    'create_time' => time(),
                    'create_name' => request()->user->name,
                ]);
            }

            ClassManagementModel::where('id', $id)->update($updateData);

            OperationLogService::addOperationLog([
                'obj_name' => 'class_management',
                'obj_id' => $id,
                'content' => '修改分类管理',
                'create_time' => time(),
                'create_name' => request()->user->name,
            ]);
        }

        return $id;
    }

    /**
     * 更新状态
     *
     * @param int $id 记录ID
     * @param int $status 状态
     * @return bool
     * @throws \Exception
     */
    public function updateStatus($id, $status)
    {
        if (empty($id)) {
            throw new InvalidRequestException('ID不能为空');
        }

        if (!in_array($status, [ClassManagementModel::STATUS_PENDING, ClassManagementModel::STATUS_PROCESSED])) {
            throw new InvalidRequestException('状态值不正确');
        }

        $model = ClassManagementModel::find($id);

        if (!$model) {
            throw new InvalidRequestException('记录不存在');
        }

        $model->status = $status;
        $model->update_time = time();
        $model->update_uid = Auth::id() ?? 0;
        $model->update_name = Auth::user()->name ?? '';

        // 添加操作日志
        OperationLogService::addOperationLog(
            'lie_class_management',
            $id,
            OperationLogModel::OPERATION_TYPE_UPDATE,
            '更新分类管理状态',
            json_encode(['status' => $status])
        );

        return $model->save();
    }

    /**
     * 更新分类并标记为已处理
     *
     * @param int $id 记录ID
     * @param int $classId 新分类ID
     * @return bool
     * @throws \Exception
     */
    public function updateClassification($id, $classId)
    {
        if (empty($id)) {
            throw new InvalidRequestException('ID不能为空');
        }

        if (empty($classId)) {
            throw new InvalidRequestException('分类ID不能为空');
        }

        $model = ClassManagementModel::find($id);

        // 保存原始分类信息
        $originalClassId = $model->class_id;

        // 更新分类和状态
        $model->class_id = $classId;
        $model->status = ClassManagementModel::STATUS_PROCESSED; // 标记为已处理
        $model->update_time = time();
        $model->update_uid = request()->user->userId ?? 0;
        $model->update_name = request()->user->name ?? '';
        $result = $model->save();
        if ($result) {
            $class = PoolClassModel::where('class_id', $classId)->first();
            $className = $class->class_name;
            $this->pushClassChange($model->goods_name, $classId, $className);
        }
        return $result;
    }

    /**
     * 删除分类管理
     *
     * @param int $id ID
     * @return bool
     */
    public function deleteClassManagement($id)
    {
        $model = ClassManagementModel::find($id);
        if (!$model) {
            throw new InvalidRequestException('数据不存在');
        }

        $result = ClassManagementModel::where('id', $id)->delete();

        if ($result) {
            OperationLogService::addOperationLog([
                'obj_name' => 'class_management',
                'obj_id' => $id,
                'content' => '删除分类管理',
                'create_time' => time(),
                'create_name' => request()->user->name,
            ]);
            return true;
        }

        return false;
    }


    //推送到各个系统
    public function pushClassChange($goodsName, $classId, $className)
    {
        $data = [
            'TYPE' => '6', //这个是金蝶那边 要的 字段
            'goods_name' => $goodsName,
            'class_id' => (int)$classId,
            'class_name' => $className,
        ];
        foreach ($this->getQueueMap() as $queueName => $queueValue) {
            $queueModel = new BoardcastQueueModel();
            $queueModel->insertQueue($queueValue['funcName'], $data, $queueValue['queueName'], $queueValue['soapType']);
        }
    }

    private  function getQueueMap()
    {
        $systemIdQueueMap = [
            //询报价
            self::SYSTEM_FRQ => [
                'queueName' => BoardcastQueueModel::DEFAULT_FRQ,
                'funcName'  => '/queue/goods/updateGoodsClassFirst',
                'soapType'  => BoardcastQueueModel::FORWARD_TYPE_HTTP
            ],
            //采购
            self::SYSTEM_PURCHASE => [
                'queueName' => BoardcastQueueModel::DEFAULT_PURCHASE,
                'funcName'  => '/sync/updateGoodsClassFirst',
                'soapType'  => BoardcastQueueModel::FORWARD_TYPE_HTTP
            ],
            self::SYSTEM_ORDER => [
                'queueName' => BoardcastQueueModel::DEFAULT_ORDER,
                'funcName'  => '/open/updateGoodsClassFirst',
                'soapType'  => BoardcastQueueModel::FORWARD_TYPE_HTTP
            ],
            self::SYSTEM_ERP => [
                'queueName' => BoardcastQueueModel::DEFAULT_ERP,
                'funcName'  => '/open/updateGoodsClassFirst',
                'soapType'  => BoardcastQueueModel::FORWARD_TYPE_SOAP
            ],
        ];
        return $systemIdQueueMap;
    }
}
