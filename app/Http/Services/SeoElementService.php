<?php

namespace App\Http\Services;

//标准品牌服务

use App\Http\Models\BrandModel;
use App\Http\Models\SeoElementModel;
use App\Http\Transformers\BrandTransformer;
use App\Http\Transformers\StandardBrandTransformer;
use Illuminate\Support\Facades\Redis;

class SeoElementService
{

    public static function getSeoElement($keyId, $type)
    {
        $seoElement = SeoElementModel::where('key_id', $keyId)->where('type', $type)->first();
        return !empty($seoElement) ? $seoElement->toArray() : [];
    }

    public function saveSeoElement($data)
    {
        $data['update_time'] = time();
        $exist = SeoElementModel::where('key_id', $data['key_id'])->where('type', $data['type'])->exists();
        if ($exist) {
            SeoElementModel::where('key_id', $data['key_id'])->where('type', $data['type'])->update($data);
        } else {
            $data['add_time'] = time();
            SeoElementModel::insert($data);
        }
        $this->saveSeoElementToRedis($data);
        return true;
    }

    public function saveSeoElementToRedis($data)
    {
        $redis = Redis::connection('sku');
        $key = $data['type'] . '_' . $data['key_id'];
        return $redis->hset('jishi_seo_element', $key, json_encode($data));
    }
}
