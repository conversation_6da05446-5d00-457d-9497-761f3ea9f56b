<?php

namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\BigData\ShopAttrMappingModel;
use App\Http\Models\BigData\ShopAttrModel;
use App\Http\Models\BigData\ShopClassMappingModel;
use App\Http\Models\BigData\ShopClassModel;
use App\Http\Models\EncapModel;
use App\Http\Models\PoolClass\PoolClassAttrModel;
use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Models\SampleLogModel;
use App\Http\Models\StandardAttrModel;
use App\Http\Models\StandardEncapMappingModel;
use App\Http\Models\StandardEncapModel;
use App\Http\Transformers\ShopAttrMappingTransformer;
use App\Http\Transformers\StandardEncapMappingTransformer;
use App\Imports\SampleImport;
use App\Imports\StandardEncapMappingImport;
use App\Jobs\UploadSample;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class ShopAttrMappingService
{
    public static function getShopAttrMappingList($map)
    {
        $limit = request()->get('limit', 10);
        $model = new ShopAttrMappingModel();
        $query = $model->with(['shop_attr', 'lie_attr'])->orderBy('id', 'desc');
        if (!empty($map['lie_attr_name'])) {
            $lieAttrIdList = PoolClassAttrModel::where('attr_name', 'like', '%' . $map['lie_attr_name'] . '%')->pluck('attr_id')->toArray();
            $query->whereIn('lie_attr_id', $lieAttrIdList);
        }
        if (!empty($map['shop_attr_name'])) {
            $shopAttrIdList = ShopAttrModel::where('attr_name', 'like', '%' . $map['shop_attr_name'] . '%')->pluck('id')->toArray();
            $query->whereIn('shop_attr_id', $shopAttrIdList);
        }
        if (!empty($map['lie_class_name'])) {
            $lieClassIdList = PoolClassModel::where('class_name', 'like', '%' . $map['lie_class_name'] . '%')->pluck('class_id')->toArray();
            $shopClassIdList = ShopClassMappingModel::whereIn('lie_class_id', $lieClassIdList)->pluck('shop_class_id')->toArray();
            $originShopClassIdList = ShopClassModel::whereIn('id', $shopClassIdList)->pluck('class_id')->toArray();
            $shopAttrIdList = ShopAttrModel::whereIn('class_id', $originShopClassIdList)->pluck('id')->toArray();
            $query->whereIn('shop_attr_id', $shopAttrIdList);
        }

        if (!empty($map['shop_class_name'])) {
            $shopClassIdList = ShopClassModel::where('class_name', 'like', '%' . $map['shop_class_name'] . '%')->pluck('class_id')->toArray();
            $shopAttrIdList = ShopAttrModel::whereIn('class_id', $shopClassIdList)->pluck('id')->toArray();
            $query->whereIn('shop_attr_id', $shopAttrIdList);
        }

        if (!empty($map['origin_shop_class_id'])) {
            $shopAttrIdList = ShopAttrModel::where('class_id', $map['origin_shop_class_id'])->pluck('id')->toArray();
            $query->whereIn('shop_attr_id', $shopAttrIdList);
        }

        if (!empty($map['lie_class_id'])) {
            $shopClassIdList = ShopClassMappingModel::where('lie_class_id', $map['lie_class_id'])->pluck('shop_class_id')->toArray();
            $originShopClassIdList = ShopClassModel::whereIn('id', $shopClassIdList)->pluck('class_id')->toArray();
            $shopAttrIdList = ShopAttrModel::whereIn('class_id', $originShopClassIdList)->pluck('id')->toArray();
            $query->whereIn('shop_attr_id', $shopAttrIdList);
        }

        if (!empty($map['shop_class_id'])) {
            //先去找到分类映射对应的原始分类列表
            $originShopClassId = ShopClassModel::where('id', $map['shop_class_id'])->value('class_id');
            $shopAttrIdList = ShopAttrModel::where('class_id', $originShopClassId)->pluck('id')->toArray();
            $query->whereIn('shop_attr_id', $shopAttrIdList);
        }

        if (!empty($map['platform'])) {
            $query->where('platform', $map['platform']);
        }

        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }

        $list = $query->paginate($limit)->toArray();
        $transformer = new ShopAttrMappingTransformer();
        $list['data'] = $transformer->listTransformer($list['data']);
        return [
            'data' => $list['data'],
            'count' => $list['total'],
        ];
    }

    public static function saveShopAttrMapping($shopClassId, $mappingData, $platform, $isDraft = false)
    {
        if (empty($mappingData)) {
            return true;
        }
        $redis = Redis::connection('sku');
        //草稿的处理逻辑
        //存到redis,shop_class_id + platform 作为 hash 的key
        $draftRedisKey = $shopClassId . '_' . $platform;
        //判断是不是草稿,如果是草稿的话,存到redis里面去
        if (!$isDraft) {
            $result = \DB::connection('distribution')->transaction(function () use ($shopClassId, $mappingData, $redis, $draftRedisKey, $platform) {
                //先删除没有提交过来的映射
                $classId = ShopClassModel::where('id', $shopClassId)->value('class_id');
                $shopAttrIds = ShopAttrModel::where('class_id', $classId)->where('platform', $platform ?? '')->pluck('id')->toArray();
                $submitShopAttrId = array_map('intval', array_column($mappingData, 'origin_shop_attr_id'));
                $needDeleteShopAttrId = array_diff($shopAttrIds, $submitShopAttrId);
                ShopAttrModel::whereIn('id', $needDeleteShopAttrId)->update([
                    'is_mapping' => 1,
                    'update_time' => time(),
                ]);
                ShopAttrMappingModel::whereIn('shop_attr_id', $needDeleteShopAttrId)->delete();
                //删除后,还要把mappingData里面对应的数据删除
                // $mappingData = array_filter($mappingData, function ($item) use ($needDeleteShopAttrId) {
                //     return !in_array($item['shop_attr_id'], $needDeleteShopAttrId);
                // });
                foreach ($mappingData as $mapping) {
                    //判断是否已经存在,存在就更新
                    $value = is_array($mapping['value']) ? json_encode($mapping['value']) : $mapping['value'];
                    $mapping['shop_attr_id'] = $mapping['shop_attr_id'] ?: $mapping['origin_shop_attr_id'];
                    $mappingId = 0;
                    if (ShopAttrMappingModel::where('shop_attr_id', $mapping['shop_attr_id'])
                        ->where('platform', $mapping['platform'])->exists()
                    ) {
                        $mappingId = $mapping['id'];
                        ShopAttrMappingModel::where('shop_attr_id', $mapping['shop_attr_id'])->where('platform', $mapping['platform'])
                            ->update([
                                'value' => $value,
                                'input_type' => $mapping['input_type'],
                                'platform' => $mapping['platform'],
                                'lie_attr_id' => $mapping['lie_attr_id'],
                                'update_time' => time(),
                                'update_uid' => request()->user->userId,
                                'update_name' => request()->user->name,
                            ]);
                    } else {
                        if (empty($mapping['id'])) {
                            $mappingId = ShopAttrMappingModel::insertGetId([
                                'shop_attr_id' => $mapping['shop_attr_id'],
                                'lie_attr_id' => $mapping['lie_attr_id'],
                                'value' => $value,
                                'input_type' => $mapping['input_type'],
                                'platform' => $mapping['platform'],
                                'create_time' => time(),
                                'create_uid' => request()->user->userId,
                                'create_name' => request()->user->name,
                            ]);
                        }
                    }
                    //判断对应的参数shop_attr是不是status=0(被删除),是的话删除对应的映射
                    $shopAttrId = $mapping['shop_attr_id'];
                    if (ShopAttrModel::where('id', $shopAttrId)->value('status') == 0) {
                        ShopAttrMappingModel::where('id', $mappingId)->delete();
                        ShopAttrModel::where('id', $shopAttrId)->update([
                            'is_required' => 0,
                        ]);
                    }
                    //统一修改是否映射
                    ShopAttrModel::where('id', $mapping['shop_attr_id'])->update([
                        'is_mapping' => 1,
                        'update_time' => time(),
                    ]);
                    //删除对应的草稿
                    $redis->hdel('lie_shop_attr_mapping_draft', $draftRedisKey);
                }
            });
        } else {
            //存起来的数据,也是根据shop_attr_id + platform存储
            $draftData = [];
            foreach ($mappingData as $mapping) {
                $mapping['value'] = is_array($mapping['value']) ? json_encode($mapping['value']) : $mapping['value'];
                $index = $mapping['origin_shop_attr_id'] . "_" . $mapping['platform'];
                $draftData[$index] = $mapping;
            }
            $result = $redis->hset('lie_shop_attr_mapping_draft', $draftRedisKey, json_encode($draftData));
        }
        return $result;
    }

    public static function deleteShopAttrMapping($id)
    {
        return ShopAttrMappingModel::where('id', $id)->delete();
    }
}
