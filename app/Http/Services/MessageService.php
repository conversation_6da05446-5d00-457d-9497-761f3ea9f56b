<?php


namespace App\Http\Services;

use App\Http\Models\SupplierModel;
use Illuminate\Support\Facades\DB;

class MessageService
{
    public function sendMessage($keyword, $data, $to_user = '', $cnUncode = false)
    {
        // 推送给内部人员
        if ($cnUncode) {
            if (isset($data['data'])) {
                $send_data = json_encode($data['data'], JSON_UNESCAPED_UNICODE); //防止中文乱码
            } else {
                $send_data = json_encode($data, JSON_UNESCAPED_UNICODE); //防止中文乱码
            }
        } else {
            $send_data = json_encode($data['data']);
        }
        if (!$to_user) { // 给正式的内部人员推送
            $to_user = 'INNER_PERSON';
        }

        $toUserJson = json_encode($to_user);
        $check['touser'] = $toUserJson;
        $check['data'] = $send_data;
        $check['pf'] = -1;
        $check['keyword'] = $keyword;
        $check['is_ignore'] = 1;

        $check = array_merge($check, $this->authkey());
        $res = curl(config('website.MessageUrl') . '/msg/sendMessageByAuto', $check, true);
        if (!empty($res)) {
            $res = json_decode($res, true);
        }
        return $res;
    }

    public function authkey($pf = -1)
    {
        return array(
            'pf' => 1,
            'k1' => $_SERVER['REQUEST_TIME'],
            'k2' => $this->pwdhash($_SERVER['REQUEST_TIME'], config('website.MessageKey'))
        );
    }

    private function pwdhash($pwd, $salt)
    {
        return md5(md5($pwd).$salt);
    }
}
