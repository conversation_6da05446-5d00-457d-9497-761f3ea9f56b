<?php


namespace App\Http\Services;

use App\Http\Models\Cms\CmsDepartmentModel as DepartmentModel;
use App\Http\Models\Cms\CmsUserDepartmentModel;
use App\Http\Models\Cms\CmsUserInfoModel;
use Illuminate\Support\Facades\DB;

//部门数据服务
class DepartmentService
{

    public function getSubordinateUserIds($adminId)
    {
        $departmentId = CmsUserInfoModel::where('userId', $adminId)->value('department_id');
        $users = $this->getUserByDepartmentId($departmentId);
        return array_column($users, 'userId');
    }

    public function getSubordinateUserNames($adminId)
    {
        $departmentId = CmsUserInfoModel::where('userId', $adminId)->value('department_id');
        $users = $this->getUserByDepartmentId($departmentId);
        return array_column($users, 'name');
    }

    // 获取部门人员
    public function getUserByDepartmentId($departmentId, $status = '', $filter = '')
    {
        $departmentIds = [];

        $this->getSubDepartmentId($departmentId, $departmentIds);

        return CmsUserInfoModel::whereIn('department_id', $departmentIds)
            ->where(function ($query) use ($status) {
                if ($status !== '') {
                    $query->where('status', '=', $status);
                }
            })
            ->where(function ($query) use ($filter) {
                if ($filter) {
                    $query->whereRaw($filter);
                }
            })
            ->select(['userId', 'name', 'status'])
            ->get();
    }

    // 获取下级部门ID
    public function getSubDepartmentId($departmentId, &$departmentIds)
    {
        // 获取下级部门
        $sub_department = CmsUserDepartmentModel::where('parent_id', $departmentId)->select('department_id',
            'department_name')->get();
        if ($sub_department) {
            foreach ($sub_department as $v) {
                $this->getSubDepartmentId($v['department_id'], $departmentIds);
            }
        }

        $departmentIds[] = $departmentId;

        return $departmentIds;
    }
}
