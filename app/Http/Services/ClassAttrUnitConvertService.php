<?php

namespace App\Http\Services;

//供应链品牌服务
use App\Exceptions\InvalidRequestException;
use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Models\PoolClass\ClassAttrUnitConvertItemModel;
use App\Http\Models\PoolClass\ClassAttrUnitConvertModel;
use App\Http\Models\StandardBrandModel;
use App\Http\Transformers\ClassAttrUnitConvertSpuTransformer;
use App\Http\Transformers\ClassAttrUnitConvertTransformer;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

class ClassAttrUnitConvertService
{

    public function getClassAttrUnitConvertList($map)
    {
        $model = new ClassAttrUnitConvertModel();
        $query = $model->with('items')->withCount('items')->orderBy('id', 'desc');

        if (!empty($map['convert_name'])) {
            $query->where('convert_name', 'like', "%{$map['convert_name']}%");
        }


        if (!empty($map['status'])) {
            $query->where('status', $map['status']);
        }

        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }

        $limit = \Arr::get($map, 'limit', 15);
        $result = $query->paginate($limit);

        $result = $result ? $result->toArray() : [];
        $transformer = new ClassAttrUnitConvertTransformer();
        $result['data'] = $transformer->listTransformer($result['data']);
        return [
            'data' => \Arr::get($result, 'data', []),
            'count' => \Arr::get($result, 'total'),
        ];
    }

    //保存代理品牌
    public function saveClassAttrUnitConvert($data)
    {
        //先找出pm用户名
        if (empty($data['id'])) {
            if (ClassAttrUnitConvertModel::where('convert_name', $data['convert_name'])->exists()) {
                throw new InvalidRequestException('该单位名称已经存在,请修改后提交');
            }
            unset($data['id']);
            $data['create_time'] = time();
            $data['create_name'] = request()->user->name;
            $data['create_uid'] = request()->user->userId;
            return DB::transaction(function () use ($data) {
                $items = $data['items'];

                unset($data['items']);
                $convertId = ClassAttrUnitConvertModel::insertGetId($data);
                if ($convertId) {
                    $items = array_map(function ($item) use ($convertId) {
                        $item['convert_id'] = $convertId;
                        $item['create_time'] = time();
                        $item['create_name'] = request()->user->name;
                        $item['create_uid'] = request()->user->userId;
                        return $item;
                    }, $items);
                    return ClassAttrUnitConvertItemModel::insert($items);
                }
            });
        } else {
            $existId = ClassAttrUnitConvertModel::where('convert_name', $data['convert_name'])->value('id');
            if ($existId != $data['id']) {
                throw new InvalidRequestException('该单位名称已经存在,请修改后提交');
            }
            return DB::transaction(function () use ($data) {
                $data['update_time'] = time();
                $data['update_name'] = request()->user->name;
                $data['update_uid'] = request()->user->userId;
                $items = $data['items'];
                unset($data['items']);
                $result = ClassAttrUnitConvertModel::where('id', $data['id'])->update($data);
                if ($result) {
                    //找出要删除的item
                    $oldItemIds = ClassAttrUnitConvertItemModel::where('convert_id', $data['id'])->pluck('id')->toArray();
                    $itemIds = array_column($items, 'id');
                    $needDeleteItemIds = array_diff($oldItemIds, $itemIds);
                    if ($needDeleteItemIds) {
                        ClassAttrUnitConvertItemModel::whereIn('id', $needDeleteItemIds)->delete();
                    }
                    foreach ($items as $item) {
                        if (!empty($item['id'])) {
                            $item['update_time'] = time();
                            $item['update_name'] = request()->user->name;
                            $item['update_uid'] = request()->user->userId;
                            ClassAttrUnitConvertItemModel::where('id', $item['id'])->update($item);
                        } else {
                            $item['convert_id'] = $data['id'];
                            $item['create_time'] = time();
                            $item['create_name'] = request()->user->name;
                            $item['create_uid'] = request()->user->userId;
                            ClassAttrUnitConvertItemModel::insert($item);
                        }
                    }
                    return $result;
                }
            });
        }
    }

    public function changeClassAttrUnitConvertStatus($id, $status)
    {
        return ClassAttrUnitConvertModel::where('id', $id)->update([
            'update_time' => time(),
            'update_name' => request()->user->name,
            'update_uid' => request()->user->userId,
            'status' => $status,
        ]);
    }

    public function getClassAttrUnitConvertInfo($id)
    {
        $convert = ClassAttrUnitConvertModel::where('id', $id)->first()->toArray();
        $items = ClassAttrUnitConvertItemModel::where('convert_id', $id)->get()->toArray();
        $convert['items'] = $items;
        return $convert;
    }
}
