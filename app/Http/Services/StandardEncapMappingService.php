<?php

namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\EncapModel;
use App\Http\Models\SampleLogModel;
use App\Http\Models\StandardEncapMappingModel;
use App\Http\Models\StandardEncapModel;
use App\Http\Transformers\StandardEncapMappingTransformer;
use App\Imports\SampleImport;
use App\Imports\StandardEncapMappingImport;
use App\Jobs\UploadSample;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class StandardEncapMappingService
{
    public function getStandardMappingList($map)
    {
        $limit = request()->get('limit', 10);
        $model = new StandardEncapMappingModel();
        $query = $model->orderBy('id', 'desc');
        $query->with(['encap', 'standard_encap']);
        if (!empty($map['encap_name'])) {
            $query->whereHas('encap', function ($q) use ($map) {
                $q->where('encap_name', 'like', "${map['encap_name']}%");
            });
        }
        if (!empty($map['standard_encap_name'])) {
            $query->whereHas('standard_encap', function ($q) use ($map) {
                $q->where('encap_name', 'like', "${map['standard_encap_name']}%");
            });
        }
        if (!empty($map['standard_encap_id'])) {
            $query->where('standard_encap_id', $map['standard_encap_id']);
        }
        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }
        $list = $query->paginate($limit)->toArray();
        $transformer = new StandardEncapMappingTransformer();
        $list['data'] = $transformer->listTransformer($list['data']);
        return [
            'data' => $list['data'],
            'count' => $list['total'],
        ];
    }

    //找出没有映射的原始品牌
    public function findNotMappingStandEncap($map)
    {
        $limit = \Arr::get($map, 'limit', 10);
        $encapModel = new EncapModel();
        $query = $encapModel->leftjoin('lie_encap_standard_mapping', function ($join) {
            $join->on('lie_encap.encap_id', '=', 'lie_encap_standard_mapping.encap_id');
        })->where('lie_encap.status', '=', 1)
            ->whereNull('lie_encap_standard_mapping.id')->select('lie_encap.*');
        if (!empty($map['encap_name'])) {
            $query->where('lie_encap.encap_name', 'like', "${map['encap_name']}%");
        }
        if (!empty($map['encap_id'])) {
            $query->where('lie_encap.encap_id', $map['encap_id']);
        }
        $list = $query->orderBy('encap_id', 'desc')->paginate($limit)->toArray();
        return [
            'data' => $list['data'],
            'count' => $list['total'],
        ];
    }

    //添加映射
    public function addStandardEncapMapping($standardEncapId, $mappingEncapIds)
    {
        $mappingData = [];
        foreach ($mappingEncapIds as $key => $encapId) {
            $mappingData[] = [
                'standard_encap_id' => $standardEncapId,
                'encap_id' => $encapId,
                'admin_id' => request()->user->userId,
                'admin_name' => request()->user->name,
            ];
        }
        $mappingModel = new StandardEncapMappingModel();
        foreach ($mappingData as $key => $mapping) {
            //先去检查数据库是否有已经存在的关系
            $encapId = $mapping['encap_id'];
            $count = $mappingModel->where('encap_id', $encapId)->count();
            if ($count) {
                continue;
            }
            $mapping['status'] = 1;
            $mapping['create_time'] = time();
            $result = $mappingModel->insert($mapping);
            $this->saveStandardEncapMappingToRedis($mapping);
            StandardEncapService::saveMappingDataToRedis($mapping['id']);
            if (!$result) {
                return false;
            }
        }
        return true;
    }

    public function saveStandardEncapMappingToRedis($mapping)
    {
        $redis = Redis::connection('sku');
        $redis->hset('standard_encap_mapping', $mapping['encap_id'], $mapping['standard_encap_id']);
    }

    public function getStandardEncapMappingListByEncapId($map)
    {
        $standardEncapId = $map['standard_encap_id'];
        $model = new StandardEncapMappingModel();
        $query = $model::with(['encap'])->where('standard_encap_id', $standardEncapId);
        if (!empty($map['encap_name'])) {
            $query->whereHas('encap', function ($q) use ($map) {
                $q->where('encap_name', 'like', "${map['encap_name']}%");
            });
        }
        $data = $query->paginate(10)->toArray();
        $transformer = new StandardEncapMappingTransformer();
        $data['data'] = $transformer->listTransformer($data['data']);
        return [
            'data' => \Arr::get($data, 'data', []),
            'count' => \Arr::get($data, 'total', 0),
        ];
    }

    public function deleteStandardMapping($id)
    {
        $model = new StandardEncapMappingModel();
        $mappingEncapId = $model->where('id', $id)->value('encap_id');
        $result = $model->where('id', $id)->delete();
        //还要去删除redis
        if ($result) {
            if ($mappingEncapId) {
                $redis = Redis::connection('sku');
                $redis->hdel('standard_encap_mapping', $mappingEncapId);
                QueueService::publishQueue('stand_encap_map', [
                    'encap_id' => (int)$mappingEncapId,
                    'stand_encap_id' => 0,
                    'stand_encap_name' => '',
                ]);
            }
        }
        return $result;
    }

    //导出映射
    public function exportStandEncapMapping($map = [])
    {
        Excel::create('标准品牌映射导出', function ($excel) {
            $excel->sheet('标准品牌映射', function ($sheet) {
                $model = new StandardEncapMappingModel();
                $result = $model->select([
                    'lie_encap.encap_id as 映射品牌Id',
                    'lie_encap.encap_name as 映射品牌名称',
                    'lie_encap_standard.standard_encap_id as 标准品牌Id',
                    'lie_encap_standard.encap_name as 标准品牌名称'
                ])->leftjoin('lie_encap', 'lie_encap_standard_mapping.encap_id', '=', 'lie_encap.encap_id')
                    ->leftjoin('lie_encap_standard', 'lie_encap_standard_mapping.standard_encap_id', '=',
                        'lie_encap_standard.standard_encap_id')
                    ->get()->toArray();
                $sheet->fromArray($result);
            });
            $excel->setTitle('标准品牌映射导出');
        })->export('xls');
    }

    //导入映射
    public static function uploadStandardEncapMapping($file)
    {
        $path = $file->store('temp');
        $import = new StandardEncapMappingImport();
        Excel::import($import, $path);
        \Storage::delete($path);
    }
}
