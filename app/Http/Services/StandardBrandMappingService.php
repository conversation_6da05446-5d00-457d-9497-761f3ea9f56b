<?php

namespace App\Http\Services;

use App\Http\Models\BrandModel;
use App\Http\Models\StandardBrandMappingModel;
use App\Http\Models\StandardBrandModel;
use App\Http\Transformers\StandardBrandMappingTransformer;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class StandardBrandMappingService
{
    public function getStandardMappingList($map)
    {
        $limit = request()->get('limit', 10);
        $model = new StandardBrandMappingModel();
        $query = $model->orderBy('add_time', 'desc');
        $query->with(['brand', 'standard_brand', 'user']);
        if (!empty($map['brand_name'])) {
            $query->whereHas('brand', function ($q) use ($map) {
                $q->where('brand_name', 'like', "${map['brand_name']}%");
            });
        }
        if (!empty($map['standard_brand_name'])) {
            $query->whereHas('standard_brand', function ($q) use ($map) {
                $q->where('brand_name', 'like', "${map['standard_brand_name']}%");
            });
        }
        if (!empty($map['standard_brand_id'])) {
            $query->where('standard_brand_id', $map['standard_brand_id']);
        }
        if (!empty($map['add_time'])) {
            $startTime = strtotime(explode('~', $map['add_time'])[0]);
            $endTime = strtotime(explode('~', $map['add_time'])[1]);
            $query->whereBetween('add_time', [$startTime, $endTime]);
        }
        $list = $query->paginate($limit)->toArray();
        $transformer = new StandardBrandMappingTransformer();
        $list['data'] = $transformer->listTransformer($list['data']);
        return [
            'data' => $list['data'],
            'count' => $list['total'],
        ];
    }

    //找出没有映射的原始品牌
    public function findNotMappingStandBrand($map)
    {
        $limit = \Arr::get($map, 'limit', 10);
        $brandModel = new BrandModel();
        $query = $brandModel->leftjoin('lie_brand_standard_mapping', function ($join) {
            $join->on('lie_brand.brand_id', '=', 'lie_brand_standard_mapping.brand_id');
        })->where('lie_brand.status', '=', 1)->where('ignore_handle',-1)
            ->whereNull('lie_brand_standard_mapping.id')->select('lie_brand.*');
        if (!empty($map['brand_name'])) {
            $query->where('lie_brand.brand_name', 'like', "${map['brand_name']}%");
        }
        if (!empty($map['brand_id'])) {
            $query->where('lie_brand.brand_id', $map['brand_id']);
        }
        $list = $query->orderBy('brand_id', 'desc')->paginate($limit)->toArray();
        return [
            'data' => $list['data'],
            'count' => $list['total'],
        ];
    }

    //添加映射
    public function addStandardBrandMapping($standardBrandId, $mappingBrandIds)
    {
        $mappingData = [];
        foreach ($mappingBrandIds as $key => $brandId) {
            $mappingData[] = [
                'standard_brand_id' => $standardBrandId,
                'brand_id' => $brandId,
                'admin_id' => request()->user->userId,
            ];
        }
        $mappingModel = new StandardBrandMappingModel();
        foreach ($mappingData as $key => $mapping) {
            //先去检查数据库是否有已经存在的关系
            $brandId = $mapping['brand_id'];
            $count = $mappingModel->where('brand_id', $brandId)->count();
            if ($count) {
                continue;
            }
            $mapping['status'] = 1;
            $mapping['add_time'] = time();
            $result = $mappingModel->insert($mapping);
            $this->saveStandardBrandMappingToRedis($mapping);
            if (!$result) {
                return false;
            }
        }
        return true;
    }

    public function saveStandardBrandMappingToRedis($mapping)
    {
        $redis = Redis::connection('sku');
        $redis->hset("standard_brand_mapping", $mapping['brand_id'], $mapping['standard_brand_id']);
        QueueService::publishQueue('stand_brand_map', [
            'brand_id' => (int)$mapping['brand_id'],
            'stand_brand_id' => (int)$mapping['standard_brand_id'],
            'stand_brand_name' => StandardBrandModel::where('standard_brand_id',$mapping['standard_brand_id'])->value('brand_name'),
        ]);
    }

    public function getStandardBrandMappingListByBrandId($map)
    {
        $standardBrandId = $map['standard_brand_id'];
        $model = new StandardBrandMappingModel();
        $query = $model::with(['brand'])->where('standard_brand_id', $standardBrandId);
        if (!empty($map['brand_name'])) {
            $query->whereHas('brand', function ($q) use ($map) {
                $q->where('brand_name', 'like', "${map['brand_name']}%");
            });
        }
        $data = $query->paginate(10)->toArray();
        $transformer = new StandardBrandMappingTransformer();
        $data['data'] = $transformer->listTransformer($data['data']);
        return [
            'data' => \Arr::get($data, 'data', []),
            'count' => \Arr::get($data, 'total', 0),
        ];
    }

    public function deleteStandardMapping($id)
    {
        $model = new StandardBrandMappingModel();
        $mappingBrandId = $model->where('id', $id)->value('brand_id');
        $result = $model->where('id', $id)->delete();
        //还要去删除redis
        if ($result) {
            if ($mappingBrandId) {
                $redis = Redis::connection('sku');
                $redis->hdel('standard_brand_mapping', $mappingBrandId);
                QueueService::publishQueue('stand_brand_map', [
                    'brand_id' => (int)$mappingBrandId,
                    'stand_brand_id' => 0,
                    'stand_brand_name' => '',
                ]);
            }
        }
        return $result;
    }

    //导出映射
    public function exportStandBrandMapping($map = [])
    {
        Excel::create('标准品牌映射导出', function ($excel) {
            $excel->sheet('标准品牌映射', function ($sheet) {
                $model = new StandardBrandMappingModel();
                $result = $model->select([
                    'lie_brand.brand_id as 映射品牌Id',
                    'lie_brand.brand_name as 映射品牌名称',
                    'lie_brand_standard.standard_brand_id as 标准品牌Id',
                    'lie_brand_standard.brand_name as 标准品牌名称'
                ])->leftjoin('lie_brand', 'lie_brand_standard_mapping.brand_id', '=', 'lie_brand.brand_id')
                    ->leftjoin('lie_brand_standard', 'lie_brand_standard_mapping.standard_brand_id', '=',
                        'lie_brand_standard.standard_brand_id')
                    ->get()->toArray();
                $sheet->fromArray($result);
            });
            $excel->setTitle('标准品牌映射导出');
        })->export('xls');
    }
}
