<?php

namespace App\Http\Services;

//标准品牌服务
use App\Http\Models\EncapModel;
use App\Http\Models\StandardEncapMappingModel;
use App\Http\Models\StandardEncapModel;
use App\Http\Models\StandardEncapScmMappingModel;
use App\Http\Models\StandardEncapSpuModel;
use App\Http\Transformers\StandardEncapTransformer;
use App\Imports\StandardEncapSpuImport;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class StandardEncapService
{
    public function getStandardEncapList($map)
    {
        $model = new StandardEncapModel();
        $query = $model->where('status', '!=', 0)->orderBy('standard_encap_id', 'desc');
        $query = $this->filter($query, $map);
        $limit = \Arr::get($map, 'limit', 15);
        $result = $query->paginate($limit);

        $result = $result ? $result->toArray() : [];
        $transformer = new StandardEncapTransformer();
        $result['data'] = $transformer->listTransformer($result['data']);
        return [
            'data' => \Arr::get($result, 'data', []),
            'count' => \Arr::get($result, 'total'),
        ];
    }

    public function filter($query, $map)
    {
        if (!empty($map['encap_name'])) {
            $query->where('encap_name', 'like', "${map['encap_name']}%");
        }
        if (!empty($map['standard_encap_id'])) {
            $query->where('standard_encap_id', '=', $map['standard_encap_id']);
        }

        if (!empty($map['status']) || (isset($map['status']) && $map['status'] == '0')) {
            $query->where('status', $map['status']);
        }

        return $query;
    }

    public function saveStandardEncap($map)
    {
        $map = \Arr::only($map, [
            'standard_encap_id',
            'encap_name',
        ]);
        $model = new StandardEncapModel();
        //新增
        if (empty($map['standard_encap_id'])) {
            $data = $map;
            unset($map['encap_id']);
            $data['create_time'] = time();
            $data['create_name'] = request()->user->name;
            $data['create_uid'] = request()->user->userId;
            $result = $model->insertGetId($data);
            $standardEncapId = $result;
        } else {
            $data = $map;
            $data['update_time'] = time();
            $data['update_name'] = request()->user->name;
            $data['update_uid'] = request()->user->userId;
            $result = $model->where('standard_encap_id', $map['standard_encap_id'])->update($data);
            $standardEncapId = $data['standard_encap_id'];
            $mappingList = StandardEncapMappingModel::where('standard_encap_id', $standardEncapId)->get()->toArray();
            if (!empty($mappingList)) {
                foreach ($mappingList as $mapping) {
                    StandardEncapService::saveMappingDataToRedis($mapping['id']);
                }
            }

        }
        $encap = $model->where('standard_encap_id', $standardEncapId)->first()->toArray();
        $this->saveStandardEncapToRedis($standardEncapId, $encap);
        return $result;
    }

    public function saveStandardEncapToRedis($standardEncapId, $encap)
    {
        $redis = Redis::connection('sku');
        $redis->hset('standard_encap', $standardEncapId, json_encode($encap));
    }

    //检查是否有映射品牌
    public function checkHasMappingEncap($standardEncapId)
    {
        return (bool)StandardEncapMappingModel::where('standard_encap_id', $standardEncapId)->exists();
    }

    public function disableStandardEncap($encapId)
    {
        $standardEncapModel = new StandardEncapModel();
        $result = $standardEncapModel->where('standard_encap_id', $encapId)->update([
            'update_time' => time(),
            'status' => $standardEncapModel::STATUS_DISABLE,
        ]);
        $encap = $standardEncapModel->where('standard_encap_id', $encapId)->first()->toArray();
        if ($result) {
            $this->saveStandardEncapToRedis($encapId, $encap);
        }
        return $result;
    }

    public function enableStandardEncap($encapId)
    {
        $standardEncapModel = new StandardEncapModel();
        $result = $standardEncapModel->where('standard_encap_id', $encapId)->update([
            'update_time' => time(),
            'status' => $standardEncapModel::STATUS_OK,
        ]);
        $encap = $standardEncapModel->where('standard_encap_id', $encapId)->first()->toArray();
        if ($result) {
            $this->saveStandardEncapToRedis($encapId, $encap);
        }
        return $result;
    }


    //根据品牌id获取标准品牌名字列表
    public function getStandardEncapNameListByEncapIds($encapIds)
    {
        $encapIds = explode(',', trim($encapIds, ','));
        if (empty($encapIds)) {
            return '';
        }
        $redis = Redis::connection('sku');
        $encaps = $redis->hmget('standard_encap', $encapIds);
        $standardEncapNameList = [];
        foreach ($encaps as $encap) {
            $encap = json_decode($encap, true);
            $standardEncapNameList[] = $encap['encap_name'];
        }
        return $standardEncapNameList ? implode(',', $standardEncapNameList) : '';
    }

    public function checkStandardEncapNameList($standardEncapNameList = [])
    {
        //去数据库查询
        $validStandardEncapList = StandardEncapModel::whereIn('encap_name',
            $standardEncapNameList)
            ->pluck('encap_name', 'standard_encap_id')->toArray();
        $validStandardEncapMap = array_flip($validStandardEncapList);
        $invalidEncapNameList = [];
        $validStandardEncapNameList = [];
        $validStandardEncapIds = [];
        //判断哪些是错误的,哪些是正确的
        $redis = Redis::connection('sku');
        foreach ($standardEncapNameList as $key => $standardEncapName) {
            if (in_array($standardEncapName, array_values($validStandardEncapList))) {
                $validStandardEncapIds[] = $validStandardEncapMap[$standardEncapName];
                $validStandardEncapNameList[] = $standardEncapName;
                unset($standardEncapNameList[$key]);
            }
        }
        //剩下没有能直接找到标准品牌的,先当作普通品牌处理,然后去找出标准品牌,如果还是找不到,那就是无效品牌了
        foreach ($standardEncapNameList as $key => $checkStandardEncapName) {
            //先去找对应的标准品牌
            $encapId = EncapModel::where('encap_name', $checkStandardEncapName)->value('encap_id');
            $standardEncapId = $redis->hget('standard_encap_mapping', $encapId);
            if (empty($standardEncapId)) {
                $invalidEncapNameList[] = $checkStandardEncapName;
                continue;
            }
            $standardEncap = $redis->hget('standard_encap', $standardEncapId);
            $standardEncap = json_decode($standardEncap, true);
            $standardEncapName = Arr::get($standardEncap, 'encap_name', '');
            $standardEncapId = Arr::get($standardEncap, 'standard_encap_id', '');
            if (empty($standardEncapName)) {
                $invalidEncapNameList[] = $checkStandardEncapName;
                continue;
            }
            $validStandardEncapIds[] = $standardEncapId;
            $validStandardEncapNameList[] = $standardEncapName;
        }
        //整合数据,返回标准的数据和无效的数据
        return [
            'invalid_encap_name_list' => $invalidEncapNameList,
            'valid_encap_name_list' => $validStandardEncapNameList,
            'valid_encap_ids' => $validStandardEncapIds,
        ];
    }

    public function searchStandardEncap($encapName)
    {
        $query = StandardEncapModel::where('encap_name', 'like', $encapName . '%');
        $count = $query->count();
        $encapList = $query->select(['encap_name', 'standard_encap_id'])->paginate(15);
        $encapList = $encapList ? $encapList->toArray() : [];
        $data = Arr::get($encapList, 'data');
        $lastPage = Arr::get($encapList, 'last_page');
        return [
            'errcode' => 0,
            'errmsg' => 'ok',
            'total' => $count,
            'count' => $count,
            'data' => $data,
            'last_page' => $lastPage
        ];
    }

    public function getStandardEncapById($standardEncapId)
    {
        $redis = Redis::connection('sku');
        $standardEncap = $redis->hget('standard_encap', $standardEncapId);
        return json_decode($standardEncap, true);
    }

    public static function saveMappingDataToRedis($mappingId)
    {
        $mapping = StandardEncapMappingModel::where('id', $mappingId)->first();
        if (empty($mapping)) {
            return;
        }
        $encapName = EncapModel::where('encap_id', $mapping['encap_id'])->value('encap_name');
        $standardEncapName = StandardEncapModel::where('standard_encap_id', $mapping['standard_encap_id'])->value('encap_name');
        $redis = Redis::connection('sku');
        $redis->hset('standard_encap_mapping', strtoupper($encapName), $standardEncapName);
    }

    //根据分类id获取普通封装和标准封装
    public static function getEncapByClassId($classId1,$classId2)
    {
        //dd($classId2);
        $mongo = \DB::connection('mongodb');
        if (empty($classId2)) {
            $encapList = $mongo->table('spu_encap')->where('class_id1',(int)$classId1)->get();
        }else{
            $encapList = $mongo->table('spu_encap')->where('class_id2',(int)$classId2)->get();
        }

        return $encapList;
    }

}
