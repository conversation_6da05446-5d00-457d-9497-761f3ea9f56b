<?php
/**
 * Created by PhpStorm.
 * User: duwenjun
 * Date: 2021/8/25
 * Time: 5:33 PM
 */

namespace App\Http\Services;


use App\Http\Models\Cms\CmsBusinessConfigModel;
use App\Http\Models\Cms\CmsRolePermModel;
use App\Http\Models\Cms\CmsUserDepartmentModel;
use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Models\Cms\CmsUserPermModel;
use Illuminate\Support\Facades\Log;

class PermService
{

    const ROLE_ADMIN = 1; // 管理员角色

    const ROLE_BUYER = 2; // 采购员

    const ROLE_BUYER_LEADER = 3; // 采购经理

    const ROLE_BUYER_DIRECTOR = 4; // 采购总监

    const ROLE_NULL = 0;  // 未设置角色

    private static $roleNameMap = [
        "管理员" => self::ROLE_ADMIN,
        "采购员" => self::ROLE_BUYER,
        "采购经理" => self::ROLE_BUYER_LEADER,
        "采购总监" => self::ROLE_BUYER_DIRECTOR
    ];

    // 获取当前用户角色
    public static function getUserRoles($uid = 0, $email = "")
    {
        $admin = request()->get("user");
        $uid = $uid ?: $admin->userId;
        $email = $email ?: $admin->email;
        // 如果是管理员邮箱，直接返回管理员角色
        if ($email == '<EMAIL>') {
            return [self::ROLE_ADMIN];
        }

        // 根据域名查询系统业务ID
        $business = self::getBusinessInfo();
        if (!$business) {
            return [];
        }
        $bid = $business['bid'];

        // 权限系统配置的管理帐号， 如果是权限系统中配置的管理员邮箱，直接返回管理员角色
        $adminAccount = json_decode($business['admin'], true);
        if (in_array($email, $adminAccount)) {
            return [self::ROLE_ADMIN];
        }

        // 根据用户ID和业务ID查看角色
        $userPerm = CmsUserPermModel::getUserPermByUidAndBid($uid, $bid);
        if (empty($userPerm)) {
            return [];
        }

        // 没有选择角色
        if ($userPerm['roles'] == 'null') {
            return [];
        }

        $roleList = [];
        $roleIds = json_decode($userPerm['roles'], true);
        if ($roleIds) {
            $rolePermList = CmsRolePermModel::getRolePermInfosByRoleIds($roleIds, $bid);
            if ($rolePermList) {
                $roleNameList = array_column($rolePermList, 'name');
                foreach ($roleNameList as $roleName) {
                    if (isset(self::$roleNameMap[$roleName])) {
                        $roleList[] = self::$roleNameMap[$roleName];
                    }
                }
            }
        }

        return $roleList;
    }

    // 判断用户是否拥有某角色
    public static function hasRole($role, $roleList)
    {
        return in_array($role, $roleList);
    }

    // 获取用户所有权限
    public static function getUserPerms()
    {
        $userPerms = [];
        $business = self::getBusinessInfo();
        $bid = $business['bid'];
        $userPerm = CmsUserPermModel::getUserPermByUidAndBid(request()->user->userId, $bid);//获取用户权限信息
        if (!isset($userPerm['roles'])) {
            Log::error("该用户未配置权限，请联系管理员,uid:" . request()->user->userId);
            return $userPerms;
        }
        $roleIds = json_decode($userPerm['roles'], true);//用户所有的角色
        if ($roleIds) {
            $rolePerm = CmsRolePermModel::getRolePermInfosByRoleIds($roleIds, $bid);
            if ($rolePerm) {
                foreach ($rolePerm as $param) {//获取用户的角色权限
                    $arrParam = json_decode($param['perms']);
                    $userPerms = array_merge($userPerms, $arrParam);
                }
            }
        }
        return $userPerms;
    }

    // 获取permId(通过分割路由,得到最后一个就是方法名字
    public static function getPermId($url)
    {
        $permId = '';
        $urlInfo = parse_url($url);
        if (isset($urlInfo['path'])) {
            $pathArr = explode("/", $urlInfo['path']);
            $pathArr = array_filter($pathArr);
            $permId = implode("_", $pathArr);
        }
        return $permId;
    }

    // 判断用户是否拥有某权限
    public static function hasPerm($permId)
    {
        $userPerms = self::getUserPerms();
        return in_array($permId, $userPerms);
    }

    // 获取系统信息
    public static function getBusinessInfo()
    {
        return CmsBusinessConfigModel::getInfoByTitle('新版基石系统');
    }

    private static function getDomain()
    {
        return Config("website.footstone_domain");
    }

    // 获取指定用户下级所有人员
    public static function getSubUserId($userId)
    {
        $subUserIds = [];
        array_unshift($subUserIds, $userId); // 将当前用户添加到数组

        $userInfo = CmsUserInfoModel::getUserInfoById($userId);

        if (!$userInfo['department_id']) {
            return $subUserIds;
        }

        // 获取所有下级部门
        $departmentIds = self::_getDepartmentIds($userInfo['department_id']);
        // 获取下级部门的人员
        $subUserIds = CmsUserInfoModel::getUserIdsByDepartmentIds($departmentIds);
        return array_unique($subUserIds);
    }

    // 获取查询的部门id，查询销售和采购部门下所有子部门的ids
    // 这里要使用循环的查询方法，如果改部门下面还有子部门，那么一并查询，最终合并用户子部门id集
    public static function _getDepartmentIds($top_department_id)
    {
        $allDepartmentIds = $nextDepartmentIds = [$top_department_id];
        while ($nextDepartmentIds) {
            $nextDepartmentIds = CmsUserDepartmentModel::getDepartmentIdsParrentIds($nextDepartmentIds);
            $allDepartmentIds = array_merge($allDepartmentIds, $nextDepartmentIds);
        }
        return $allDepartmentIds;
    }

}
