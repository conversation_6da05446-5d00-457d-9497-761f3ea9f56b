<?php


namespace App\Http\Services;

use App\Http\Models\ClassAttrUnitModel;
use App\Http\Models\ClassAttrValueModel;
use App\Http\Models\PoolClassAttrModel;
use Illuminate\Support\Facades\DB;

class AttrService
{
    public function updateAttrValues($spuId, $attrValues)
    {
        $attrValues = $this->transformAttrValues($attrValues);
        if (!empty($attrValues)) {
            $dt = resolve($spuId);
            //先去mongo修改数据
            $this->saveAttrValuesToMongo($spuId, $attrValues);
            return DB::connection($dt['db'])->table($dt['table'])->where('spu_id',
                $spuId)->update([
                'update_time' => time(),
                'attr_values' => $attrValues
            ]);
        }
        return true;
    }

    //保存到mongo
    private function saveAttrValuesToMongo($spuId, $attrValues)
    {
        $dbConnection = DB::connection('pool_class');
        $attrsExtend = [];
        foreach (\GuzzleHttp\json_decode($attrValues, true) as $value) {
            $value = explode(',', $value);
            //获取各类信息
            $attrName = $dbConnection->table('class_attr')->where('attr_id', $value[0])->value('attr_name');
            $attrValue = $dbConnection->table('class_attr_value')->where('attr_value_id',
                $value[1])->value('value');
            $attrUnit = $dbConnection->table('class_attr_unit')->where('attr_unit_id',
                $value[2])->value('attr_unit_name');
            $attrsExtend[] = [
                'attr_name' => $attrName,
                'attr_value' => $attrValue,
                'attr_unit' => $attrUnit,
            ];
        }

        $spuAttr = DB::connection('mongodb')->collection('spu_attrs2')->where('spu_id', '=', (int)$spuId)->first();
        unset($spuAttr['_id']);
        $spuAttr['attrs_extend'] = $attrsExtend;
        if ($spuAttr) {
            $map['spu_id'] = (int)$spuId;
            $save = DB::connection('mongodb')->collection('spu_attrs2')->where('spu_id', '=',
                (int)$spuId)->update($spuAttr);
        }
    }

    //转换数据,得到最终存到数据库的数据
    public function transformAttrValues($attrValues)
    {
        //筛选掉为空的值
        $attrValues = array_filter($attrValues, function ($value) {
            return $value['attr_value_and_unit'] != "";
        });
        $result = [];
        foreach ($attrValues as $key => $value) {
            $result[] = (int)$value['attr_id'] . "," . (int)explode(',',
                    $value['attr_value_and_unit'])[0] . "," . (int)explode(',', $value['attr_value_and_unit'])[1];
        }
        return $result ? \GuzzleHttp\json_encode($result) : '';
    }

    //根据spu获取属性
    //isSpec是否是重要属性,获取重要属性不需要属性名称
    public function getAttrsExtendBySpuIds($spuIds = [], $isSpec = false)
    {
        $spuIds = array_map(function ($spuId) {
            return intval($spuId);
        }, $spuIds);
        $attrs = DB::connection('mongodb')->collection('spu_attrs2')->whereIn('spu_id', $spuIds)->get();
        $attrsExtend = [];
        if (empty($attrs)) {
            return [];
        }
        foreach ($attrs as $key => $attr) {
            $data = [];
            if (empty($attr['attrs_extend'])) {
                continue;
            }
            foreach ($attr['attrs_extend'] as $k => $v) {
                if ($isSpec) {
                    if (\Arr::get($v, 'is_spec') == 1) {
                        $data[] = $v['attr_value'] . $v['attr_unit'];
                    }
                } else {
                    $data[] = $v['attr_name'] . " : " . $v['attr_value'] . $v['attr_unit'];
                }
            }
            $attrsExtend[$attr['spu_id']] = $data;
        }
        return $attrsExtend;
    }

    //根据spu获取简单属性
    public function getAttrsExtend($spuId)
    {
        $attr = DB::connection('mongodb')->collection('spu_attrs2')->where('spu_id', (int)$spuId)->first();
        if (empty($attr['attrs_extend'])) {
        return [];
        }
        return $attr['attrs_extend'];
    }


    //把新增的属性和分类关联到sku
    public function addAttrToSpu($spuId, $classId, $uploadAttrs = [])
    {
        $attrValues = [];
        $attrModel = new PoolClassAttrModel();
        $attrUnitModel = new ClassAttrUnitModel();
        $attrValueModel = new ClassAttrValueModel();
        foreach ($uploadAttrs as $key => $uploadAttr) {
            //根据分类id去找属性
            $attrId = $attrModel->where('class_id', $classId)->where('attr_name',
                $uploadAttr['attr_name'])->value('attr_id');
            //如果分类属性不存在,就去新建一个然后获取分类属性id
            if (!$attrId) {
                $attrData = [
                    'class_id' => $classId,
                    'attr_name' => $uploadAttr['attr_name'],
                    'status' => 1,
                    'add_time' => time(),
                    'update_time' => time(),
                    'attr_unit_id' => $attrUnitModel::DEFAULT_NULL_UNIT_ID,
                ];
                $attrId = $attrModel->insertGetId($attrData);
                $attrValueData = [
                    'attr_id' => $attrId,
                    'value' => $uploadAttr['attr_value'],
                    'attr_unit_id' => $attrUnitModel::DEFAULT_NULL_UNIT_ID,
                    'add_time' => time(),
                    'update_time' => time(),
                ];
                //还要新增attr_value
                $attrValueId = $attrValueModel->insertGetId($attrValueData);
            } else {
                //先去找class_attr_value表里面是否存在对应的value
                $attrValueId = $attrValueModel->where('value', $uploadAttr['attr_value'])->where('attr_id',
                    $attrId)->value('attr_value_id');
                if (empty($attrValueId)) {
                    $attrValueData = [
                        'attr_id' => $attrId,
                        'value' => $uploadAttr['attr_value'],
                        'attr_unit_id' => $attrUnitModel::DEFAULT_NULL_UNIT_ID,
                        'add_time' => time(),
                        'update_time' => time(),
                    ];
                    //还要新增attr_value
                    $attrValueId = $attrValueModel->insertGetId($attrValueData);
                }
            }
            //拼装参数id存到sku的数据库
            $attrValues[] = $attrId . ',' . $attrValueId . ',' . $attrUnitModel::DEFAULT_NULL_UNIT_ID;
        }
        $db = resolve($spuId);
        $attrValues = json_encode($attrValues);
        DB::connection($db['db'])->table($db['table'])->where('spu_id', $spuId)
            ->update(['attr_values' => $attrValues]);
    }

    //把新增的sku属性写到mongo
    public function addSkuAttrToMongo($spuId, $uploadAttr)
    {
        $spuAttr = DB::connection('mongodb')->collection('spu_attrs2')
            ->where('spu_id', '=', (int)$spuId)->first();
        if (!empty($spuAttr)) {
            $spuAttr['attrs_extend'] = $uploadAttr;
            DB::connection('mongodb')->collection('spu_attrs2')
                ->where('spu_id', '=', (int)$spuId)->update($spuAttr);
        } else {
            $spuAttr["spu_id"] = (int)$spuId;
            $spuAttr["attrs_extend"] = $uploadAttr;
            DB::connection('mongodb')->collection('spu_attrs2')
                ->insert($spuAttr);
        }

    }

    //获取spu的mongo数据
    public static function getSpuAttr($spuId)
    {
        $spuAttr = DB::connection('mongodb')->collection('spu_attrs2')
            ->where('spu_id', '=', (int)$spuId)->first();
        dd($spuAttr);
    }

}
