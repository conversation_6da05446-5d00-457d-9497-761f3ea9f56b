<?php

namespace App\Http\Services;

use Illuminate\Support\Arr;
use App\Http\Models\SupplierModel;
use Illuminate\Support\Facades\Log;
use App\Http\Queue\RabbitQueueModel;
use Illuminate\Support\Facades\Redis;
use App\Http\Models\AbilityLevelRuleModel;
use App\Exceptions\InvalidRequestException;
use App\Http\Models\Supplier\SupplierChannelModel;
use App\Http\Transformers\AbilityLevelRuleTransformer;

class AbilityLevelRuleService
{
    public function getAbilityLevelRuleList($map)
    {
        $limit = Arr::get($map, 'limit', 10);
        $query = AbilityLevelRuleModel::with(['supplier', 'supplier_channel'])->orderBy('id', 'desc');

        if (!empty($map['id'])) {
            $query->where('id', $map['id']);
        }

        if (!empty($map['supplier_id'])) {
            $query->where('supplier_id', $map['supplier_id']);
        }

        if (!empty($map['supplier_code'])) {
            $query->where('supplier_code', 'like', '%' . $map['supplier_code'] . '%');
        }

        if (!empty($map['level']) || (isset($map['level']) && $map['level'] === '0')) {
            $query->where('level', $map['level']);
        }

        if (isset($map['status']) && $map['status'] !== '') {
            $query->where('status', $map['status']);
        }

        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }

        $result = $query->paginate($limit)->toArray();

        $transformer = new AbilityLevelRuleTransformer();
        $result['data'] = $transformer->listTransformer($result['data']);

        return [
            'data' => Arr::get($result, 'data', []),
            'count' => Arr::get($result, 'total'),
        ];
    }

    public function saveAbilityLevelRule($params)
    {
        // 检查supplier_code是否存在
        if ($this->checkSupplierCodeExists($params['supplier_code'], $params['id'] ?? 0)) {
            throw new InvalidRequestException('该供应商编码已经存在规则，不能新增');
        }

        // 当supplier_id不为17时，检查supplier_id是否存在
        if ($params['supplier_id'] != 17) {
            $supplierExists = SupplierModel::where('supplier_id', $params['supplier_id'])->exists();
            if (!$supplierExists) {
                throw new InvalidRequestException('该渠道不存在');
            }
        }

        if (empty($params['id'])) {
            // 新增
            $id = AbilityLevelRuleModel::insertGetId([
                'supplier_id' => $params['supplier_id'],
                'supplier_code' => \trim($params['supplier_code'], ','),
                'source' => $params['source'],
                'level' => $params['level'],
                'status' => AbilityLevelRuleModel::STATUS_ENABLED,
                'create_time' => time(),
                'create_uid' => request()->user->userId,
                'create_name' => request()->user->name,
            ]);
            OperationLogService::addOperationLog([
                'obj_name' => 'ability_level',
                'obj_id' => $id,
                'content' => '新增履约能力规则',
                'create_time' => time(),
                'create_name' => request()->user->name,
            ]);
        } else {
            $oldAbilityLevel = AbilityLevelRuleModel::find($params['id'])->value('level');
            // 更新
            $id = $params['id'];
            AbilityLevelRuleModel::where('id', $id)->update([
                'supplier_id' => $params['supplier_id'],
                'supplier_code' => \trim($params['supplier_code'], ','),
                'source' => $params['source'],
                'level' => $params['level'],
                'update_time' => time(),
                'update_uid' => request()->user->userId,
                'update_name' => request()->user->name,
            ]);

            OperationLogService::addOperationLog([
                'obj_name' => 'ability_level',
                'obj_id' => $id,
                'content' => '修改履约能力规则 : 履约程度由' . (config('field.AbilityLevelRuleLevel')[$oldAbilityLevel] ?? '未知') . '改为' . (config('field.AbilityLevelRuleLevel')[$params['level']] ?? '未知'),
                'create_time' => time(),
                'create_name' => request()->user->name,
            ]);
        }

        // 保存规则到Redis并比较变化
        $this->compareAndSendUpdates();
        return $id;
    }

    /**
     * 保存规则到Redis并比较变化
     *
     * @param bool $compareAndSend 是否比较变化并发送队列
     * @return bool|array 成功返回true，如果需要比较则返回新数据
     */
    public static function saveRuleToRedis($compareAndSend = false)
    {
        // 如果需要比较变化，先获取旧数据
        $oldRulesByLevel = [];
        $redis = Redis::connection('sku');
        $redisKey = 'ability_level';

        if ($compareAndSend) {
            // 获取所有现有的level
            $levels = $redis->hkeys($redisKey);
            foreach ($levels as $level) {
                $levelData = $redis->hget($redisKey, $level);
                if ($levelData) {
                    $oldRulesByLevel[$level] = json_decode($levelData, true);
                }
            }
        }

        // 获取所有启用状态的规则
        $rules = AbilityLevelRuleModel::where('status', AbilityLevelRuleModel::STATUS_ENABLED)
            ->get()
            ->toArray();
        // 按履约程度分组初始化数据结构
        $rulesByLevel = [];
        foreach ($rules as $rule) {
            $level = $rule['level'];
            if (!isset($rulesByLevel[$level])) {
                $rulesByLevel[$level] = [
                    'canal' => [],
                    'supplier_id' => []
                ];
            }
            // 处理供应商编码和来源
            if (!empty($rule['supplier_code'])) {
                // 查找是否已存在该supplier_code
                $canalIndex = array_search($rule['supplier_code'], array_column($rulesByLevel[$level]['canal'], 'supplier_code'));

                if ($canalIndex === false) {
                    // 不存在则添加新记录
                    $rulesByLevel[$level]['canal'][] = [
                        'supplier_code' => $rule['supplier_code'],
                        'source' => !empty($rule['source']) ? array_map('intval', explode(',', $rule['source'])) : []
                    ];
                } else {
                    // 存在则合并source
                    if (!in_array($rule['source'], $rulesByLevel[$level]['canal'][$canalIndex]['source'])) {
                        $rulesByLevel[$level]['canal'][$canalIndex]['source'][] = $rule['source'];
                    }
                }
            }
            // 处理supplier_id
            if (!empty($rule['supplier_id']) && $rule['supplier_id'] != 17) {
                if (!in_array($rule['supplier_id'], $rulesByLevel[$level]['supplier_id'])) {
                    $rulesByLevel[$level]['supplier_id'][] = (int)$rule['supplier_id'];
                }
            }
        }
        // 写入Redis
        // 删除旧数据
        $redis->del($redisKey);
        // 写入新数据
        foreach ($rulesByLevel as $level => $ruleData) {
            // 对supplier_id数组去重
            $ruleData['supplier_id'] = array_values(array_unique($ruleData['supplier_id']));
            // 对每个canal的source数组去重并排序
            foreach ($ruleData['canal'] as &$canal) {
                $canal['source'] = array_values(array_unique($canal['source']));
                sort($canal['source']);
            }
            unset($canal);
            $redis->hset($redisKey, $level, json_encode($ruleData));
        }

        if ($compareAndSend) {
            return $rulesByLevel;
        }

        return true;
    }

    public function updateStatus($id, $status)
    {
        AbilityLevelRuleModel::where('id', $id)->update([
            'status' => $status,
            'update_time' => time(),
            'update_uid' => request()->user->userId,
            'update_name' => request()->user->name,
        ]);
        OperationLogService::addOperationLog([
            'obj_name' => 'ability_level',
            'obj_id' => $id,
            'content' => '修改履约能力规则状态 : 状态由' . ($status == AbilityLevelRuleModel::STATUS_ENABLED ? '启用' : '禁用') . '改为' . ($status == AbilityLevelRuleModel::STATUS_ENABLED ? '禁用' : '启用'),
            'create_time' => time(),
            'create_name' => request()->user->name,
        ]);
        $this->compareAndSendUpdates();
        return true;
    }

    public function deleteAbilityLevelRule($id)
    {
        $result = AbilityLevelRuleModel::where('id', $id)->delete();
        $this->compareAndSendUpdates();
        return $result;
    }

    public function checkSupplierCodeExists($supplierCode, $id = 0)
    {
        if (empty($supplierCode)) {
            return false;
        }
        $query = AbilityLevelRuleModel::where('supplier_code', $supplierCode);

        if ($id > 0) {
            $query->where('id', '!=', $id);
        }

        return $query->exists();
    }

    /**
     * 比较Redis中的新旧数据并发送更新队列
     *
     * @return bool
     */
    public function compareAndSendUpdates()
    {
        // 获取旧数据
        $redis = Redis::connection('sku');
        $redisKey = 'ability_level';
        $oldRulesByLevel = [];

        // 获取所有现有的level
        $levels = $redis->hkeys($redisKey);
        foreach ($levels as $level) {
            $levelData = $redis->hget($redisKey, $level);
            if ($levelData) {
                $oldRulesByLevel[$level] = json_decode($levelData, true);
            }
        }

        // 保存新规则到Redis并获取新数据
        $newRulesByLevel = self::saveRuleToRedis(true);

        // 比较新旧数据
        $this->detectChangesAndSendQueue($oldRulesByLevel, $newRulesByLevel);

        return true;
    }

    /**
     * 检测变化并发送队列
     *
     * @param array $oldRulesByLevel 旧规则数据
     * @param array $newRulesByLevel 新规则数据
     * @return void
     */
    private function detectChangesAndSendQueue($oldRulesByLevel, $newRulesByLevel)
    {
        $queue = new RabbitQueueModel('trading');
        $queueName = 'lie_footstone_update_sku_info';

        // 检查supplier_id的变化
        $this->checkSupplierIdChanges($oldRulesByLevel, $newRulesByLevel, $queue, $queueName);
        // 检查canal的变化
        $this->checkCanalChanges($oldRulesByLevel, $newRulesByLevel, $queue, $queueName);
    }

    /**
     * 检查supplier_id的变化并发送队列
     *
     * @param array $oldRulesByLevel 旧规则数据
     * @param array $newRulesByLevel 新规则数据
     * @param RabbitQueueModel $queue 队列模型
     * @param string $queueName 队列名称
     * @return void
     */
    private function checkSupplierIdChanges($oldRulesByLevel, $newRulesByLevel, $queue, $queueName)
    {
        // 遍历所有旧数据中的supplier_id
        foreach ($oldRulesByLevel as $level => $oldRuleData) {
            foreach ($oldRuleData['supplier_id'] as $supplierId) {
                // 检查supplier_id是否在新数据中存在
                $exists = false;
                $newLevel = null;

                // 在新数据中查找此supplier_id
                foreach ($newRulesByLevel as $newLevel => $newRuleData) {
                    if (in_array($supplierId, $newRuleData['supplier_id'])) {
                        $exists = true;
                        // 如果级别变化了，发送更新
                        if ($level != $newLevel) {
                            $this->sendUpdateQueue($queue, $queueName, $newLevel, $supplierId);
                        }
                        break;
                    }
                }

                // 如果不存在，说明被删除了，默认设为弱履约(level=0)
                if (!$exists) {
                    $this->sendUpdateQueue($queue, $queueName, 0, $supplierId);
                }
            }
        }

        // 检查新增的supplier_id
        foreach ($newRulesByLevel as $level => $newRuleData) {
            foreach ($newRuleData['supplier_id'] as $supplierId) {
                $existsInOld = false;

                // 检查是否在旧数据中存在
                foreach ($oldRulesByLevel as $oldRuleData) {
                    if (in_array($supplierId, $oldRuleData['supplier_id'])) {
                        $existsInOld = true;
                        break;
                    }
                }

                // 如果是新增的，发送更新
                if (!$existsInOld) {
                    $this->sendUpdateQueue($queue, $queueName, $level, $supplierId);
                }
            }
        }
    }

    /**
     * 检查canal的变化并发送队列
     *
     * @param array $oldRulesByLevel 旧规则数据
     * @param array $newRulesByLevel 新规则数据
     * @param RabbitQueueModel $queue 队列模型
     * @param string $queueName 队列名称
     * @return void
     */
    private function checkCanalChanges($oldRulesByLevel, $newRulesByLevel, $queue, $queueName)
    {
        // 遍历所有旧数据中的canal
        foreach ($oldRulesByLevel as $level => $oldRuleData) {
            foreach ($oldRuleData['canal'] as $oldCanal) {
                $supplierCode = $oldCanal['supplier_code'];

                // 检查canal是否在新数据中存在
                $exists = false;
                $newLevel = null;

                // 在新数据中查找此canal
                foreach ($newRulesByLevel as $newLevel => $newRuleData) {
                    foreach ($newRuleData['canal'] as $newCanal) {
                        if ($newCanal['supplier_code'] == $supplierCode) {
                            $exists = true;
                            // 如果级别变化了，发送更新
                            if ($level != $newLevel) {
                                $this->sendUpdateQueue($queue, $queueName, $newLevel, null, $supplierCode);
                            }
                            break 2;
                        }
                    }
                }

                // 如果不存在，说明被删除了，默认设为弱履约(level=0)
                if (!$exists) {
                    $this->sendUpdateQueue($queue, $queueName, 0, null, $supplierCode);
                }
            }
        }

        // 检查新增的canal
        foreach ($newRulesByLevel as $level => $newRuleData) {
            foreach ($newRuleData['canal'] as $newCanal) {
                $supplierCode = $newCanal['supplier_code'];
                $existsInOld = false;

                // 检查是否在旧数据中存在
                foreach ($oldRulesByLevel as $oldRuleData) {
                    foreach ($oldRuleData['canal'] as $oldCanal) {
                        if ($oldCanal['supplier_code'] == $supplierCode) {
                            $existsInOld = true;
                            break 2;
                        }
                    }
                }

                // 如果是新增的，发送更新
                if (!$existsInOld) {
                    $this->sendUpdateQueue($queue, $queueName, $level, null, $supplierCode);
                }
            }
        }
    }

    /**
     * 发送更新队列
     *
     * @param RabbitQueueModel $queue 队列模型
     * @param string $queueName 队列名称
     * @param int $abilityLevel 履约级别
     * @param int|null $supplierId 供应商ID
     * @param string|null $canal 供应商编码
     * @return bool
     */
    private function sendUpdateQueue($queue, $queueName, $abilityLevel, $supplierId = null, $canal = null)
    {
        // if ($canal) {
        //     $id = AbilityLevelRuleModel::where('supplier_code', $canal)->value('id');
        // } else {
        //     $id = AbilityLevelRuleModel::where('supplier_id', $supplierId)->value('id');
        // }
        // // 构建队列数据
        // $queueData = [
        //     'update_type' => 1, // 写死
        //     'ability_level' => (int)$abilityLevel, // 履约级别：0：弱履约，1：中履约；2：强履约
        //     'obj_id' => $id,
        // ];

        // // supplier_id和canal不能同时存在
        // if ($supplierId !== null) {  
        //     $queueData['supplier_id'] = (string)$supplierId;
        // } elseif ($canal !== null) {
        //     $queueData['canal'] = $canal;
        // } else {
        //     // 如果两者都为空，不发送队列
        //     return false;
        // }
        // // dd($queueData);
        // // return true;
        // // 记录日志
        // Log::info("AbilityLevelRule-发送更新队列:" . json_encode($queueData) . "\n");
        // // dd($queueData,$queueName);
        // // 发送队列
        // return $queue->insertRawQueue('', $queueData, $queueName);
    }
}
