<?php


namespace App\Http\Services;

use App\Http\Models\SupplierModel;
use Illuminate\Support\Facades\DB;

class AttrPregService
{
    //获取纯数字
    public static function extractPurNumber($value)
    {
        $value = self::transformFraction($value);
        // 使用正则表达式匹配所有数字（包括负数）
        preg_match_all('/-?\d+(\.\d+)?/', $value, $matches);
        $purNumber = $matches[0];
        return $purNumber;
    }

    //转换分数
    public static function transformFraction($value)
    {
        if (preg_match("/(\d+\/\d+)/", $value, $matches)) {
            $fraction = $matches[1]; // 提取出的分数
            list($numerator, $denominator) = explode('/', $fraction); // 使用explode函数将分子和分母分开
            return $numerator / $denominator;
        } else {
            return $value;
        }
    }

    //提取出单位
    public static function extractUnit($value)
    {
        //if (preg_match("/([A-Za-z℃%Ω°μCμF]+)/", $value, $matches)) {
        if (preg_match('/[^\d±]+$/u', $value, $matches)) {
            $unit = $matches[0]; // 提取出的单位
            return trim($unit);
        } else {
            return '';
        }
    }
}
