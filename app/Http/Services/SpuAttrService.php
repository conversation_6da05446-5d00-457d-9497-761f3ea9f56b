<?php

namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\BrandModel;
use App\Http\Models\PoolClass\ClassAttrUnitConvertItemModel;
use App\Http\Models\PoolClass\ClassAttrUnitConvertModel;
use App\Http\Models\PoolClass\ClassAttrUnitModel;
use App\Http\Models\PoolClass\ClassAttrValueModel;
use App\Http\Models\PoolClass\PoolClassAttrModel;
use App\Http\Models\StandardBrandMappingModel;
use App\Http\Models\StandardBrandModel;
use App\Http\Transformers\StandardBrandMappingTransformer;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class SpuAttrService
{
    //组装参数数据
    public function combineAttrData($attrData)
    {
        if (empty($attrData)) {
            return [];
        }
    }

    /**
     * 根据DB的attr_values组装给mongo的attrs_extend的数据
     */
    public function saveSpuAttr($spuId, $attrs, $attrExtraList = [])
    {
        if (empty($attrs) && empty($attrExtraList)) {
            return [];
        }

        // 过滤和清理属性数据
        $attrs = $this->filterAndCleanAttrs($attrs);

        // 获取SPU信息
        $spu = $this->getSpuInfo($spuId);
        if (!$spu) {
            return true;
        }

        // 初始化数据容器
        $attrsExtend = [];
        $attrsFormat = [];
        $attrMix = [];
        $attrMixText = [];

        // 处理每个属性
        foreach ($attrs as $attr) {
            if (empty($attr['origin_value'])) {
                continue;
            }

            $processedAttr = $this->processAttribute($attr, $attrExtraList, $attrsExtend, $attrsFormat, $attrMixText);
            if ($processedAttr['isPureText']) {
                $attrMix[] = str_replace(' ', '', $processedAttr['attrExtra']['origin_value']);
            }
        }

        // 组装最终数据并保存
        return $this->assembleAndSaveSpuAttr($spuId, $spu, $attrsExtend, $attrsFormat, $attrMix, $attrMixText);
    }

    /**
     * 过滤和清理属性数据
     */
    private function filterAndCleanAttrs($attrs)
    {
        $attrs = array_filter($attrs, function ($attr) {
            return !empty($attr['valid']);
        });

        return array_map(function ($attr) {
            return \Arr::only($attr, ['attr_id', 'origin_value']);
        }, $attrs);
    }

    /**
     * 获取SPU信息
     */
    private function getSpuInfo($spuId)
    {
        $spu = Redis::connection('spu')->hget('spu', $spuId);
        return empty($spu) ? null : json_decode($spu, true);
    }

    /**
     * 处理单个属性
     */
    private function processAttribute($attr, $attrExtraList, &$attrsExtend, &$attrsFormat, &$attrMixText)
    {
        // 获取属性额外数据
        $attrExtra = $this->getAttrExtra($attr, $attrExtraList);

        // 获取属性基础数据并合并
        $attrData = PoolClassAttrModel::where('attr_id', $attr['attr_id'])->first()->toArray();
        $attr = array_merge($attr, $attrData);

        // 处理属性值ID
        $attrValueId = $this->processAttrValueId($attr);

        // 添加到扩展数据
        $attrsExtend[] = [
            'attr_name' => $attr['attr_name'],
            'attr_value' => $attr['origin_value'],
            'attr_id' => intval($attr['attr_id']),
            'attr_value_id' => intval($attrValueId),
        ];

        // 确定插入类型
        $insertType = $this->determineInsertType($attrData, $attrExtra);

        // 根据类型处理格式化数据
        $result = $this->processAttrByType($insertType, $attrData, $attrExtra, $attr, $attrMixText);

        if (!$result['isPureText']) {
            $attrsFormat[] = $result['format'];
        }

        return $result;
    }

    /**
     * 获取属性额外数据
     */
    private function getAttrExtra($attr, $attrExtraList)
    {
        if (!empty($attrExtraList)) {
            return $attrExtraList[$attr['attr_id']];
        }

        $attrExtra = (new ClassAttrService())->extractAttr($attr['attr_id'], $attr['origin_value']);
        if (!is_array($attrExtra)) {
            throw new InvalidRequestException($attrExtra);
        }

        return $attrExtra;
    }

    /**
     * 处理属性值ID
     */
    private function processAttrValueId($attr)
    {
        $existingValue = ClassAttrValueModel::where('attr_id', $attr['attr_id'])
            ->where('value', $attr['origin_value'])
            ->first();

        if (!$existingValue) {
            return ClassAttrValueModel::insertGetId([
                'attr_id' => (int)$attr['attr_id'],
                'value' => $attr['origin_value'],
                'attr_unit_id' => (int)$attr['attr_unit_id'],
                'add_time' => time(),
            ]);
        }

        // 更新单位信息（如果需要）
        if (!$existingValue->attr_unit_id && !empty($attr['attr_unit_id'])) {
            $existingValue->update(['attr_unit_id' => $attr['attr_unit_id']]);
        }

        return $existingValue->attr_value_id;
    }

    /**
     * 确定插入类型
     */
    private function determineInsertType($attrData, $attrExtra)
    {
        $insertType = !empty($attrExtra['real_insert_type']) ? $attrExtra['real_insert_type'] : $attrData['insert_type'];

        if (isTwoDimensionalArray($attrExtra) && !empty($attrExtra[0]['real_insert_type'])) {
            $insertType = $attrExtra[0]['real_insert_type'];
        }

        return $insertType;
    }

    /**
     * 根据类型处理属性数据
     */
    private function processAttrByType($insertType, $attrData, $attrExtra, $attr, &$attrMixText)
    {
        switch ($insertType) {
            case 1: case 3: case 6: case 8: // 单数值相关类型
                return $this->processSingleValueType($attrData, $attrExtra, $attr, $attrMixText);

            case 2: case 4: case 7: case 9: // 范围值相关类型
                return $this->processRangeValueType($attrData, $attrExtra, $attr, $attrMixText);

            default:
                return [
                    'isPureText' => true,
                    'format' => [],
                    'attrExtra' => $attrExtra
                ];
        }
    }

    /**
     * 处理单数值类型
     */
    private function processSingleValueType($attrData, $attrExtra, $attr, &$attrMixText)
    {
        $valueMulti = [];

        if (isTwoDimensionalArray($attrExtra)) {
            $standardUnitName = $attrExtra[0]['standard_unit'] ?? '';
            foreach ($attrExtra as $item) {
                $value = self::convertScientificNotation($item['value']);
                $valueMulti[] = (string)$value;
                $this->addAttrMixText($attrMixText, $valueMulti, $item);
            }
        } else {
            $standardUnitName = $attrExtra['standard_unit'] ?? '';
            $value = self::convertScientificNotation($attrExtra['value']);
            $valueMulti[] = (string)$value;
            $this->addAttrMixText($attrMixText, $valueMulti, $attrExtra);
        }

        return [
            'isPureText' => false,
            'format' => [
                'attr_name' => $attrData['attr_name'],
                'standard_attr_unit' => $standardUnitName,
                'attr_id' => (int)$attr['attr_id'],
                'attr_value_origin' => $attr['origin_value'],
                'attr_value_multi' => $valueMulti
            ],
            'attrExtra' => $attrExtra
        ];
    }

    /**
     * 处理范围值类型
     */
    private function processRangeValueType($attrData, $attrExtra, $attr, &$attrMixText)
    {
        if (isTwoDimensionalArray($attrExtra)) {
            return $this->processMultiRangeValues($attrData, $attrExtra, $attr, $attrMixText);
        } else {
            return $this->processSingleRangeValue($attrData, $attrExtra, $attr, $attrMixText);
        }
    }

    /**
     * 处理多个范围值
     */
    private function processMultiRangeValues($attrData, $attrExtra, $attr, &$attrMixText)
    {
        $valueRange = [];

        foreach ($attrExtra as $item) {
            $attrValueMin = (string)self::convertScientificNotation($item['value_min']);
            $attrValueMax = (string)self::convertScientificNotation($item['value_max']);

            $valueRange[] = [
                'attr_value_min' => $attrValueMin,
                'attr_value_max' => $attrValueMax,
            ];

            if (!empty($item['attr_text'])) {
                $attrMixText[] = ($attrValueMin . $item['standard_unit']) . '~' . ($attrValueMax . $item['standard_unit']) . $item['attr_text'];
                $attrMixText[] = $item['attr_text'];
            }
        }

        return [
            'isPureText' => false,
            'format' => [
                'attr_name' => $attrData['attr_name'],
                'standard_attr_unit' => $attrExtra[0]['standard_unit'] ?? '',
                'attr_id' => (int)$attr['attr_id'],
                'attr_value_origin' => $attr['origin_value'],
                'attr_value_range' => $valueRange
            ],
            'attrExtra' => $attrExtra
        ];
    }

    /**
     * 处理单个范围值
     */
    private function processSingleRangeValue($attrData, $attrExtra, $attr, &$attrMixText)
    {
        if (!isset($attrExtra['value_min'])) {
            // 当作单值处理
            $valueMulti = [(string)($attrExtra['value'] ?? '')];
            $this->addAttrMixText($attrMixText, $valueMulti, $attrExtra);

            return [
                'isPureText' => false,
                'format' => [
                    'attr_name' => $attrData['attr_name'],
                    'standard_attr_unit' => $attrExtra['standard_unit'] ?? '',
                    'attr_id' => (int)$attr['attr_id'],
                    'attr_value_origin' => $attr['origin_value'],
                    'attr_value_multi' => $valueMulti
                ],
                'attrExtra' => $attrExtra
            ];
        }

        // 处理范围值
        $valueRange = [[
            'attr_value_min' => (string)self::convertScientificNotation($attrExtra['value_min']),
            'attr_value_max' => (string)self::convertScientificNotation($attrExtra['value_max']),
        ]];

        if (!empty($attrExtra['attr_text'])) {
            foreach ($valueRange as $value) {
                $attrMixText[] = ($value['attr_value_min'] . $attrExtra['standard_unit']) . '~' . ($value['attr_value_max'] . $attrExtra['standard_unit']) . $attrExtra['attr_text'];
                $attrMixText[] = $attrExtra['attr_text'];
            }
        }

        return [
            'isPureText' => false,
            'format' => [
                'attr_name' => $attrData['attr_name'],
                'standard_attr_unit' => $attrExtra['standard_unit'] ?? '',
                'attr_id' => (int)$attr['attr_id'],
                'attr_value_origin' => $attr['origin_value'],
                'attr_value_range' => $valueRange
            ],
            'attrExtra' => $attrExtra
        ];
    }

    /**
     * 添加属性混合文本
     */
    private function addAttrMixText(&$attrMixText, $valueMulti, $item)
    {
        if (!empty($item['attr_text'])) {
            foreach ($valueMulti as $value) {
                $attrMixText[] = $value . $item['standard_unit'] . $item['attr_text'];
            }
            $attrMixText[] = $item['attr_text'];
        }
    }

    /**
     * 组装最终数据并保存到MongoDB
     */
    private function assembleAndSaveSpuAttr($spuId, $spu, $attrsExtend, $attrsFormat, $attrMix, $attrMixText)
    {
        array_unshift($attrMix, $spu['spu_name']);

        $attrsMixAll = implode(' ', array_unique(array_merge($attrMix, $attrMixText)));
        $attrsMixAll = str_replace('@@', '@', $attrsMixAll);

        $mongo = DB::connection('mongodb');
        $spuAttr = $mongo->table('spu_attrs2')->where('spu_id', (int)$spuId)->first();
        $spuAttr = (array)$spuAttr;

        $dataToSave = [
            'attrs_extend' => $attrsExtend,
            'attrs_format' => $attrsFormat,
            'attrs_mix' => $attrsMixAll
        ];


        if (!empty($spuAttr)) {
            unset($spuAttr['_id']);
            $spuAttr = array_merge($spuAttr, $dataToSave);
            $mongo->table('spu_attrs2')->where('spu_id', (int)$spuId)->update($spuAttr);
        } else {
            $spuAttr = array_merge(['spu_id' => (int)$spuId], $dataToSave);
            $mongo->table('spu_attrs2')->insert($spuAttr);
        }

        return $spuAttr;
    }

    public static function checkHasSpuAttr($spuId)
    {
        $mongo = DB::connection('mongodb');
        $spuAttr = $mongo->table('spu_attrs2')->where('spu_id', (int)$spuId)->first();
        if (!empty($spuAttr)) {
            return true;
        }
        return false;
    }

    public static function convertScientificNotation($value)
    {
        if (is_numeric($value) || strpos(strtolower((string)$value), 'e') !== false) {
            // 将科学计数法转换为纯小数
            $value = sprintf('%.10f', $value);
            // 去除尾部多余的0
            $value = rtrim(rtrim($value, '0'), '.');
            // 如果小数点后全是0，则去掉小数点
            if (strpos($value, '.') !== false && substr($value, -1) === '0') {
                $value = substr($value, 0, -1);
            }
        }
        return $value;
    }
}
