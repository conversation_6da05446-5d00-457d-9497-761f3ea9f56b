<?php

namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\BigData\ShopAttrMappingModel;
use App\Http\Models\BigData\ShopAttrModel;
use App\Http\Models\BigData\ShopClassMappingModel;
use App\Http\Models\BigData\ShopClassModel;
use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Transformers\ShopClassMappingTransformer;

class ShopClassMappingService
{
    public static function getShopClassMappingList($map)
    {
        $limit = request()->get('limit', 10);
        $model = new ShopClassMappingModel();
        $query = $model->with(['shop_class', 'lie_class'])->orderBy('id', 'desc');
        if (!empty($map['lie_class_name'])) {
            $lieClassIdList = PoolClassModel::where('class_name', 'like', '%' . $map['lie_class_name'] . '%')->pluck('class_id')->toArray();
            $query->whereIn('lie_class_id', $lieClassIdList);
        }
        if (!empty($map['shop_class_name'])) {
            $shopClassIdList = ShopClassModel::where('class_name', 'like', '%' . $map['shop_class_name'] . '%')->pluck('id')->toArray();
            $query->whereIn('shop_class_id', $shopClassIdList);
        }

        if (!empty($map['origin_shop_class_id'])) {
            //查出对应的shop_class_id
            $shopClassId = ShopClassModel::where('class_id', $map['origin_shop_class_id'])->pluck('id')->toArray();
            $query->whereIn('shop_class_id', $shopClassId);
        }

        if (!empty($map['shop_class_id'])) {
            $query->where('shop_class_id', $map['shop_class_id']);
        }

        if (!empty($map['lie_class_id'])) {
            $query->where('lie_class_id', $map['lie_class_id']);
        }


        if (!empty($map['platform'])) {
            $query->where('platform', $map['platform']);
        }

        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }


        if (!empty($map['is_export'])) {
            $list = $query->get()->toArray();
            $transformer = new ShopClassMappingTransformer();
            return $transformer->listTransformer($list);
        }

        $list = $query->paginate($limit)->toArray();
        $transformer = new ShopClassMappingTransformer();
        $list['data'] = $transformer->listTransformer($list['data']);

        //校验是否有参数需要处理的映射
        $ids = collect($list['data'])->pluck('id')->toArray();
        $invalidShopClassId = ShopAttrService::checkShopAttrChangeByClassMappingIds($ids);
        foreach ($list['data'] as &$item) {
            $item['has_need_deal_attr'] = 0;
            if (in_array($item['shop_class_id'], $invalidShopClassId)) {
                $item['has_need_deal_attr'] = 1;
            }
        }
        unset($item);

        return [
            'data' => $list['data'],
            'count' => $list['total'],
        ];
    }

    public static function saveShopClassMapping($data)
    {
        //判断是否已经存在
        if (empty($data['id'])) {
            $exists = ShopClassMappingModel::where('platform', $data['platform'])
                ->where('shop_class_id', $data['shop_class_id'])->where('lie_class_id', $data['class_id2'])->exists();
            if ($exists) {
                throw new InvalidRequestException('该平台对应的分类映射已经存在,不能重复绑定');
            }
        } else {
            //if (ShopClassMappingModel::where('shop_class_id', $data['shop_class_id'])->where('id', '!=', $data['id'])->exists()) {
            //    throw new InvalidRequestException('该第三方分类已经被绑定,不能重复绑定');
            //}
        }



        if (isset($data['is_warranty']) && $data['is_warranty'] == 0) {
            $data['warranty_month'] = 0;
        }

        if (empty($data['id'])) {
            return ShopClassMappingModel::insert([
                'shop_class_id' => $data['shop_class_id'],
                'lie_class_id' => $data['class_id2'],
                'image' => $data['image'],
                'create_time' => time(),
                'platform' => $data['platform'],
                'warranty_month' => $data['warranty_month'],
                'is_warranty' => $data['is_warranty'],
                'create_uid' => request()->user->userId,
                'create_name' => request()->user->name,
            ]);
        } else {
            return ShopClassMappingModel::where('id', $data['id'])->update([
                'shop_class_id' => $data['shop_class_id'],
                'lie_class_id' => $data['class_id2'],
                'image' => $data['image'],
                'warranty_month' => $data['warranty_month'],
                'is_warranty' => $data['is_warranty'],
                'title_jd_attr_id' => $data['title_jd_attr_id'],
                'update_time' => time(),
                'update_uid' => request()->user->userId,
                'update_name' => request()->user->name,
            ]);
        }
    }

    public static function deleteShopClassMapping($id)
    {
        //先去判断是否有参数映射
        $shopClassId = ShopClassMappingModel::where('id', $id)->value('shop_class_id');
        $platform = ShopClassMappingModel::where('id', $id)->value('platform');
        //找出对应的分类id
        $shopCategoryId = ShopClassModel::where('id', $shopClassId)->where('platform', $platform)->pluck('class_id')->toArray();
        $shopAttrIds = ShopAttrModel::where('class_id', $shopCategoryId)->where('platform',$platform)->pluck('id')->toArray();
        if (ShopAttrMappingModel::whereIn('shop_attr_id', $shopAttrIds)->exists()) {
            throw new InvalidRequestException('存在参数映射,不能删除');
        }
        return ShopClassMappingModel::where('id', $id)->delete();
    }


}
