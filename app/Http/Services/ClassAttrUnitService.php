<?php


namespace App\Http\Services;

use App\Http\Models\PoolClass\ClassAttrUnitModel;
use App\Http\Models\PoolClass\ClassAttrValueModel;
use App\Http\Models\PoolClass\PoolClassAttrModel;
use App\Http\Models\SupplierModel;
use Arr;
use Illuminate\Support\Facades\DB;

class ClassAttrUnitService
{
    //获取所有参数单位
    public function getUnitIdAndNames()
    {
        return ClassAttrUnitModel::pluck('attr_unit_name', 'attr_unit_id');
    }
}
