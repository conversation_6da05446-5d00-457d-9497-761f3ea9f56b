<?php


namespace App\Http\Services;

use Illuminate\Support\Arr;
use App\Http\Models\SupplierModel;
use Illuminate\Support\Facades\DB;
use App\Http\Models\OperationLogModel;

class OperationLogService
{
    public static function getOperationLogList($objName, $objId, $map)
    {
        $limit = Arr::get($map, 'limit', 10);
        $query = OperationLogModel::orderBy('id', 'desc');
        if (!empty($objName)) {
            $query->where('obj_name', $objName);
        }
        if (!empty($objId)) {
            $query->where('obj_id', $objId);
        }
        $result = $query->paginate($limit)->toArray();
        foreach ($result['data'] as $key => $value) {
            $result['data'][$key]['create_time'] = date('Y-m-d H:i:s', $value['create_time']);
        }
        return $result;
    }

    /**
     * 添加操作日志（兼容旧方法）
     * 
     * @param array $map 日志数据
     * @return bool
     */
    public static function addOperationLog($objName = null, $objId = null, $operationType = null, $content = null, $data = null)
    {
        // 兼容旧方法的数组参数调用
        if (is_array($objName)) {
            $map = $objName;
            $map['create_time'] = time();
            $map['create_name'] = request()->user->name ?? 'system';
            return OperationLogModel::insert($map);
        }
        
        // 新方法的多参数调用
        $map = [
            'obj_name' => $objName,
            'obj_id' => $objId,
            'operation_type' => $operationType,
            'content' => $content,
            'data' => $data,
            'create_time' => time(),
            'create_name' => request()->user->name ?? 'system',
            'create_uid' => request()->user->userId ?? 0
        ];
        
        return OperationLogModel::insert($map);
    }
}
