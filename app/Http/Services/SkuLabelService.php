<?php


namespace App\Http\Services;

use App\Http\Models\SupplierModel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

class SkuLabelService
{

    //初始化基础的数据
    // 0 无 1 国内现货，2国际现货， 3 猎芯期货 ,4 询价现货,5原厂直售,6猎芯精选
    public static function initBaseSkuLabel()
    {
        $data = [
            1 => [
                'id' => 1,
                'name' => '国内现货',
                'content' => '',
                'url' => ''
            ],
            2 => [
                'id' => 2,
                'name' => '国际现货',
                'content' => '',
                'url' => ''
            ],
            3 => [
                'id' => 3,
                'name' => '猎芯期货',
                'content' => '',
                'url' => ''
            ],
            4 => [
                'id' => 4,
                'name' => '询价现货',
                'content' => '',
                'url' => ''
            ],
            5 => [
                'id' => 5,
                'name' => '原厂直售',
                'content' => '',
                'url' => ''
            ],
            6 => [
                'id' => 6,
                'name' => '猎芯精选',
                'content' => '',
                'url' => ''
            ],
            7 => [
                'id' => 7,
                'name' => '猎芯自营',
                'content' => '',
                'url' => ''
            ],
        ];

        $redis = Redis::connection('sku');
        foreach ($data as $key => $item) {
            $redis->hset('goods_label_remark', $key, json_encode($item));
        }
    }

    //goods_label_remark
    public static function getSkuLabelList()
    {
        $redis = Redis::connection('sku');
        $exists = $redis->exists('goods_label_remark');
        if (empty($exists)) {

            //先去本地文本缓存

            self::initBaseSkuLabel();
        }
        $goodsLabelList = $redis->hgetall('goods_label_remark');
        $goodsLabelList = array_map(function ($item) {
            return json_decode($item, true);
        },$goodsLabelList);

        usort($goodsLabelList, function ($a, $b) {
            return $a['id'] - $b['id'];
        });

        return [
            'data' => $goodsLabelList,
            'count' => count($goodsLabelList),
        ];
    }

    //保存label信息
    public static function saveSkuLabel($labelId, $data)
    {
        $redis = Redis::connection('sku');
        $label = $redis->hget('goods_label_remark', $labelId);
        $label = json_decode($label, true);
        if (empty($label['create_time'])) {
            $label['create_time'] = date('Y-m-d H:i:s', time());
        }
        $label['update_time'] = date('Y-m-d H:i:s', time());
        $label['content'] = $data['content'];
        $label['url'] = $data['url'];
        $redis->hset('goods_label_remark', $labelId, json_encode($label));

    }
}
