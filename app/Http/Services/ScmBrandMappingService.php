<?php

namespace App\Http\Services;

//供应链品牌服务
use App\Exceptions\InvalidRequestException;
use App\Http\Models\BrandModel;
use App\Http\Models\ScmBrandMappingModel;
use App\Http\Models\ScmBrandModel;
use App\Http\Transformers\ScmBrandMappingTransformer;
use App\Http\Transformers\ScmBrandTransformer;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use MongoDB\Database;

class ScmBrandMappingService
{

    //找出没有映射的原始品牌
    public function getScmBrandMappingList($map)
    {
        $limit = request()->get('limit', 10);
        $model = new ScmBrandMappingModel();
        $query = $model->where('del_status', $model::DEL_STATUS_OK)->orderBy('add_time', 'desc');
        $query->with(['brand', 'scmBrand', 'user']);
        if (!empty($map['brand_name'])) {
            $query->whereHas('brand', function ($q) use ($map) {
                $q->where('brand_name', 'like', "${map['brand_name']}%");
            });
        }
        if (!empty($map['scm_brand_id'])) {
            $query->where('scm_brand_id', $map['scm_brand_id']);
        }
        if (!empty($map['add_time'])) {
            $startTime = strtotime(explode('~', $map['add_time'])[0]);
            $endTime = strtotime(explode('~', $map['add_time'])[1]);
            $query->whereBetween('add_time', [$startTime, $endTime]);
        }
        $list = $query->paginate($limit)->toArray();
        $transformer = new ScmBrandMappingTransformer();
        $list['data'] = $transformer->listTransformer($list['data']);
        return [
            'data' => $list['data'],
            'count' => $list['total'],
        ];
    }

    public function addScmBrandMapping($map)
    {
        //查找供应链标准品牌的状态
        $scmBrandModel = new ScmBrandModel();
        $scmBrand = $scmBrandModel->where('scm_brand_id', $map['scm_brand_id'])
            ->select('erp_status')->first();
        if (!$scmBrand) {
            throw new InvalidRequestException('没有这个供应链品牌');
        }
        if ($scmBrand->erp_status !== 1) {
            throw new InvalidRequestException('当前标准品牌状态异常');
        }

        //查找映射关系
        return DB::connection('spu')->transaction(function () use ($map) {
            $scmMappingModel = new ScmBrandMappingModel();
            $brandIds = $map['mapping_brand_ids'];
            unset($map['mapping_brand_ids']);
            $redis = Redis::connection('sku');
            $mappingResult = [];
            foreach ($brandIds as $k => $brandId) {
                $find = $scmMappingModel->where('scm_brand_id', '=', $brandId)->select('mapping_id')->first();
                $data['scm_brand_id'] = $map['scm_brand_id'];
                $data['brand_id'] = $brandId;
                $data['admin_id'] = request()->user->userId;
                $data['admin_name'] = request()->user->name;
                $data['update_time'] = time();
                $data['del_status'] = 1;
                if (!$find) {
                    //新增
                    $data['add_time'] = time();
                    $result = $scmMappingModel->insert($data);
                } else {
                    $result = $scmMappingModel->where('mapping_id', '=', $find->mapping_id)->update($data);
                }
                $redis->hset('pool_scm_brand_mapping', $brandId, $data['scm_brand_id']);
                if (!$result) {
                    throw new InvalidRequestException('加入映射失败');
                }
            }
            return $mappingResult;
        });
    }

    public function getScmBrandMappingBrand($map)
    {
        $map['del_status'] = ScmBrandMappingModel::DEL_STATUS_OK;
        $scmMappingModel = new ScmBrandMappingModel();
        $field = ['mapping_id', 'scm_brand_id', 'brand_id'];
        $mappingList = $scmMappingModel->from('lie_scm_brand_mapping as m')
            ->where($scmMappingModel->map($map))->with('brand')
            ->select($field)->get();
        if ($mappingList) {
            $mappingList = $mappingList->toArray();
        }
        return $mappingList;
    }

    public function findNotMappingBrand($map)
    {
        $brandModel = new BrandModel();
        $map['status'] = BrandModel::STATUS_OK;
        $limit = $map['limit'];
        unset($map['limit']);
        $list = $brandModel->where($brandModel->map($map))->leftjoin('lie_scm_brand_mapping', function ($join) {
            $join->on('lie_brand.brand_id', '=', 'lie_scm_brand_mapping.brand_id')->where('del_status', '=', 1);
        })->whereNull('mapping_id')->select(['brand_name', 'lie_brand.brand_id', 'brand_area'])
            ->paginate($limit)->toArray();
        $list = Arr::only($list, ['data', 'total']);
        $brandArea = config('field.BrandArea');
        $list['data'] = array_map(function ($v) use ($brandArea) {
            $v['brand_area_name'] = Arr::get($brandArea, $v['brand_area'], '未知');
            return $v;
        }, $list['data']);
        return $list;
    }

    public function deleteScmBrandMapping($mappingId)
    {
        $map = ['mapping_id' => $mappingId];
        $mappingModel = new ScmBrandMappingModel();
        $find = $mappingModel->where($map)->select('brand_id',
            'del_status')->first();
        if (!$find || $find->del_status !== 1) {
            throw new InvalidRequestException('不存在这个映射关系');
        }
        $result = $mappingModel->where($map)->update([
            'del_status' => 2,
            'update_time' => time()
        ]);
        if (!$result) {
            throw new InvalidRequestException('删除失败');
        }

        //删除掉缓存
        $redis = Redis::connection('sku');
        return $redis->hdel('pool_scm_brand_mapping', $find->brand_id);
    }
}
