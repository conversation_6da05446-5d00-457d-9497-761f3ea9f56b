<?php


namespace App\Http\Services;

use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Models\Cms\CmsUserIntraCodeModel;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class EncodedService
{
    //获取内部编码相关信息
    public function getEncodedUserByEncoded($encodedList = [],$types=1)
    {
        $encodedUsers = [];
        $codes = CmsUserIntraCodeModel::select('code_id', 'admin_id')
            ->whereIn('code_id', $encodedList)
            ->get()->toArray();
        $adminIds = array_column($codes, 'admin_id');
        $users = CmsUserInfoModel::whereIn('userId', $adminIds)->pluck('name', 'userId');
        foreach ($codes as $key => $code) {
            if ($types == 2){
                $encodedUsers[$code['code_id']] = ["name"=>Arr::get($users, $code['admin_id']),"userId"=>$code['admin_id']];
            }else{
                $encodedUsers[$code['code_id']] = Arr::get($users, $code['admin_id']);
            }
        }
        return $encodedUsers;
    }

    //获取所有人的内部编码
    public function getEncodedList()
    {
        $userModel = new CmsUserInfoModel();
        $admin = $userModel->pluck('name', 'userId');
        $intracodeModel = new CmsUserIntraCodeModel();
        $code = $intracodeModel->select('code_id', 'admin_id')
            ->get()->toArray();
        foreach ($code as $k => $v) {
            $v = (array)$v;

            if (!empty($v['admin_id'])) {
                if (isset($admin[$v['admin_id']])) {
                    $encodedList[$v['code_id']] = $admin[$v['admin_id']] . '(' . $v['code_id'] . ')';
                }
            }
        }
        return $encodedList;
    }
}
