<?php


namespace App\Http\Services;

use App\Http\Models\PoolClass\ClassAttrValueModel;
use App\Http\Models\SupplierModel;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class ClassAttrValueService
{
    public function getClassAttrValueList($collert, $limit)
    {
        $valueModel = new ClassAttrValueModel();
        $list = $valueModel->where($valueModel->map($collert))
            ->whereIn('class_attr_value.status', [1, 2])
            ->join('class_attr as a', 'a.attr_id', '=', 'class_attr_value.attr_id')
            ->join('class as c', 'c.class_id', '=', 'a.class_id')
            ->leftjoin('class_attr_unit_conversion as co', function ($join) {
                $join->on('co.attr_unit_id_1', '=', 'class_attr_value.attr_unit_id')
                    ->on('co.attr_unit_id_2', '=', 'a.attr_unit_id');
            })
            ->leftjoin('class_attr_unit_conversion as co2', function ($join) {
                $join->on('co2.attr_unit_id_2', '=', 'class_attr_value.attr_unit_id')
                    ->on('co2.attr_unit_id_1', '=', 'a.attr_unit_id');
            })
            ->select('class_name', 'c.class_id', 'a.attr_id', 'attr_name', 'a.attr_unit_id as de_attr_unit_id', 'value',
                'class_attr_value.attr_unit_id', 'co.ratio', 'co2.ratio as de_ratio', 'class_attr_value.status',
                'attr_value_id')
            ->with(['DeUnitName', 'UnitName'])
            ->paginate($limit)->toArray();
        return Arr::only($list, ['data', 'total']);
    }
}
