<?php

namespace App\Http\Services;

//供应链品牌服务
use App\Exceptions\InvalidRequestException;
use App\Http\Models\BrandModel;
use App\Http\Models\SearchForbidModel;
use App\Http\Transformers\SearchForbidTransformer;
use Illuminate\Support\Facades\Redis;

class SearchForbidService
{

    public function getSearchForbidList($map)
    {
        $model = new SearchForbidModel();
        $query = $model->orderBy('id', 'desc');


        if (!empty($map['supplier_id'])) {
            $query->where('supplier_id', $map['supplier_id']);
        }

        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }

        $limit = \Arr::get($map, 'limit', 15);
        $result = $query->paginate($limit);

        $result = $result ? $result->toArray() : [];
        $transformer = new SearchForbidTransformer();
        $result['data'] = $transformer->listTransformer($result['data']);
        return [
            'data' => \Arr::get($result, 'data', []),
            'count' => \Arr::get($result, 'total'),
        ];
    }

    public function saveSearchForbid($data)
    {
        if ($data['supplier_id'] == 0) {
            $data['sku_id'] = '';
            $data['sku_id_file_url'] = '';
            $data['sku_id_file_name'] = '';
        }else{
            $data['spu_id'] = '';
            $data['spu_id_file_url'] = '';
            $data['spu_id_file_name'] = '';
        }
        $eccnList = explode(',', $data['eccn']);
        if (!empty($eccnList)) {
            foreach ($eccnList as $eccn) {
                if (substr_count($eccn, '%') == 1) {
                    throw  new InvalidRequestException('eccn模糊匹配只支持双向模糊匹配,比如%A99%');
                }
            }
        }
        //先去判断是否有已经存在的供应商配置
        if (empty($data['id'])) {
            if (SearchForbidModel::where('supplier_id', $data['supplier_id'])->exists()) {
                throw new InvalidRequestException('已经存在对应供应商的屏蔽规则');
            }
            $data['create_time'] = time();
            $data['create_uid'] = request()->user->userId;
            $data['create_name'] = request()->user->name;
            $result = SearchForbidModel::insert($data);
        } else {
            if (SearchForbidModel::where('supplier_id', $data['supplier_id'])->where('id', '!=', $data['id'])->exists()) {
                throw new InvalidRequestException('已经存在对应供应商的屏蔽规则');
            }
            $data['update_time'] = time();
            $data['update_uid'] = request()->user->userId;
            $data['update_name'] = request()->user->name;
            $result = SearchForbidModel::where('id', $data['id'])->update($data);
        }

        if ($result) {
            //写入到redis
            $redis = Redis::connection('sku');
            $ruler = [
                'supplier_id' => (int)$data['supplier_id'],
                'standard_brand_id' => $data['standard_brand_id'],
                'eccn' => $data['eccn'],
                'goods_name' => $data['goods_name'],
                'sku_id' => $data['sku_id'],
                'spu_id' => $data['spu_id'],
            ];
            $ruler = json_encode($ruler);
            $redis->hset('forbid_search_supplier', $data['supplier_id'], $ruler);
            $redis->sadd('supplier_forbid_update_list', $data['supplier_id']);
        }

        return $result;
    }

    public function deleteSearchForbid($id)
    {
        $supplierId = SearchForbidModel::where('id', $id)->value('supplier_id');
        $result = SearchForbidModel::where('id', $id)->delete();
        if ($result) {
            $redis = Redis::connection('sku');
            $redis->hdel('forbid_search_supplier', $supplierId);
            $redis->srem('supplier_forbid_update_list', $supplierId);
        }
        return $result;
    }
}
