<?php

namespace App\Http\Services;

use App\Http\Models\PoolClass\ClassifyAttrModel;
use App\Http\Models\PoolClass\PoolClassAttrModel;
use App\Http\Models\PoolClass\PoolClassModel;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;

//第三方通用导出
class ThirdExportService
{
    private $nowDomain; //当前项目地址
    private $exportDomain; //导出网址
    private $exportConfig; //导出配置
    const  IS_DEBUG = 0; //是否调试
    const  EXPORT_KEY = "&&NKgfda8645&*(3";

    public function __construct()
    {
        $this->exportConfig = get_resource_config('domain');
        $this->nowDomain = str_replace(request()->path(), "", request()->url());
        $this->exportDomain = $this->exportConfig["export"]["export_domain"];
    }

    //推送导出
    public function push($param = [])
    {
        $response = Http::asForm()->post($this->exportDomain . "/recall", $param);
        if (data_get($_REQUEST, "debug")) {
            print_r($this->exportDomain . "/recall");
            print_r("<br/>");
            print_r($param);
            print_r("<br/>");
            print_r($response->body());
            print_r("<br/>");
            die();
        }
        $res = json_decode($response->body(), true);
        return $res;
    }

    //spu列表导出
    public function exportSpuList($input)
    {
        $time = time();
        $input["page_size"] = 100;
        $data = [
            "type" => 3,  # 类型 1:模板调用 2: hprose 回调导出  3：api 回调导出
            "source_items_id" => $this->exportConfig["export"]["footstone_spu_export"], #来源明细id，必填
            "file_name" => "SPU列表导出",
            "excel_suf" => "csv", #导出文件格式 csv,xls
            "header" => ["SPUID", "SPU分类", "SPU型号", "制造商", "标准品牌", "封装", "SPU状态", "更新时间"],
            "query_param" => $input, #查询参数
            "callbackurl" => $this->nowDomain . "open/exportSpuList", #api 回调地址
            "time" => $time, #时间
            "sign" => md5(self::EXPORT_KEY . $time), #签名
            "create_uid" => $input["login_uid"], #创建人id
        ];
        return self::push($data, 2);
    }

    //spu列表导出(去重导出)
    public function exportSpuUniqueList($input)
    {

        $time = time();
        $input["page_size"] = 100;
        $input["types"] = 3;
        $data = [
            "type" => 3,  # 类型 1:模板调用 2: hprose 回调导出  3：api 回调导出
            "source_items_id" => $this->exportConfig["export"]["footstone_spu_unique"], #来源明细id，必填
            "file_name" => "SPU列表导出(型号+标准品牌去重导出)",
            "excel_suf" => "csv", #导出文件格式 csv,xls
            "header" => ["SPUID", "SPU型号", "品牌", "标准品牌", "封装"],
            "query_param" => $input, #查询参数
            "callbackurl" => $this->nowDomain . "open/exportSpuUniqueList", #api 回调地址
            "time" => $time, #时间
            "sign" => md5(self::EXPORT_KEY . $time), #签名
            "create_uid" => $input["login_uid"], #创建人id
        ];
        return self::push($data, 2);
    }

    //spu属性导出
    public function exportSpuAttr($input)
    {

        $last_class_id = Arr::get($input, "class_id2/condition", 0);
        if (!$last_class_id) {
            return false;
        }
        $classInfo = (new PoolClassModel())->getClassInfo($last_class_id);
        if ($classInfo["parent_id"] == 0) {
            return false;
        }

        $headera = ["SPUID", "型号", "品牌", "封装"];
        $classAttr = PoolClassAttrModel::where(["class_id" => $last_class_id, "status" => 1])->get()->toArray();
        $headerb = array_column($classAttr, "attr_name");
        $header = array_merge($headera, $headerb);

        $time = time();
        $input["page_size"] = 100;
        $data = [
            "type" => 3,  # 类型 1:模板调用 2: hprose 回调导出  3：api 回调导出
            "source_items_id" => $this->exportConfig["export"]["footstone_spu_attr_export"], #来源明细id，必填
            "file_name" => "spu属性导出",
            "excel_suf" => "xls", #导出文件格式 csv,xls
            "header" => $header,
            "query_param" => $input, #查询参数
            "callbackurl" => $this->nowDomain . "open/exportSpuAttr", #api 回调地址
            "time" => $time, #时间
            "sign" => md5(self::EXPORT_KEY . $time), #签名
            "create_uid" => $input["login_uid"], #创建人id
        ];
        return self::push($data, 2);
    }

    //sku 列表导出
    public function exportSkuList($input)
    {
        $time = time();
        $input["page_size"] = 100;
        $header = [
            "SPUID",
            "SKUID",
            "SPU型号",
            "SKU型号",
            "品牌",
            "标准品牌",
            "SKU分类",
            "来源",
            "供应商编码",
            "SKU采购",
            "库存",
            "阶梯成本价",
            "批次",
            "封装",
            "标准包装量",
            "起订量",
            "递增量",
            "状态",
            "过期",
            "上架有效期",
            "大陆交期",
            "国外交期",
            "渠道标签",
            "标签",
            "特殊规则",
            "ECCN",
            "活动标签",
            "更新时间",
            "创建时间",
        ];
        if ($input['org_id'] == 3) {
            $header[] = '标准价';
            $header[] = '企业价';
        }
        $data = [
            "type" => 3,  # 类型 1:模板调用 2: hprose 回调导出  3：api 回调导出
            "source_items_id" => $this->exportConfig["export"]["footstone_sku_export"], #来源明细id，必填
            "file_name" => "SKU列表导出",
            "excel_suf" => "csv", #导出文件格式 csv,xls
            "header" => $header,
            "query_param" => $input, #查询参数
            "callbackurl" => $this->nowDomain . "open/exportSkuList", #api 回调地址
            "time" => $time, #时间
            "sign" => md5(self::EXPORT_KEY . $time), #签名
            "create_uid" => $input["login_uid"], #创建人id
        ];
        return self::push($data, 2);
    }

    //导出SKU推送日志
    public function exportSkuPushLogList($input)
    {
        $time = time();
        $input["page_size"] = 100;
        $data = [
            "type" => 3,  # 类型 1:模板调用 2: hprose 回调导出  3：api 回调导出
            "source_items_id" => isOnlineEnv() ? 79 : 74, //写死的配置 http://data.ichunt.net/database/1199#1
            "file_name" => "SKU推送日志导出",
            "excel_suf" => "csv", #导出文件格式 csv,xls
            "header" => [
                "序号",
                "外部平台",
                "店铺名称",
                "推送状态",
                "审核状态",
                "猎芯分类名称",
                "SKUID",
                "商品型号",
                "标准品牌",
                "推送时间",
                "异常原因",
            ],
            "query_param" => $input, #查询参数
            "callbackurl" => $this->nowDomain . "open/exportShopSkuPushLog", #api 回调地址
            "time" => $time, #时间
            "sign" => md5(self::EXPORT_KEY . $time), #签名
            "create_uid" => $input["login_uid"], #创建人id
        ];
        return self::push($data, 2);
    }

    //导出SPU推送日志
    public function exportSpuPushLogList($input)
    {
        $time = time();
        $input["page_size"] = 100;
        $data = [
            "type" => 3,  # 类型 1:模板调用 2: hprose 回调导出  3：api 回调导出
            "source_items_id" => isOnlineEnv() ? 81 : 77, //写死的配置 http://data.ichunt.net/database/1199#1
            "file_name" => "SPU推送日志导出",
            "excel_suf" => "csv", #导出文件格式 csv,xls
            "header" => [
                "序号",
                "外部平台",
                "推送任务号",
                "批次号",
                "推送状态",
                "第三方校验状态",
                "SPU_ID",
                "商品型号",
                "标准品牌",
                "推送时间",
                "异常原因",
            ],
            "query_param" => $input, #查询参数
            "callbackurl" => $this->nowDomain . "open/exportShopSpuPushLog", #api 回调地址
            "time" => $time, #时间
            "sign" => md5(self::EXPORT_KEY . $time), #签名
            "create_uid" => $input["login_uid"], #创建人id
        ];
        return self::push($data, 2);
    }

    //导出SKU推送日志
    public function exportShopSkuList($input)
    {
        $time = time();
        $input["page_size"] = 100;
        $data = [
            "type" => 3,  # 类型 1:模板调用 2: hprose 回调导出  3：api 回调导出
            "source_items_id" => isOnlineEnv() ? 83 : 79, //写死的配置 http://data.ichunt.net/database/1199#1
            "file_name" => "店铺SKU列表导出",
            "excel_suf" => "csv", #导出文件格式 csv,xls
            "header" => [
                'ID',
                '猎芯SKUID',
                '猎芯分类',
                '供应商编码',
                '第三方店铺SKUID',
                '型号',
                '品牌',
                '商品标题',
                '库存',
                '价格',
                '交期(转换后)',
            ],
            "query_param" => $input, #查询参数
            "callbackurl" => $this->nowDomain . "open/exportShopSkuList", #api 回调地址
            "time" => $time, #时间
            "sign" => md5(self::EXPORT_KEY . $time), #签名
            "create_uid" => $input["login_uid"], #创建人id
        ];
        return self::push($data, 2);
    }
}
