<?php

namespace App\Http\Services;

use Illuminate\Support\Arr;
use App\Http\Models\TaskLogModel;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Redis;
use App\Http\Models\BigData\ShopInfoModel;
use App\Exceptions\InvalidRequestException;
use App\Http\Models\BigData\ShopConfigModel;

class ShopInfoService
{
    public static function getShopInfoList($map)
    {
        $query = ShopInfoModel::orderBy('shop_id', 'desc');
        if (!empty($map['shop_id'])) {
            $query->where('shop_id', $map['shop_id']);
        }
        if (!empty($map['shop_type'])) {
            $query->where('shop_type', $map['shop_type']);
        }
        if (!empty($map['shop_name'])) {
            $query->where('shop_name', 'like', '%' . $map['shop_name'] . '%');
        }
        if (!empty($map['shop_name_en'])) {
            $query->where('shop_name_en', 'like', '%' . $map['shop_name_en'] . '%');
        }
        if (!empty($map['shop_url'])) {
            $query->where('shop_url', 'like', '%' . $map['shop_url'] . '%');
        }
        if ((!empty($map['status'])) || (isset($map['status']) && $map['status'] === '0')) {
            $query->where('status', $map['status']);
        }
        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }
        if (!empty($map['update_time'])) {
            $startTime = strtotime(explode('~', $map['update_time'])[0]);
            $endTime = strtotime(explode('~', $map['update_time'])[1]);
            $query->whereBetween('update_time', [$startTime, $endTime]);
        }
        $limit = Arr::get($map, 'limit', 10);
        $result = $query->paginate($limit);
        $result = $result ? $result->toArray() : [];
        if (!empty($result)) {
            foreach ($result['data'] as $key => &$value) {
                $value['shop_type_name'] = Arr::get(config('field.ShopType'), $value['shop_type']);
                $value['platform_name'] = Arr::get(config('field.ShopPlatform'), $value['platform']);
                $value['create_time'] = $value['create_time'] ? date('Y-m-d H:i:s', $value['create_time']) : '';
                $value['update_time'] = $value['update_time'] ? date('Y-m-d H:i:s', $value['update_time']) : '';
            }
            unset($value);
        }

        return [
            'data' => Arr::get($result, 'data', []),
            'count' => Arr::get($result, 'total'),
            'total' => Arr::get($result, 'total'),
        ];
    }

    public static function getShopConfigList($map)
    {
        $query = ShopConfigModel::with('shop')->orderBy('id', 'desc');
        if (!empty($map['shop_id'])) {
            $query->where('shop_id', $map['shop_id']);
        }
        if (!empty($map['app_key'])) {
            $query->where('app_key', 'like', '%' . $map['app_key'] . '%');
        }
        if (!empty($map['app_secret'])) {
            $query->where('app_secret', 'like', '%' . $map['app_secret'] . '%');
        }
        if (!empty($map['token_redis_name'])) {
            $query->where('token_redis_name', 'like', '%' . $map['token_redis_name'] . '%');
        }
        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }
        $limit = Arr::get($map, 'limit', 10);
        $result = $query->paginate($limit);
        $result = $result ? $result->toArray() : [];
        if (!empty($result)) {
            foreach ($result['data'] as $key => &$value) {
                $value['create_time'] = $value['create_time'] ? date('Y-m-d H:i:s', $value['create_time']) : '';
                $value['update_time'] = $value['update_time'] ? date('Y-m-d H:i:s', $value['update_time']) : '';
            }
            unset($value);
        }

        return [
            'data' => Arr::get($result, 'data', []),
            'count' => Arr::get($result, 'total'),
            'total' => Arr::get($result, 'total'),
        ];
    }
}
