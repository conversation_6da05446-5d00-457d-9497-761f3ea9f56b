<?php


namespace App\Http\Services;

use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Models\SupplierModel;
use App\Http\Models\SupplierRatioModel;
use Arr;
use Illuminate\Support\Facades\Redis;

class NoticeService
{
    private $type;
    private $url;
    private $node;
    private $pre;
    private $info;
    private $User;
    private $adminId;

    public function sendNotice($type = 0, $node = 0, $adminId = 0, $url = '')
    {
        $this->type = $type;
        $this->url = $url;
        $this->node = $node;
        $this->pre = 'Self_SendNotice';
        if (config('website.footstone_domain') == 'footstone.ichunt.net') {
            $robotArr = [
                'PoolSupplier' => 'https://oapi.dingtalk.com/robot/send?access_token=0df9a4833e69911b1158519b9ed70ea9472631efbcbb3df2ff5e60362b701377',
            ];
        } else {
            $robotArr = [
                'PoolSupplier' => 'https://oapi.dingtalk.com/robot/send?access_token=92917a6e090a8a39832c4843a579d6c6f9dfecc46fa275f8753ddee2b4399045',
            ];
        }
        $robotArr = [
            'PoolSupplier' => 'https://oapi.dingtalk.com/robot/send?access_token=0df9a4833e69911b1158519b9ed70ea9472631efbcbb3df2ff5e60362b701377',
        ];
        $this->adminId = $adminId;
        $this->info = [
            1 => [
                'title' => '供应商修改通知',
                'key' => 'save_supplier',
                'Url' => $robotArr['PoolSupplier']
            ],
            2 => [
                'title' => '系数调整通知',
                'key' => 'save_coefficient',
                'Url' => $robotArr['PoolSupplier']
            ],
            3 => [
                'title' => '附加费调整通知',
                'key' => 'save_extra',
                'Url' => $robotArr['PoolSupplier']
            ]
        ];

        $this->handle();
    }

    public function handle()
    {
        try {
            if ($this->adminId === 0) {
                $this->User = '系统';
            } else {
                $this->User = CmsUserInfoModel::getUserInfoName($this->adminId);
            }
            if ($this->type === 0) {
                return SendRobot($this->url, $this->node);
            } else {
                //为0时，直接发送通知
                $action = $this->info[$this->type]['key'];
                return $this->$action();
            }
        } catch (\Exception $e) {
            SendAlarm($e);
        }

    }

    //修改供应商通知
    private function save_supplier()
    {
        try {
            $action = $this->info[$this->type]['key'];
            $redis = Redis::connection('sku');
            $history = $redis->hget($this->pre, $action . '_' . $this->node);//取出历史数据
            $supplierModel = new SupplierModel();
            $info = $supplierModel->where('lie_supplier.supplier_id', '=', $this->node)
                ->join('lie_supplier_extra', 'lie_supplier.supplier_id', '=', 'lie_supplier_extra.supplier_id')
                ->select([
                    'type_id',
                    'supplier_name',
                    'lie_supplier.status',
                    'ad_text',
                    'supplier_logo',
                    'hk_delivery',
                    'cn_delivery',
                    'ad_url',
                    'sort',
                    'supplier_nickname'
                ])->first();//取出现有数据
            if (!$info) {
                return false;
            }
            $info = $info->toArray();
            $Arr = [
                'type_id' => '类别',
                'supplier_name' => '名字',
                'status' => '状态',
                'ad_text' => '宣传文案',
                'supplier_logo' => 'Logo',
                'hk_delivery' => '香港货期',
                'cn_delivery' => '大陆货期',
                'ad_url' => '运营链接',
                'sort' => '排序',
                'supplier_nickname' => '昵称'
            ];
            $TypeID = config('field.SupplierTypeID');
            $Status = config('field.SupplierStatus');
            $info['type_id'] = empty($TypeID[$info['type_id']]) ? $info['type_id'] : $TypeID[$info['type_id']];
            $info['status'] = empty($Status[$info['status']]) ? $info['status'] : $Status[$info['status']];
            $jsonInfo = json_encode($info);
            if ($history != $jsonInfo) {//不全等时才发通知
                $history = json_decode($history, true);
                $sendInfo = $this->info[$this->type]['title'] . "\n";
                foreach ($info as $k => $v) {
                    $historyInfo = empty($history[$k]) ? ' 未知' : $history[$k];
                    if ($historyInfo != $v) {
                        $sendInfo .= $Arr[$k] . ' : ' . $historyInfo . ' -> ' . $v . "\n";
                    }
                }
                $sendInfo .= '供应商 : ' . $info['supplier_name'] . "\n";
                $sendInfo .= '操作人： ' . $this->User;
                $redis->hset($this->pre, $action . '_' . $this->node, $jsonInfo);//更新历史记录
                SendRobot($this->info[$this->type]['Url'], $sendInfo);
            }
        } catch (\Exception $e) {
            SendAlarm($e);
        }
    }

    //调整附加费
    private function save_extra()
    {
        try {
            $action = $this->info[$this->type]['key'];
            $redis = Redis::connection('sku');
            $history = $redis->hget($this->pre, $action . '_' . $this->node);//取出历史数据
            $supplierModel = new SupplierModel();
            $info = $supplierModel->where('lie_supplier.supplier_id', '=', $this->node)->select([
                'supplier_name',
                'supp_extend_fee'
            ])->first();//取出现有数据
            if (!$info) {
                return false;
            }
            $info = $info->toArray();
            $jsonInfo = json_decode($info['supp_extend_fee'], true);
            if ($history != $info['supp_extend_fee']) {//不全等时才发通知
                $FeeInfo = json_decode($history, true);
                $sendInfo = $this->info[$this->type]['title'] . "\n";
                if ($FeeInfo) {
                    $sendInfo .= '原美金附加费：不满' . Arr::get($FeeInfo, 'hk.max', 0) . '收取' . Arr::get($FeeInfo, 'hk.price',
                            0) . "运费\n";
                    $sendInfo .= '原人民币附加费：不满' . Arr::get($FeeInfo, 'cn.max', 0) . '收取' . Arr::get($FeeInfo,
                            'cn.price', 0) . "运费\n";
                }
                $sendInfo .= '新美金附加费：不满' . Arr::get($jsonInfo, 'hk.max', 0) . '收取' . Arr::get($jsonInfo, 'hk.price',
                        0) . "运费\n";
                $sendInfo .= '新人民币附加费：不满' . Arr::get($jsonInfo, 'cn.max', 0) . '收取' . Arr::get($jsonInfo, 'cn.price',
                        0) . "运费\n";

                $sendInfo .= '供应商 : ' . $info['supplier_name'] . "\n";
                $sendInfo .= '操作人： ' . $this->User;
                $redis->hset($this->pre, $action . '_' . $this->node, $info['supp_extend_fee']);//更新历史记录
                SendRobot($this->info[$this->type]['Url'], $sendInfo);
            }
        } catch (\Exception $e) {
            SendAlarm($e);
        }
    }

    //调整系数
    private function save_coefficient()
    {
        try {
            $supplierModel = new SupplierRatioModel();
            $info = $supplierModel->where('id', '=', $this->node)
                ->join('lie_supplier', 'lie_supplier_ratio.supplier_id', '=', 'lie_supplier.supplier_id')
                ->select([
                    'supplier_name',
                    'cn',
                    'hk',
                    'extra_ratio',
                    'ratio',
                    'is_default',
                    'lie_supplier_ratio.status'
                ])->first();//取出现有数据
            if (!$info) {
                return false;
            }
            $info = $info->toArray();
            $is_default = $info['is_default'] === 1 ? '否' : '是';
            $status = $info['status'] === 1 ? '否' : '是';
            $sendInfo = $this->info[$this->type]['title'] . "\n";
            $sendInfo .= '供应商 : ' . $info['supplier_name'] . "\n";
            $sendInfo .= '系数ID : ' . $this->node . "\n";
            $sendInfo .= '人民币利润 : ' . $info['cn'] . "\n";
            $sendInfo .= '美金利润 : ' . $info['hk'] . "\n";
            $sendInfo .= '原始币种转美金汇率 : ' . $info['extra_ratio'] . "\n";
            $sendInfo .= '美金转人民币汇率 : ' . $info['ratio'] . "\n";
            $sendInfo .= '是否默认 : ' . $is_default . "\n";
            $sendInfo .= '是否禁用 : ' . $status . "\n";
            $sendInfo .= '操作人： ' . $this->User;
            SendRobot($this->info[$this->type]['Url'], $sendInfo);
        } catch (\Exception $e) {
            SendAlarm($e);
        }
    }
}
