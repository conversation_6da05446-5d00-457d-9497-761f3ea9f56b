<?php


namespace App\Http\Services;

use App\Http\Models\SupplierModel;
use Illuminate\Support\Facades\DB;

class SkuDetailService
{
    public function saveSkuDetail($skuId, $detail)
    {
        $skuDetail = $this->getSkuDetail($skuId);
        if ($skuDetail) {
            DB::connection('mongodb')
                ->collection('sku_detail')
                ->where('sku_id', (string)$skuId)
                ->update(['detail' => $detail]);
        } else {
            DB::connection('mongodb')
                ->collection('sku_detail')
                ->insert(['sku_id' => (string)$skuId, 'detail' => $detail]);
        }
    }

    //从mongo获取sku详情(富文本)
    public function getSkuDetail($skuId)
    {
        $result = DB::connection('mongodb')
            ->collection('sku_detail')
            ->where('sku_id', '=', (string)$skuId)
            ->first();
        return $result ? $result['detail'] : '';
    }
}
