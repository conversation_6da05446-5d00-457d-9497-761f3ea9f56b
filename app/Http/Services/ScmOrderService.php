<?php

namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use App\Http\IdSender\IdSender;
use App\Http\Models\PurchasePlanModel;
use App\Http\Models\ScmOrderModel;
use App\Http\Models\ScmOrderTemporaryModel;
use App\Http\Models\StockInItemModel;
use App\Http\Models\StockInModel;
use App\Http\Services\Sync\ScmOrderSyncService;
use Illuminate\Support\Facades\DB;

class ScmOrderService
{

    //获取暂存列表
    public static function getScmOrderList($params)
    {
        $scmOrderTemporaryList = ScmOrderModel::getScmOrderList($params);
        $scmOrderTemporaryList['data'] = ScmOrderTransformer::transformList($scmOrderTemporaryList['data']);
        return $scmOrderTemporaryList;
    }

    public static function getScmOrderStockInList($params)
    {
        $scmOrderTemporaryList = ScmOrderModel::getScmOrderStockInList($params);
        $scmOrderTemporaryList['data'] = ScmOrderTransformer::transformStockInList($scmOrderTemporaryList['data']);
        return $scmOrderTemporaryList;
    }


    //根据暂存单ID去存储暂存区数据
    public static function addScmOrder($temporaryIds)
    {
        //先去找出暂存区数据
        $scmOrderTemporaryList = ScmOrderTemporaryModel::getScmOrderTemporaryByTemporaryIds($temporaryIds);
        if (!$scmOrderTemporaryList) {
            throw new InvalidRequestException('选择的暂存数据不能为空');
        }
        //还要去判断数据的状态,如果存在同步失败或者同步成功的,不允许同步
        $statusList = array_column($scmOrderTemporaryList, 'status');
        foreach ($statusList as $status) {
            if ($status != ScmOrderTemporaryModel::STATUS_SYNC_NEED_SYNC) {
                throw new InvalidRequestException('选择的暂存数据必须是待同步状态');
            }
        }
        //构建数据放入委托暂存
        $scmOrderData = [];
        //这里还要根据入仓单号来拆单
        $temporaryByWarehouseReceiptSn = [];
        foreach ($scmOrderTemporaryList as $temporary) {
            $temporaryByWarehouseReceiptSn[$temporary['stock_in_item']['warehouse_receipt_sn']][] = $temporary;
        }
        //拆完单以后就可以进行插入
        foreach ($temporaryByWarehouseReceiptSn as $temporaryList) {
            //获取单号
            $scmOrderSn = IdSender::getSn(IdSender::TYPE_SCM_ORDER, PurchasePlanModel::PURCHASE_COMPANY_SM);
            foreach ($temporaryList as $temporary) {
                $exist = ScmOrderTemporaryModel::where('temporary_id', $temporary['temporary_id'])
                    ->where('status', ScmOrderTemporaryModel::STATUS_SYNC_COMPLETED)->exists();
                if ($exist) {
                    continue;
                }
                if (ScmOrderModel::where('stock_in_item_id', $temporary['stock_in_item_id'])->exists()) {
                    continue;
                }
                $scmOrder = [
                    'temporary_id' => $temporary['temporary_id'],
                    'scm_order_sn' => $scmOrderSn,
                    'goods_id' => $temporary['goods_id'],
                    'goods_sn' => $temporary['goods_sn'],
                    'goods_name' => $temporary['goods_name'],
                    'brand_id' => $temporary['brand_id'],
                    'brand_name' => $temporary['brand_name'],
                    'date_code' => $temporary['date_code'],
                    'purchase_uid' => $temporary['purchase_uid'],
                    'purchase_name' => $temporary['purchase_name'],
                    'purchase_prices' => $temporary['purchase_prices'],
                    'goods_number' => $temporary['out_qty'],
                    'goods_unit' => $temporary['goods_unit'],
                    'status' => ScmOrderModel::STATUS_NEED_AUDIT,
                    'supplier_id' => $temporary['supplier_id'],
                    'supplier_name' => $temporary['supplier_name'],
                    'purchase_id' => $temporary['purchase_id'],
                    'purchase_item_id' => $temporary['purchase_item_id'],
                    'purchase_sn' => $temporary['purchase_sn'],
                    'stock_in_id' => $temporary['stock_in_id'],
                    'scm_brand_name' => $temporary['scm_brand_name'],
                    'warehouse_receipt_sn' => $temporary['stock_in_item']['warehouse_receipt_sn'],
                    'stock_in_item_id' => $temporary['stock_in_item_id'],
                    'stock_in_sn' => $temporary['stock_in_sn'],
                    'create_name' => $temporary['create_name'],
                    'create_uid' => $temporary['create_uid'],
                    'create_time' => time(),
                ];
                $scmOrderData[] = $scmOrder;
            }
            //得到数据后,先添加到暂存表,然后还要修改暂存详情的暂存状态
            $result = \DB::transaction(function () use ($scmOrderData) {
                //先找出temporary_ids
                $temporaryIds = array_column($scmOrderData, 'temporary_id');
                if (ScmOrderModel::addScmOrder($scmOrderData)) {
                    //去修改是否加入委托的状态
                    ScmOrderTemporaryModel::whereIn('temporary_id', $temporaryIds)->update([
                        'update_time' => time(),
                        'is_added_scm_order' => ScmOrderTemporaryModel::IS_ADDED_SCM_ORDER,
                    ]);
                }
                return true;
            });
            if ($result) {
                ScmOrderSyncService::syncScmOrderToGYL($temporaryIds);
            }
            //清空数据
            $scmOrderData = [];
        }

        return true;
    }

    public static function deleteScmOrder($temporaryIds)
    {
        return ScmOrderModel::deleteScmOrder($temporaryIds);
    }

    //根据ids获取委托单列表
    public static function getScmOrderListByItemIds($scmOrderItemIds)
    {
        $scmOrderItemIds = explode(',', $scmOrderItemIds);
        $scmOrderList = ScmOrderModel::getScmOrderListByItemIds($scmOrderItemIds);
        return ScmOrderTransformer::transformCustomsData($scmOrderList);
    }

    //添加报关,要去同步金蝶生成入库单
    public static function addCustoms($scmOrderItemIds)
    {
        $scmOrderItemIds = explode(',', $scmOrderItemIds);
        //先检查是否入仓单号都是完整的
        $exist = ScmOrderModel::whereIn('scm_order_item_id', $scmOrderItemIds)->where('warehouse_receipt_sn', '')
            ->exists();
        if ($exist) {
            throw new InvalidRequestException('存在没有入仓单号的数据');
        }
        //直接请求报关,等到供应链返回结果的时候才写入报关入库
        ScmOrderSyncService::syncStockInToGYL($scmOrderItemIds);
        return true;
    }

    //这个是生成报关的入库数据
    public static function addScmOrderStockIn($scmOrderItemIds = [])
    {
        //构建入库数据
        $scmOrders = ScmOrderModel::with(['stock_in_item', 'stock_in', 'purchase_order'])
            ->whereIn('scm_order_item_id', $scmOrderItemIds)->get()->toArray();
        //从入库表修改一些不一样的数据即可,不需要自己手动从采购单那边构建数据了
        //为什么不从委托单去取数据呢,因为委托单的数据也就是从入库明细里面来的
        //要先拆成多个入库主单(根据入仓单号来拆分,原因是到了金蝶那边的入库的时候,其实也是按照单号来拆分进行入库的)

        /** 注意注意,因为只有b单,货主是深贸电子的采购入库明细才能加入到暂存区,所以到这边生成委托入库的时候(深圳入库)
         * 其实这个时候已经是币别和公司都发生了变化了,变成了人民币,以及货主都是猎芯科技了
         * 而且委托入库的供应商都是猎芯供应链,所以要固定成供应链的id和名称
         **/
        //供应链对应的供应商ID
        $gylSupplierId = config('field.GYLSupplierId');
        $supplier = SupplierService::getSupplierBySupplierId($gylSupplierId);
        $scmOrderItemIds = array_column($scmOrders, 'scm_order_item_id');
        $exist = StockInItemModel::whereIn('scm_order_item_id', $scmOrderItemIds)->exists();
        if ($exist) {
            return false;
        }
        //原始的采购主单
        $purchaseStockIn = $scmOrders[0]['stock_in'];
        $scmOrder = $scmOrders[0];
        $stockInType = StockInModel::STOCK_IN_TYPE_SCM_ORDER;

        $stockIn = [
            'stock_in_sn' => IdSender::getSn(IdSender::TYPE_STOCK_IN, StockInModel::PURCHASE_COMPANY_LX),
            'purchase_type' => $purchaseStockIn['purchase_type'],
            'stock_in_type' => $stockInType,
            //委托单回来的科技入库,公司一定是猎芯科技了
            'company_id' => StockInModel::PURCHASE_COMPANY_LX,
            'company_name' => \Arr::get(config('field.PurchaseCompany'), StockInModel::PURCHASE_COMPANY_LX, ''),
            'supplier_id' => $supplier['supplier_id'],
            'supplier_name' => $supplier['supplier_name'],
            'create_uid' => $scmOrder['create_uid'],
            'create_name' => $scmOrder['create_name'],
            'purchase_uid' => $purchaseStockIn['purchase_uid'],
            'purchase_name' => $purchaseStockIn['purchase_name'],
            'status' => StockInModel::STATUS_TODO,
            'create_time' => time(),
        ];
        $stockInId = DB::transaction(function () use ($stockIn, $scmOrders) {
            $stockInId = StockInModel::insertGetId($stockIn);
            if (!$stockInId) {
                return false;
            }
            //因为退货详情里面是有入库明细ID的,所以可以直接从入库明细里面拿到数据,然后直接修改一些相关的字段就行
            $stockInItems = [];
            foreach ($scmOrders as $scmOrder) {
                $stockInItem = $scmOrder['stock_in_item'];
                unset($stockInItem['stock_in_item_id']);
                unset($stockInItem['erp_stock_in_item_id']);
                $stockInItem['stock_in_id'] = $stockInId;
                $stockInItem['date_code'] = $scmOrder['date_code'];
                $stockInItem['wms_batch_sn'] = '';
                $stockInItem['scm_order_item_id'] = $scmOrder['scm_order_item_id'];
                $stockInItem['scm_order_sn'] = $scmOrder['scm_order_sn'];
                $stockInItem['in_qty'] = 0;
                $stockInItem['return_qty'] = 0;
                $stockInItem['item_status'] = StockInItemModel::ITEM_STATUS_NEED_STOCK_IN;
                $stockInItem['create_time'] = time();
                $stockInItem['stock_in_time'] = 0;
                $stockInItem['update_time'] = 0;
                $stockInItem['is_temporary'] = -1;
                $stockInItem['currency'] = StockInItemModel::CURRENCY_RMB;
                $stockInItems[] = $stockInItem;
            }
            StockInItemModel::insert($stockInItems);
            return $stockInId;
        });
        return $stockInId;
    }

    //统计委托入库里面的单价

    //获取委托单里面的供应商列表
    public static function getScmOrderSupplierList($params)
    {
        $suppliers = ScmOrderModel::getScmOrderSuppliers($params);
        $supplierList = [];
        foreach ($suppliers as $supplier) {
            $supplierList[] = [
                'id' => $supplier['supplier_id'],
                'name' => $supplier['supplier_name'],
            ];
        }
        return $supplierList;
    }
}
