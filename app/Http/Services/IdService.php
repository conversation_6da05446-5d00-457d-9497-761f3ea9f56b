<?php


namespace App\Http\Services;

use App\Http\Models\SupplierModel;
use Illuminate\Support\Facades\DB;

class IdService
{
    //skuId或者spuId
    function structure($type = 'spu')
    {
        if ($type == 'sku') {
            $id = '1';
            $db = rand(0, 9) . rand(0, 9);
        } else {
            $id = '2';
            $db = '0' . rand(0, 9);
        }
        $id .= time() . random(6, true) . $db;
        return $id;
    }
}
