<?php

namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\StandardBrandModel;
use App\Http\Transformers\ShopDistributeRuleTransformer;
use App\Http\Models\BigData\ShopBrandModel;
use App\Http\Models\BigData\ShopDistributeRuleModel;
use Illuminate\Support\Facades\Redis;

class ShopDistributeRuleService
{
    public static function saveShopDistributeRule($data)
    {

        $eccnList = explode(',', $data['eccn']);
        if (!empty($eccnList)) {
            foreach ($eccnList as $eccn) {
                if (substr_count($eccn, '%') == 1) {
                    throw new InvalidRequestException('eccn模糊匹配只支持双向模糊匹配,比如%A99%');
                }
            }
        }

        $data['canals'] = trim($data['canals'], ',');
        $data['canals'] = $data['supplier_id_list'] == 17 ? $data['canals'] : '';
        if ($data['is_copy'] == 1) {
            $data['id'] = 0;
        }

        if (empty($data['id'])) {
            //判断是否存在
            if (ShopDistributeRuleModel::where('shop_id', $data['shop_id'])->where('supplier_id_list', $data['supplier_id_list'])->exists()) {
                throw new InvalidRequestException('该商店对应的渠道分发规则已经存在,不能新增');
            }
            $id = ShopDistributeRuleModel::insertGetId([
                'supplier_id_list' => trim($data['supplier_id_list'],','),
                'brand_id_list' => trim($data['brand_id_list'],','),
                'platform' => $data['platform'],
                'shop_id' => $data['shop_id'],
                'status' => 1,
                'brand_type' => $data['brand_type'] ?? 1,
                'eccn' => $data['eccn'] ?? '',
                'eccn_file_url' => $data['eccn_file_url'] ?? '',
                'eccn_file_name' => $data['eccn_file_name'] ?? '',
                'create_time' => time(),
                'create_uid' => request()->user->userId,
                'create_name' => request()->user->name,
                'canals' => $data['canals'],
            ]);
        } else {
            //判断是否存在
            if (ShopDistributeRuleModel::where('shop_id', $data['shop_id'])->where('supplier_id_list', $data['supplier_id_list'])
                ->where('id', '!=', $data['id'])->exists()) {
                throw new InvalidRequestException('该商店对应的渠道分发规则已经存在,不能新增');
            }
            $id = $data['id'];
            ShopDistributeRuleModel::where('id', $data['id'])->update([
                'supplier_id_list' =>trim($data['supplier_id_list'],','),
                'brand_id_list' => trim($data['brand_id_list'],','),
                'shop_id' => $data['shop_id'],
                'brand_type' => $data['brand_type'] ?? 1,
                'eccn' => $data['eccn'] ?? '',
                'eccn_file_url' => $data['eccn_file_url'] ?? '',
                'eccn_file_name' => $data['eccn_file_name'] ?? '',
                'update_time' => time(),
                'update_uid' => request()->user->userId,
                'update_name' => request()->user->name,
                'canals' => $data['canals'],
            ]);
        }
        self::saveShopDistributeRuleToRedis($id);
        return $id;
    }

    public static function saveShopDistributeRuleToRedis($id)
    {
        $distributeRule = ShopDistributeRuleModel::find($id);
        $rule = [
            'platform' => $distributeRule->platform,
            'shop_id' => $distributeRule->shop_id,
            'supplier_id_list' => $distributeRule->supplier_id_list ? array_map('intval', explode(',', $distributeRule->supplier_id_list)) : [],
            'brand_id_list' => $distributeRule->brand_id_list ? array_map('intval', explode(',', $distributeRule->brand_id_list)) : [],
            'brand_type' => $distributeRule->brand_type ?? 1,
            'create_time' => $distributeRule->create_time,
            'update_time' => $distributeRule->update_time,
            'eccn_list' => $distributeRule->eccn ? explode(',', trim($distributeRule->eccn, ',')) : [],
            'canals' => $distributeRule->canals ? explode(',', trim($distributeRule->canals, ',')) : [],
        ];
        $redis = Redis::connection('sku');
        return $redis->hset('shop_distribution', $id, json_encode($rule));
    }

    public static function getShopDistributeRuleList($map)
    {
        $limit = request()->get('limit', 10);
        $model = new ShopDistributeRuleModel();
        $query = $model->with(['shop'])->orderBy('id', 'desc');

        if (!empty($map['status'])) {
            $query->where('status', $map['status']);
        }

        if (!empty($map['platform'])) {
            $query->where('platform', $map['platform']);
        }

        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }
        $list = $query->paginate($limit)->toArray();
        $transformer = new ShopDistributeRuleTransformer();
        $list['data'] = $transformer->listTransformer($list['data']);
        return [
            'data' => $list['data'],
            'count' => $list['total'],
        ];
    }

    public static function deleteShopDistributeRule($id)
    {
        $result = ShopDistributeRuleModel::where('id', $id)->delete();
        if ($result) {
            $redis = Redis::connection('sku');
            $redis->hdel('shop_distribution', $id);
        }
        return $result;
    }


    public static function updateDistributeRuleStatus($id, $status)
    {
        $result = ShopDistributeRuleModel::where('id', $id)->update([
            'status' => $status,
            'update_time' => time(),
            'update_name' => request()->user->name,
            'update_uid' => request()->user->userId,
        ]);
        if ($result && $status == -1) {
            $redis = Redis::connection('sku');
            $redis->hdel('shop_distribution', $id);
        }

        if ($result && $status == 1) {
            self::saveShopDistributeRuleToRedis($id);
        }

        return $result;
    }
}
