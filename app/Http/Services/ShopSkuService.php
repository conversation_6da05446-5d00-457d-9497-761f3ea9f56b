<?php

namespace App\Http\Services;

use Illuminate\Support\Arr;
use App\Jobs\UpdateShopSkuStock;
use App\Http\Models\TaskLogModel;
use App\Imports\ShopSkuRateImport;
use App\Imports\ShopSkuStockImport;
use App\Http\Models\ShopSkuLogModel;
use Illuminate\Support\Facades\Http;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Redis;
use App\Http\Models\StandardBrandModel;
use App\Http\Models\BigData\ShopSkuModel;
use App\Exceptions\InvalidRequestException;
use App\Http\Models\BigData\ShopSkuPushLogModel;
use App\Http\Models\Supplier\SupplierChannelModel;

class ShopSkuService
{
    public static function uploadShopSku($file)
    {
        if (empty($file)) {
            throw new InvalidRequestException('请上传文件');
        }
        if ($file->isValid()) {
            $clientName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            if (!in_array($extension, ['xlsx', 'xls', 'csv'])) {
                throw new InvalidRequestException('上传文件非法');
            }
            $newName = md5(date('ymdhis') . $clientName) . '_' . $clientName . "." . $extension;
            $uploadDir = storage_path() . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR;
            $path = $uploadDir . $newName;
            $file->move($uploadDir, $newName);
            $result = UpdateShopSkuStock::dispatch($path);
            if (!$result) {
                throw new InvalidRequestException('推送上传任务失败');
            }
            return $result;
        } else {
            throw new InvalidRequestException('上传文件非法');
        }
    }


    public static function getShopSkuList($map)
    {
        $query = ShopSkuModel::orderBy('id', 'desc');
        if (!empty($map['goods_name'])) {
            $query->where('goods_name', 'like', '%' . $map['goods_name'] . '%');
        }
        if (!empty($map['lx_sku_id'])) {
            $query->where('lx_sku_id', $map['lx_sku_id']);
        }
        if (!empty($map['shop_sku_id'])) {
            $query->where('shop_sku_id', $map['shop_sku_id']);
        }
        if (!empty($map['shop_sku_id'])) {
            $query->where('shop_sku_id', $map['shop_sku_id']);
        }
        if (!empty($map['shop_item_id'])) {
            $query->where('shop_item_id', $map['shop_item_id']);
        }
        if (!empty($map['brand_name'])) {
            $map['brand_name'] = trim($map['brand_name'], ',');
            $map['brand_name'] = \explode(',', $map['brand_name']);
            $query->whereIn('brand_name', $map['brand_name']);
        }
        if (!empty($map['goods_title'])) {
            $query->where('goods_title', $map['goods_title']);
        }
        if (!empty($map['shop_name'])) {
            $query->whereHas('shop', function ($q) use ($map) {
                $q->where('shop_name', 'like', '%' . $map['shop_name'] . '%');
            });
        }

        if (!empty($map['lx_class_id'])) {
            $map['lx_class_id'] = trim($map['lx_class_id'], ',');
            $map['lx_class_id'] = \explode(',', $map['lx_class_id']);
            $query->whereIn('lx_class_id', $map['lx_class_id']);
        }

        if (!empty($map['canal'])) {
            $map['canal'] = trim(strtoupper($map['canal']), ',');
            $query->where('canal', $map['canal']);
            // $map['canal'] = \explode(',', $map['canal']);
            // $query->whereIn('canal', $map['canal']);
        }

        if (!empty($map['supplier_id'])) {
            $map['supplier_id'] = trim($map['supplier_id'], ',');
            $map['supplier_id'] = \explode(',', $map['supplier_id']);
            $query->whereIn('supplier_id', $map['supplier_id']);
        }

        if (!empty($map['shop_id'])) {
            $query->where('shop_id', (int)$map['shop_id']);
        }

        if (!empty($map['init_status'])) {
            $query->where('init_status', $map['init_status']);
        }

        if (!empty($map['is_on_sale'])) {
            $query->where('is_on_sale', $map['is_on_sale']);
        }
        if ((!empty($map['status'])) || (isset($map['status']) && $map['status'] === '0')) {
            $query->where('status', $map['status']);
        }
        if (!empty($map['has_stock'])) {
            if ($map['has_stock'] == 1) {
                $query->where('stock', '>', 0);
            } else {
                $query->where('stock', 0);
            }
        }

        //为什么这里写死,因为要导出,不适合关联模型
        $shopAndPlatformMap = [
            1 => config('field.ShopPlatform')[1],
            2 => config('field.ShopPlatform')[1],
            3 => config('field.ShopPlatform')[3],
        ];

        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }
        if (!empty($map['update_time'])) {
            $startTime = strtotime(explode('~', $map['update_time'])[0]);
            $endTime = strtotime(explode('~', $map['update_time'])[1]);
            $query->whereBetween('update_time', [$startTime, $endTime]);
        }


        //判断如果是导出的数据,那么就要整理数据排好给导出使用
        $limit = Arr::get($map, 'limit', 10);
        $result = $query->with(['shop'])->paginate($limit);
        $result = $result ? $result->toArray() : [];
        $skuSnList = array_column($result['data'], 'sku_sn');
        $skuSnList = array_unique($skuSnList);
        $skuSnList = array_filter($skuSnList, function ($item) {
            return !empty($item);
        });
        $logList = [];
        if (!empty($skuSnList)) {
            $logList = ShopSkuPushLogModel::whereIn('sku_sn', $skuSnList)->pluck('init_status', 'sku_sn')->toArray();
        }
        if (!empty($result)) {
            foreach ($result['data'] as $key => &$value) {
                $value['platform_name'] = $shopAndPlatformMap[$value['shop_id'] ?: 1] ?? '';
                $value['audit_status_name'] = Arr::get(config('field.ShopSkuAuditStatus'), $value['status']);
                $value['create_time'] = $value['create_time'] ? date('Y-m-d H:i:s', $value['create_time']) : '';
                $value['update_time'] = $value['update_time'] ? date('Y-m-d H:i:s', $value['update_time']) : '';
                $value['lx_sku_id'] = (string)$value['lx_sku_id'];
                $sku = (new SkuService())->getSkuCacheInfo($value['lx_sku_id']);
                $value['shop_sku_id'] = (string)$value['shop_sku_id'];
                $value['shop_item_id'] = (string)$value['shop_item_id'];
                $value['init_status_name'] = Arr::get(config('field.ShopSkuInitStatus'), $value['push_log']['init_status'] ?? 0);
                $value['push_log_status'] = $logList[$value['sku_sn']] ?? '';
                if (empty($value['push_log_status'])) {
                    $value['init_status_name'] = '';
                }
                //如果是pop店铺,并且没有sku_sn.厂直状态为通过
                if (empty($value['sku_sn'])) {
                    $value['init_status_name'] = '成功';
                }
                $deliveryTime = self::getShopSkuDeliveryTime($sku);
                $value['cn_delivery_time'] = $deliveryTime['cn_delivery_time'] ?? '';
                $value['hk_delivery_time'] = $deliveryTime['hk_delivery_time'] ?? '';
                $value['cn_delivery_time_transfer'] = $deliveryTime['cn_delivery_time_transfer'] ?? '';
                $value['hk_delivery_time_transfer'] = $deliveryTime['hk_delivery_time_transfer'] ?? '';
            }
            unset($value);
        }

        if (Arr::get($map, 'is_export')) {
            $skuIds = array_column($result['data'], 'lx_sku_id');
            $skuList = [];
            $canalList = [];
            foreach ($skuIds as $k => $skuId) {
                $sku = (new SkuService())->getSkuCacheInfo($skuId);
                $skuList[$skuId] = $sku;
                if (!empty($sku['canal'])) {
                    $canalList[] = $sku['canal'];
                }
            }
            $canalList = array_unique($canalList);
            $canalMap = [];
            $supplierCodeMap = SupplierChannelModel::whereIn('supplier_code', $canalList)->pluck('supplier_name', 'supplier_code')->toArray();
            foreach ($canalList as $k => $canal) {
                $canalMap[$canal] = $supplierCodeMap[$canal];
            }
            foreach ($result['data'] as $key => $item) {
                if (!empty($item['lx_sku_id'])) {
                    //补充sku的信息
                    $sku = $skuList[$item['lx_sku_id']];
                    if (!empty($sku)) {
                        $item['lx_class_name'] = $sku['class_name2'];
                        $item['supplier_name'] = $canalMap[$sku['canal']] ?? '';
                        $item['canal'] = $sku['canal'];
                    }
                    $deliveryTime = self::getShopSkuDeliveryTime($sku);
                    $item['cn_delivery_time'] = $deliveryTime['cn_delivery_time'] ?? '';
                    $item['hk_delivery_time'] = $deliveryTime['hk_delivery_time'] ?? '';
                    $item['cn_delivery_time_transfer'] = $deliveryTime['cn_delivery_time_transfer'] ?? '';
                    $item['hk_delivery_time_transfer'] = $deliveryTime['hk_delivery_time_transfer'] ?? '';
                }

                $excelData[] = [
                    'ID' => $item['id'] . "\t",
                    '猎芯SKUID' => $item['lx_sku_id'] . "\t",
                    '猎芯分类' => $item['lx_class_name'] ?? '' . "\t",
                    '供应商编码' => $item['canal'] ?? '' . "\t",
                    '第三方店铺SKUID' => $item['shop_sku_id'] . "\t",
                    '型号' => $item['goods_name'] . "\t",
                    '品牌' => $item['brand_name'] . "\t",
                    '商品标题' => $item['goods_title'],
                    '库存' => $item['stock'] . "\t",
                    '价格' => $item['shop_price'] . "\t",
                    '交期(转换后)' => $item['cn_delivery_time_transfer'] . "\t",
                ];
            }
            return [
                'data' => $excelData,
                'count' => Arr::get($result, 'total'),
                'total' => Arr::get($result, 'total'),
            ];
        } else {
            foreach ($result['data'] as $key => $item) {
            }
            return [
                'data' => Arr::get($result, 'data', []),
                'count' => Arr::get($result, 'total'),
                'total' => Arr::get($result, 'total'),
            ];
        }
    }

    public static function getShopSkuDeliveryTime($sku)
    {
        //先判断专营还是非专营
        $supplierId = $sku['supplier_id'] ?? 0;
        $deliveryTime = [];
        if ($supplierId == 17) {
            //专营
            $deliveryTime['cn_delivery_time'] = $sku['cn_delivery_time'] ?? '';
            $deliveryTime['hk_delivery_time'] = $sku['hk_delivery_time'] ?? '';
        } else {
            $redis = Redis::connection('sku');
            //非专营,去获取渠道相关
            $supplierRatio = $redis->hget('SUPPLIER_REDIS_INFO_', $supplierId);
            if (empty($supplierRatio)) {
                return $deliveryTime;
            }
            $supplierRatio = json_decode($supplierRatio, true);
            $deliveryTime['cn_delivery_time'] = $supplierRatio['cn_delivery'] ?? '';
            $deliveryTime['hk_delivery_time'] = $supplierRatio['hk_delivery'] ?? '';
        }

        if (!empty($deliveryTime)) {
            // 处理CN交期
            if (!empty($deliveryTime['cn_delivery_time'])) {
                $cnDelivery = $deliveryTime['cn_delivery_time'];

                // 处理包含"周"的情况
                if (strpos($cnDelivery, '周') !== false) {
                    preg_match('/(\d+)-(\d+)周/', $cnDelivery, $matches);
                    if (!empty($matches)) {
                        $weeks = $matches[2];
                        $days = $weeks * 7 + 5;
                        $deliveryTime['cn_delivery_time_transfer'] = date('Y-m-d', strtotime("+{$days} days"));
                    }
                }
                // 处理包含"工作日"、"日"或"天"的情况
                elseif (preg_match('/(\d+)(?:-(\d+))?(?:个工作日|工作日|日|天)/', $cnDelivery, $matches)) {
                    // 如果有范围，取最大值，否则取单一值
                    $days = !empty($matches[2]) ? $matches[2] : $matches[1];
                    // 加5天缓冲
                    $days = $days + 5;
                    $deliveryTime['cn_delivery_time_transfer'] = date('Y-m-d', strtotime("+{$days} days"));
                }
            }

            // 处理HK交期
            if (!empty($deliveryTime['hk_delivery_time'])) {
                $hkDelivery = $deliveryTime['hk_delivery_time'];

                // 处理包含"周"的情况
                if (strpos($hkDelivery, '周') !== false) {
                    preg_match('/(\d+)-(\d+)周/', $hkDelivery, $matches);
                    if (!empty($matches)) {
                        $weeks = $matches[2];
                        $days = $weeks * 7 + 5;
                        $deliveryTime['hk_delivery_time_transfer'] = date('Y-m-d', strtotime("+{$days} days"));
                    }
                }
                // 处理包含"工作日"、"日"或"天"的情况
                elseif (preg_match('/(\d+)(?:-(\d+))?(?:个工作日|工作日|日|天)/', $hkDelivery, $matches)) {
                    // 如果有范围，取最大值，否则取单一值
                    $days = !empty($matches[2]) ? $matches[2] : $matches[1];
                    // 加5天缓冲
                    $days = $days + 5;
                    $deliveryTime['hk_delivery_time_transfer'] = date('Y-m-d', strtotime("+{$days} days"));
                }
            }
        }

        return $deliveryTime;
    }

    //修改库存
    public static function updateShopSkuStock($id, $stock)
    {
        $sku = ShopSkuModel::where('id', $id)->first()->toArray();

        //已审核的才能修改库存
        if ($sku['status'] != ShopSkuModel::STATUS_AUDITED && !empty($sku['lx_sku_id'])) {
            throw new InvalidRequestException('商品未审核,不能修改库存');
        }
        $result = Http::post(config('website.ShopApi') . '/update_stock', [
            'shop_id' => $sku['shop_id'],
            'shop_sku_id' => $sku['shop_sku_id'],
            'stock' => $stock,
        ])->json();
        if (isset($result['code'])) {
            if ($result['code'] != 0) {
                throw new InvalidRequestException($result['msg']);
            }
        }
        return ShopSkuModel::where('id', $id)->update([
            'stock' => $stock,
            'update_time' => time(),
        ]);
    }

    public static function fetchSkuInfo($shopId, $shopSkuId)
    {

        if (is_array($shopSkuId)) {
            foreach ($shopSkuId as $key => $value) {
                $value = trim($value);
                if (empty($value)) {
                    continue;
                }
                $result = Http::post(config('website.ShopApi') . '/getSelectSku', [
                    'shopId' => $shopId,
                    'wareId' => $value,
                ])->json();
                if (isset($result['code'])) {
                    if ($result['code'] != 0) {
                        throw new InvalidRequestException($result['msg']);
                    }
                }
            }
        }

        return true;
    }

    //打厂直标
    public static function changeCzFlag($data)
    {
        foreach ($data as $key => $shopSku) {
            if (empty($shopSku)) {
                continue;
            }
            $result = Http::post(config('website.ShopApi') . '/changeCzFlag', [
                'shopId' => $shopSku['shop_id'],
                'shopSkuId' => $shopSku['shop_sku_id'],
                'stock' => $shopSku['stock'],
            ])->json();
            if (empty($result)) {
                throw new InvalidRequestException('打厂直标失败,接口返回为空');
            }
            if (isset($result['code'])) {
                if ($result['code'] != 0) {
                    throw new InvalidRequestException($result['msg']);
                }
            }
        }
        return true;
    }

    //导入价格系数
    public static function batchUpdateSkuRate($file)
    {
        $result = Excel::toArray(new ShopSkuRateImport, $file);
        $data = $result[0];
        foreach ($data as $key => $value) {
            if (empty($value)) {
                continue;
            }
            $shopSkuId = trim($value[0]);
            if (empty($shopSkuId)) {
                continue;
            }
            $rate = (float)trim($value[1]);
            if (empty($rate)) {
                continue;
            }

            ShopSkuModel::where('shop_item_id', $shopSkuId)->update([
                'rate' => $rate,
                'update_time' => time(),
            ]);
        }
        return true;
    }
}
