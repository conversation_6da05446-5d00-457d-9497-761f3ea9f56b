<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class BaseRequest extends FormRequest
{

    protected function failedValidation(Validator $validator)
    {
        $error = $validator->errors()->all();
        \Log::channel("formRequest")->info(
            sprintf(
                "错误：请求参数 %s,错误原因:%s",
                print_r(request()->all(),true),
                json_encode($error,JSON_UNESCAPED_UNICODE)
            )
        );
        throw new HttpResponseException(response()->json(['code' => 1, 'msg' => $error[0]]));
    }


    public function authorize()
    {
        return true;
    }

    public function validated()
    {
        return $this->validator->validated();
    }



    public function __get($key)
    {
        if ($key == 'user') {
            return request()->get('user');
        }

        if ($key == 'perms') {
            return request()->get('perms');
        }
    }





}
