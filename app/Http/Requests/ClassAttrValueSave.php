<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ClassAttrValueSave extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'attr_id' => 'required',
            'value' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'value.required' => '属性值不能为空',
        ];
    }
}
