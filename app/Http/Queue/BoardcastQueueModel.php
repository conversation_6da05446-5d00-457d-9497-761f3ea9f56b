<?php

namespace App\Http\Queue;

use SoapClient;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;
use PhpAmqpLib\Message\AMQPMessage;
use Monolog\Formatter\LineFormatter;
use App\Exceptions\InvalidRequestException;
use PhpAmqpLib\Connection\AMQPStreamConnection;

class BoardcastQueueModel
{

    private $connection;

    const DEFAULT_QUEUE = 'lie_queue_united_data'; //本系统
    const DEFAULT_SUPPLIER = 'lie_queue_supplier'; //供应商
    const DEFAULT_PURCHASE = 'lie_queue_pur'; //采购
    const DEFAULT_ORDER = 'lie_queue_order'; //订单
    const DEFAULT_FRQ = 'lie_queue_frq'; //frq
    const DEFAULT_ERP = 'lie_queue_erp'; //erp

    const FORWARD_TYPE_HTTP = "http";

    const FORWARD_TYPE_SOAP = "soap";

    public function __construct(string $connect_name = '')
    {
        if ($connect_name) {
            $config = Config('rabbitmq.connections.' . $connect_name);
        } else {
            $default_connect_name = 'trading';
            $config = Config('rabbitmq.connections.' . $default_connect_name);
        }

        try {
            // 创建rabbitmq链接
            $this->connection = new AMQPStreamConnection(
                $config['host'],
                $config['port'],
                $config['login'],
                $config['password'],
                $config['vhost']
            );
        } catch (\Exception $e) {
            $this->connection = null;
            $err_json = json_encode([
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
                'code'    => $e->getCode()
            ]);
            (new Logger('queue'))->pushHandler(new StreamHandler(storage_path('logs/queue.log')))->info($err_json);
        }
    }

    // 队列推送
    public function insertQueue(
        $route_key,
        $queue_data,
        $queue_name = self::DEFAULT_QUEUE,
        $forward_type = self::FORWARD_TYPE_HTTP
    ) {
        switch ($forward_type) {
            case self::FORWARD_TYPE_SOAP :
                try {
                    ini_set('soap.wsdl_cache_enabled', '0');
                    ini_set('default_socket_timeout', 300);//设置socket超时时间
                    libxml_disable_entity_loader(false);
                    $erp = new SoapClient(Config('config.erp_domain') . '/ormrpc/services/WSIchuntjKFacade?wsdl');

                    $res = $erp->CallBackUpData(json_encode($queue_data));
                } catch (\Throwable $e) {
                    throw new InvalidRequestException('ERP接口异常，请联系技术查看日志，错误信息：' . $e->getMessage());
                }
                break;

            default:
                try {
                    $queue_message = [];
                    $queue_message["__route_key"] = $route_key;
                    $queue_message['__insert_time'] = time();
                    $queue_message['__from'] = $this->getFromUrl() ? $this->getFromUrl() : gethostname();
                    $queue_message['__uk'] = (string)self::_calc_uniqid($queue_message);
                    $queue_message['__type'] = $forward_type;
                    $queue_message['__search_key'] = isset($queue_data['__search_key']) ? $queue_data['__search_key'] : $route_key;
                    $queue_message['__trace_id'] = '';
                    $queue_message['data'] = $queue_data;

                    if ($this->connection) {
                        $message = new AMQPMessage(json_encode($queue_message, JSON_UNESCAPED_UNICODE));
                        $channel = $this->connection->channel();
                        $channel->queue_declare($queue_name, false, true, false, false);
                        $channel->basic_publish($message, '', $queue_name); // 推送消息
                        $channel->close();
                        $this->log("queue_insert", $queue_message, $queue_name);
                    } else {
                        throw new \Exception("rabbit connection is faild");
                    }
                } catch (\Exception $e) {
                    $this->log("queue_insert_error", $queue_message, $queue_name);
                    return false;
                }
                break;
        }

        return true;
    }

    public function getConnection()
    {
        return $this->connection;
    }

    private function getFromUrl()
    {
        $domain = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '';
        $request_url = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '';
        $from_url = '';
        if ($domain && $request_url) {
            $from_url = $domain . $request_url;
        }
        return $from_url;
    }

    private function _calc_uniqid($params)
    {
        return crc32(json_encode($params) . time());
    }

    private function log($sign, $queue_message, $queue_name = '')
    {
        $user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
        $log_message = $sign . "|##data:" . json_encode(['queue_name' => $queue_name, 'mq_data' => $queue_message]) . ",##" . $user_agent;
        $dateFormat = "Y-m-d H:i:s";
        // 最后创建一个格式化器
        $formatter = new LineFormatter(null, $dateFormat);
        $stream = new StreamHandler(storage_path('logs/queue.log'));
        $stream->setFormatter($formatter);
        (new Logger('queue'))->pushHandler($stream)->info($log_message);
    }

    public function __destruct()
    {
        if ($this->connection) {
            $this->connection->close();
        }
    }
}
