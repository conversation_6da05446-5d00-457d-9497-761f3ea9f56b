<?php

namespace App\Http\Validator;

use App\Exceptions\ValidatorException;
use App\Exceptions\InvalidRequestException;

class DistributePriceValidator
{
   public function check(array $params)
   {
    if (empty($params['supplier_id'])) {
        throw new InvalidRequestException('猎芯渠道不能为空');
    }
    if (empty($params['agreement_price_coefficient'])) {
        throw new InvalidRequestException('协议价系数不能为空');
    }
    if (empty($params['guide_price_coefficient'])) {
        throw new InvalidRequestException('指导价系数不能为空');
    }
    //价格最多支持两位小数
    if (!empty($params['agreement_price_coefficient']) && !is_numeric($params['agreement_price_coefficient'])) {
        throw new InvalidRequestException('协议价系数最多支持两位小数');
    }
    if (!empty($params['guide_price_coefficient']) && !is_numeric($params['guide_price_coefficient'])) {
        throw new InvalidRequestException('指导价系数最多支持两位小数');
    }
   }
}
