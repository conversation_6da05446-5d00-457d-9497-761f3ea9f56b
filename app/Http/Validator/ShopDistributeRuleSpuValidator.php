<?php

namespace App\Http\Validator;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\AgentBrandModel;
use App\Http\Models\StandardBrandModel;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class ShopDistributeRuleSpuValidator
{
    public function check($reqData)
    {
        $rules = [
            'platform' => [
                'required',
            ],
            //'brand_id_list' => 'required',
            //'class_id_list' => 'required',
        ];
        $validator = Validator::make($reqData, $rules, [], [
            'platform' => '外部平台',
            'brand_id_list' => '猎芯品牌',
            'class_id_list' => '猎芯末级分类',
        ]);
        if ($validator->fails()) {
            throw new InvalidRequestException($validator->errors()->first());
        }

        return true;
    }
}
