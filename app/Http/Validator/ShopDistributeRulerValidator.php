<?php

namespace App\Http\Validator;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\AgentBrandModel;
use App\Http\Models\StandardBrandModel;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class ShopDistributeRulerValidator
{
    public function check($reqData)
    {
        $rules = [
            'platform' => [
                'required',
            ],
            'shop_id' => 'required',
            //'brand_id_list' => 'required',
            'supplier_id_list' => 'required',
        ];
        $validator = Validator::make($reqData, $rules, [], [
            'supplier_id_list' => '猎芯渠道',
            'platform' => '外部平台',
            'shop_id' => '店铺名称',
            'brand_id_list' => '猎芯品牌',
        ]);
        if ($validator->fails()) {
            throw new InvalidRequestException($validator->errors()->first());
        }

        return true;
    }
}
