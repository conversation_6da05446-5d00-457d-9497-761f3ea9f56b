<?php

namespace App\Http\Validator;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\AgentBrandModel;
use App\Http\Models\StandardBrandModel;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class ShopBrandValidator
{
    public function check($reqData)
    {
        $rules = [
            'platform_list' => 'required',
            'brand_id' => 'required',
            'brand_name' => 'required',
        ];
        $validator = Validator::make($reqData, $rules, [], [
            'brand_id' => '品牌id',
            'platform' => '平台',
            'brand_name' => '品牌名称',
        ]);
        if ($validator->fails()) {
            throw new InvalidRequestException($validator->errors()->first());
        }

        return true;
    }
}
