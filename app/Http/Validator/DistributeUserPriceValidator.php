<?php

namespace App\Http\Validator;

use App\Exceptions\ValidatorException;
use App\Exceptions\InvalidRequestException;

class DistributeUserPriceValidator
{
   public function check(array $params)
   {
        if (empty($params['user_name'])) {
            throw new InvalidRequestException('客户名称不能为空');
        }


        if (empty($params['price_type'])) {
            throw new InvalidRequestException('对接价格不能为空');
        }

        if (empty($params['rules'])) {
            throw new InvalidRequestException('规则数据不能为空');
        }
   }
}
