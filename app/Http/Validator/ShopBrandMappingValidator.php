<?php

namespace App\Http\Validator;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\AgentBrandModel;
use App\Http\Models\StandardBrandModel;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class ShopBrandMappingValidator
{
    public function check($reqData)
    {
        $rules = [
            'lie_brand_id' => [
                'required',
            ],
            'platform' => 'required',
            'shop_brand_id' => 'required',
        ];
        $validator = Validator::make($reqData, $rules, [], [
            'lie_brand_id' => '猎芯标准品牌',
            'platform' => '映射平台',
            'shop_brand_id' => '映射平台品牌',
        ]);
        if ($validator->fails()) {
            throw new InvalidRequestException($validator->errors()->first());
        }

        return true;
    }
}
