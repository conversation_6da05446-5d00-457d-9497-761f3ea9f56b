<?php

namespace App\Http\Validator;

use App\Exceptions\ValidatorException;
use App\Http\Models\AbilityLevelRuleModel;
use App\Exceptions\InvalidRequestException;

class AbilityLevelRuleValidator
{
    public function check(array $params)
    {
        //判断是否已经存在
        if (!$params['id']) {
            $abilityLevelRule = AbilityLevelRuleModel::where('supplier_id', $params['supplier_id'])
                ->where('supplier_code', $params['supplier_code'])
                ->first();
            if ($abilityLevelRule) {
                throw new InvalidRequestException('该供应商对应的规则已经存在');
            }
        }

        if (empty($params['supplier_id'])) {
            throw new InvalidRequestException('渠道ID不能为空');
        }

        if (empty($params['supplier_code']) && $params['supplier_id'] == 17) {
            throw new InvalidRequestException('供应商编码不能为空');
        }

        if (isset($params['level'])) {
            if ($params['level'] === '0' || !empty($params['level'])) {
            } else {
                throw new InvalidRequestException('履约程度不能为空');
            }
        }

        // 验证level是否为有效值
        if (!in_array($params['level'], [0, 1, 2])) {
            throw new InvalidRequestException('履约程度值无效');
        }

        if ($params['supplier_id'] == 17 && empty($params['source'])) {
            throw new InvalidRequestException('配置专营的供应商,来源不能为空');
        }
    }
}
