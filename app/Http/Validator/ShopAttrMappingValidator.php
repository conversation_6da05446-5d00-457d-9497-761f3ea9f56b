<?php

namespace App\Http\Validator;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\AgentBrandModel;
use App\Http\Models\BigData\ShopAttrModel;
use App\Http\Models\StandardBrandModel;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class ShopAttrMappingValidator
{
    public function check($request)
    {
        $shopAttrIdList = $request->input('shop_attr_id_list');
        $attrValueList = $request->input('attr_value_list');
        $lieAttrIdList = $request->input('lie_attr_id_list');
        $isRequiredList = $request->input('is_required_list');
        $shopAttrNameList = $request->input('shop_attr_name_list');
        $inputTypeList = $request->input('input_type_list');
        $idList = $request->input('id_list');
        $originShopAttrIdList = $request->input('origin_shop_attr_id_list');
        $unitList = $request->input('unit_list');
        $isDraft = $request->input('is_draft');
        $mergedResult = [];
        for ($i = 0; $i < count($shopAttrIdList); $i++) {
            $originShopAttrId = $originShopAttrIdList[$i] ?? 0;
            if (empty($originShopAttrId)) {
                continue;
            }
            //找出状态
            $status = ShopAttrModel::where('id', $originShopAttrId)->value('status');
            if (!$status) {
                continue;
            }
            if ($isRequiredList[$i] == 1 && !$isDraft) {
                if (empty($lieAttrIdList[$i]) && empty($attrValueList[$i])) {
                    throw new InvalidRequestException("分类参数 : " . $shopAttrNameList[$i] . "必须补充完整");
                }
            }

            if (empty($lieAttrIdList[$i]) && !empty($attrValueList[$i]) && !$isDraft) {
                throw new InvalidRequestException("分类参数 : " . $shopAttrNameList[$i] . "必须补充完整");
            }

            //组装拼接需要存到数据库的value
            //判断各种输入方式是否符合标准
            switch ($inputTypeList[$i]) {
                case 1:
                case 2:
                    $value = $attrValueList[$i];
                    $value = $value ? explode(',', $value) : [];
                    $value = array_map(function ($value) {
                        return (int)$value;
                    }, $value);
                    break;
                case 3:
                    $value = $attrValueList[$i];
                    break;
                case 7:
                    if (!empty($attrValueList[$i]) && !is_numeric($attrValueList[$i])) {
                        throw new InvalidRequestException("分类参数 : " . $shopAttrNameList[$i] . "必须是纯数值");
                    }
                    $value = $attrValueList[$i];
                    break;
                case 10:
                    $unit = array_shift($unitList);
                    $value = [
                        'value' => $attrValueList[$i],
                        'unit' => $unit ?? '',
                    ];
                    break;
                default:
                    $value = $attrValueList[$i];
            }
            if (empty($value) && empty($lieAttrIdList[$i])) {
                continue;
            }

            if (is_array($value) && isset($value['value'])) {
                if (empty($value['value'])) {
                    continue;
                }
            }

            $mergedResult[] = [
                "shop_attr_id" => $shopAttrIdList[$i],
                "origin_shop_attr_id" => $originShopAttrIdList[$i],
                "id" => $idList[$i],
                "shop_attr_name" => $shopAttrNameList[$i],
                "attr_value" => $attrValueList[$i],
                "lie_attr_id" => $lieAttrIdList[$i],
                "is_required" => $isRequiredList[$i] ? 1 : 0,
                'unit' => $inputTypeList[$i] == 10 ? $unit ?? '' : '',
                "value" => $value,
                "input_type" => $inputTypeList[$i],
                "platform" => $request->input('platform'),
            ];
        }

        return $mergedResult;
    }
}
