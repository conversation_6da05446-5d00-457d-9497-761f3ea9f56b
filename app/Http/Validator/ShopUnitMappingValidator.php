<?php

namespace App\Http\Validator;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\AgentBrandModel;
use App\Http\Models\StandardBrandModel;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class ShopUnitMappingValidator
{
    public function check($reqData)
    {
        $rules = [
            'platform' => [
                'required',
            ],
            'ratio' => 'required|numeric',
            //'attr_name' => 'required',
            'unit_name' => 'required',
            'standard_unit_name' => 'required',
        ];
        $validator = Validator::make($reqData, $rules, [], [
            'platform' => '映射平台',
            'ratio' => '转换系数',
            'standard_unit_name' => '猎芯单位',
            'attr_name' => '映射平台单位名称',
            'unit_name' => '映射平台单位',
        ]);
        if ($validator->fails()) {
            throw new InvalidRequestException($validator->errors()->first());
        }

        return true;
    }
}
