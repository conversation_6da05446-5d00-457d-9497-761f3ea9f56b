<?php

namespace App\Http\Validator;

use App\Http\Models\AgentBrandModel;
use App\Http\Models\StandardBrandModel;
use App\Http\Models\Supplier\SupplierChannelModel;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class AgentBrandValidator
{
    public function check($reqData)
    {
        $standardBrandId = $reqData['standard_brand_id'];
        $rules = [
            'standard_brand_id' => [
                'required',
            ],
            'application_area' => 'required',
            'agent_certificate' => 'required',
            'pm_user_id' => 'required',
        ];
        $validator = Validator::make($reqData, $rules, [
            'standard_brand_id.unique' => '该标准品牌已经存在',
        ], [
            'standard_brand_id' => '标准品牌',
            'application_area' => '应用领域',
            'agent_certificate' => '代理证',
            'certificate_effective_time' => '代理证有效时间',
            'pm_user_id' => 'PM负责人',
        ]);
        if ($validator->fails()) {
            return $validator->errors()->first();
        }

        if (empty($reqData['is_certificate_permanent'])) {
            if (empty($reqData['certificate_effective_time'])) {
                return '代理证有效时间未设置长期有效时,有效时间必须设置';
            }
        }

        //判断这个标品是否已经存在
        if (empty($reqData['id'])) {
            if (AgentBrandModel::where('standard_brand_id', $standardBrandId)->exists()) {
                return '该标准品牌已经存在';
            }
        }

        $supplierExist = SupplierChannelModel::where('supplier_code', $reqData['supplier_code'])->exists();
        if (!$supplierExist) {
            return '供应商编码不存在';
        }

        return true;
    }
}
