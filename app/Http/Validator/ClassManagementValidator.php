<?php

namespace App\Http\Validator;

use App\Exceptions\InvalidRequestException;

class ClassManagementValidator
{
    /**
     * 验证数据
     *
     * @param array $params
     * @throws InvalidRequestException
     */
    public function check($params)
    {
        if (empty($params['goods_name'])) {
            throw new InvalidRequestException('型号不能为空');
        }

        if (empty($params['brand_name'])) {
            throw new InvalidRequestException('品牌不能为空');
        }

        if (empty($params['class_id'])) {
            throw new InvalidRequestException('分类不能为空');
        }

        if (empty($params['order_sn'])) {
            throw new InvalidRequestException('订单号不能为空');
        }

        if (empty($params['sales_name'])) {
            throw new InvalidRequestException('销售员不能为空');
        }

        if (empty($params['sales_id'])) {
            throw new InvalidRequestException('销售ID不能为空');
        }

        if (empty($params['spu_id'])) {
            throw new InvalidRequestException('SPUID不能为空');
        }

        if (empty($params['sku_id'])) {
            throw new InvalidRequestException('SKUID不能为空');
        }
    }
}
