<?php

namespace App\Http\Validator;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\AgentBrandModel;
use App\Http\Models\StandardBrandModel;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class ShopClassMappingValidator
{
    public function check($reqData)
    {
        $rules = [
            'class_id2' => [
                'required',
            ],
            'platform' => 'required',
            'image' => 'required',
            'shop_class_id' => 'required',
        ];
        $validator = Validator::make($reqData, $rules, [], [
            'class_id2' => '猎芯二级分类id',
            'platform' => '映射平台',
            'image' => '映射平台末级分类【默认图片】',
            'shop_class_id' => '映射平台末级分类',
        ]);
        if ($validator->fails()) {
            throw new InvalidRequestException($validator->errors()->first());
        }

        return true;
    }
}
