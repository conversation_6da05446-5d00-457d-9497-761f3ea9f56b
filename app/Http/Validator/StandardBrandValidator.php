<?php

namespace App\Http\Validator;

use App\Http\Models\StandardBrandModel;
use Illuminate\Support\Facades\Validator;

class StandardBrandValidator
{
    public function check()
    {
        $reqData = request()->all();
        $rules = [
            'brand_name' => 'required',
            'first_letter' => 'required',
//            'seo_brand_name' => 'required',
//            'seo_title' => 'required',
//            'seo_keywords' => 'required',
//            'seo_description' => 'required',
        ];
        $message = [
            'brand_name.required' => '品牌名字不能为空',
            'first_letter.required' => '品牌首字母不能为空',
//            'seo_brand_name.required' => 'SEO品牌名字不能为空',
//            'seo_title.required' => 'SEO title 不能为空',
//            'seo_keywords.required' => 'SEO keywords 不能为空',
//            'seo_description.required' => 'SEO description 不能为空',
        ];
        $validator = Validator::make($reqData, $rules, $message);
        if ($validator->fails()) {
            return $validator->errors()->first();
        }

        $reqData['brand_name'] = trim($reqData['brand_name']);

        $standBrandId = empty($reqData['standard_brand_id']) ? 0 : $reqData['standard_brand_id'];
        if (!$standBrandId) {
            //新增的时候还要判断这个品牌名称是否已经存在标准库
            $count = StandardBrandModel::where('brand_name', $reqData['brand_name'])->count();
            if ($count) {
                return "该品牌已经存在于标准库,请确认后再提交";
            }
        }

        //校验品牌名称是否和别的品牌的展示名称、英文全称、英文简称、中文全称、中文简称重复
        $count = StandardBrandModel::when($standBrandId, function ($query) use ($reqData, $standBrandId) {
                $query->where('standard_brand_id', '!=', $standBrandId);
            })->where('show_name', $reqData['brand_name'])->count();
        if ($count) {
            return "该品牌名称和已存在的标品的 展示名称 重复,请确认后再提交";
        }
        $count = StandardBrandModel::when($standBrandId, function ($query) use ($reqData, $standBrandId) {
            $query->where('standard_brand_id', '!=', $standBrandId);
        })->where('brand_name_en', $reqData['brand_name'])->count();
        if ($count) {
            return "该品牌名称和已存在的标品的 英文全称 重复,请确认后再提交";
        }

        $count = StandardBrandModel::when($standBrandId, function ($query) use ($reqData, $standBrandId) {
            $query->where('standard_brand_id', '!=', $standBrandId);
        })->where('brand_short_name_en', $reqData['brand_name'])->count();
        if ($count) {
            return "该品牌名称和已存在的标品的 英文简称 重复,请确认后再提交";
        }

        $count = StandardBrandModel::when($standBrandId, function ($query) use ($reqData, $standBrandId) {
            $query->where('standard_brand_id', '!=', $standBrandId);
        })->where('brand_name_cn', $reqData['brand_name'])->count();
        if ($count) {
            return "该品牌名称和已存在的标品的 中文全称 重复,请确认后再提交";
        }

        $count = StandardBrandModel::when($standBrandId, function ($query) use ($reqData, $standBrandId) {
            $query->where('standard_brand_id', '!=', $standBrandId);
        })->where('brand_short_name_cn', $reqData['brand_name'])->count();
        if ($count) {
            return "该品牌名称和已存在的标品的 中文简称 重复,请确认后再提交";
        }

        return true;
    }
}
