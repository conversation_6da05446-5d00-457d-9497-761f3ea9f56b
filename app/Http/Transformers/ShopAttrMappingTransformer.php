<?php


namespace App\Http\Transformers;


use App\Http\Models\BigData\ShopAttrModel;
use App\Http\Models\BigData\ShopClassMappingModel;
use App\Http\Models\BigData\ShopClassModel;
use App\Http\Models\PoolClass\PoolClassAttrModel;
use App\Http\Models\PoolClass\PoolClassModel;
use Illuminate\Support\Facades\Redis;

class ShopAttrMappingTransformer
{
    public function listTransformer($attrList)
    {
        if (!empty($attrList)) {
            $redis = Redis::connection('sku');
            foreach ($attrList as $key => &$attr) {
                $attr['create_time'] = date('Y-m-d H:i:s', $attr['create_time']);
                $attr['platform_name'] = config('field.ShopPlatform')[$attr['platform']] ?? '无';
                $lieClassId = PoolClassAttrModel::where('attr_id', $attr['lie_attr_id'])->value('class_id');
                $lieClassName = PoolClassModel::where('class_id', $lieClassId)->value('class_name');
                $attr['lie_class_name'] = $lieClassName;
                $attr['lie_class_id'] = $lieClassId;
                if (empty($lieClassName)) {
                    //找到参数id对应的原始参数id
                    $classId = ShopAttrModel::where('id', $attr['shop_attr_id'])->value('class_id');
                    $shopClassId = ShopClassModel::where('class_id', $classId)->where('platform', $attr['platform'])->value('id');
                    $lieClassId = ShopClassMappingModel::where('shop_class_id', $shopClassId)->where('platform', $attr['platform'])->value('lie_class_id');
                    $lieClass = $redis->hget('pool_class_info', $lieClassId);
                    $lieClass = json_decode($lieClass, true);
                    //$lieClassName = PoolClassModel::where('class_id', $lieClassId)->value('class_name');
                    $attr['lie_class_name'] = $lieClass['class_name'] ?? '';
                    $attr['lie_class_id'] = $lieClass['class_id'] ?? '';

                }

                $originShopClassId = ShopAttrModel::where('id', $attr['shop_attr_id'])->value('class_id');
                $shopClassId = ShopClassModel::where('class_id', $originShopClassId)->where('platform', $attr['platform'])->value('id');
                $shopClassName = ShopClassModel::where('class_id', $originShopClassId)->value('class_name');
                $attr['shop_class_name'] = $shopClassName;
                $attr['origin_shop_class_id'] = $originShopClassId;
                $attr['shop_class_id'] = $shopClassId;
                $attr['input_type_name'] = config('field.ShopAttrInputType')[$attr['input_type']] ?? '';
            }
            unset($attr);
        }
        return $attrList;
    }
}
