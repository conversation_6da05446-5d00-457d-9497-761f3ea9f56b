<?php


namespace App\Http\Transformers;


use Illuminate\Support\Facades\Redis;

class StandardEncapTransformer
{
    public function listTransformer($encapList)
    {
        foreach ($encapList as $key => &$encap) {
            $encap['create_time'] = date('Y-m-d H:i:s', $encap['create_time']);
            $encap['status_name'] = \Arr::get(config('field.StandardEncapStatus'), $encap['status'], '未知状态');
        }
        unset($encap);
        return $encapList;
    }

}
