<?php


namespace App\Http\Transformers;


class ScmBrandMappingTransformer
{
    public function listTransformer($brandList)
    {
        if (!empty($brandList)) {
            foreach ($brandList as $key => &$brand) {
                $brand['add_time'] = date('Y-m-d H:i:s', $brand['add_time']);
                $brand['admin_name'] = $brand['admin_id'];
            }
            unset($brand);
        }
        return $brandList;
    }
}
