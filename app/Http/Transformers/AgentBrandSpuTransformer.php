<?php


namespace App\Http\Transformers;


use App\Http\Models\AgentBrandModel;
use App\Http\Models\BrandStandardMappingModel;
use App\Http\Services\AgentBrandService;
use App\Http\Services\SpuService;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;

class AgentBrandSpuTransformer
{
    public function listTransformer($spuList)
    {
        if (!empty($spuList)) {
            $standardBrandId = AgentBrandModel::where('id', $spuList[0]['agent_brand_id'])->value('standard_brand_id');
            foreach ($spuList as $key => &$spu) {
                $spu['create_time'] = $spu['create_time'] ? date('Y-m-d H:i:s', $spu['create_time']) : '';
                $spu['update_time'] = $spu['update_time'] ? date('Y-m-d H:i:s', $spu['update_time']) : '';
                //找到一个相关的spu
                if (empty($spu['spu_id'])) {
                    $spuCache = (new SpuService())->getSpuBySpuNameAndStandardBrandId($spu['spu_name'], $standardBrandId);
                }else{
                    $spuCache = (new SpuService())->getSpuInfo($spu['spu_id']);
                }
                if ($spuCache) {
                    $spu = array_merge($spuCache, $spu);
                }
            }
        }


        unset($spu);
        return $spuList;
    }
}
