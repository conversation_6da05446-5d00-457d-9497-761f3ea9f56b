<?php


namespace App\Http\Transformers;


use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Models\StandardEncapMappingModel;
use App\Http\Models\StandardEncapModel;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;

class EncapTransformer
{
    public function listTransformer($encapList)
    {
        $redis = Redis::connection('sku');
        foreach ($encapList as $key => &$encap) {
            $encap['create_time'] = date('Y-m-d H:i:s', $encap['create_time']);
            $encap['update_time'] = !empty($encap['update_time']) ? date('Y-m-d H:i:s', $encap['update_time']) : '';
            //是否有映射
            $encap['has_mapping'] = $encap['standard_encap_mapping'] ? '有' : '无';
            $encap['standard_encap_id'] = $encap['standard_encap_mapping'] ? $encap['standard_encap_mapping']['standard_encap_id'] : 0;
            $encap['standard_encap_name'] = '';
            if ($encap['encap_id']) {
                $standardEncapId = StandardEncapMappingModel::where('encap_id', $encap['encap_id'])->value('standard_encap_id');
                $standardEncap = StandardEncapModel::where('standard_encap_id', $standardEncapId)->first();
                if ($standardEncap) {
                    $encap['standard_encap_name'] = $standardEncap->encap_name;
                }
            }
        }

        unset($encap);
        return $encapList;
    }
}
