<?php


namespace App\Http\Transformers;


class ShopBrandMappingTransformer
{
    public function listTransformer($brandList)
    {
        if (!empty($brandList)) {
            foreach ($brandList as $key => &$brand) {
                $brand['create_time'] = date('Y-m-d H:i:s', $brand['create_time']);
                $brand['platform_name'] = config('field.ShopPlatform')[$brand['platform']] ?? '无';
            }
            unset($brand);
        }
        return $brandList;
    }
}
