<?php

namespace App\Http\Transformers;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

class StandardBrandSpuTransformer
{
    public function listTransformer($spuList)
    {
        $redis = Redis::connection('sku');
        $spuRedis = Redis::connection('spu');
        if (!empty($spuList)) {
            foreach ($spuList as $key => &$spu) {
                $spuCache = json_decode($spuRedis->hget('spu', $spu['spu_id']), true);
                $brand = $redis->hget('brand', $spuCache['brand_id']);
                $spu['create_time'] = date('Y-m-d H:i:s', $spu['create_time']);
                $spu['update_time'] = $spu['update_time'] ? date('Y-m-d H:i:s', $spu['update_time']) : '';
                $spu['spu_name'] = $spuCache['spu_name'];
                $spu['brand_name'] = $brand;
                $spu['spu_type'] = \Arr::get(config('field.StandardBrandSpuType'), $spu['type']);
                //获取渠道数量(sku数量)
                $spu['sku_num'] = $this->getSkuNum($spu['spu_id']);
                $spu['spu_id'] = (string)$spu['spu_id'];
            }
            unset($spu);
        }
        return $spuList;
    }

    private function getSkuNum($spuId)
    {
        $mongodb = DB::connection('mongodb')->collection('sku');
        return $mongodb->where(['spu_id' => (int)$spuId])->count();
    }
}
