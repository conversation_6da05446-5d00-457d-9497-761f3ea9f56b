<?php


namespace App\Http\Transformers;


use Illuminate\Support\Arr;
use App\Http\Services\ClassService;
use Illuminate\Support\Facades\Redis;
use App\Http\Models\BrandStandardMappingModel;

class AgentBrandTransformer
{
    public function listTransformer($brandList)
    {
        foreach ($brandList as $key => &$brand) {
            $brand['create_time'] = $brand['create_time'] ? date('Y-m-d H:i:s', $brand['create_time']) : '';
            $brand['update_time'] = $brand['update_time'] ? date('Y-m-d H:i:s', $brand['update_time']) : '';
            $brand['status_name'] = $brand['status'] == 1 ? '启用' : '禁用';
            $brand['application_area_name'] = implode(',', array_map(function ($area) {
                return Arr::get(config('field.SpuApplicationArea'), $area, '');
            }, explode(',', $brand['application_area'])));
            $brand['brand_level_name'] = $brand['agent_brand_level'] ? config('field.AgentBrandLevel')[$brand['agent_brand_level']] : '无';
            $brand['brand_area_name'] = $brand['standard_brand']['brand_area'] ? config('field.StandardBrandArea')[$brand['standard_brand']['brand_area']] : '';
            $brand['certificate_effective_time'] = $brand['certificate_effective_time'] == 0 ? '长期' : date('Y-m-d', $brand['certificate_effective_time']);
            if (!empty($brand['agent_class_id'])) {
                $nameList = [];
                $agentClassIds = \explode(',',$brand['agent_class_id']);
                foreach ($agentClassIds as $key => $classId) {
                    $nameList[] = config('field.AgentBrandClass')[$classId] ?? '';
                }
                $brand['agent_class_name'] = \implode(',',$nameList);
            }

            $classList = $brand['main_product_class_ids'] ? ClassService::getClassListByClassIdsFromCache(explode(',', $brand['main_product_class_ids'])) : '';
            $brand['main_product_class_name'] = $classList ? implode(',', array_column($classList, 'class_name')) : '';
        }

        unset($brand);
        return $brandList;
    }
}
