<?php


namespace App\Http\Transformers;


use App\Http\Services\ClassService;
use Illuminate\Support\Facades\Redis;

class ShopClassMappingTransformer
{
    public function listTransformer($classList)
    {
        $redis = Redis::connection('sku');
        if (!empty($classList)) {
            foreach ($classList as $key => &$class) {
                $class['parent_class_name'] = ClassService::getParentClassNameByClassId($class['lie_class_id']);
                $class['create_time'] = date('Y-m-d H:i:s', $class['create_time']);
                $class['platform_name'] = config('field.ShopPlatform')[$class['platform']] ?? '无';
                $class['has_draft'] = (bool)$redis->hget('lie_shop_attr_mapping_draft', $class['shop_class_id'] . '_' . $class['platform']);
                $classType = $class['lie_class']['class_type'] ?? 0;
                $class['class_type_name'] = config('field.ClassType')[$classType] ?? '未知';
            }
            unset($class);
        }
        return $classList;
    }
}
