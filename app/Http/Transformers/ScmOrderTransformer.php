<?php

namespace App\Http\Transformers;

use App\Http\Models\StockInItemModel;
use App\Http\Models\StockInModel;
use App\Http\Utils\NameConvert;
use Illuminate\Support\Arr;

class ScmOrderTransformer
{
    public static function transformList($scmOrderList)
    {
        if (!$scmOrderList) {
            return [];
        }

        foreach ($scmOrderList as $key => &$scmOrder) {
            $scmOrder = self::commonTransform($scmOrder);
        }
        unset($scmOrder);
        return $scmOrderList;
    }

    private static function commonTransform($scmOrder)
    {
        $scmOrder['create_time'] = NameConvert::getDateTime($scmOrder['create_time']);
        $scmOrder['customs_status_name'] = Arr::get(config('field.CustomsStatus'), $scmOrder['customs_status']);
        $scmOrder['status_name'] = Arr::get(config('field.ScmOrderStatus'), $scmOrder['status'], '未知');
        $scmOrder['commodity_inspection_name'] = $scmOrder['commodity_inspection'] ? '是' : '否';
        $scmOrder['shipping_name'] = Arr::get($scmOrder['stock_in'], 'shipping_name');
        $scmOrder['shipment_number'] = Arr::get($scmOrder['stock_in'], 'shipment_number');
        $scmOrder['delivery_time'] = Arr::get($scmOrder['stock_in'], 'delivery_time');
        $scmOrder['estimate_arrival_time'] = Arr::get($scmOrder['stock_in'], 'estimate_arrival_time');
        $scmOrder = self::setDefaultNullValue($scmOrder);
        return $scmOrder;
    }

    //为报关的数据转换(其实也是委托明细数据)
    public static function transformCustomsData($scmOrderItemList)
    {
        if (!$scmOrderItemList) {
            return [];
        }
        foreach ($scmOrderItemList as &$item) {
            $item['sub_total'] = self::getSubTotal($item);
            //报关数量
            $item['customs_number'] = $item['goods_number'];
        }
        unset($item);
        return $scmOrderItemList;
    }

    //获取小计
    private static function getSubTotal($scmOrderItem)
    {
        return price_format($scmOrderItem['purchase_prices'] * $scmOrderItem['goods_number']);
    }

    public static function transformStockInList($stockInItemList)
    {
        if (!$stockInItemList) {
            return [];
        }
        foreach ($stockInItemList as $key => &$stockInItem) {
            $stockInItem['company_name'] = Arr::get($stockInItem['stock_in'], 'company_name');
            $stockInItem['stock_in_type'] = Arr::get($stockInItem['stock_in'], 'stock_in_type');
            $stockInItem['stock_in_sn'] = Arr::get($stockInItem['stock_in'], 'stock_in_sn');
            $stockInItem['erp_stock_in_sn'] = Arr::get($stockInItem['stock_in'], 'erp_stock_in_sn');
            $stockInItem['item_status_name'] = Arr::get(StockInItemModel::$ITEM_STATUS, $stockInItem['item_status']);
//            unset($stockInItem['stock_in']);
        }
        unset($stockInItem);
        return $stockInItemList;
    }

    //因为产品说的是,某些字段如果为空,那么就要展示 '-' 符号,所以要有这个方法去设置
    public static function setDefaultNullValue($item)
    {
        $fields = [
            'tariff_rate',
            'tariff',
            'origin_tax',
            'delivery_time',
            'estimate_arrival_time',
            'scm_brand_name'
        ];
        foreach ($item as $key => &$value) {
            if (in_array($key, $fields)) {
                $value = empty($value) ? '-' : $value;
            }
        }
        unset($value);
        return $item;
    }
}
