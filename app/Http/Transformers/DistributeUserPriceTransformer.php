<?php

namespace App\Http\Transformers;

use Illuminate\Support\Arr;
use App\Http\Models\SupplierModel;
use App\Http\Models\DistributeUserPriceRuleModel;
use App\Http\Models\Supplier\SupplierChannelModel;

class DistributeUserPriceTransformer
{
    public function listTransformer($list)
    {
        $supplierList = SupplierModel::pluck('supplier_name', 'supplier_id')->toArray();
        foreach ($list as $key => &$item) {
            $item['create_time'] = $item['create_time'] ? date('Y-m-d H:i:s', $item['create_time']) : '';
            $item['update_time'] = $item['update_time'] ? date('Y-m-d H:i:s', $item['update_time']) : '';
            $item['status_name'] = Arr::get(config('field.DistributeUserPriceStatus'), $item['status'], '未知状态');
            //1是协议价,2是指导价,1,2是协议价和指导价
            //存在1,2,则协议价和指导价都存在
            switch ($item['price_type']) {
                case "1":
                    $item['price_type_name'] = '协议价';
                    break;
                case "2":
                    $item['price_type_name'] = '指导价';
                    break;
                case "1,2" || "2,1":
                    $item['price_type_name'] = '协议价和指导价';
                    break;
                default:
                    # code...
                    break;
            }
            $supplierNames = [];
            if (!empty($item['price'])) {
                foreach ($item['price'] as $price) {
                    if ($price['supplier_id'] != 17) {
                        if ($price['supplier_id'] == -2) {
                            $supplierNames[] = '猎芯自营';
                        } else {
                            $supplierNames[] = $supplierList[$price['supplier_id']] ?? '';
                        }
                    } else {
                        $supplierNames[] = self::getSupplierNameBySupplierCodes($price['supplier_code']);
                    }
                }
            }
            $item['supplier_names'] = implode(',', $supplierNames);
        }

        unset($item);
        return $list;
    }

    public static function getSupplierNameBySupplierCodes($supplierCodes)
    {
        if (!\is_array($supplierCodes)) {
            $supplierCodes = explode(',', $supplierCodes);
        }
        $supplierNames = [];
        foreach ($supplierCodes as $supplierCode) {
            $supplierNames[] = SupplierChannelModel::where('supplier_code', $supplierCode)->value('supplier_name');
        }

        return implode(',', $supplierNames);
    }
}
