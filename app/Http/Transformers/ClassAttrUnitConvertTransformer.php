<?php


namespace App\Http\Transformers;


use App\Http\Models\BrandStandardMappingModel;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;

class ClassAttrUnitConvertTransformer
{
    public function listTransformer($convertList)
    {
        foreach ($convertList as $key => &$convert) {
            $convert['create_time'] = $convert['create_time'] ? date('Y-m-d H:i:s', $convert['create_time']) : '';
            $convert['update_time'] = $convert['update_time'] ? date('Y-m-d H:i:s', $convert['update_time']) : '';

        }

        unset($convert);
        return $convertList;
    }
}
