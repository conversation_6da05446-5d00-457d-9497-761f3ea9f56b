<?php

namespace App\Http\Transformers;

class ClassManagementTransformer
{
    /**
     * 列表数据转换
     *
     * @param array $data
     * @return array
     */
    public function listTransformer($data)
    {
        foreach ($data as &$item) {
            $item['status_text'] = $this->getStatusText($item['status']);
            $item['create_time_text'] = $item['create_time'] ? date('Y-m-d H:i:s', $item['create_time']) : '';
            $item['update_time_text'] = $item['update_time'] ? date('Y-m-d H:i:s', $item['update_time']) : '';
        }
        return $data;
    }

    /**
     * 获取状态文本
     *
     * @param int $status
     * @return string
     */
    private function getStatusText($status)
    {
        $statusMap = [
            0 => '待处理',
            1 => '已处理',
        ];
        return $statusMap[$status] ?? '未知状态';
    }
}
