<?php


namespace App\Http\Transformers;


class ShopSpuLogTransformer
{
    public function listTransformer($logList)
    {
        if (!empty($logList)) {
            foreach ($logList as $key => &$log) {
                $log['create_time'] = date('Y-m-d H:i:s', $log['create_time']);
                $log['platform_name'] = config('field.ShopPlatform')[$log['platform']] ?? '无';
            }
            unset($log);
        }
        return $logList;
    }
}
