<?php


namespace App\Http\Transformers;


class StandardEncapMappingTransformer
{
    public function listTransformer($encapList)
    {
        if (!empty($encapList)) {
            foreach ($encapList as $key => &$encap) {
                $encap['create_time'] = date('Y-m-d H:i:s', $encap['create_time']);
                //$encap['admin_name'] = $encap['admin_id'];
                $encap['encap_name'] = \Arr::get($encap['encap'], 'encap_name','');
            }
            unset($encap);
        }
        return $encapList;
    }
}
