<?php


namespace App\Http\Transformers;


use App\Http\Services\StandardBrandService;
use App\Http\Services\SupplierService;

class ShopUnitMappingTransformer
{
    public function listTransformer($mappingList)
    {
        if (!empty($mappingList)) {
            foreach ($mappingList as $key => &$mapping) {
                $mapping['create_time'] = date('Y-m-d H:i:s', $mapping['create_time']);
                $mapping['update_time'] = $mapping['update_time'] ? date('Y-m-d H:i:s', $mapping['update_time']) : '';
                $mapping['platform_name'] = config('field.ShopPlatform')[$mapping['platform']] ?? '无';
            }
            unset($mapping);
        }
        return $mappingList;
    }
}
