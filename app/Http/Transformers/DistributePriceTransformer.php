<?php


namespace App\Http\Transformers;


use App\Http\Models\BrandStandardMappingModel;
use App\Http\Models\SupplierModel;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;

class DistributePriceTransformer
{
    public function listTransformer($list)
    {
        $redis = Redis::connection('sku');
        foreach ($list as $key => &$item) {
            $item['create_time'] = $item['create_time'] ? date('Y-m-d H:i:s', $item['create_time']) : '';
            $item['update_time'] = $item['update_time'] ? date('Y-m-d H:i:s', $item['update_time']) : '';
            if ($item['standard_brand_id']) {
                $standardBrandIdList = explode(',', $item['standard_brand_id']);
                $standardBrandList = $redis->hmget('standard_brand', $standardBrandIdList);
                $standardBrandList = array_map(function ($value) {
                    return json_decode($value, true);
                }, $standardBrandList);
                $standardBrandNameList = array_column($standardBrandList, 'brand_name');
                $standardBrandNameList = implode(',', $standardBrandNameList);
                $item['standard_brand_name'] = $standardBrandNameList;
            }
            $item['supplier_name'] = $item['supplier']['supplier_name'] ?? '';
            if ($item['supplier_id'] === -2) {
                $item['supplier_name'] = '猎芯自营';
            }
            $item['status_name'] = Arr::get(config('field.DistributePriceStatus'), $item['status'], '未知状态');
        }

        unset($item);
        return $list;
    }
}
