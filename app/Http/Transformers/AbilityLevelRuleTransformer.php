<?php

namespace App\Http\Transformers;

use App\Http\Models\SupplierModel;
use App\Http\Models\Supplier\SupplierChannelModel;
use Illuminate\Support\Arr;

class AbilityLevelRuleTransformer
{
    public function listTransformer($list)
    {
        foreach ($list as $key => &$item) {
            $item['create_time'] = $item['create_time'] ? date('Y-m-d H:i:s', $item['create_time']) : '';
            $item['update_time'] = $item['update_time'] ? date('Y-m-d H:i:s', $item['update_time']) : '';

            // 转换level为中文
            $item['level_name'] = Arr::get(config('field.AbilityLevelRuleLevel'), $item['level'], '');

            // 转换状态为中文
            $statusMap = [
                1 => '启用',
                -1 => '禁用'
            ];
            $item['status_name'] = Arr::get($statusMap, $item['status'], '');
            $item['source_name'] = '';
            $source = explode(',', $item['source']);
            foreach ($source as $value) {
                $item['source_name'] .= Arr::get(config('field.SkuSource'), $value, '') . ',';
            }
            $item['source_name'] = rtrim($item['source_name'], ',');
        }

        return $list;
    }
}
