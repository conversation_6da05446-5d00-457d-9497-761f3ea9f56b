<?php


namespace App\Http\Transformers;


use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Services\ClassService;
use App\Http\Services\StandardBrandService;
use App\Http\Services\SupplierService;

class ShopDistributeRuleSpuTransformer
{
    public function listTransformer($ruleList)
    {
        if (!empty($ruleList)) {
            foreach ($ruleList as $key => &$rule) {
                $rule['create_time'] = date('Y-m-d H:i:s', $rule['create_time']);
                $rule['update_time'] = $rule['update_time'] ? date('Y-m-d H:i:s', $rule['update_time']) : '';
                $rule['platform_name'] = config('field.ShopPlatform')[$rule['platform']] ?? '无';
                if (!empty($rule['class_id_list'])) {
                    $rule['class_name_list'] = PoolClassModel::whereIn('class_id', explode(',', $rule['class_id_list']))->pluck('class_name')->join(',');
                }else{
                    $rule['class_name_list'] = '';
                }
                $rule['brand_name_list'] = StandardBrandService::getStandardBrandNamesByBrandIds($rule['brand_id_list']);
            }
            unset($rule);
        }
        return $ruleList;
    }
}
