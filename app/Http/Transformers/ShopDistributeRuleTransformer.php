<?php


namespace App\Http\Transformers;


use App\Http\Services\StandardBrandService;
use App\Http\Services\SupplierService;

class ShopDistributeRuleTransformer
{
    public function listTransformer($brandList)
    {
        if (!empty($brandList)) {
            foreach ($brandList as $key => &$brand) {
                $brand['create_time'] = date('Y-m-d H:i:s', $brand['create_time']);
                $brand['update_time'] = $brand['update_time'] ? date('Y-m-d H:i:s', $brand['update_time']) : '';
                $brand['platform_name'] = config('field.ShopPlatform')[$brand['platform']] ?? '无';
                $brand['supplier_name_list'] = SupplierService::getSupplierNamesBySupplierIds($brand['supplier_id_list']);
                $brand['brand_name_list'] = StandardBrandService::getStandardBrandNamesByBrandIds($brand['brand_id_list']);
            }
            unset($brand);
        }
        return $brandList;
    }
}
