<?php


namespace App\Http\Transformers;


class StandardBrandMappingTransformer
{
    public function listTransformer($brandList)
    {
        if (!empty($brandList)) {
            foreach ($brandList as $key => &$brand) {
                $brand['add_time'] = date('Y-m-d H:i:s', $brand['add_time']);
                $brand['admin_name'] = $brand['admin_id'];
                $brand['brand_name'] = \Arr::get($brand['brand'], 'brand_name','');
            }
            unset($brand);
        }
        return $brandList;
    }
}
