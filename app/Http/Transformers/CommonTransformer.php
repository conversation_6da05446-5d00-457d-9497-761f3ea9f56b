<?php

namespace App\Http\Transformers;

use App\Http\Models\PurchasePlanModel;
use App\Http\Models\SupplierAddressModel;
use App\Http\Models\SupplierContactModel;
use App\Http\Utils\NameConvert;
use Illuminate\Support\Arr;

class CommonTransformer
{
    //这个currency可以传currency或者company_id,因为这两个的值都是类型相等的,前者为1,后者肯定也是1
    public static function priceTransform($price, $currency, $number = 0)
    {
        $price = $number > 0 ? number_format($price, $number) : $price;
        $price = $price ? ($currency == PurchasePlanModel::CURRENCY_RMB ? '¥' : '$') . '' . $price : 0;
        return $price;
    }
}
