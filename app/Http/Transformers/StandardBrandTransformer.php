<?php


namespace App\Http\Transformers;


use Illuminate\Support\Facades\Redis;

class StandardBrandTransformer
{
    public function listTransformer($brandList)
    {
        foreach ($brandList as $key => &$brand) {
            $brand['create_time'] = date('Y-m-d H:i:s', $brand['create_time']);
            $brand['status_name'] = \Arr::get(config('field.StandardBrandStatus'), $brand['status'], '未知状态');
            $brand['brand_area'] = \Arr::get(config('field.StandardBrandArea'), $brand['brand_area'], '未知');
            $brand['has_brand_desc'] = trim($brand['brand_desc']) ? '有' : '无';
            $brand['has_brand_brief'] = trim($brand['brand_brief']) ? '有' : '无';
        }
        unset($brand);
        return $brandList;
    }

}
