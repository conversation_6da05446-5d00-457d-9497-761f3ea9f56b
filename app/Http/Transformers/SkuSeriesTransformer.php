<?php


namespace App\Http\Transformers;


use App\Http\Models\BrandStandardMappingModel;
use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Models\SkuSeriesModel;
use App\Http\Services\SkuService;
use App\Http\Services\StandardBrandService;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;

class SkuSeriesTransformer
{
    public function listTransformer($skuSeriesList)
    {
        $classNameList = PoolClassModel::where('class_type', PoolClassModel::CLASS_TYPE_IEDGE)
            ->pluck('class_name', 'class_id')->toArray();
        foreach ($skuSeriesList as $key => &$skuSeries) {
            $skuSeries['create_time'] = date('Y-m-d H:i:s', $skuSeries['create_time']);
            $skuSeries['update_time'] = date('Y-m-d H:i:s', $skuSeries['update_time']);
            $skuSeries['status_name'] = Arr::get(config('field.SkuSeriesStatus'), $skuSeries['status']);
            $standardBrand = (new StandardBrandService())->getStandardBrandById($skuSeries['standard_brand_id']);
            $skuSeries['standard_brand_name'] = Arr::get($standardBrand, 'brand_name', '');
            $skuSeries['class_name'] = '';
            $skuSeries['class_name1'] = Arr::get($classNameList, $skuSeries['class_id1']);
            $skuSeries['class_name2'] = Arr::get($classNameList, $skuSeries['class_id2']);
            if (!empty($skuSeries['class_name2'])) {
                $skuSeries['class_name'] = $skuSeries['class_name1'] . ' / ' . $skuSeries['class_name2'];
            } else {
                $skuSeries['class_name'] = $skuSeries['class_name1'];
            }
        }

        unset($skuSeries);
        return $skuSeriesList;
    }

    public function itemListTransformer($itemList)
    {
        if (empty($itemList)) {
            return [];
        }
        $seriesId = $itemList[0]['series_id'];
        $series = SkuSeriesModel::where('id', $seriesId)->first()->toArray();
        foreach ($itemList as $key => &$item) {
            $sku = (new SkuService())->getSkuCacheInfo($item['sku_id']);
            $skuId = (string)$item['sku_id'];
            $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
            $item['update_time'] = date('Y-m-d H:i:s', $item['update_time']);
            $item['standard_brand_name'] = Arr::get($sku, 'standard_brand_name', '');
            $item['class_name'] = '';
            $item['class_name1'] = Arr::get($sku, $series['class_id1']);
            $item['class_name2'] = Arr::get($sku, $series['class_id2']);
            $item = array_merge($item, $sku);
            $item['sku_id'] = $skuId;
            if (!empty($skuData['class_name2'])) {
                $item['class_name'] = $item['class_name1'] . ' / ' . $item['class_name2'];
            }
        }

        unset($item);
        return $itemList;
    }
}
