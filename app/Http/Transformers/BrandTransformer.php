<?php


namespace App\Http\Transformers;


use App\Http\Models\BrandStandardMappingModel;
use App\Http\Models\Cms\CmsUserInfoModel;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;

class BrandTransformer
{
    public function listTransformer($brandList)
    {
        $redis = Redis::connection('sku');
        foreach ($brandList as $key => &$brand) {
            $brand['brand_area'] = Arr::get(config('field.BrandArea'), $brand['brand_area']);
            $brand['create_time'] = date('Y-m-d H:i:s', $brand['create_time']);
            $brand['update_time'] = !empty($brand['update_time']) ? date('Y-m-d H:i:s', $brand['update_time']) : '';
            $brand['insert_type_name'] = Arr::get(config('field.BrandInsertType'), $brand['insert_type'], '未知状态');
            //是否有映射
            $brand['has_mapping'] = $brand['standard_brand_mapping'] ? '有' : '无';
            $brand['standard_brand_id'] = $brand['standard_brand_mapping'] ? $brand['standard_brand_mapping']['standard_brand_id'] : 0;
            $brand['standard_brand_name'] = '';
            if ($brand['standard_brand_id']) {
                $standardBrand = $redis->hget('standard_brand', $brand['standard_brand_id']);
                $brand['standard_brand_name'] = Arr::get(json_decode($standardBrand, true), 'brand_name');
            }

            $brand['goods_amount'] = $redis->hget('hhs_brand_id_num', $brand['brand_id']);
            $brand['goods_amount'] = $brand['goods_amount'] ?: 0;
            $showStatus = 0;
            if (!empty($brand['brand_logo']) && $brand['status'] == 1 && $brand['goods_amount'] > 0) {
                $showStatus = 1;
            }
            $brand['show_status'] = $showStatus;
            $brand['show_info'] = '是否有图片 : ' . (!empty($brand['brand_logo']) ? "有" : "无") . ' | 上架商品数量 : ' . $brand['sku_number'];
            if (is_numeric($brand['update_name'])) {
                $brand['update_name'] = CmsUserInfoModel::where('userId', $brand['update_name'])->value('name');
            }
        }

        unset($brand);
        return $brandList;
    }
}
