<?php


namespace App\Http\Transformers;


use App\Http\Models\BrandStandardMappingModel;
use App\Http\Models\SkuSeriesItemModel;
use App\Http\Models\Supplier\SupplierChannelModel;
use App\Http\Services\ClassService;
use App\Http\Services\SupplierService;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Redis;
use phpDocumentor\Reflection\PseudoTypes\IntegerRange;

class SkuTransformer
{
    //$prev_sku 是否是归档数据
    public static function listTransformer($data,$prev_sku=0)
    {
        $redis = Redis::connection('sku');
        $spuRedis = Redis::connection('spu');
        $skuStatus = config('field.SkuStatus');
        $skuType = config('field.SkuType');

        $skuList = [];
        //处理供应商
        $supplier = $redis->hgetall('supplier');
        $ability_level = config('field.AbilityLevelRuleLevel');
        //有些数据还要去商品服务拿....
        $skuIds = implode(',', $data['data']['goods_id']);
        $goodsServerUrl = config('config.goods_server_domain') . '/synchronization?goods_id=' . $skuIds;
        $skuServerData = Arr::get(Http::get($goodsServerUrl)->json(), 'data');

        #查询代购采购
        $supplierOutSeaId = [];
        foreach ($data['data']['goods_id'] as $a) {
            $goodsId = (string)$a;
            $skuData = Arr::get($skuServerData, $goodsId);
            if ($skuData) {
               if ($skuData["supplier_id"] != 17){
                   $supplierOutSeaId[] = $skuData["supplier_id"];
               }
            }
        }

        $connectionSpu = DB::connection("spu");
        $seaSupplierArr = [];
        if ($supplierOutSeaId){
            $seaSupplier = $connectionSpu->table("lie_supplier")->select("supplier_id","code")->wherein('supplier_id', array_unique($supplierOutSeaId))->get()->toArray();
            $seaSupplierArr = array_column($seaSupplier,"code","supplier_id");
        }

        foreach ($data['data']['goods_id'] as $v) {

            if (empty($v) || $v == 0) {
                $skuList[] = [];
                continue;
            }
            $goodsId = (string)$v;

            if ($prev_sku == "1"){ //从mongodb获取关联关系
                $mongo = DB::connection('zhuanyin');
                $sku = $mongo->table('prev_sku')->where('sku_id', (int)$goodsId)->first();
            }else{
                //下面的是从redis获取的数据
                $sku = json_decode($redis->hget('sku', $v), true);
            }

            if (!$sku) {
                $skuList[] = [
                    'goods_id' => $goodsId,
                ];
                continue;
            }

            $canal = Arr::get($sku,"canal",0); //渠道标签
            $spu = json_decode($spuRedis->hget('spu', $sku['spu_id']), true);
            //赋值
            $sku['goods_id'] = $v;
            $sku['update_time'] = $prev_sku ? "" : date('Y-m-d H:i:s', $sku['update_time']);
            $sku['type_name'] = Arr::get($skuType, @$sku['goods_type']);
            $sku['status_name'] = $prev_sku ? "删除" :$skuStatus[$sku['goods_status'] ?? -100] ?? '';
            $sku['ability_level_cn'] = $prev_sku ? "" : $ability_level[Arr::get($sku,"ability_level",0)]??'';
            $sku['brand_name'] = $redis->hget('brand', $spu['brand_id']);
            $sku['spu_name'] = $spu['spu_name'];
            $sku['canal_name'] =  "";
            $sku['canal_id'] = 0;
            if ($canal){
                $sinfo = (new SupplierService())->getSupplierByCanal($canal);
                $sku['canal_name'] = !empty($sinfo["supplier_name"]) ? $sinfo["supplier_name"] : "";
                $sku['canal_id'] = !empty($sinfo["supplier_id"]) ? $sinfo["supplier_id"] : 0;
            }

            if ($sku['supplier_id'] != 17) {
                $sku['goods_label_name'] = '代购现货';
            } else {
                $goodsTag = $redis->hget('goods_tag', $goodsId);
                $goodsTag = json_decode($goodsTag, true);
                $goodsLabel = Arr::get($goodsTag, 'goods_label');
                $sku['goods_label_name'] = !$goodsLabel ? "" :Arr::get(config('field.GoodsLabel'), $goodsLabel, '');
            }
            //添加sku是否有效
            if ($sku['supplier_id'] == 17) {
                if (isset($data['data']['cp_time'][$v])) {
                    if ($data['data']['cp_time'][$v] > time()) {
                        $sku['is_expire'] = '否';
                    } else {
                        if (empty($data['data']['cp_time'][$v])) {
                            $sku['is_expire'] = '是';
                        } else {
                            $sku['is_expire'] = '是';
                        }
                    }
                } else {
                    $sku['is_expire'] = '是';
                }
            } else {
                $sku['is_expire'] = !empty(@$sku['is_expire']) ? '是' : '否';
            }

            //供应商
            $sku['supplier_name'] = Arr::get($supplier, @$sku['supplier_id']);
            $sku['encap'] = Arr::get($spu, 'encap', '');
            $spu = json_decode($spuRedis->hget('spu', @$sku['spu_id']), true);
            //型号处理
            if (empty($sku['goods_name'])) {
                $sku['goods_name'] = $spu['spu_name'];
            }
            //制造商处理
            if (empty($sku['brand_name'])) {
                $brand = $redis->hget('brand', $spu['brand_id']);
                if ($brand) {
                    $sku['brand_name'] = $brand;
                }
            }
            $skuData = Arr::get($skuServerData, $goodsId);
            if ($skuData) {
                //去商品服务获取相关数据,有些数据只能商品服务拿
                //为啥拿商品服务的阶梯价当作展示价,因为商品服务的阶梯价就是经过各种换算后的展示价
                $sku['canal'] = @$skuData['canal'] ?: '';
                $sku['supplier_id'] = @$skuData['supplier_id'] ?: '';
                $sku['class_id1'] = @$skuData['class_id1'] ?: '';
                $sku['class_id1_name'] = @$skuData['class_name1'] ?: '';
                $sku['class_id2'] = @$skuData['class_id2'] ?: '';
                $sku['class_id2_name'] = @$skuData['class_name2'] ?: '';
                $sku['packing'] = @$skuData['packing'] ?: '';
                $sku['multiple'] = @$skuData['multiple'] ?: 1;

                $sku['cn_delivery_time'] = @$skuData['cn_delivery_time'] ?: '';
                $sku['hk_delivery_time'] = @$skuData['hk_delivery_time'] ?: '';
                $sku['moq'] = @$skuData['moq'] ?: '';
                $sku['mpq'] = @$skuData['mpq'] ?: '';
                $sku['batch_sn'] = @$skuData['batch_sn'] ?: '';
                $sku['source'] = @$skuData['source'] ?: 0;

                $sku['show_price'] = @$skuData['ladder_price'] ?: [];
                $sku['ladder_price'] = Arr::get($skuData, 'original_price', []);
                $sku['standard_brand_name'] = Arr::get($skuData['standard_brand'], 'brand_name',"");
                $sku['standard_brand_id'] = Arr::get($skuData['standard_brand'], 'standard_brand_id',0);
                $sku['class_name'] = '';
                if (!empty(@$skuData['class_name2'])) {
                    $sku['class_name'] = Arr::get($skuData, 'class_name1') . ' / ' . Arr::get($skuData,
                            'class_name2');
                }
                if (!empty(@$skuData['class_id1']) && empty(@$skuData['class_id2'])) {
                    $sku['class_name'] = ClassService::getClassNameFromCache($skuData['class_id1']) . '/';
                }
                $sku['is_buy'] = @$skuData['is_buy']; //是否能购买
                if (!empty($skuData['goods_tag']['goods_tag_names'])) {
                   $sku['goods_tag_names'] = \implode(',',$skuData['goods_tag']['goods_tag_names']);
                }
            }
            //去db找出相关数据,有些数据只能去db找....
            $dbInfo = getSpuSkuDb($goodsId);
            $connection = DB::connection($dbInfo["db"]);
            $table = $dbInfo['table'];
            $selectFields = ['goods_id', 'create_time', 'eccn', 'source', 'encoded'];
            $skuDBData = $connection->table($table)->select($selectFields)->where('goods_id', $goodsId)->first();

            $skuDBData = $skuDBData ? (array)$skuDBData : [];
            if (!empty($skuDBData)) {
                $sku['eccn'] = $skuDBData['eccn'];
                if ($sku['supplier_id'] != 17) { //代购查代购采购
                    $sku['encoded'] = $seaSupplierArr[$sku['supplier_id']??0]??'';
                }else{
                    $sku['encoded'] = $skuDBData['encoded'];
                }
                $sku['create_time'] = $skuDBData['create_time'] ? date('Y-m-d H:i:s', $skuDBData['create_time']) : '';
                $sku['source_name'] = Arr::get(config('field.SkuSource'), $skuDBData['source'], '');
            }
            $skuList[] = $sku;

            $sku['brand_name'] = $sku['brand_name'] ?: '';
        }
        return $skuList;
    }

}
