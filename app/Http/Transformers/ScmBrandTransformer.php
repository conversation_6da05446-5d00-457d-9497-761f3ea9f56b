<?php


namespace App\Http\Transformers;


use App\Http\Models\BrandStandardMappingModel;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;

class ScmBrandTransformer
{
    public function listTransformer($brandList)
    {


        foreach ($brandList as $key => &$brand) {
            $brand['add_time'] = date('Y-m-d H:i:s', $brand['add_time']);
            $brand['update_time'] = date('Y-m-d H:i:s', $brand['update_time']);
            $brand['status_name'] = Arr::get(config('field.ScmBrandStatus'), $brand['erp_status']);
        }

        unset($brand);
        return $brandList;
    }
}
