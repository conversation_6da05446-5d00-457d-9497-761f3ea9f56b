<?php


namespace App\Http\Transformers;


use App\Http\Services\SkuService;
use Illuminate\Support\Facades\Redis;
use App\Http\Services\SupplierService;
use App\Http\Services\StandardBrandService;

class ShopDistributeSkuTransformer
{
    public function listTransformer($skuList)
    {
        $redis = Redis::connection('sku');
        $rate = $redis->get('erp_rate', 2);
        if (!empty($skuList)) {
            foreach ($skuList as $key => &$sku) {
                $sku['sku_id'] = (string)$sku['sku_id'];
                $sku['sku'] = (new SkuService())->getSkuCacheInfo($sku['sku_id']);
                $sku['status_name'] = config('field.ShopDistributeSkuStatus')[$sku['status']] ?? '';
                $sku['purchase_coefficient'] = empty($sku['purchase_coefficient']) ? 0 : $sku['purchase_coefficient'];
                $sku['sale_coefficient'] = empty($sku['sale_coefficient']) ? 0 : $sku['sale_coefficient'];
                $sku['create_time'] = $sku['create_time'] ? date('Y-m-d H:i:s', $sku['create_time']) : '';
                $sku['update_time'] = $sku['update_time'] ? date('Y-m-d H:i:s', $sku['update_time']) : '';
                // 获取单价,根据起订量去ladder_price中获取
                $sku['ladder_price'] = $sku['sku']['ladder_price'] ?? [];
                //起订量在某个区间
                $moq = $sku['moq'];
                //成本价
                $costUsPrice = 0;
                $costCnPrice = 0;
                if (!empty($sku['ladder_price'])) {
                    foreach ($sku['ladder_price'] as $item) {
                        if ($moq >= $item['purchases']) {
                            $costUsPrice = $item['price_us'];
                            $costCnPrice = $item['price_cn'];
                        }
                    }

                    if ($costCnPrice == 0 && !empty($costUsPrice)) {
                        $costCnPrice = $costUsPrice * $rate;
                    }

                    $totalCost = $costCnPrice * $sku['moq'];
                }

                $sku['total_cost'] = $totalCost ?? 0;
                $sku['cost_price'] = $costCnPrice ?? 0;
                if (empty($sku['purchase_price'])) {
                    $sku['purchase_price'] = $sku['purchase_coefficient'] ? $costCnPrice * (float)$sku['purchase_coefficient'] : 0;
                }
                if (empty($sku['sale_price'])) {
                    if ($sku['sale_price'] == 0) {
                        $sku['sale_price'] = $sku['purchase_price'] ? $sku['purchase_price'] * (float)$sku['sale_coefficient'] : 0;
                    }
                }
                unset($sku);
            }
            return $skuList;
        }
    }
}
