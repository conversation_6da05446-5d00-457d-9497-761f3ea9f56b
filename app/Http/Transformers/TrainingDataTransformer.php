<?php

namespace App\Http\Transformers;

use App\Http\Models\TrainingDataModel;

class TrainingDataTransformer
{
    public function listTransformer($data)
    {
        $result = [];
        foreach ($data as $item) {
            $result[] = [
                'id' => $item['id'],
                'content' => $item['content'],
                'type' => $item['type'],
                'type_name' => TrainingDataModel::TYPE_NAMES[$item['type']] ?? '',
                'create_time' => $item['create_time'] ? date('Y-m-d H:i:s', $item['create_time']) : '',
                'create_name' => $item['create_name'],
                'update_time' => $item['update_time'] ? date('Y-m-d H:i:s', $item['update_time']) : '',
                'update_name' => $item['update_name'],
            ];
        }
        return $result;
    }
}
