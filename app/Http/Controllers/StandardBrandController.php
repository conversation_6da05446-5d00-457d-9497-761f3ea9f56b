<?php

namespace App\Http\Controllers;

use App\Http\Models\ScmBrandModel;
use App\Http\Models\StandardBrandScmMappingModel;
use App\Http\Models\StandardBrandSpuModel;
use App\Http\Services\MenuService;
use App\Http\Models\BrandModel;
use App\Http\Services\SpuService;
use App\Http\Transformers\BrandTransformer;
use App\Http\Models\StandardBrandModel;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class StandardBrandController extends Controller
{

    //委托单列表
    public function standardBrandList(Request $request)
    {
        return view('standardBrand.standardBrandList');
    }

    //保存标准品牌
    public function saveStandardBrand(Request $request)
    {
        $standardBrandId = $request->input('standard_brand_id');
        $data = [];
        if (!empty($standardBrandId)) {
            $model = new StandardBrandModel();
            $brand = $model->where('standard_brand_id', $standardBrandId)->first()->toArray();
            $data['brand'] = $brand;
            $data['scm_brand_id'] = (new StandardBrandScmMappingModel())->getScmBrandIdByStandardBrandId($standardBrandId);
        }
        $data['scm_brand_list'] = (new ScmBrandModel())->getAllScmBrandIdAndName();
        return view('standardBrand.saveStandardBrand', $data);
    }

    //添加标准品牌主要商品
    public function addStandardBrandSpu(Request $request)
    {
        $id = $request->get('id');
        $data = [];
        if ($id) {
            $spu = StandardBrandSpuModel::where('id', $id)->first()->toArray();
            $spuInfo = (new SpuService())->getSpuInfo($spu['spu_id']);
            $spu['spu_name'] = $spuInfo['spu_name'];
            $spu['brand_name'] = $spuInfo['brand_name'];
            $data['spu'] = $spu;
        }
        return view('standardBrand.addStandardBrandSpu', $data);
    }
}
