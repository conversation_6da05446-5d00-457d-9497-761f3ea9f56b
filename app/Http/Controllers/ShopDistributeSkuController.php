<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Services\SkuService;
use App\Http\Services\ClassService;
use App\Http\Services\SupplierService;
use App\Http\Models\BigData\ShopInfoModel;
use App\Http\Services\StandardBrandService;
use App\Http\Services\ShopDistributeSkuService;
use App\Http\Models\BigData\ShopDistributeSkuModel;
use App\Http\Models\Supplier\SupplierChannelModel;

class ShopDistributeSkuController extends Controller
{
    public function shopDistributeSkuList(Request $request)
    {
        $supplierList = SupplierService::getSupplierListForXmSelect();
        $standardBrandList = StandardBrandService::getStandardBrandListForXmSelect();
        $classList = ClassService::getAllClassListForXmSelect();
        $supplierCodeList = [];
        $supplierChannelList = SupplierChannelModel::select(['supplier_code', 'supplier_name'])->get()->toArray();
        foreach ($supplierChannelList as $supplierChannel) {
            $supplierCodeList[] = [
                'value' => $supplierChannel['supplier_code'],
                'name' => $supplierChannel['supplier_code'] . '(' . $supplierChannel['supplier_name'] . ')'
            ];
        }
        $shopList = ShopInfoModel::select(['shop_id', 'shop_name'])->pluck('shop_name', 'shop_id')->toArray();
        return view('shopDistributeSku.shopDistributeSkuList', [
            'shopList' => $shopList ?? [],
            'supplierList' => $supplierList ?? [],
            'standardBrandList' => $standardBrandList ?? [],
            'classList' => $classList ?? [],
            'supplierCodeList' => $supplierCodeList ?? []
        ]);
    }

    public function saveShopDistributeSku(Request $request)
    {
        $skuIds = $request->input('sku_ids');
        $skuIds = trim($skuIds, ',');
        $shopList = ShopInfoModel::select(['shop_id', 'shop_name'])->pluck('shop_name', 'shop_id')->toArray();
        $shopListForXmSelect = [];
        foreach ($shopList as $shopId => $shopName) {
            $shopListForXmSelect[] = [
                'value' => $shopId,
                'name' => $shopName
            ];
        }

        if (!empty($skuIds)) {
            $skuIds = explode(',', $skuIds);
            //先去判断已经存在的,就跳过
            $exitSkuIds = ShopDistributeSkuModel::whereIn('sku_id', $skuIds)->pluck('sku_id')->toArray();
            $skuIds = array_diff($skuIds, $exitSkuIds);
        }

        return view('shopDistributeSku.saveShopDistributeSku', [
            'distributeSku' => $distributeSku ?? [],
            'shopListForXmSelect' => $shopListForXmSelect ?? []
        ]);
    }

    public function batchSaveShopDistributeSku(Request $request)
    {
        $skuIds = $request->input('sku_ids');
        if (empty($skuIds)) {
            return redirect()->back()->with('error', '请选择需要绑定的SKU');
        }
        $skuIds = trim($skuIds, ',');
        $shopList = ShopInfoModel::select(['shop_id', 'shop_name'])->pluck('shop_name', 'shop_id')->toArray();
        $shopListForXmSelect = [];
        foreach ($shopList as $shopId => $shopName) {
            $shopListForXmSelect[] = [
                'value' => $shopId,
                'name' => $shopName
            ];
        }
        $skuList = [];
        if (!empty($skuIds)) {
            $skuIds = explode(',', $skuIds);
            //先去判断已经存在的,就跳过
            // $exitSkuIds = ShopDistributeSkuModel::whereIn('sku_id', $skuIds)->pluck('sku_id')->toArray();
            // $skuIds = array_diff($skuIds, $exitSkuIds);
            //获取sku列表信息
            foreach ($skuIds as $skuId) {
                $sku = (new SkuService())->getSkuCacheInfo($skuId);
                $sku['goods_id'] = $skuId;
                $skuList[] = $sku;
            }
        }
        return view('shopDistributeSku.batchSaveShopDistributeSku', [
            'shopListForXmSelect' => $shopListForXmSelect ?? [],
            'skuList' => $skuList ?? [],
        ]);
    }

    /**
     * 导入SKU页面
     */
    public function importShopDistributeSku(Request $request)
    {
        $shopList = ShopInfoModel::select(['shop_id', 'shop_name'])->pluck('shop_name', 'shop_id')->toArray();
        $shopListForXmSelect = [];
        foreach ($shopList as $shopId => $shopName) {
            $shopListForXmSelect[] = [
                'value' => $shopId,
                'name' => $shopName
            ];
        }

        return view('shopDistributeSku.importShopDistributeSku', [
            'shopListForXmSelect' => $shopListForXmSelect ?? []
        ]);
    }
}
