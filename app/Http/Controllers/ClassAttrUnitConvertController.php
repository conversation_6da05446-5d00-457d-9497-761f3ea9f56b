<?php

namespace App\Http\Controllers;

use App\Http\Models\ClassAttrUnitConvertModel;
use App\Http\Models\ClassAttrUnitConvertSpuModel;
use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Models\Cms\CmsUserIntraCodeModel;
use App\Http\Models\ScmBrandModel;
use App\Http\Services\ClassAttrUnitConvertService;
use App\Http\Services\BrandService;
use App\Http\Services\MenuService;
use App\Http\Models\BrandModel;
use App\Http\Services\ScmBrandService;
use App\Http\Services\SpuService;
use App\Http\Transformers\BrandTransformer;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class ClassAttrUnitConvertController extends Controller
{
    //品牌列表
    public function classAttrUnitConvertList(Request $request)
    {
        $data = [];
        return view('classAttrUnitConvert.classAttrUnitConvertList', $data);
    }

    //编辑代理品牌
    public function saveClassAttrUnitConvert(Request $request)
    {
        $data = [];
        if (!empty($request->input('id'))) {
            $data['convert'] = (new ClassAttrUnitConvertService())->getClassAttrUnitConvertInfo($request->input('id'));
        }
        return view('classAttrUnitConvert.saveClassAttrUnitConvert', $data);
    }
}
