<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Models\SupplierModel;
use App\Http\Models\AbilityLevelRuleModel;
use App\Http\Models\Supplier\SupplierChannelModel;
use App\Http\Services\SupplierService;

class AbilityLevelRuleController extends Controller
{
    public function abilityLevelRuleList(Request $request)
    {
        $data['supplierListForXmSelect'] = (new SupplierModel())->getSupplierListForXmSelect();
        $data['canalListForXmSelect'] = SupplierChannelModel::getCanalListForXmSelect();
        $data['supplierList'] = (new SupplierModel())->getSuppliersForSelect();
        return view('abilityLevelRule.abilityLevelRuleList', $data);
    }

    public function saveAbilityLevelRule(Request $request)
    {
        $id = $request->input('id');
        if (!empty($id)) {
            $data['rule'] = AbilityLevelRuleModel::find($id)->toArray() ?? [];
            $canalInitValue = ShopDistributeRuleController::getCanalInitValue($data['rule']['supplier_code']);
        }
        $data['supplierListForXmSelect'] = (new SupplierModel())->getSupplierListForXmSelect();
        $data['canalListForXmSelect'] = SupplierChannelModel::getCanalListForXmSelect();
        $data['levelListForXmSelect'] = [
            ['name' => '高', 'value' => 2],
            ['name' => '中', 'value' => 1],
            ['name' => '低', 'value' => 0]
        ];
        $data['supplierList'] = (new SupplierModel())->getSuppliersForSelect();
        $data['sourceListForXmSelect'] = [];
        foreach (config('field.SkuSource') as $key => $value) {
            $data['sourceListForXmSelect'][] = ['name' => $value, 'value' => $key];
        }
        $data['canal_init_value'] = $canalInitValue ?? [];
        return view('abilityLevelRule.saveAbilityLevelRule', $data);
    }
}
