<?php

namespace App\Http\Controllers;

use App\Http\Models\StandardBrandModel;
use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use App\Exports\ShopSkuExport;
use App\Http\Models\BrandModel;
use App\Http\Models\SampleModel;
use App\Http\Models\SupplierModel;
use App\Http\Services\MenuService;
use App\Http\Services\BrandService;
use App\Http\Services\ClassService;
use App\Exports\StandardBrandExport;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Models\SampleClassModel;
use App\Http\Services\SupplierService;
use App\Http\Models\BigData\ShopSkuModel;
use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Models\BigData\ShopInfoModel;
use App\Http\Transformers\BrandTransformer;
use App\Http\Models\Supplier\SupplierChannelModel;

//第三方供应商sku相关
class ShopSkuController extends Controller
{
    public function shopSkuList(Request $request)
    {
        $shopList = ShopInfoModel::pluck('shop_name', 'shop_id')->toArray();
        $brandList = StandardBrandModel::pluck('brand_name')->toArray();
        $brandListForXmSelect = array_map(function ($item) {
            return [
                'name' => $item,
                'value' => $item
            ];
        }, $brandList);
        return view('shopSku.shopSkuList', [
            'brandList' => $brandList,
            'shopList' => $shopList,
            'classListForXmSelect' => ClassService::getAllClassListForXmSelect(),
            'canalListForXmSelect' => SupplierChannelModel::getCanalListForXmSelect(),
            'supplierListForXmSelect' => SupplierModel::getSupplierListForXmSelect(),
            'brandListForXmSelect' => $brandListForXmSelect,
        ]);
    }

    //拉取商品信息
    public function fetchSkuInfo(Request $request)
    {
        $shopList = ShopInfoModel::pluck('shop_name', 'shop_id')->toArray();
        return view('shopSku.fetchSkuInfo', [
            'shopList' => $shopList
        ]);
    }
}
