<?php

namespace App\Http\Controllers;

use App\Http\Models\ScmBrandModel;
use App\Http\Services\BrandService;
use App\Http\Services\MenuService;
use App\Http\Models\BrandModel;
use App\Http\Services\ScmBrandService;
use App\Http\Transformers\BrandTransformer;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class ScmBrandController extends Controller
{
    //品牌列表
    public function scmBrandList(Request $request)
    {
        return view('scmBrand.scmBrandList');
    }

    public function scmBrandMapping(Request $request)
    {
        $scmBrandId = $request->input('scm_brand_id');
        $data['info'] = (new ScmBrandService())->getScmBrandWithMappingCount($scmBrandId);
        return view('scmBrand.scmBrandMapping', $data);
    }
}
