<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Models\ClassManagementModel;
use App\Http\Models\PoolClass\PoolClassModel;

class ClassManagementController extends Controller
{
    /**
     * 分类管理列表页面
     *
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function classManagementList(Request $request)
    {
        return view('classManagement.classManagementList');
    }

    /**
     * 保存分类管理页面
     *
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function saveClassManagement(Request $request)
    {
        $id = $request->input('id');
        $data = [];

        if (!empty($id)) {
            $classManagement = ClassManagementModel::find($id);
            if ($classManagement) {
                $data['classManagement'] = $classManagement->toArray();
                // 获取当前分类信息
                $data['current_class'] = $this->getClassNameById($classManagement->class_id);
                // 预填充商品信息
                $data['goods_name'] = $classManagement->goods_name;
                $data['brand_name'] = $classManagement->brand_name;
                $data['spu_id'] = $classManagement->spu_id;
                $data['sku_id'] = $classManagement->sku_id;
            }
        }

        // 获取分类列表
        $data['classList'] = $this->getClassList();
        return view('classManagement.saveClassManagement', $data);
    }

    /**
     * 根据ID获取分类名称
     *
     * @param int $classId
     * @return string
     */
    private function getClassNameById($classId)
    {
        $class = PoolClassModel::where('class_id', $classId)->first();
        if ($class) {
            return $class->class_name;
        }

        return '无';
    }

    /**
     * 获取分类列表
     *
     * @return array
     */
    private function getClassList()
    {
        $classList = [];

        // 获取一级分类
        $classes = PoolClassModel::where('parent_id', 0)
            ->get();

        foreach ($classes as $class) {
            $classList[] = [
                'id' => $class->class_id,
                'name' => $class->class_name
            ];
        }

        return $classList;
    }
}
