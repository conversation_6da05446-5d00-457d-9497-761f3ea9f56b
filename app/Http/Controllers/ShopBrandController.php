<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Models\StandardBrandModel;
use App\Http\Models\BigData\ShopInfoModel;
use App\Http\Models\BigData\ShopBrandModel;
use App\Http\Models\BigData\ShopBrandMappingModel;

class ShopBrandController extends Controller
{

    public function shopBrandList(Request $request)
    {
        return view('shopBrand.shopBrandList');
    }

    public function shopBrandMappingList(Request $request)
    {
        return view('shopBrand.shopBrandMappingList');
    }

    public function shopBrandMapping(Request $request)
    {
        $id = $request->input('id');
        $shopList = ShopInfoModel::pluck('shop_name', 'shop_id')->toArray();
        $shopBrandList = ShopBrandModel::pluck('brand_name', 'id')->toArray();
        if (!empty($id)) {
            $mapping = ShopBrandMappingModel::where('id', $id)->first()->toArray();
        }
        $lieBrandId = empty($mapping) ? $request->input('standard_brand_id') : $mapping['lie_brand_id'];
        $lieBrandList = StandardBrandModel::select([
            \DB::raw('brand_name as name'),
            \DB::raw('standard_brand_id as value')])
            ->where('status', StandardBrandModel::STATUS_OK)
            ->get()->toArray();
        return view('shopBrand.shopBrandMapping', [
            'shopList' => $shopList,
            'shopBrandList' => $shopBrandList,
            'mapping' => $mapping ?? [],
            'lieBrandList' => $lieBrandList ?? [],
            'lieBrandId' => $lieBrandId ?? '',
        ]);
    }

    public function saveShopBrand(Request $request)
    {
        $brandList = StandardBrandModel::select([
            \DB::raw('brand_name as name'),
            \DB::raw('standard_brand_id as value')])
            ->get()->toArray();

        $platformList = [];
        foreach (config('field.ShopPlatform') as $key => $value) {
            $platformList[] = [
                'name' => $value,
                'value' => $key,
            ];
        }
        return view('shopBrand.saveShopBrand', [
            'platformList' => $platformList,
        ]);
    }
}
