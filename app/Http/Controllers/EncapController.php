<?php

namespace App\Http\Controllers;

use App\Http\Services\EncapService;
use App\Http\Services\MenuService;
use App\Http\Models\EncapModel;
use App\Http\Transformers\EncapTransformer;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class EncapController extends Controller
{

    //品牌列表
    public function encapList(Request $request)
    {
        return view('encap.encapList');
    }

    public function saveEncap(Request $request)
    {
        $encapId = $request->input('encap_id');
        $encap = [];
        if ($encapId) {
            $service = new EncapService();
            $encap = $service->getEncap($encapId);
        }
        $data['encap'] = $encap;
        return view('encap.saveEncap', $data);
    }

    //映射标准品牌数据
    public function mappingStandardEncap(Request $request)
    {
        $encapId = $request->input('encap_id');
        $encap = (new EncapService())->getEncap($encapId);
        return view('encap.mappingStandardEncap',compact('encap'));
    }

}
