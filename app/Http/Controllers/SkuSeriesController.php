<?php

namespace App\Http\Controllers;

use App\Http\Models\BrandModel;
use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Models\SkuSeriesItemModel;
use App\Http\Models\SkuSeriesModel;
use App\Http\Services\SkuService;
use App\Http\Services\StandardBrandService;
use Illuminate\Http\Request;

class SkuSeriesController extends Controller
{

    /*
     * 商品系列列表
     */
    public function skuSeriesList(Request $request)
    {
        //获取筛选框的品牌数据
        $brandSelectValue = [];
        $selectBrandIds = $request->get('brand_id/condition');
        if ($selectBrandIds) {
            $brandSelectValue = BrandModel::whereIn('brand_id', explode(',', $selectBrandIds))->get()->toArray();
            $brandSelectValue = array_map(function ($value) {
                return Arr::only($value, ['brand_name', 'brand_id']);
            }, $brandSelectValue);
        }
        $data = [
            'brandSelectValue' => json_encode($brandSelectValue),
        ];
        $classModel = new PoolClassModel();
        $data['parentClassList'] = $classModel->where('parent_id', 0)
            ->where('class_type', PoolClassModel::CLASS_TYPE_IEDGE)
            ->pluck('class_name', 'class_id')->toArray();

        return view('skuSeries.skuSeriesList', $data);
    }

    //新增商品系列
    public function addSkuSeries(Request $request)
    {
        $id = $request->input('id');
        $classModel = new PoolClassModel();
        $data['parentClassList'] = $classModel->where('parent_id', 0)
            ->where('class_type', PoolClassModel::CLASS_TYPE_IEDGE)
            ->pluck('class_name', 'class_id')->toArray();
        if (!empty($id)) {
            $series = SkuSeriesModel::where('id', $id)->first()->toArray();
            $data['skuSeries'] = $series;
            if ($series['class_id1']) {
                $data['classList'] = $classModel->where('parent_id', '=',
                    $series['class_id1'])
                    ->pluck('class_name', 'class_id')->toArray();
            }
        }
        $data['standardBrandList'] = (new StandardBrandService())->getStandardBrandMap();

        return view('skuSeries.addSkuSeries', $data);
    }

    //编辑商品系列
    public function saveSkuSeries(Request $request)
    {
        $id = $request->input('id');
        $classModel = new PoolClassModel();
        $data['parentClassList'] = $classModel->where('parent_id', 0)
            ->where('class_type', PoolClassModel::CLASS_TYPE_IEDGE)
            ->pluck('class_name', 'class_id')->toArray();
        if (!empty($id)) {
            $series = SkuSeriesModel::where('id', $id)->first()->toArray();
            $data['skuSeries'] = $series;
            if ($series['class_id1']) {
                $data['classList'] = $classModel->where('parent_id', '=', $series['class_id1'])
                    ->pluck('class_name', 'class_id')->toArray();
            }
        }
        $data['standardBrandList'] = (new StandardBrandService())->getStandardBrandMap();

        return view('skuSeries.saveSkuSeries', $data);
    }

    public function batchUpdateItemSeries(Request $request)
    {

        $skuIds = $request->input('sku_ids');
        $skuIds = $skuIds ? explode(',', $skuIds) : [];
        $ids = $request->input('ids');
        $ids = $ids ? explode(',', $ids) : [];
        if (empty($skuIds) && empty($ids)) {
            return '请选择商品';
        }
        if (!empty($skuIds)) {
            $skuId = $skuIds[0];
        }

        if (!empty($ids)) {
            $skuId = SkuSeriesItemModel::where('id', $ids[0])->value('sku_id');
        }
        $sku = (new SkuService())->getSkuCacheInfo($skuId);
        $standardBrandId = !empty($sku['s_brand_id']) ? $sku['s_brand_id'] : 0;
        $classId1 = !empty($sku['class_id1']) ? $sku['class_id1'] : 0;
        $classId2 = !empty($sku['class_id2']) ? $sku['class_id2'] : 0;
        $data['seriesList'] = SkuSeriesModel::where('status', SkuSeriesModel::STATUS_ENABLE)
            ->where([
                'class_id1' => $classId1,
                'class_id2' => $classId2,
                'standard_brand_id' => $standardBrandId,
            ])
            ->get()->toArray();
        return view('skuSeries.batchUpdateItemSeries', $data);
    }
}
