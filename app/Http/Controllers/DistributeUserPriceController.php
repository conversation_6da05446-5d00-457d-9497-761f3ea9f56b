<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Models\SupplierModel;
use App\Http\Models\DistributeUserModel;
use App\Http\Models\DistributePriceModel;
use App\Http\Models\DistributeUserPriceModel;
use App\Http\Services\DistributePriceService;
use App\Http\Services\DistributeUserPriceService;
use App\Http\Models\Supplier\SupplierChannelModel;

class DistributeUserPriceController extends Controller
{
    public function distributeUserPriceList(Request $request)
    {
        $data['supplierListForXmSelect'] = (new SupplierModel())->getSupplierListForXmSelect();
        return view('distributeUserPrice.distributeUserPriceList', $data);
    }

    public function saveDistributeUserPrice(Request $request)
    {
        $id = $request->input('id');
        if (!empty($id)) {
            $data['user'] = DistributeUserModel::find($id)->toArray() ?? [];
            $data['priceList'] = DistributeUserPriceModel::where('user_id', $id)->get()->toArray() ?? [];
        }
        $data['supplierList'] = DistributePriceService::getSupplierList();
        $data['priceTypeList'] = [
            ['name' => '协议价', 'value' => 1],
            ['name' => '指导价', 'value' => 2],
        ];
        return view('distributeUserPrice.saveDistributeUserPrice', $data);
    }
}
