<?php

namespace App\Http\Controllers;

use App\Http\Models\StandardEncapModel;
use App\Http\Services\StandardEncapMappingService;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class StandardEncapMappingController extends Controller
{

    //映射列表
    public function standardEncapMappingList(Request $request)
    {
        return view('standardEncapMapping.standardEncapMappingList');
    }

    //添加标准品牌映射
    public function addStandardEncapMapping(Request $request)
    {
        $standardEncapId = $request->get('standard_encap_id');
        $standardEncapModel = new StandardEncapModel();
        $encap = $standardEncapModel->where('standard_encap_id', $standardEncapId)->first();
        $data['encap'] = $encap ? $encap->toArray() : [];
        return view('standardEncapMapping.addStandardEncapMapping', $data);
    }

    //添加标准品牌映射
    public function manageStandardEncapMapping(Request $request)
    {
        $standardEncapId = $request->get('standard_encap_id');
        $standardEncapModel = new StandardEncapModel();
        $encap = $standardEncapModel->where('standard_encap_id', $standardEncapId)->first()->toArray();
        $data['encap'] = $encap;
        return view('standardEncapMapping.manageStandardEncapMapping', $data);
    }
}
