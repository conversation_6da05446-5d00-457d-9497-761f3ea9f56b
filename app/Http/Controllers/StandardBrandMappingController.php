<?php

namespace App\Http\Controllers;

use App\Http\Models\StandardBrandModel;
use App\Http\Services\StandardBrandMappingService;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class StandardBrandMappingController extends Controller
{

    //映射列表
    public function standardBrandMappingList(Request $request)
    {
        return view('standardBrandMapping.standardBrandMappingList');
    }

    //添加标准品牌映射
    public function addStandardBrandMapping(Request $request)
    {
        $standardBrandId = $request->get('standard_brand_id');
        $standardBrandModel = new StandardBrandModel();
        $brand = $standardBrandModel->where('standard_brand_id', $standardBrandId)->first();
        $data['brand'] = $brand ? $brand->toArray() : [];
        return view('standardBrandMapping.addStandardBrandMapping', $data);
    }

    //添加标准品牌映射
    public function manageStandardBrandMapping(Request $request)
    {
        $standardBrandId = $request->get('standard_brand_id');
        $standardBrandModel = new StandardBrandModel();
        $brand = $standardBrandModel->where('standard_brand_id', $standardBrandId)->first()->toArray();
        $data['brand'] = $brand;
        return view('standardBrandMapping.manageStandardBrandMapping', $data);
    }
}
