<?php

namespace App\Http\Controllers;

use App\Http\Models\BigData\ShopAttrMappingModel;
use App\Http\Models\BigData\ShopAttrModel;
use App\Http\Models\BigData\ShopClassMappingModel;
use App\Http\Models\BigData\ShopClassModel;
use App\Http\Models\BigData\ShopInfoModel;
use App\Http\Models\PoolClass\PoolClassAttrModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;

class ShopAttrController extends Controller
{

    public function shopAttrList(Request $request)
    {
        return view('shopAttr.shopAttrList');
    }

    public function shopAttrMappingList(Request $request)
    {
        return view('shopAttr.shopAttrMappingList');
    }

    public function shopAttrMapping(Request $request)
    {
        $shopList = ShopInfoModel::pluck('shop_name', 'shop_id')->toArray();
        $shopAttrList = ShopAttrModel::pluck('attr_name', 'id')->toArray();
        $shopClassId = $request->get("shop_class_id");
        $platform = $request->input('platform');
        $id = $request->get("id");
        if ($shopClassId) {
            $classInitValue = ShopClassModel::where('id', $shopClassId)->select([
                \DB::raw('class_name as name'),
                \DB::raw('id as value')
            ])->get()->toArray();
            $classMapping = ShopClassMappingModel::where('shop_class_id', $shopClassId)->where('platform', $platform)->first()->toArray();
            $classId = ShopClassModel::where('id', $classMapping['shop_class_id'])->value('class_id');
            $shopAttrIds = ShopAttrModel::where('class_id', $classId)->where('platform', $platform)
                ->orderBy('is_required', 'desc')->get()->toArray();
            $shopAttrIds = array_filter($shopAttrIds, function ($item) {
                //如果状态为删除,并且is_mapping是否处理过为1的话,就不要这个参数了
                return !($item['status'] == 0 && $item['is_mapping'] == 1);
            });
            $shopAttrList = array_values($shopAttrList);
            $shopAttrIds = array_column($shopAttrIds, 'id');
            //这里是已经映射的列表
            $mappingList = ShopAttrMappingModel::with(['shop_attr'])->whereIn('shop_attr_id', $shopAttrIds)->get()->toArray();
            // $mappingList = array_reduce($mappingList, function ($carry, $item) {
            //     if (!isset($carry[$item['shop_attr_id']]) || $carry[$item['shop_attr_id']]['platform'] !== $item['platform']) {
            //         $carry[$item['shop_attr_id']] = $item;
            //     }
            //     return $carry;
            // }, []);
            foreach ($mappingList as &$mapping) {
                $mapping['input_type_name'] = config('field.ShopAttrInputType')[$mapping['input_type']] ?? '未知';
                if (!empty($mapping['shop_attr'])) {
                    $mapping['shop_attr']['input_type_name'] = config('field.ShopAttrInputType')[$mapping['shop_attr']['input_type']] ?? '未知';
                }
                if (isJsonDeserializable($mapping['value'])) {
                    $mapping['value'] = json_decode($mapping['value'], true);
                }
            }
            unset($mapping);
            //要把mappingList的坐标和shopAttrList的坐标一一对应,方便前端操作
            //还要分配对应的index给mappingList方便前端操作
            $result = [];
            foreach ($shopAttrIds as $index => $shopAttrId) {
                foreach ($mappingList as $mapping) {
                    //dump($shopAttrId, $mapping['shop_attr_id']);
                    if ($mapping['shop_attr_id'] == $shopAttrId) {
                        $result[$index] = $mapping;
                        break;
                    }
                }
                if (empty($result[$index])) {
                    $result[$index] = [];
                }
            }

            //寻找草稿
            $redis = Redis::connection('sku');
            $draftRedisKey = $shopClassId . '_' . $platform;
            $draft = $redis->hget('lie_shop_attr_mapping_draft', $draftRedisKey);
            $draft = json_decode($draft, true);
            if (!empty($draft)) {
                foreach ($draft as $key => &$item) {
                    if (isJsonDeserializable($item['value'])) {
                        $item['value'] = json_decode($item['value'], true);
                    }
                }
                unset($item);
            }
            //dd($draft);
        }
        return view('shopAttr.shopAttrMapping', [
            'shopList' => $shopList,
            'shopAttrList' => $shopAttrList,
            'classInitValue' => $classInitValue ?? '',
            'mappingList' => $result ?? '',
            'draft' => $draft ?? '',
        ]);
    }

    public function saveShopAttrMappingItem(Request $request)
    {
        $id = $request->input('id');
        //找到对应的数据
        $mapping = ShopAttrMappingModel::where('id', $id)->first()->toArray();
        $attrData = ShopAttrModel::where('id', $mapping['shop_attr_id'])->first()->toArray();
        $lieAttrData = PoolClassAttrModel::where('attr_id', $mapping['lie_attr_id'])->first();
        $lieAttrData = $lieAttrData ? $lieAttrData->toArray() : [
            'attr_name' => config('field.LieSpuColumnAttr')[$mapping['lie_attr_id']] ?? '未知参数',
        ];
        //dd($attrData);
        switch ($mapping['input_type']) {
            case 1:
            case 2:
                $mapping['value'] = json_decode($mapping['value'], true);
                $attrData['enum_value'] = json_decode($attrData['enum_value'], true);
                break;
            case 3:
                break;
            case 7:
                break;
            case 10:
                $mapping['value'] = json_decode($mapping['value'], true);
                $attrData['unit'] = json_decode($attrData['unit'], true);
                break;
        }
        return view('shopAttr.saveShopAttrMappingItem', [
            'mapping' => $mapping,
            'attrData' => $attrData,
            'lieAttrData' => $lieAttrData,
        ]);
    }
}
