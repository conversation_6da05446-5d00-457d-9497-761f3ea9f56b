<?php

namespace App\Http\Controllers;

use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Models\SampleClassModel;
use App\Http\Services\BrandService;
use App\Http\Services\MenuService;
use App\Http\Models\BrandModel;
use App\Http\Transformers\BrandTransformer;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class SampleClassController extends Controller
{

    public function sampleClassList(Request $request)
    {
        return view('sampleClass.sampleClassList');
    }

    public function saveSampleClass(Request $request)
    {
        $id = $request->input('id');
        $sampleClass = [];
        if ($id) {
            $sampleClass = SampleClassModel::where('id', $id)->first()->toArray();
        }
        $data['sampleClass'] = $sampleClass;
        return view('sampleClass.saveSampleClass', $data);
    }

}
