<?php

namespace App\Http\Controllers;

use App\Http\Models\BigData\ShopBrandMappingModel;
use App\Http\Models\BigData\ShopDistributeRuleSpuModel;
use App\Http\Models\BigData\ShopInfoModel;
use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Models\StandardBrandModel;
use App\Http\Models\SupplierModel;
use App\Http\Services\ShopDistributeRuleSpuService;
use Illuminate\Http\Request;

class ShopDistributeRuleSpuController extends Controller
{

    public function shopDistributeRuleSpuList(Request $request)
    {
        return view('shopDistributeRuleSpu.shopDistributeRuleSpuList');
    }

    public function saveShopDistributeRuleSpu(Request $request)
    {
        $id = $request->input('id');
        if (!empty($id)) {
            $rule = ShopDistributeRuleSpuModel::find($id)->toArray();
            $shopInfoList = ShopInfoModel::where('platform', $rule['platform'])->select(['shop_id', 'shop_name'])->get()->toArray();
        }
        //还要找出已经映射的品牌
        $brandList = StandardBrandModel::select([
            \DB::raw('brand_name as name'),
            \DB::raw('standard_brand_id as value')])
            ->get()->toArray();

        $classList = PoolClassModel::where('class_type', PoolClassModel::CLASS_TYPE_NORMAL)->select([
            \DB::raw('class_name as name'),
            \DB::raw('class_id as value')])
            ->get()->toArray();

        return view('shopDistributeRuleSpu.saveShopDistributeRuleSpu', [
            'brandList' => $brandList,
            'classList' => $classList,
            'rule' => $rule ?? [],
            'shopInfoList' => $shopInfoList ?? [],
        ]);
    }
}
