<?php

namespace App\Http\Controllers;

use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Models\SampleClassModel;
use App\Http\Models\SampleModel;
use App\Http\Services\BrandService;
use App\Http\Services\MenuService;
use App\Http\Models\BrandModel;
use App\Http\Transformers\BrandTransformer;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class SampleController extends Controller
{
    public function sampleList(Request $request)
    {
        $classList = SampleClassModel::where('status', SampleClassModel::STATUS_OK)->pluck('class_name', 'id')->toArray();
        $data['classList'] = $classList;
        return view('sample.sampleList', $data);
    }

    public function saveSample(Request $request)
    {
        $id = $request->input('id');
        $classList = SampleClassModel::where('status', SampleClassModel::STATUS_OK)->pluck('class_name', 'id')->toArray();
        $data['classList'] = $classList;
        $data['sample'] = SampleModel::where('id', $id)->first()->toArray();
        return view('sample.saveSample', $data);
    }


    public function sampleUploadLog(Request $request)
    {
        $admin = CmsUserInfoModel::getUserList();
        $data['adminId'] = $admin;
        $data['status'] = config('field.SampleUploadStatus');
        return view('sample.sampleUploadLog', $data);
    }
}
