<?php

namespace App\Http\Controllers;

use App\Http\Models\PoolClass\ClassAttrUnitConversionModel;
use App\Http\Models\PoolClass\ClassAttrUnitModel;
use App\Http\Models\PoolClass\ClassAttrValueModel;
use App\Http\Models\PoolClass\PoolClassAttrModel;
use App\Http\Models\PoolClass\PoolClassModel;
use Illuminate\Http\Request;

class ClassAttrUnitController extends Controller
{
    /*
     * 参数值单位列表
     */
    public function classAttrUnitList(Request $request)
    {
        return view('classAttrUnit.classAttrUnitList');
    }

    /*
     * 新增/编辑参数值单位
     */
    public function saveClassAttrUnit(Request $request)
    {
        $attrUnitId = $request->input('attr_unit_id');
        $data = [];
        if ($attrUnitId) {
            $unitModel = new ClassAttrUnitModel();
            $field = ['attr_unit_id', 'attr_unit_name', 'remark', 'status'];
            $find = $unitModel->where('attr_unit_id', '=', $attrUnitId)->whereIn('status', [1, 2])
                ->select($field)->first()->toArray();
            if (!$find) {
                return ('没有这个单位');
            }
            $data['info'] = $find;
        }
        return view('classAttrUnit.saveClassAttrUnit', $data);
    }

    /*
     * 新增/编辑单位换算
     */
    public function saveClassAttrValueUnitConversion(Request $request)
    {
        $conversion_id = $request->input('conversion_id');
        $attrUnitId = $request->input('attr_unit_id');
        if (!$attrUnitId) {
            return ('没有选择单位');
        }
        $unitModel = new ClassAttrUnitModel();
        $unit = $unitModel->where('attr_unit_id', '=', $attrUnitId)->whereIn('status', [1, 2])->select('attr_unit_id',
            'attr_unit_name')->first();

        if (!$unit) {
            return ('这个单位不允许添加换算');
        }

        $data['unit'] = $unit->toArray();
        if ($conversion_id) {
            $conversionModel = new ClassAttrUnitConversionModel();
            $data['info'] = $conversionModel->where('conversion_id', '=', $conversion_id)
                ->select('attr_unit_id_1', 'attr_unit_id_2', 'conversion_id', 'ratio', 'status')
                ->with(['AttrUnitId1', 'AttrUnitId2'])
                ->first();

            if (!$data['info']) {
                return ('未查找到换算关系');
            }
            $data['info'] = $data['info']->toArray();
            if ($attrUnitId == $data['info']['attr_unit_id_1']) {
                $data['toUnit'] = $data['info']['attr_unit_id2'];
                $data['toUnit']['ratio'] = $data['info']['ratio'];
            } elseif ($attrUnitId == $data['info']['attr_unit_id_2']) {
                $data['toUnit'] = $data['info']['attr_unit_id1'];
                $data['toUnit']['ratio'] = 1 / $data['info']['ratio'];
            }
        }
        return view('classAttrUnit.saveClassAttrValueUnitConversion', $data);
    }

}
