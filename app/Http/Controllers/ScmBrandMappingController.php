<?php

namespace App\Http\Controllers;

use App\Http\Models\ScmBrandMappingModel;
use App\Http\Models\ScmBrandModel;
use App\Http\Services\ScmBrandMappingService;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class ScmBrandMappingController extends Controller
{

    //映射列表
    public function scmBrandMappingList(Request $request)
    {
        return view('scmBrandMapping.scmBrandMappingList');
    }

    //添加标准品牌映射
    public function addScmBrandMapping(Request $request)
    {
        $scmBrandId = $request->get('scm_brand_id');
        $scmBrandModel = new ScmBrandMappingModel();
        $brand = $scmBrandModel->where('scm_brand_id', $scmBrandId)->first();
        $data['brand'] = $brand ? $brand->toArray() : [];
        return view('scmBrandMapping.addScmBrandMapping', $data);
    }

    //添加标准品牌映射
    public function manageScmBrandMapping(Request $request)
    {
        $scmBrandId = $request->get('scm_brand_id');
        $scmBrandModel = new ScmBrandModel();
        $brand = $scmBrandModel->where('scm_brand_id', $scmBrandId)->first()->toArray();
        $data['brand'] = $brand;
        return view('scmBrandMapping.manageScmBrandMapping', $data);
    }
}
