<?php

namespace App\Http\Controllers;

use App\Http\Models\TrainingDataModel;
use Illuminate\Http\Request;

class TrainingDataController extends Controller
{
    // 训练数据列表
    public function trainingDataList(Request $request)
    {
        return view('trainingData.trainingDataList');
    }

    // 保存训练数据
    public function saveTrainingData(Request $request)
    {
        $trainingDataId = $request->input('id');
        $data = [];
        if (!empty($trainingDataId)) {
            $model = new TrainingDataModel();
            $trainingData = $model->where('id', $trainingDataId)->first();
            $data['trainingData'] = $trainingData ? $trainingData->toArray() : [];
        }
        return view('trainingData.saveTrainingData', $data);
    }
}
