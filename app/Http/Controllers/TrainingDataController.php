<?php

namespace App\Http\Controllers;

use App\Http\Models\TrainingDataModel;
use Illuminate\Http\Request;

class TrainingDataController extends Controller
{
    // 训练数据列表
    public function trainingDataList(Request $request)
    {
        return view('trainingData.trainingDataList');
    }

    // 保存训练数据
    public function saveTrainingData(Request $request)
    {
        $trainingDataId = $request->input('id');
        $data = [];
        if (!empty($trainingDataId)) {
            $model = new TrainingDataModel();
            $trainingData = $model->where('id', $trainingDataId)->first();
            $data['trainingData'] = $trainingData ? $trainingData->toArray() : [];
        }
        return view('trainingData.saveTrainingData', $data);
    }

    // 品牌训练数据列表
    public function brandTrainingDataList(Request $request)
    {
        return view('trainingData.brandTrainingDataList');
    }

    // 保存品牌训练数据
    public function saveBrandTrainingData(Request $request)
    {
        $trainingDataId = $request->input('id');
        $data = [];
        if (!empty($trainingDataId)) {
            $model = new TrainingDataModel();
            $trainingData = $model->where('id', $trainingDataId)->where('type', TrainingDataModel::TYPE_BRAND)->first();
            $data['trainingData'] = $trainingData ? $trainingData->toArray() : [];
        }
        return view('trainingData.saveBrandTrainingData', $data);
    }

    // 型号训练数据列表
    public function modelTrainingDataList(Request $request)
    {
        return view('trainingData.modelTrainingDataList');
    }

    // 保存型号训练数据
    public function saveModelTrainingData(Request $request)
    {
        $trainingDataId = $request->input('id');
        $data = [];
        if (!empty($trainingDataId)) {
            $model = new TrainingDataModel();
            $trainingData = $model->where('id', $trainingDataId)->where('type', TrainingDataModel::TYPE_MODEL)->first();
            $data['trainingData'] = $trainingData ? $trainingData->toArray() : [];
        }
        return view('trainingData.saveModelTrainingData', $data);
    }

    // 分类训练数据列表
    public function categoryTrainingDataList(Request $request)
    {
        return view('trainingData.categoryTrainingDataList');
    }

    // 保存分类训练数据
    public function saveCategoryTrainingData(Request $request)
    {
        $trainingDataId = $request->input('id');
        $data = [];
        if (!empty($trainingDataId)) {
            $model = new TrainingDataModel();
            $trainingData = $model->where('id', $trainingDataId)->where('type', TrainingDataModel::TYPE_CATEGORY)->first();
            $data['trainingData'] = $trainingData ? $trainingData->toArray() : [];
        }
        return view('trainingData.saveCategoryTrainingData', $data);
    }
}
