<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Models\SampleLogModel;
use App\Http\Models\SampleModel;
use App\Http\Services\AlikeSpuService;
use App\Http\Services\BrandService;
use App\Http\Services\SampleService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Response;

class SampleApiController extends Controller
{

    public function getSampleList(Request $request)
    {
        $map = $request->all();
        $data = SampleService::getSampleList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    public function uploadSample(Request $request)
    {
        $file = $request->file('file');
        $result = SampleService::uploadSample($file);
        if (!$result) {
            return $this->setError('上传处理失败,请联系管理员');
        }

        return $this->setSuccess('上传成功,请耐心等待处理完成');
    }

    public function saveSample(Request $request)
    {
        $id = $request->input('id');
        $maxNumber = $request->input('max_number');
        $sampleStock = $request->input('sample_stock');
        $classId = $request->input('class_id');
        if (empty($maxNumber)) {
            return $this->setError('单次申请数量不能为空');
        }
        if (empty($sampleStock)) {
            return $this->setError('样品库存不能为空');
        }
        if (empty($classId)) {
            return $this->setError('样品分类不能为空');
        }
        $result = SampleService::saveSample($id, $maxNumber, $sampleStock, $classId);
        if (!$result) {
            return $this->setError('保存样品失败,请联系管理员');
        }

        return $this->setSuccess('保存样品成功');
    }

    public function deleteSample(Request $request)
    {
        $isBatch = $request->input('is_batch');
        if ($isBatch == 1) {
            $ids = $request->input('ids');
            $result = SampleService::batchDeleteSample($ids);
        } else {
            $id = $request->get('id');
            $result = SampleService::deleteSample($id);
        }
        if (!$result) {
            return $this->setError('删除失败,请联系管理员');
        }
        if ($isBatch) {
            foreach ($request->input('ids') as $id) {
                Redis::connection('sku')->hdel('lie_sample_list', $id);
            }
        } else {
            Redis::connection('sku')->hdel('lie_sample_list', $request->get('id'));
        }
        return $this->setSuccess('删除成功');
    }

    //上传记录
    public function getSampleUploadLogList(Request $request)
    {
        $map = $request->all();
        $list = SampleService::getSampleUploadList($map);
        return $this->setSuccessData($list['data'], $list['total']);
    }

    public function downloadUploadResult(Request $request)
    {
        $id = $request->input('id');
        $log = SampleLogModel::where('id', $id)->first()->toArray();
        $resultFileUrl = $log['result_file_url'];
        $filePath = $resultFileUrl;
        $path = storage_path() . '/' . 'app/' . $filePath;
        if ($path) {
            return Response::download($path);
        }

        return "找不到结果文件";
    }
}
