<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Services\TaskLogService;
use App\Http\Services\OperationLogService;


class OperationLogApiController extends Controller
{

    //获取任务日志
    public function getOperationLogList(Request $request)
    {
        $objName = $request->input('obj_name', '');
        $objId = $request->input('obj_id', '');
        $map = $request->all();
        $result = OperationLogService::getOperationLogList($objName, $objId, $map);
        return $this->setSuccessData($result['data'], $result['total']);
    }

}
