<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Imports\AlikeSpuImport;
use App\Exports\AgentBrandExport;
use App\Http\Services\SpuService;
use App\Http\Controllers\Controller;
use App\Http\Models\AgentBrandModel;
use App\Imports\AgentBrandSpuImport;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Models\AgentBrandSpuModel;
use App\Http\Services\AgentBrandService;
use App\Exceptions\InvalidRequestException;
use App\Http\Validator\AgentBrandValidator;

class AgentBrandApiController extends Controller
{

    //获取代理品牌列表
    public function getAgentBrandList(Request $request)
    {
        $map = $request->all();
        $data = (new AgentBrandService())->getAgentBrandList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    //保存代理品牌
    public function saveAgentBrand(Request $request)
    {
        $data = $request->only([
            'standard_brand_id',
            'id',
            'agent_certificate',
            'agent_certificate_name',
            'application_area',
            'certificate_effective_time',
            'pm_user_id',
            'is_certificate_permanent',
            'agent_class_id',
            'main_product_class_ids',
            'supplier_code',
            'agent_brand_level',
        ]);
        if (!empty($data['is_certificate_permanent'])) {
            $data['certificate_effective_time'] = 0;
        } else {
            $data['is_certificate_permanent'] = 0;
            $data['certificate_effective_time'] = strtotime($data['certificate_effective_time']);
        }
        if (!empty($data['application_area']) && is_array($data['application_area'])) {
            $data['application_area'] = implode(',', array_keys($data['application_area']));
        } else {
            $data['application_area'] = [];
        }
        $validator = new AgentBrandValidator();
        $check = $validator->check($data);
        if ($check !== true) {
            return $this->setError($check);
        }
        $result = (new AgentBrandService())->saveAgentBrand($data);
        if (!$result) {
            return $this->setError('保存代理品牌失败');
        }

        return $this->setSuccess('保存代理品牌成功');
    }

    //禁用代理品牌
    public function changeAgentBrandStatus(Request $request)
    {
        $id = $request->input('id');
        $status = $request->input('status');
        $result = (new AgentBrandService())->changeAgentBrandStatus($id, $status);
        if (!$result) {
            return $this->setError('禁用失败');
        }

        return $this->setSuccess();
    }

    //获取代理品牌产品列表
    public function getAgentBrandSpuList(Request $request)
    {
        $map = $request->only(['agent_brand_id']);
        if (empty($map['agent_brand_id'])) {
            return $this->setSuccessData();
        }
        $data = (new AgentBrandService())->getAgentBrandSpuList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    //新增代理品牌产品
    public function saveAgentBrandSpu(Request $request)
    {
        $data = $request->only([
            'id',
            'spu_name',
            'type_name',
            'agent_brand_id',
        ]);
        if (empty($data['spu_name'])) {
            return $this->setError('商品型号不能为空');
        }
        //if (empty($data['type_name'])) {
        //    return $this->setError('产品类型不能为空');
        //}
        $result = (new AgentBrandService())->saveAgentBrandSpu($data);
        if (!$result) {
            return $this->setError('新增代理品牌型号失败');
        }

        return $this->setSuccess();
    }


    //导入代理品牌产品数据
    public function importAgentBrandSpu(Request $request)
    {
        ini_set('memory_limit', -1);
        $agentBrandId = (int)$request->input('agent_brand_id');
        if (empty($agentBrandId)) {
            return $this->setError('代理品牌id不能为空');
        }
        // 获取上传的文件
        $file = $request->file('upload');
        // 获取上传文件的路径
        // 打开 CSV 文件
        $import = new AgentBrandSpuImport();
        Excel::import($import, $file);
        $rows = $import->getData();
        $excelData = [];
        $now = time();
        foreach ($rows as $key => $row) {
            if ($key == 0) {
                continue;
            }
            $spuName = $row[0];
            $typeName = $row[1];
            $excelData[] = [
                'spu_name' => $spuName,
                'type_name' => $typeName,
            ];
        }
        if (empty($excelData)) {
            throw new InvalidRequestException('上传的产品不能为空');
        }
        $data = [];
        //组装数据
        foreach ($excelData as $item) {
            $temp = [
                'spu_name' => $item['spu_name'],
                'create_time' => $now,
                'create_name' => $request->user->name,
                'create_uid' => $request->user->userId,
                'agent_brand_id' => $agentBrandId,
            ];
            if (!empty($item['type_name'])) {
                $temp['type_name'] = $item['type_name'];
                if (!in_array($item['type_name'], config('field.AgentBrandSpuTypeName'))) {
                    throw new InvalidRequestException('存在不规范的产品类型,产品类型只能为 ( 主打产品 | 替代料 | 其它 ) 其中一个');
                }
            }
            $data[] = $temp;
        }
        $standardBrandId = AgentBrandModel::where('id', $agentBrandId)->value('standard_brand_id');
        $filteredData = [];
        $existsCount = 0;
        $notValidSpuCount = 0;
        //过滤掉已经存在的
        foreach (array_chunk($data, 100) as $items) {
            $spuNameList = array_column($items, 'spu_name');
            $existSpuNameList = AgentBrandSpuModel::whereIn('spu_name', $spuNameList)
                ->where('agent_brand_id', (int)$request->input('agent_brand_id'))
                ->pluck('spu_name')->toArray();
            if (empty($items)) {
                continue;
            }
            foreach ($items as $item) {
                if (!in_array($item['spu_name'], $existSpuNameList)) {
                    //还要检查这个标准品牌id和产品是否对的上
                    $spuCache = (new SpuService())->getSpuBySpuNameAndStandardBrandId($item['spu_name'], $standardBrandId);
                    if ($spuCache) {
                        $filteredData[] = $item;
                    } else {
                        $notValidSpuCount++;
                    }
                } else {
                    $existsCount++;
                }
            }
        }
        foreach (array_chunk($filteredData, 1000) as $items) {
            AgentBrandSpuModel::insert($items);
        }

        return $this->setSuccess('导入成功,其中 ' . $existsCount . ' 个产品已存在;对应代理品牌下面没有对应的spu的产品有 ' . $notValidSpuCount . ' 个');
    }

    public function deleteAgentBrandSpu(Request $request)
    {
        $id = $request->input('id');
        $ids = $request->input('ids');
        if (!empty($id)) {
            $result = (new AgentBrandService())->deleteAgentBrandSpu($id);
        } else {
            $result = (new AgentBrandService())->batchDeleteAgentBrandSpu($ids);
        }
        if (!$result) {
            return $this->setError('删除失败');
        }

        return $this->setSuccess();
    }

    //导出代理品牌
    public function exportAgentBrand(Request $request)
    {
        $params = $request->input('params');
        $map = json_decode($params, true);
        return Excel::download(new AgentBrandExport($map), '代理品牌导出.xlsx');
    }
}
