<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Services\ScmBrandService;
use App\Exports\ScmBrandExport;

class ScmBrandApiController extends Controller
{

    public function getScmBrandList(Request $request)
    {
        $map = $request->all();
        $data = (new ScmBrandService())->getScmBrandList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    /**
 * 导出供应链品牌
 */
public function exportScmBrand(Request $request)
{
    $params = json_decode($request->get('params', '{}'), true);

    // 查询数据
    $list = (new ScmBrandService())->getScmBrandList($params, true);

    // 准备导出数据
    $data = [];
    foreach ($list as $item) {
        $data[] = [
            'erp_brand_sn' => $item['erp_brand_sn'],
            'erp_brand_name' => $item['erp_brand_name'],
            'status_name' => $item['status_name'],
            'add_time' => $item['add_time'],
            'update_time' => $item['update_time']
        ];
    }

    // 设置表头
    $header = [
        'erp_brand_sn' => '编码',
        'erp_brand_name' => '品牌名称',
        'status_name' => '状态',
        'add_time' => '创建时间',
        'update_time' => '更新时间'
    ];

        return Excel::download(new ScmBrandExport($data, $header), '供应链品牌列表.xlsx');
    }

}
