<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Models\AbilityLevelRuleModel;
use App\Http\Services\AbilityLevelRuleService;
use App\Http\Validator\AbilityLevelRuleValidator;
use App\Exceptions\InvalidRequestException;

class AbilityLevelRuleApiController extends Controller
{
    public function getAbilityLevelRuleList(Request $request)
    {
        $map = $request->all();
        $data = (new AbilityLevelRuleService())->getAbilityLevelRuleList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    public function saveAbilityLevelRule(Request $request)
    {
        $params = $request->only([
            'id',
            'supplier_id',
            'supplier_code',
            'level',
            'source'
        ]);
        $params['supplier_code'] = trim($params['supplier_code'], ',');
        $validator = new AbilityLevelRuleValidator();
        $validator->check($params);

        $result = (new AbilityLevelRuleService())->saveAbilityLevelRule($params);

        if (!$result) {
            return $this->setError('保存履约能力规则失败');
        }

        return $this->setSuccess('保存履约能力规则成功');
    }

    public function updateStatus(Request $request)
    {
        $id = $request->input('id');
        $status = $request->input('status');

        if (empty($id)) {
            return $this->setError('ID不能为空');
        }

        if (!in_array($status, [AbilityLevelRuleModel::STATUS_ENABLED, AbilityLevelRuleModel::STATUS_DISABLED])) {
            return $this->setError('状态值不正确');
        }

        $result = (new AbilityLevelRuleService())->updateStatus($id, $status);

        if ($result) {
            return $this->setSuccess('更新状态成功');
        } else {
            return $this->setError('更新状态失败');
        }
    }

    public function deleteAbilityLevelRule(Request $request)
    {
        $id = $request->input('id');

        if (empty($id)) {
            return $this->setError('ID不能为空');
        }

        $result = (new AbilityLevelRuleService())->deleteAbilityLevelRule($id);

        if ($result) {
            return $this->setSuccess('删除规则成功');
        } else {
            return $this->setError('删除规则失败');
        }
    }

    public function checkSupplierCodeExists(Request $request)
    {
        $supplierCode = $request->input('supplier_code');
        $id = $request->input('id', 0);

        if (empty($supplierCode)) {
            return $this->setError('供应商编码不能为空');
        }

        $exists = (new AbilityLevelRuleService())->checkSupplierCodeExists($supplierCode, $id);

        if ($exists) {
            return $this->setError('该供应商编码已存在规则');
        } else {
            return $this->setSuccess('供应商编码可用');
        }
    }
}
