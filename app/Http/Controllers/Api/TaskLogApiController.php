<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Services\TaskLogService;
use Illuminate\Http\Request;


class TaskLogApiController extends Controller
{

    //获取任务日志
    public function getTaskLogList(Request $request)
    {
        $type = $request->input('type', 1);
        $map = $request->all();
        $result = TaskLogService::getTaskLogList($type, $map);
        return $this->setSuccessData($result['data'], $result['total']);
    }

}
