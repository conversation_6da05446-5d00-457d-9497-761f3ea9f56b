<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Models\TaskLogModel;
use App\Http\Controllers\Controller;
use App\Http\Services\TaskLogService;
use Illuminate\Support\Facades\Response;


class TaskLogApiController extends Controller
{

    //获取任务日志
    public function getTaskLogList(Request $request)
    {
        $type = $request->input('type', 1);
        $map = $request->all();
        $result = TaskLogService::getTaskLogList($type, $map);
        return $this->setSuccessData($result['data'], $result['total']);
    }


     //下载sku更新结果
     public function downloadTaskLogResult(Request $request)
     {
         $id = $request->input('id');
         $log = TaskLogModel::where('id', $id)->first()->toArray();
         $resultFileUrl = $log['result_file_url'];
         $filePath = $resultFileUrl;
         $path = storage_path() . '/' . 'app/' . $filePath;
         if ($path) {
             return Response::download($path);
         }

         return "找不到结果文件";
     }
}
