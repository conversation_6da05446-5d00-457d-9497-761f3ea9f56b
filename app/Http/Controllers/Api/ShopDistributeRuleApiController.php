<?php

namespace App\Http\Controllers\Api;


use App\Http\Controllers\Controller;
use App\Http\Services\ShopDistributeRuleService;
use App\Http\Validator\ShopDistributeRulerValidator;
use Illuminate\Http\Request;


class ShopDistributeRuleApiController extends Controller
{

    public function getShopDistributeRuleList(Request $request)
    {
        $map = $request->all();
        $data = ShopDistributeRuleService::getShopDistributeRuleList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    //根据平台来获取分类给xm-select使用
    public function getShopDistributeRuleByPlatform(Request $request)
    {
        $platform = $request->input('platform');
        $result = ShopDistributeRuleService::getShopDistributeRuleByPlatform($platform);
        return $this->setSuccessData($result);
    }

    public function saveShopDistributeRule(Request $request)
    {
        $params = $request->only([
            'id',
            'platform',
            'shop_id',
            'supplier_id_list',
            'brand_id_list',
            'brand_type',
            'eccn',
            'eccn_file_url',
            'eccn_file_name',
            'is_copy',
            'canals'
        ]);

        $validator = new ShopDistributeRulerValidator();
        $validator->check($params);
        $result = ShopDistributeRuleService::saveShopDistributeRule($params);

        if (!$result) {
            return $this->setError('保存分发规则失败');
        }

        return $this->setSuccess('保存分发规则成功');

    }

    public function updateDistributeRuleStatus(Request $request)
    {
        $status = $request->input('status');
        $id = $request->input('id');
        $result = ShopDistributeRuleService::updateDistributeRuleStatus($id, $status);
        if (!$result) {
            return $this->setError('修改状态失败');
        }

        return $this->setSuccess('修改状态成功');
    }

    public function deleteShopDistributeRule(Request $request)
    {
        $id = $request->input('id');
        if (empty($id)) {
            return $this->setError('需要删除的分发规则id不能为空');
        }
        $result = ShopDistributeRuleService::deleteShopDistributeRule($id);
        if (!$result) {
            return $this->setError('删除分发规则失败');
        }

        return $this->setSuccess('删除分发规则成功');
    }


}
