<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Services\DistributePriceService;
use App\Http\Validator\DistributePriceValidator;

class DistributePriceApiController extends Controller
{
    public function getDistributePriceList(Request $request)
    {
        $map = $request->all();
        $data = (new DistributePriceService())->getDistributePriceList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    public function saveDistributePrice(Request $request)
    {
        $params = $request->only([
            'id',
            'supplier_id',
            'standard_brand_id',
            'agreement_price_coefficient',
            'guide_price_coefficient',
            'sku_id',
            'sku_id_file_url',
            'sku_id_file_name',
            'eccn',
            'eccn_file_url',
            'eccn_file_name',
            'canal',
        ]);
        $validator = new DistributePriceValidator();
        $validator->check($params);
        $isCopy = request()->input('copy') ?? 0;
        //非专营渠道为空
        if ($params['supplier_id'] != 17) {
            $params['canal'] = '';
        }
        $result = (new DistributePriceService())->saveDistributePrice($params, $isCopy);

        if (!$result) {
            return $this->setError('保存分发规则失败');
        }

        return $this->setSuccess('保存分发规则成功');
    }

    public function deleteDistributePrice(Request $request)
    {
        $id = $request->input('id');
        if (empty($id)) {
            return $this->setError('需要删除的分发规则id不能为空');
        }
        $result = (new DistributePriceService())->deleteDistributePrice($id);
        if (!$result) {
            return $this->setError('删除分发规则失败');
        }

        return $this->setSuccess('删除分发规则成功');
    }

    public function updateStatus(Request $request)
    {
        $id = $request->input('id');
        $status = $request->input('status');
        $result = (new DistributePriceService())->updateStatus($id, $status);
        if (!$result) {
            return $this->setError('修改状态失败');
        }

        return $this->setSuccess('修改状态成功');
    }
}
