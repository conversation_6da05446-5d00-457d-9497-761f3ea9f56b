<?php

namespace App\Http\Controllers\Api;

use App\Exports\StandardBrandExport;
use App\Http\Controllers\Controller;
use App\Http\Services\SpuService;
use App\Http\Services\StandardBrandService;
use App\Http\Transformers\StandardBrandSpuTransformer;
use App\Http\Validator\StandardBrandValidator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class StandardBrandApiController extends Controller
{
    public function getStandardBrandList(Request $request)
    {
        $map = $request->all();
        $service = new StandardBrandService();
        $data = $service->getStandardBrandList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    public function disableStandardBrand(Request $request)
    {
        $brandId = $request->input('standard_brand_id');
        $service = new StandardBrandService();
        if ($service->checkHasMappingBrand($brandId)) {
            return $this->setError('该品牌有相关的映射品牌,不能被禁用');
        }
        $result = $service->disableStandardBrand($brandId);
        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('禁用失败');
        }
    }

    public function enableStandardBrand(Request $request)
    {
        $brandId = $request->input('standard_brand_id');
        $service = new StandardBrandService();
        $result = $service->enableStandardBrand($brandId);
        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('禁用失败');
        }
    }

    public function getMainSpuList(Request $request)
    {
        $standardBrandId = $request->get('standard_brand_id');
        $map = $request->all();
        $service = new StandardBrandService();
        $data = $service->getMainSpuList($standardBrandId, $map);
        $data['data'] = (new StandardBrandSpuTransformer())->listTransformer($data['data']);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    public function saveStandardBrand(Request $request)
    {
        $map = $request->all();
        $validator = new StandardBrandValidator();
        $check = $validator->check();
        if ($check !== true) {
            return $this->setError($check);
        }
        $service = new StandardBrandService();
        $result = $service->saveStandardBrand($map);
        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('修改失败');
        }
    }

    //保存新增的主要产品
    public function saveStandardBrandSpu(Request $request)
    {
        $data = $request->only([
            'type',
            'standard_brand_id',
            'spu_name',
            'brand_name',
            'sort',
            'spu_id',
        ]);
        $standardBrandId = $request->get('standard_brand_id');
        if (!$standardBrandId) {
            return $this->setError('标准品牌id不能为空');
        }
        if (empty($data['spu_id']) && (empty($data['spu_name']) || empty($data['brand_name']))) {
            return $this->setError('不填写spuId的话,商品名称或者品牌名称不能为空');
        }
        $spuService = new SpuService();
        $spuRedis = Redis::connection('spu');
        $redis = Redis::connection('sku');
        if ($data['spu_id']) {
            $spu = $spuRedis->hget('spu', $data['spu_id']);
            if (empty($spu)) {
                return $this->setError('输入的spu_id找不到对应的商品');
            }
            $spuId = $data['spu_id'];
        } else {
            $spuId = $spuService->getSpuId($data['brand_name'], $data['spu_name']);
            if (!$spuId) {
                return $this->setError('平台找不到对应的商品(spu_id),请修正后提交');
            }
        }

        $service = new StandardBrandService();
        $data['spu_id'] = $spuId;
        $result = $service->addStandardBrandSpu($spuId, $data);
        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('添加失败');
        }
    }

    //删除标准品牌相关商品
    public function deleteStandardBrandSpu(Request $request)
    {
        $ids = $request->get('ids');
        $ids = explode(',', $ids);
        $service = new StandardBrandService();
        $result = $service->deleteStandardBrandSpu($ids);
        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('操作失败');
        }
    }

    public function importStandardBrandSpu(Request $request)
    {
        $standardBrandId = $request->get('standard_brand_id');
        if (!$standardBrandId) {
            return $this->setError('标准品牌id不能为空');
        }
        $file = $request->file('upload');
        if (empty($file)) {
            return $this->setError('上传的导入文件不能为空');
        }
        $service = new StandardBrandService();
        $result = $service->importStandardBrandSpu($standardBrandId, $file);
        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('导入失败');
        }
    }

    public function exportStandardBrand(Request $request)
    {
        $params = $request->input('params');
        $map = json_decode($params, true);
        return Excel::download(new StandardBrandExport($map), '标准品牌导出.xlsx');
    }

    public function searchStandardBrand(Request $request)
    {
        $brandName = $request->input('brand_name');
        $result = (new StandardBrandService())->searchStandardBrand($brandName);
        echo json_encode($result);
    }

    //获取单个标品的信息
    public function getStandardBrandById(Request $request)
    {
        $standardBrandId = $request->input('standard_brand_id');
        $standardBrand = (new StandardBrandService())->getStandardBrandById($standardBrandId);
        return $this->setSuccessData($standardBrand);
    }
}
