<?php

namespace App\Http\Controllers\Api;

use App\Exports\StandardEncapExport;
use App\Exports\StandardEncapMappingExport;
use App\Http\Controllers\Controller;
use App\Http\Models\StandardEncapMappingModel;
use App\Http\Models\StandardEncapModel;
use App\Http\Services\QueueService;
use App\Http\Services\StandardEncapMappingService;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class StandardEncapMappingApiController extends Controller
{
    public function Entrance(Request $request, $id)
    {
        return $this->$id($request, $id);
    }

    //封装映射列表
    public function getStandardEncapMappingList(Request $request)
    {
        $field = ['encap_name', 'standard_encap_name', 'create_time','standard_encap_id'];
        $map = $request->only($field);
        $map = array_map(function ($value) {
            return trim($value);
        }, $map);
        $service = new StandardEncapMappingService();
        $data = $service->getStandardMappingList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    //查找未映射的封装
    public function findNotMappingEncap(Request $request)
    {
        $field = ['encap_name','limit','page','encap_id'];
        $map = $request->only($field);
        $service = new StandardEncapMappingService();
        $data = $service->findNotMappingStandEncap($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    //添加映射的接口
    public function addStandardEncapMapping(Request $request)
    {
        $map = $request->only(['standard_encap_id', 'mapping_encap_ids']);
        $service = new StandardEncapMappingService();
        $result = $service->addStandardEncapMapping($map['standard_encap_id'], $map['mapping_encap_ids']);
        if (!$result) {
            return $this->setError('添加映射失败');
        }

        return $this->setSuccess('添加映射成功');
    }


    //删除映射
    public function deleteStandardEncapMapping(Request $request)
    {
        $id = $request->get('id');
        $service = new StandardEncapMappingService();
        $result = $service->deleteStandardMapping($id);
        if (!$result) {
            return $this->setError('删除映射失败');
        }

        return $this->setSuccess('删除映射成功');
    }

    //导出映射
    public function exportStandardEncapMapping(Request $request)
    {
        $params = $request->input('params');
        $map = json_decode($params, true);
        return Excel::download(new StandardEncapMappingExport($map), '封装映射导出.xlsx');
    }


    //导入封装
    public function importStandardEncapMapping(Request $request)
    {
        $file = $request->file('upload');
        StandardEncapMappingService::uploadStandardEncapMapping($file);
        return $this->setSuccess('上传成功,请耐心等待处理完成');
    }
}
