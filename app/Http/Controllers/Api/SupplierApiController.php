<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Models\SeoElementModel;
use App\Http\Models\SupplierModel;
use App\Http\Models\SupplierRatioModel;
use App\Http\Services\BrandService;
use App\Http\Services\SupplierService;
use Illuminate\Http\Request;

class SupplierApiController extends Controller
{

    //供应商列表
    public function getSupplierList(Request $request)
    {
        $supplierService = new SupplierService();
        $arr = ['supplier_id', 'supplier_name', 'type_id', 'status', 'limit', 'p'];
        $map = TrimX('', true, $arr);
        $data = $supplierService->getSupplierList($map);
        return $this->setSuccessData($data['data'], $data['total']);
    }

    public function saveSupplier(Request $request)
    {
        $field = ['supplier_name', 'supplier_id', 'type_id', 'status', 'channel_type', 'purchase_uid', 'supplier_channel_code', 'sku_optional_batch'];
        $data = TrimX('', false, $field);
        if (!$request->input('cn_delivery')) {
            return  $this->setError('大陆货期不能为空');
        }
        if (!$request->input('hk_delivery') && $data['supplier_id'] != 1688) {
            return  $this->setError('香港货期不能为空');
        }
        $result = (new SupplierService())->saveSupplier($data);
        if (!$result) {
            return $this->setError('提交失败');
        }
        return $this->setSuccess('提交成功');
    }

    public function saveSuppExtendFee(Request $request)
    {
        $field = ['supplier_id', 'cn', 'hk'];
        $collert = $request->only($field);
        if (!empty($collert['cn'])) {
            $suppExtendFee['cn'] = $collert['cn'];
        }
        if (!empty($collert['hk'])) {
            $suppExtendFee['hk'] = $collert['hk'];
        }
        //附加费具体可以询问龙哥
        if (empty($suppExtendFee)) {
            return $this->setError('没有提交附加费');
        }
        $result = (new SupplierService())->saveSuppExtendFee($collert['supplier_id'], json_encode($suppExtendFee));
        if (!$result) {
            return $this->setError('提交失败');
        }
        return $this->setSuccess('提交成功');
    }

    public function saveSupplierSeo(Request $request)
    {
        $field = ['type', 'key_id', 'keywords', 'description', 'title'];
        $collert = $request->only($field);
        $result = (new SupplierService())->saveSupplierSeo($collert);

        if (!$result) {
            return $this->setError('提交失败');
        }
        return $this->setSuccess('提交成功');
    }

    public function getSupplierRatioList(Request $request)
    {
        $field = ['supplier_id', 'is_default', 'status', 'currency', 'limit'];
        $collert = $request->only($field);
        $collert = TrimX($collert, true, $field);
        $limit = 10;
        if (!empty($collert['limit'])) {
            $limit = $collert['limit'];
            unset($collert['limit']);
        }
        $model = new SupplierRatioModel();
        if (empty($collert)) {
            $collert = [];
        }
        $list = $model->getSupplierRatioList($collert, $limit);
        foreach ($list['data'] as $k => &$v) {
            if ($v['status'] == 1) {
                $v['status_s'] = '正常';
            } else {
                $v['status_s'] = '禁用';
            }
        }
        return $this->setSuccessData($list['data'], $list['total']);
    }

    //保存供应商系数
    public function saveSupplierRatio(Request $request)
    {
        $field = [
            'id',
            'supplier_id',
            'status',
            'is_default',
            'cn',
            'hk',
            'extra_ratio',
            'ratio',
            'brand_name',
            'file_url',
            'sort'
        ];
        $collert = $request->only($field);
        $collert = TrimX($collert, false, $field);
        //这个$id是引用处理,注意一下
        $id = 0;
        $result = (new SupplierService())->saveSupplierRatio($id, $collert);
        if ($result !== true) {
            return $this->setError('处理失败');
        }
        return $this->setSuccessData(['data' => $id]);
    }

    //保存系数状态
    public function saveSupplierRatioStatus(Request $request)
    {
        $Field = ['id', 'status'];
        $collert = $request->only($Field);
        $collert = TrimX($collert, false, $Field);
        //这个$id是引用处理,注意一下
        $id = 0;
        $result = (new SupplierService())->saveSupplierRatioStatus($id, $collert);
        if ($result !== true) {
            return $this->setError('处理失败');
        }
        return $this->setSuccessData(['data' => $id]);
    }

    //删除供应商系数
    public function deleteSupplierRatio(Request $request)
    {
        $id = $request->input('id');
        if (empty($id)) {
            return $this->setError('请选择要删除的系数');
        }
        $info = (new SupplierRatioModel())->getSupplierRatioStatus($id);
        if (!$info) {
            return $this->setError('不存在这个系数');
        }
        if ($info['status'] != 2) {
            return $this->setError('只能删除禁用系数');
        }
        $result = (new SupplierRatioModel())->deleteSupplierRatio($id, $info['supplier_id']);
        if (!$result) {
            return $this->setError('删除系数失败');
        }
        return $this->setSuccess('删除系数成功');
    }
}
