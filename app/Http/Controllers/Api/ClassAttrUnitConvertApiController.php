<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\InvalidRequestException;
use App\Http\Controllers\Controller;
use App\Http\Services\ClassAttrUnitConvertService;
use App\Http\Services\SpuService;
use App\Imports\AlikeSpuImport;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class ClassAttrUnitConvertApiController extends Controller
{

    //获取代理品牌列表
    public function getClassAttrUnitConvertList(Request $request)
    {
        $map = $request->all();
        $data = (new ClassAttrUnitConvertService())->getClassAttrUnitConvertList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    //保存代理品牌
    public function saveClassAttrUnitConvert(Request $request)
    {
        $data = $request->only([
            'id',
            'convert_name',
            'standard_unit_name',
            'remark',
            'items',
        ]);
        $data['standard_unit_name'] = $data['standard_unit_name'][0];
        if (empty($data['convert_name'])) {
            return $this->setError('单位名称不能为空');
        }
        if (empty($data['standard_unit_name'])) {
            return $this->setError('标准单位不能为空');
        }
        $result = (new ClassAttrUnitConvertService())->saveClassAttrUnitConvert($data);
        if (!$result) {
            return $this->setError('保存代理品牌失败');
        }
        return $this->setSuccess('保存代理品牌成功');
    }

    //禁用代理品牌
    public function changeClassAttrUnitConvertStatus(Request $request)
    {
        $id = $request->input('id');
        $status = $request->input('status');
        $result = (new ClassAttrUnitConvertService())->changeClassAttrUnitConvertStatus($id, $status);
        if (!$result) {
            return $this->setError('禁用失败');
        }
        return $this->setSuccess();
    }
}
