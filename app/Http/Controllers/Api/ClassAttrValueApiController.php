<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Models\PoolClass\ClassAttrValueModel;
use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Requests\ClassAttrValueSave;
use App\Http\Requests\ClassSave;
use App\Http\Services\ClassAttrValueService;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;

class ClassAttrValueApiController extends Controller
{

    /*
   * 参数值列表
   */
    public function getClassAttrValueList(Request $request)
    {
        $limit = $request->get('limit', 15);
        $field = ['class_name', 'attr_name', 'attr_unit_id', 'status', 'show_name'];
        $collert = $request->only($field);
        $list = (new ClassAttrValueService())->getClassAttrValueList($collert, $limit);
        return $this->setSuccessData($list['data'], $list['total']);
    }

    /*
     * 新增/修改参数值
     */
    public function saveClassAttrValue(ClassAttrValueSave $request)
    {
        $field = ['attr_value_id', 'value', 'attr_id', 'attr_unit_id', 'remark', 'status'];
        $collert = $request->only($field);

        $attrValueModel = new ClassAttrValueModel();
        $collert['update_time'] = time();

        $find = $attrValueModel->where('attr_id', '=', $collert['attr_id'])->where('value', '=', $collert['value'])
            ->where('attr_unit_id', '=', $collert['attr_unit_id'])->whereIn('status', [1, 2])
            ->select('attr_value_id')->first();

        if (empty($collert['attr_value_id'])) {
            if ($find) {
                return $this->apiReturn(10001, '属性值已经存在，无需重复添加');
            }
            $collert['add_time'] = time();
            $result = $collert['attr_value_id'] =  $attrValueModel->insertGetId($collert);
        } else {
            $originalAttrValue = $find->value;
            if ($find && $find->attr_value_id != $collert['attr_value_id']) {
                return $this->apiReturn(10001, '属性值已经存在，修改失败');
            }
            $result = $attrValueModel->where('attr_value_id', '=', $collert['attr_value_id'])->update($collert);
        }
        $attrValue = $attrValueModel->where('attr_value_id', $collert['attr_value_id'])->first()->toArray();
        $redis = Redis::connection('sku');
        $redis->hset('class_attr_value_by_id', $attrValue['attr_value_id'], $attrValue['value']);
        //反向的也存一个
        if (!empty($originalAttrValue)) {
            $redis->hdel('class_attr_value_by_val', $originalAttrValue);
            $redis->hset('class_attr_value_by_val', $attrValue['value'], $attrValue['attr_value_id']);
        }

        if (!$result) {
            return $this->setError('提交失败');
        }

        return $this->setSuccess();
    }

    /*
     * 显示/隐藏参数值
     */
    public function hideClassAttrValue(Request $request)
    {
        $field = ['attr_value_id', 'status'];
        $collert = $request->only($field);

        $classModel = new ClassAttrValueModel();
        $result = $classModel->setAttrValueStatus($collert['attr_value_id'], $collert['status']);
        if (!$result) {
            return $this->setError('修改失败');
        }
        return $this->setSuccess();

    }
}


