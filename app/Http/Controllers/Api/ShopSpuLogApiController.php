<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Services\BrandService;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Redis;
use App\Http\Services\AlikeSpuService;
use App\Http\Services\ShopSpuLogService;
use Illuminate\Support\Facades\Response;
use App\Http\Services\ThirdExportService;
use App\Exceptions\InvalidRequestException;
use App\Http\Models\BigData\ShopSpuLogModel;
use App\Http\ApiHelper\ApiCode;

class ShopSpuLogApiController extends Controller
{

    public function getShopSpuPushLogList(Request $request)
    {
        $map = $request->all();
        $data = ShopSpuLogService::getShopSpuPushLogList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    public function exportShopSpuPushLogList(Request $request)
    {
        $param = $request->input();
        //判断这个数组的每个value是否都为空,如果都为空,那么就返回错误
        if (empty(array_filter($param))) {
            return $this->setError('参数不能为空');
        }
        $param["login_uid"] = $request->user->userId;
        $param["login_name"] = $request->user->name;

        $ExportService = new ThirdExportService();
        $data = $ExportService->exportSpuPushLogList($param);
        return $this->setSuccessData($data["data"], 0, ApiCode::API_CODE_SUCCESS, $data["err_msg"]);
    }
}
