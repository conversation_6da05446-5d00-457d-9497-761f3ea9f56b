<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Services\SpuLogService;
use Illuminate\Http\Request;


class SpuLogApiController extends Controller
{
    //SPUl列表
    public function getSpuLogList(Request $request)
    {
        $result = SpuLogService::getSpuLogList($request);
        return $this->setSuccessData($result['list'], $result['total']);
    }

}
