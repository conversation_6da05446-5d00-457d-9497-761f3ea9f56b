<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Queue\RabbitQueueModel;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use App\Http\Services\ShopDistributeSkuService;

class ShopDistributeSkuApiController extends Controller
{
    public function getShopDistributeSkuList(Request $request)
    {
        $map = $request->all();
        $data = ShopDistributeSkuService::getShopDistributeSkuList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    public function saveShopDistributeSku(Request $request)
    {
        $map = $request->only([
            'sku_ids',
            'shop_ids',
        ]);

        //表单校验
        $validator = Validator::make($map, [
            'shop_ids' => 'required|string',
            'sku_ids' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->setError($validator->errors()->first());
        }

        $result = ShopDistributeSkuService::saveShopDistributeSku($map['sku_ids'], $map['shop_ids']);

        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('保存失败');
        }
    }

    public function batchSaveShopDistributeSku(Request $request)
    {
        $map = $request->only([
            'sku_ids',
            'shop_ids',
        ]);
        //表单校验
        $validator = Validator::make($map, [
            'shop_ids' => 'required|string',
            'sku_ids' => 'required|string',
        ]);
        if ($validator->fails()) {
            return $this->setError($validator->errors()->first());
        }

        $result = ShopDistributeSkuService::batchSaveShopDistributeSku($map['sku_ids'], $map['shop_ids']);
        // return $this->setError('保存失败');
        return $this->setSuccess('保存成功');
    }

    public function batchSaveShopDistributeSkuByFilter(Request $request)
    {
        $params = $request->only([
            'shop_id',
            'supplier_code',
            'standard_brand_ids',
            'class_id2_list',
            'has_attr',
            'stock',
            'ability_level',
            'is_expired',
            'has_stock'
        ]);
        //表单校验
        $validator = Validator::make($params, [
            'shop_id' => 'required|string',
            'supplier_code' => 'required|string',
        ], [
            'shop_id.required' => '请选择店铺',
            'supplier_code.required' => '请选择供应商编码',
        ]);
        if ($validator->fails()) {
            return $this->setError($validator->errors()->first());
        }

        // Check if there was a recent push (within 30 minutes) using Redis
        $redis = Redis::connection('sku');
        $redisKey = 'shop_distribute_sku_push:' . $params['shop_id'] . ':' . $params['supplier_code'];
        $lastPushTime = $redis->get($redisKey);
        $currentTime = time();

        if ($lastPushTime && ($currentTime - $lastPushTime) < 5) { // 1800 seconds = 30 minutes
            return $this->setError('该店铺操作过于频繁，请30分钟后再试');
        }

        // Set the current time as the last push time
        $redis->set($redisKey, $currentTime);
        $redis->expire($redisKey, 5); // Set TTL to 30 minutes
        $params['shop_id'] = (int)$params['shop_id'];
        //获取推送json
        $pushJson = $params;
        $pushJson['standard_brand_ids'] = $params['standard_brand_ids'] ?? '';
        $pushJson['class_id2_list'] = $params['class_id2_list'] ?? '';
        $pushJson['has_attr'] =  $params['has_attr'] ?? 0;
        $pushJson['stock'] = $params['stock'] ?? []; // Pass stock filter to service

        if (empty($pushJson['stock'])) {
            if (!empty($params['has_stock'])) {
                if ($params['has_stock'] == 1) {
                    $pushJson['stock'] = ['gt', 0];
                }
                if ($params['has_stock'] == -1) {
                    $pushJson['stock'] = ['eq', 0];
                }
            }
        }

        $pushJson['ability_level'] = isset($params['ability_level']) ? $params['ability_level'] : -1;
        $pushJson['is_expired'] =  $params['is_expired'] ?? 0;
        $pushJson['create_name'] = $request->user->name;

        (new RabbitQueueModel('trading'))->insertRawQueue('', $pushJson, 'liexin_nplm_filt_sku_to_push_pool');

        return $this->setSuccess('保存成功');
    }

    public function deleteShopDistributeSku(Request $request)
    {
        $id = $request->input('id');

        if (empty($id)) {
            return $this->setError('ID不能为空');
        }

        $result = ShopDistributeSkuService::deleteShopDistributeSku($id);

        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('删除失败');
        }
    }

    public function exportShopDistributeSku(Request $request)
    {
        $map = $request->input('params');
        $map = json_decode($map, true);
        return ShopDistributeSkuService::exportShopDistributeSku($map);
    }

    /**
     * 导入SKU
     */
    public function importShopDistributeSku(Request $request)
    {

        ini_set('memory_limit', '-1');
        $validator = Validator::make($request->all(), [
            'shop_id' => 'required',
            'file' => 'required|file',
        ], [
            'shop_id.required' => '请选择店铺',
            'file.required' => '请选择文件',
            'file.file' => '请上传有效文件',
            //'file.mimes' => '请上传Excel文件(xlsx或xls)',
        ]);

        if ($validator->fails()) {
            return $this->setError($validator->errors()->first());
        }

        $file = $request->file('file');
        $shopId = $request->input('shop_id');

        //保存文件到本地
        // 保存文件到存储磁盘（例如 'public' 磁盘）
        $originalName = $file->getClientOriginalName(); // 获取原始文件名，例如 'example.xlsx'
        $filePath = $file->storeAs('uploads', $originalName, 'public');
        $filePath = Storage::disk('public')->path($filePath);
        // 调用服务处理导入的SKU
        $result = ShopDistributeSkuService::importShopDistributeSku($originalName, $filePath, $shopId);
        return $this->setSuccess('导入任务开始,请耐心等待后台任务完成 : ' . $filePath);
        // return $this->setError('导入失败');
    }

    public function updateShopDistributeSku(Request $request)
    {
        $map = $request->only([
            'id',
            'purchase_coefficient',
            'sale_coefficient',
            'goods_title',
            'moq',
            'sale_price',
            'purchase_price',
            'multiple',
        ]);

        $result = ShopDistributeSkuService::updateShopDistributeSku($map);
        if ($result) {
            return $this->setSuccess('更新成功');
        } else {
            return $this->setError('更新失败');
        }
    }
}
