<?php

namespace App\Http\Controllers\Api;


use App\Http\Controllers\Controller;
use App\Http\Services\ShopDistributeRuleSpuService;
use App\Http\Validator\ShopDistributeRuleSpurValidator;
use App\Http\Validator\ShopDistributeRuleSpuValidator;
use Illuminate\Http\Request;


class ShopDistributeRuleSpuApiController extends Controller
{

    public function getShopDistributeRuleSpuList(Request $request)
    {
        $map = $request->all();
        $data = ShopDistributeRuleSpuService::getShopDistributeRuleSpuList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    //根据平台来获取分类给xm-select使用
    public function getShopDistributeRuleSpuByPlatform(Request $request)
    {
        $platform = $request->input('platform');
        $result = ShopDistributeRuleSpuService::getShopDistributeRuleSpuByPlatform($platform);
        return $this->setSuccessData($result);
    }

    public function saveShopDistributeRuleSpu(Request $request)
    {
        $params = $request->only([
            'id',
            'platform',
            'brand_id_list',
            'class_id_list',
            'brand_type',
            'eccn',
            'eccn_file_url',
            'eccn_file_name',
        ]);
        $validator = new ShopDistributeRuleSpuValidator();
        $validator->check($params);
        $result = ShopDistributeRuleSpuService::saveShopDistributeRuleSpu($params);

        if (!$result) {
            return $this->setError('保存分发规则失败');
        }

        return $this->setSuccess('保存分发规则成功');

    }

    public function updateDistributeRuleSpuStatus(Request $request)
    {
        $status = $request->input('status');
        $id = $request->input('id');
        $result = ShopDistributeRuleSpuService::updateDistributeRuleSpuStatus($id, $status);
        if (!$result) {
            return $this->setError('修改状态失败');
        }

        return $this->setSuccess('修改状态成功');
    }

    public function deleteShopDistributeRuleSpu(Request $request)
    {
        $id = $request->input('id');
        if (empty($id)) {
            return $this->setError('需要删除的分发规则id不能为空');
        }
        $result = ShopDistributeRuleSpuService::deleteShopDistributeRuleSpu($id);
        if (!$result) {
            return $this->setError('删除分发规则失败');
        }

        return $this->setSuccess('删除分发规则成功');
    }


}
