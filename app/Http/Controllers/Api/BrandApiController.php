<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Services\BrandService;
use Illuminate\Http\Request;

class BrandApiController extends Controller
{

    public function getBrandList(Request $request)
    {
        $map = $request->all();
        $data = (new BrandService())->getBrandList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    public function searchBrand(Request $request)
    {
        $brandName = $request->input('brand_name');
        $result = (new BrandService())->searchBrand($brandName);
        echo json_encode($result);
    }

    public function saveBrand(Request $request)
    {
        $data = $request->only([
            'brand_id',
            'brand_name',
            'brand_area',
            'status',
            'is_unusual',
            'brand_logo',
            'main_product',
            'brand_brief',
            'brand_desc',
            'title',
            'keywords',
            'description',
        ]);
        $result = (new BrandService())->saveBrand($data);
        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('保存失败');
        }
    }


    public function changeBrandStatus(Request $request)
    {
        $brandId = $request->input('brand_id');
        $status = $request->input('status');
        $result = (new BrandService())->changeBrandStatus($brandId, $status);
        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('修改失败');
        }
    }

    public function getBrandListForSelect(Request $request)
    {
        $brandName = $request->input('brand_name');
        $brandName = trim($brandName);
        $result = (new BrandService())->getBrandListForSelect($brandName);
        return $this->setSuccessData($result['data'], $result['total']);
    }


    //导出品牌
    public function exportBrands(Request $request)
    {
        $params = $request->input('params');
        $map = json_decode($params, true);
        return (new BrandService())->exportBrands($map);
    }

    //修改是否处理
    public function updateIgnoreHandle(Request $request)
    {
        $brandId = $request->input('brand_id');
        $brandIds = $request->input('brand_ids');
        $ignoreHandle = $request->input('ignore_handle');
        $ignoreHandleReason = $request->input('ignore_handle_reason');
        if (empty($brandIds)) {
            $result = (new BrandService())->updateIgnoreHandle($brandId, $ignoreHandle, $ignoreHandleReason);
        } else {
            $result = (new BrandService())->batchUpdateIgnoreHandle($brandIds, $ignoreHandle, $ignoreHandleReason);
        }
        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('修改失败');
        }
    }
}
