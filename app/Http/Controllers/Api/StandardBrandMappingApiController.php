<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\InvalidRequestException;
use App\Exports\StandardBrandExport;
use App\Exports\StandardBrandMappingExport;
use App\Http\Controllers\Controller;
use App\Http\Models\SampleLogModel;
use App\Http\Models\StandardBrandMappingModel;
use App\Http\Models\StandardBrandModel;
use App\Http\Services\QueueService;
use App\Http\Services\StandardBrandMappingService;
use App\Http\Services\StandardEncapMappingService;
use App\Imports\SampleImport;
use App\Jobs\UploadSample;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class StandardBrandMappingApiController extends Controller
{
    public function Entrance(Request $request, $id)
    {
        return $this->$id($request, $id);
    }

    //品牌映射列表
    public function getStandardBrandMappingList(Request $request)
    {
        $field = ['brand_name', 'standard_brand_name', 'add_time','standard_brand_id'];
        $map = $request->only($field);
        $map = array_map(function ($value) {
            return trim($value);
        }, $map);
        $service = new StandardBrandMappingService();
        $data = $service->getStandardMappingList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    //查找未映射的品牌
    public function findNotMappingBrand(Request $request)
    {
        $field = ['brand_name','limit','page','brand_id'];
        $map = $request->only($field);
        $service = new StandardBrandMappingService();
        $data = $service->findNotMappingStandBrand($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    //添加映射的接口
    public function addStandardBrandMapping(Request $request)
    {
        $map = $request->only(['standard_brand_id', 'mapping_brand_ids']);
        $service = new StandardBrandMappingService();
        $result = $service->addStandardBrandMapping($map['standard_brand_id'], $map['mapping_brand_ids']);
        if (!$result) {
            return $this->setError('添加映射失败');
        }

        return $this->setSuccess('添加映射成功');
    }


    //删除映射
    public function deleteStandardBrandMapping(Request $request)
    {
        $id = $request->get('id');
        $service = new StandardBrandMappingService();
        $result = $service->deleteStandardMapping($id);
        if (!$result) {
            return $this->setError('删除映射失败');
        }

        return $this->setSuccess('删除映射成功');
    }

    //导出映射
    public function exportStandardBrandMapping(Request $request)
    {
        $params = $request->input('params');
        $map = json_decode($params, true);
        return Excel::download(new StandardBrandMappingExport($map), '品牌映射导出.xlsx');
    }

    //重推队列
    public function resendQueue(Request $request)
    {
        $mapping = StandardBrandMappingModel::where('id', $request->input('id'))->first()->toArray();
        QueueService::publishQueue('stand_brand_map', [
            'brand_id' => (int)$mapping['brand_id'],
            'stand_brand_id' => (int)$mapping['standard_brand_id'],
            'stand_brand_name' => StandardBrandModel::where('standard_brand_id',$mapping['standard_brand_id'])->value('brand_name'),
        ]);
        return $this->setSuccess('推送成功');
    }
}
