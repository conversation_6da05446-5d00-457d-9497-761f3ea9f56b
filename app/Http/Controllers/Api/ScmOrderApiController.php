<?php

namespace App\Http\Controllers\Api\Api;

use App\Http\Controllers\Api\Controller;
use App\Http\Services\ScmOrderService;
use App\Http\Services\ScmOrderTemporaryService;
use Illuminate\Http\Request;

class ScmOrderApiController extends Controller
{
    public function getScmOrderList(Request $request)
    {
        $params = $request->only([
            'page',
            'limit',
            'scm_order_sn',
            'purchase_sn',
            'warehouse_receipt_sn',
            'stock_in_sn',
            'supplier_name',
            'goods_name',
            'create_uid',
            'customs_status',
            'status',
            'create_time'
        ]);
        $scmOrderTemporaryList = ScmOrderService::getScmOrderList($params);
        return $this->setSuccessData($scmOrderTemporaryList['data'], $scmOrderTemporaryList['total']);
    }

    //添加进委托单
    public function addScmOrder(Request $request)
    {
        $temporaryIds = $request->input('temporary_ids');
        if (!$temporaryIds) {
            return $this->setError('需要生成委托单的暂存ID不能为空');
        }
        //这边只需要接收入库单详情ID即可,因为我可以去数据库里面直接找数据
        $temporaryIds = explode(',', $temporaryIds);
        $result = ScmOrderService::addScmOrder($temporaryIds);
        if (!$result) {
            return $this->setError('生成委托单失败');
        }
        return $this->setSuccessData('生成委托单成功');
    }

    //获取报关列表
    public function getCustomsList(Request $request)
    {
        $scmOrderItemIds = $request->input('scm_order_item_ids');
        if (!$scmOrderItemIds) {
            return $this->setError('选择的委托明细ID不能为空');
        }
        $customsList = ScmOrderService::getScmOrderListByItemIds($scmOrderItemIds);
        return $this->setSuccessData($customsList);
    }

    //添加报关接口
    public function addCustoms(Request $request)
    {
        $scmOrderItemIds = $request->input('scm_order_item_ids');
        if (!$scmOrderItemIds) {
            return $this->setError('选择的委托明细ID不能为空');
        }
        $result = ScmOrderService::addCustoms($scmOrderItemIds);
        if (!$result) {
            return $this->setError('报关失败');
        }
        return $this->setSuccess('报关成功');
    }

    //获取数据里面的所有供应商列表
    public function getScmOrderSupplierList(Request $request)
    {
        $params = $request->only(['limit', 'page', 'keyword']);
        $result = ScmOrderService::getScmOrderSupplierList($params);
        return $this->setSuccessData(['list' => $result]);
    }

    //委托单入库明细
    public function getScmOrderStockInList(Request $request)
    {
        $params = $request->only([
            'limit',
            'page',
            'warehouse_receipt_sn',
            'stock_in_sn',
            'erp_stock_in_sn',
            'scm_order_sn',
            'goods_name',
            'item_status',
            'stock_in_time',
        ]);
        $stockInList = ScmOrderService::getScmOrderStockInList($params);
        return $this->setSuccessData($stockInList['data'],$stockInList['total']);
    }
}
