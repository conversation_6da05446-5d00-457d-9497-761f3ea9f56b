<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Exports\ShopBrandExport;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ShopBrandMappingExport;
use App\Http\Services\ShopBrandService;
use App\Http\Validator\ShopBrandValidator;
use App\Http\Services\ShopBrandMappingService;
use App\Http\Validator\ShopBrandMappingValidator;

class ShopBrandApiController extends Controller
{

    public function getShopBrandList(Request $request)
    {
        $map = $request->all();
        if ($request->input('is_export')) {
            $params = $request->input('params');
            $map = json_decode($params, true);
            return Excel::download(new ShopBrandExport($map), '第三方品牌导出.xlsx');
        }
        $data = ShopBrandService::getShopBrandList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    public function getShopBrandMappingList(Request $request)
    {

        if ($request->input('is_export')) {
            $params = $request->input('params');
            $map = json_decode($params, true);
            return Excel::download(new ShopBrandMappingExport($map), '第三方品牌映射导出.xlsx');
        }

        $map = $request->all();
        $data = ShopBrandMappingService::getShopBrandMappingList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }



    //根据平台来获取分类给xm-select使用
    public function getShopBrandByPlatform(Request $request)
    {
        $platform = $request->input('platform');
        $result = ShopBrandService::getShopBrandByPlatform($platform);
        return $this->setSuccessData($result);
    }

    public function saveShopBrandMapping(Request $request)
    {
        $params = $request->only([
            'id',
            'platform',
            'lie_brand_id',
            'shop_brand_id',
        ]);
        $validator = new ShopBrandMappingValidator();
        $validator->check($params);
        $result = ShopBrandMappingService::saveShopBrandMapping($params);

        if (!$result) {
            return $this->setError('保存映射失败');
        }

        return $this->setSuccess('保存映射成功');

    }

    public function saveShopBrand(Request $request)
    {
        $params = $request->only([
            'id',
            'platform_list',
            'brand_id',
            'brand_name',
        ]);


        $validator = new ShopBrandValidator();
        $validator->check($params);
        ShopBrandService::saveShopBrand($params);

        return $this->setSuccess('保存品牌成功');

    }

    public function deleteShopBrandMapping(Request $request)
    {
        $id = $request->input('id');
        if (empty($id)) {
            return $this->setError('需要删除的映射id不能为空');
        }
        $result = ShopBrandMappingService::deleteShopBrandMapping($id);
        if (!$result) {
            return $this->setError('删除映射失败');
        }

        return $this->setSuccess('删除映射成功');
    }

    public function importShopBrandMapping(Request $request)
    {
        $file = $request->file('file');
        $result = ShopBrandMappingService::importShopBrandMapping($file);
        if (!$result) {
            return $this->setError('导入失败');
        }

        return $this->setSuccess('导入成功');
    }


}
