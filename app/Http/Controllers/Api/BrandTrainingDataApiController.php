<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Services\BrandTrainingDataService;
use App\Exports\BrandTrainingDataExport;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class BrandTrainingDataApiController extends Controller
{
    public function getBrandTrainingDataList(Request $request)
    {
        $map = $request->all();
        $service = new BrandTrainingDataService();
        $data = $service->getBrandTrainingDataList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    public function saveBrandTrainingData(Request $request)
    {
        $map = $request->all();
        if (empty($map['content'])) {
            return $this->setError('内容不能为空');
        }
        $service = new BrandTrainingDataService();
        $result = $service->saveBrandTrainingData($map);
        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('保存失败');
        }
    }

    public function deleteBrandTrainingData(Request $request)
    {
        $id = $request->input('id');
        $service = new BrandTrainingDataService();
        $result = $service->deleteBrandTrainingData($id);
        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('删除失败');
        }
    }

    public function exportBrandTrainingData(Request $request)
    {
        $params = $request->input('params');
        $map = json_decode($params, true);
        return Excel::download(new BrandTrainingDataExport($map), '品牌训练数据导出.xlsx');
    }

    // 批量导入品牌数据
    public function importBrandTrainingData(Request $request)
    {
        $file = $request->file('upload');
        $service = new BrandTrainingDataService();
        $result = $service->importBrandTrainingData($file);
        if ($result['success']) {
            return $this->setSuccess($result['message']);
        } else {
            return $this->setError($result['message']);
        }
    }

    // 获取单个品牌训练数据信息
    public function getBrandTrainingDataById(Request $request)
    {
        $id = $request->input('id');
        $service = new BrandTrainingDataService();
        $trainingData = $service->getBrandTrainingDataById($id);
        return $this->setSuccessData($trainingData);
    }
}
