<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\InvalidRequestException;
use App\Http\Controllers\Controller;
use App\Http\Models\BrandModel;
use App\Http\Models\ScmBrandMappingModel;
use App\Http\Models\ScmBrandModel;
use App\Http\Services\ScmBrandMappingService;
use App\Http\Services\ScmBrandService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

class ScmBrandMappingApiController extends Controller
{

    //品牌映射列表
    public function getScmBrandMappingList(Request $request)
    {
        $field = ['brand_name', 'add_time', 'scm_brand_id'];
        $map = $request->only($field);
        $map = array_map(function ($value) {
            return trim($value);
        }, $map);
        $service = new ScmBrandMappingService();
        $data = $service->getScmBrandMappingList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    public function addScmBrandMapping(Request $request)
    {
        $field = ['scm_brand_id', 'mapping_brand_ids'];
        $map = $request->only($field);
        $mappingResult = (new ScmBrandMappingService())->addScmBrandMapping($map);
        return $this->setSuccessData($mappingResult);
    }

    public function getMappingBrand(Request $request)
    {
        $field = ['scm_brand_id'];
        $map = $request->only($field);
        $mappingList = (new ScmBrandMappingService())->getScmBrandMappingBrand($map);
        return $this->setSuccessData($mappingList);
    }

    public function findNotMappingBrand(Request $request)
    {
        $field = ['scm_brand_id', 'limit', 'brand_name'];
        $map = $request->only($field);
        $map['limit'] = $map['limit'] ?: 10;
        $mappingList = (new ScmBrandMappingService())->findNotMappingBrand($map);
        return $this->setSuccessData($mappingList['data'], $mappingList['total']);
    }

    public function deleteScmBrandMapping(Request $request)
    {
        $mappingId = $request->input('mapping_id');
        $result = (new ScmBrandMappingService())->deleteScmBrandMapping($mappingId);
        if (!$result) {
            return $this->setError('删除失败');
        }

        return $this->setSuccess();
    }

}
