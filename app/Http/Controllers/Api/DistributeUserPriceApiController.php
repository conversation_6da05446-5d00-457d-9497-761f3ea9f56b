<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Models\DistributePriceModel;
use App\Http\Services\DistributeUserPriceService;
use App\Http\Validator\DistributeUserPriceValidator;

class DistributeUserPriceApiController extends Controller
{
    public function getDistributeUserPriceList(Request $request)
    {
        $map = $request->all();
        $data = (new DistributeUserPriceService())->getDistributeUserPriceList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    public function saveDistributeUserPrice(Request $request)
    {
        $params = $request->only([
            'id',
            'user_name',
            'price_type',
            'rules',
            'app_key',
            'app_secret'
        ]);

        $isCopy = $request->input('copy');
        $validator = new DistributeUserPriceValidator();
        $validator->check($params);

        $rules = json_decode($params['rules'], true);
        $priceItemList = [];
        foreach ($rules as $rule) {
            $supplierCode = is_numeric($rule['supplier_id']) ? '' : $rule['supplier_id'];
            $priceItemList[] = [
                'price_id' => $rule['price_id'] ?? 0,
                'supplier_id' => $supplierCode ? 17 : $rule['supplier_id'],
                'supplier_code' => DistributePriceModel::where('supplier_id',$rule['supplier_id'])->value('canal'),
                'agreement_price_coefficient' => $rule['agreement_price_coefficient'],
                'guide_price_coefficient' => $rule['guide_price_coefficient']
            ];
        }
        unset($params['rules']);
        // 创建或更新主记录
        $success = (new DistributeUserPriceService())->saveDistributeUserPrice($params, $priceItemList, $isCopy);

        if (!$success) {
            return $this->setError('保存客户价格分发规则失败');
        }

        return $this->setSuccess('保存客户价格分发规则成功');
    }

    public function deleteDistributeUserPrice(Request $request)
    {
        $id = $request->input('id');
        if (empty($id)) {
            return $this->setError('需要删除的客户价格分发规则id不能为空');
        }
        $result = (new DistributeUserPriceService())->deleteDistributeUserPrice($id);
        if (!$result) {
            return $this->setError('删除客户价格分发规则失败');
        }

        return $this->setSuccess('删除客户价格分发规则成功');
    }

    public function updateStatus(Request $request)
    {
        $id = $request->input('id');
        $status = $request->input('status');
        $result = (new DistributeUserPriceService())->updateStatus($id, $status);
        if (!$result) {
            return $this->setError('修改状态失败');
        }

        return $this->setSuccess('修改状态成功');
    }
}
