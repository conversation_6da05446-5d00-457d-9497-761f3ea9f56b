<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Services\TrainingDataService;
use App\Exports\TrainingDataExport;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class TrainingDataApiController extends Controller
{
    public function getTrainingDataList(Request $request)
    {
        $map = $request->all();
        $service = new TrainingDataService();
        $data = $service->getTrainingDataList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    public function saveTrainingData(Request $request)
    {
        $map = $request->all();
        if (empty($map['content'])) {
            return $this->setError('内容不能为空');
        }
        if (empty($map['type'])) {
            return $this->setError('类型不能为空');
        }
        $service = new TrainingDataService();
        $result = $service->saveTrainingData($map);
        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('保存失败');
        }
    }

    public function deleteTrainingData(Request $request)
    {
        $id = $request->input('id');
        $service = new TrainingDataService();
        $result = $service->deleteTrainingData($id);
        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('删除失败');
        }
    }

    public function exportTrainingData(Request $request)
    {
        $params = $request->input('params');
        $map = json_decode($params, true);
        return Excel::download(new TrainingDataExport($map), '训练数据导出.xlsx');
    }

    // 批量导入品牌
    public function importBrandData(Request $request)
    {
        $file = $request->file('upload');
        $service = new TrainingDataService();
        $result = $service->importTrainingData($file, 1); // 1表示品牌
        if ($result['success']) {
            return $this->setSuccess($result['message']);
        } else {
            return $this->setError($result['message']);
        }
    }

    // 批量导入型号
    public function importModelData(Request $request)
    {
        $file = $request->file('upload');
        $service = new TrainingDataService();
        $result = $service->importTrainingData($file, 2); // 2表示型号
        if ($result['success']) {
            return $this->setSuccess($result['message']);
        } else {
            return $this->setError($result['message']);
        }
    }

    // 批量导入分类
    public function importCategoryData(Request $request)
    {
        $file = $request->file('upload');
        $service = new TrainingDataService();
        $result = $service->importTrainingData($file, 3); // 3表示分类
        if ($result['success']) {
            return $this->setSuccess($result['message']);
        } else {
            return $this->setError($result['message']);
        }
    }

    // 获取单个训练数据信息
    public function getTrainingDataById(Request $request)
    {
        $id = $request->input('id');
        $service = new TrainingDataService();
        $trainingData = $service->getTrainingDataById($id);
        return $this->setSuccessData($trainingData);
    }
}
