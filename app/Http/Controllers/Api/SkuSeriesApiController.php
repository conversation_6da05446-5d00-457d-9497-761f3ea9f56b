<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\InvalidRequestException;
use App\Http\Controllers\Controller;
use App\Http\Models\SkuSeriesItemModel;
use App\Http\Models\SkuSeriesModel;
use App\Http\Models\UploadLogModel;
use App\Http\Services\SkuSeriesService;
use App\Http\Services\BrandService;
use App\Jobs\CheckSkuSeries;
use Excel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SkuSeriesApiController extends Controller
{
    //商品系列列表
    public function getSkuSeriesList(Request $request)
    {
        $map = $request->all();
        $list = SkuSeriesService::getSkuSeriesList($map);
        return $this->setSuccessData($list['data'], $list['total']);
    }

    //保存商品系列信息
    public function saveSkuSeries(Request $request)
    {
        $data = $request->only(['id', 'series_name', 'standard_brand_id', 'class_id1', 'class_id2']);
        //校验上面的字段都不能为空
        $validator = Validator::make($data, [
            'series_name' => 'required|max:30',
            'class_id1' => 'required',
            'standard_brand_id' => 'required',
        ], [], [
            'series_name' => '系列名称',
            'class_id1' => '一级分类',
            'standard_brand_id' => '标准品牌',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            throw new InvalidRequestException($error);
        }
        $result = SkuSeriesService::saveSkuSeries($data);
        if (!$result) {
            return $this->setError('保存商品系列失败');
        }

        return $this->setSuccess('保存商品系列成功');
    }

    //修改商品系列状态
    public function changeSkuSeriesStatus(Request $request)
    {
        $status = $request->input('status');
        $id = $request->input('id');
        $result = SkuSeriesService::updateSkuSeriesStatus($id, $status);
        if (!$result) {
            return $this->setError('修改商品系列状态失败');
        }

        return $this->setSuccess('修改商品系列状态成功');
    }

    //检测商品系列商品
    public function checkSkuSeries(Request $request)
    {
        $result = CheckSkuSeries::dispatch();
        if (!$result) {
            return $this->setError('触发检测任务失败,请联系管理员');
        }
        return $this->setSuccess('检测任务开始,请耐心等待处理完成');
    }


    //items列表
    public function getSkuSeriesItemList(Request $request)
    {
        $map = $request->all();
        $list = SkuSeriesService::getSkuSeriesItemList($map);
        return $this->setSuccessData($list['data'], $list['total']);
    }

    public function checkCanBatchUpdateItemSeries(Request $request)
    {
        $skuIds = $request->input('sku_ids');
        $skuIds = explode(',', trim($skuIds, ','));
        SkuSeriesService::checkCanBatchUpdateItemSeries($skuIds);
        return $this->setSuccess();
    }

    //批量修改商品系列
    public function batchUpdateItemSeries(Request $request)
    {
        $seriesId = $request->input('series_id');
        if (empty($seriesId)) {
            return $this->setError('请选择需要切换的系列');
        }
        $ids = $request->input('ids');
        $ids = $ids ? explode(',', $ids) : [];
        $skuIds = $request->input('sku_ids');
        $skuIds = $skuIds ? explode(',', $skuIds) : [];
        $result = SkuSeriesService::batchUpdateItemSeries($ids, $skuIds, $seriesId);
        if ($result === false) {
            return $this->setError('批量绑定失败,请联系管理员');
        }
        return $this->setSuccess('批量绑定成功');
    }

    //删除商品系列商品
    public function batchDeleteSkuSeriesItem(Request $request)
    {
        $ids = $request->input('ids');
        $result = SkuSeriesService::batchDeleteSkuSeriesItem($ids);
        if ($result === false) {
            return $this->setError('移除失败,请联系管理员');
        }
        return $this->setSuccess('移除成功');
    }

    //修改商品系列字段
    public function updateSkuSeriesItem(Request $request)
    {
        $params = $request->only([
            'id',
            'order',
            'show_name'
        ]);

        $params['order'] = intval(trim($params['order']));
        $params['update_time'] = time();
        $result = SkuSeriesItemModel::where('id',$params['id'])->update($params);
        if ($result === false) {
            return $this->setError('更新失败,请联系管理员');
        }
        return $this->setSuccess('更新成功');
    }
}
