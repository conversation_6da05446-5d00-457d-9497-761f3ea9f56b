<?php

namespace App\Http\Controllers\Api;

use App\Exports\AlikeSpuExport;
use App\Http\Controllers\Controller;
use App\Http\Models\AlikeSpuItemsModel;
use App\Http\Models\AlikeSpuLogModel;
use App\Http\Models\AlikeSpuModel;
use App\Http\Models\UploadLogModel;
use App\Http\Services\AlikeSpuService;
use App\Http\Services\BrandService;
use Excel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;

class AlikeSpuApiController extends Controller
{

    //相似spu上传列表
    public function getAlikeSpuList(Request $request)
    {
        $map = $request->only([
            'spu_name',
            'alike_spu_name',
            'alike_brand_name',
            'brand_name',
            'pin_to_pin',
            'add_time',
            'admin_id',
            'limit',
            'match_rate',
            'match_rate_compare_type'
        ]);
        $list = AlikeSpuService::getAlikeSpuList($map);
        return $this->setSuccessData($list['data'], $list['total']);
    }

    //相似型号上传处理
    public function uploadAlikeSpu(Request $request)
    {
        $file = $request->file('file');
        $result = AlikeSpuService::uploadAlikeSpu($file);
        if (!$result) {
            return $this->setError('上传处理失败,请联系管理员');
        }

        return $this->setSuccess('上传任务开始,请耐心等待处理完成');
    }

    //上传记录
    public function getAlikeSpuUploadLogList(Request $request)
    {
        $map = $request->all();
        $list = AlikeSpuService::getAlikeSpuUploadList($map);
        return $this->setSuccessData($list['data'], $list['total']);
    }

    //删除替代型号映射(这边为了es那边方便操作,直接把is_deleted改成1,然后把spu_id置空就可以了
    public function deleteAlikeSpu(Request $request)
    {
        //分两种情况删除,一种是直接删除整个替代型号对应的所有spu_name
        //另一种是删除单个spu_name
        $id = $request->get('id');
        $isBatch = $request->get('is_batch');
        if (empty($id)) {
            return $this->setError('id不能为空');
        }
        $alikeModel = new AlikeSpuModel();
        $res = false;
        if ($isBatch) {
            foreach ($id as $value) {
                $res = $alikeModel->deleteAlikeSpu($value);
            }
        } else {
            $res = $alikeModel->deleteAlikeSpu($id);
        }
        if ($res) {
            return $this->setSuccess('删除成功');
        }
        return $this->setError('删除失败');
    }

    //items列表
    public function getAlikeSpuItems(Request $request)
    {
        $map = $request->all();
        $list = AlikeSpuService::getAlikeSpuItems($map);
        return $this->setSuccessData($list['data'], $list['total']);
    }

    //删除替代型号映射(这边为了es那边方便操作,直接把is_deleted改成1,然后把spu_id置空就可以了
    public function deleteAlikeSpuItem(Request $request)
    {
        $id = $request->get('id');
        if (empty($id)) {
            return $this->setError('id不能为空');
        }
        $alikeItemModel = new AlikeSpuItemsModel();
        $res = $alikeItemModel->deleteAlikeSpuItem($id);
        if ($res) {
            return $this->setSuccess('删除成功');
        }
        return $this->setError('删除失败');
    }

    public function reloadData(Request $request)
    {
        $id = $request->get('id');
        AlikeSpuService::reloadAlikeSpu($id);
        return $this->setSuccess('提交成功,请稍等数据重跑');
    }

    //导出替代型号记录
    public function export(Request $request)
    {
        return Excel::download(new AlikeSpuExport(), '国产替代映射导出.xlsx');
    }

    //删除映射的sku(被替代型号)
    public function deleteSpuSku(Request $request)
    {
        $mainId = $request->input('main_id');
        $spuName = $request->input('spu_name');
        $skuIds = $request->input('sku_ids');
        $result = AlikeSpuService::deleteSpuSku($spuName, $skuIds, $mainId);
        if ($result === false) {
            return $this->setError('删除失败,请联系管理员');
        }
        return $this->setSuccess('删除成功,请等待搜索服务处理映射');
    }

    //删除映射的sku(国产替代型号)
    public function deleteAlikeSpuSku(Request $request)
    {
        $mainId = $request->input('main_id');
        $alikeSpuName = $request->input('spu_name');
        $skuIds = $request->input('sku_ids');
        $result = AlikeSpuService::deleteAlikeSpuSku($alikeSpuName, $skuIds, $mainId);
        if ($result === false) {
            return $this->setError('删除失败,请联系管理员');
        }
        return $this->setSuccess('删除成功,请等待搜索服务处理映射');
    }

    public function downloadUploadResult(Request $request)
    {
        ini_set('memory_limit', '2048M');
        $id = $request->input('id');
        $path = AlikeSpuService::downloadUploadResult($id);
        if ($path) {
            return Response::download($path);
        }

        return "找不到结果文件";
    }
}
