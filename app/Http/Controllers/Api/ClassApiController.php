<?php

namespace App\Http\Controllers\Api;

use App\Exports\ClassExport;
use App\Http\Controllers\Controller;
use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Requests\ClassSave;
use App\Http\Services\ClassService;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class ClassApiController extends Controller
{

    public function getClassList(Request $request)
    {
        $field = ['class_name', 'class_type'];
        $collert = TrimX($request->only($field), true);
        $classModel = new PoolClassModel();
        $list = $classModel->where($classModel->map($collert))->where('parent_id', '=', 0)
            ->select(['class_id', 'show_name', 'sign', 'status', 'class_name', 'sort', 'parent_id', 'class_type'])
            ->with(['sub'])->orderBy('sort', 'desc')->get();
        $redis = Redis::connection('sku');
        $data = [];
        if ($list) {
            $list = $list->toArray();
            //将分类ID提取为key
            foreach ($list as $k => $v) {
                //获取sku数量
                $arr = $v;
                if (!empty($v['sub'])) {
                    unset($arr['sub']);
                    foreach ($v['sub'] as $k1 => $v1) {
                        $arr['sub'][$v1['class_id']] = $v1;
                    }
                }
                $data[$v['class_id']] = $arr;
            }
        }
        if (!empty($collert['class_name'])) {
            $subList = $classModel->where($classModel->map($collert))->where('parent_id', '>', 0)
                ->select(['class_id', 'show_name', 'sign', 'status', 'class_name', 'parent_id', 'sort'])
                ->with(['parent'])->withCount('attrCount')->get();
            if ($subList) {
                $subList = $subList->toArray();
                foreach ($subList as $k => $v) {
                    $classId = $v['parent']['class_id'];
                    if (empty($data[$classId])) {
                        $data[$classId] = $v['parent'];
                        unset($v['parent']);
                    }
                    $data[$classId]['sub'][$v['class_id']] = $v;
                }
            }
        }

        foreach ($data as $k => &$v) {
            $v['sku_num'] = $redis->hget('goods_optimum_class_count', $v['class_id']) ?: 0;
            $v['attr_num'] = 0;
            $v['is_mro'] = $v['class_type'] == PoolClassModel::CLASS_TYPE_MRO ? 1 : 0;
            if (!empty($v['sub'])) {
                foreach ($v['sub'] as $k1 => $v1) {
                    $v['attr_num'] += $v1['attr_count_count'];
                    $v['sub'][$k1]['sku_num'] = $redis->hget('goods_optimum_class_count', $v1['class_id']) ?: 0;
                    //二级分类直接跳转到sku列表,带上分类名称和id去搜索
                    $v['sub'][$k1]['sku_list_url'] = '/web/sku/skuList?class_id2/condition=' . $v1['class_id'];
                }
                $v['sub'] = array_values($v['sub']);
            }
        }
        return $this->setSuccessData($data, count($data));
    }

    public function saveClass(ClassSave $request)
    {
        $field = [
            'class_id',
            'show_name',
            'class_name',
            'parent_id',
            'status',
            'class_icon',
            'class_brief',
            'sort',
            'sign',
            'class_type',
        ];
        $collert = $request->only($field);
        $classModel = new PoolClassModel();

        if (empty($collert['class_id'])) {
            $result = $classModel->addClass($collert);
            if (!$result) {
                return $this->setError('新增失败');
            }
        } else {
            $result = $classModel->saveClass($collert);
            if (!$result) {
                return $this->setError('修改失败');
            }
        }

        return $this->setSuccess();

    }

    //单独修改分类状态
    public function hideClass(Request $request)
    {
        $field = ['class_id', 'status'];
        $collert = $request->only($field);

        $classModel = new PoolClassModel();
        $result = $classModel->setClassStatus($collert['class_id'], $collert['status']);
        if (!$result) {
            return $this->apiReturn(10001, '修改失败');
        }
        return $this->setSuccess();

    }

    /**
     * @param $request
     * 获取新版联营分类
     */
    public function choicePoolClass(Request $request)
    {
        $map = $request->input('class_id') ? $request->input('class_id') : 0;
        $Model = new PoolClassModel();
        $list = $Model->where('status', 1)->where('parent_id', '=', $map)->get();
        if (!$list) {
            $list = $list->toArray();
        }
        return $this->apiReturn(0, '', ['data' => $list]);
    }

    public function updateClassSort(Request $request)
    {
        $Model = new PoolClassModel();
        $classId = $request->get('class_id');
        $sort = $request->get('sort');
        $result = $Model->where('class_id', '=', $classId)
            ->update(['update_time' => time(), 'sort' => $sort]);
        //还要去修改redis
        $redis = Redis::connection('sku');
        $class = $redis->hget('pool_class_info', $classId);
        if ($class) {
            $class = json_decode($class, true);
            $class['sort'] = $sort;
            $redis->hset('pool_class_info', $classId, json_encode($class));
        }
        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('修改分类排序失败');
        }
    }

    public function updateShowName(Request $request)
    {
        $Model = new PoolClassModel();
        $classId = $request->get('class_id');
        $showName = $request->get('show_name');
        $result = $Model->where('class_id', '=', $classId)
            ->update(['update_time' => time(), 'show_name' => $showName]);
        //还要去修改redis
        $redis = Redis::connection('sku');
        $class = $redis->hget('pool_class_info', $classId);
        if ($class) {
            $class = json_decode($class, true);
            $class['show_name'] = $showName;
            $redis->hset('pool_class_info', $classId, json_encode($class));
        }
        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('修改展示名称失败');
        }
    }

    //获取子分类列表
    public function getClassSubList(Request $request)
    {
        $field = ['parent_id'];
        $collert = $request->only($field);

        if (empty($collert['parent_id'])) {
            return $this->setSuccess();
        }
        $classModel = new PoolClassModel();
        $list = $classModel->where($classModel->map($collert))->pluck('class_name', 'class_id')->toArray();
        return $this->setSuccessData($list);
    }

    public function downloadAttrImportTemplate(Request $request)
    {
        $classId = $request->input('class_id');
        if (!$classId) {
            return $this->setError('分类ID不能为空');
        }
        return (new ClassService())->generateAttrTemplate($classId);
    }

    public function updateClassType(Request $request)
    {
        $classId = $request->input('class_id');
        $classType = $request->input('class_type');
        //一级分类以及子分类都要去修改
        $result = (new ClassService())->changeClassType($classId, $classType);
        if ($result) {
            return $this->setSuccess('设置分类类型成功');
        } else {
            return $this->setError('设置分类类型失败');
        }
    }

    /**
     * 导出分类列表
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function exportClass(Request $request)
    {
        $fileName = '分类列表_' . date('YmdHis') . '.xlsx';
        return Excel::download(new ClassExport($request->all()), $fileName);
    }
}


