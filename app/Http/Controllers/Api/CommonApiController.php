<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Models\Supplier\SupplierChannelModel;
use App\Http\Services\StandardBrandService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;


class CommonApiController extends Controller
{

    public function editorUpload(Request $request)
    {
        foreach ($request->file('wang-editor-image-file') as $uploadFile) {
            try {
                $path = $uploadFile->store('images');
            } catch (\Exception $exception) {
                return $this->setError($exception->getMessage());
            }
            $file = \Storage::get($path);
            $url = config('website.UploadUrl');
            $data['upload'] = $file;
            $data['source'] = 1;
            $data['k1'] = time();
            $data['k2'] = MD5(MD5($data['k1']) . Config('website.UploadKey'));
            $data['FileType'] = 'csv';
            $result = Http::post($url, $data);
            dd($result);
            dd($result->body());
        }
    }

    //批量校验标准品牌,并且返回具体的id和错误信息
    public function checkStandardBrandNameList(Request $request)
    {
        //接收的是标准品牌名称
        $standardBrandNameList = $request->input('standard_brand_name_list');
        if (empty($standardBrandNameList)) {
            return $this->setSuccessData([
                'invalid_brand_name_list' => [],
                'valid_brand_name_list' => [],
                'valid_brand_ids' => [],
            ]);
        }
        $standardBrandNameList = explode(',', trim($standardBrandNameList, ','));
        $result = (new StandardBrandService())->checkStandardBrandNameList($standardBrandNameList);
        return $this->setSuccessData($result);
    }

    //获取供应商编码列表
    public function getCanalListForXmSelect(Request $request)
    {
        $data = [];
        $map = $request->all();
        $query = SupplierChannelModel::where('is_type', SupplierChannelModel::TYPE_official)
            ->whereIn('status', [0, -1, 1, 2]);
        if (!empty($map['supplier_name'])) {
            if (\strlen($map['supplier_name'])==8 && strpos($map['supplier_name'], 'L') === 0) {
                $query->where('supplier_code', 'like', "%{$map['supplier_name']}%");
            }else{
                $query->where('supplier_name', 'like', "%{$map['supplier_name']}%");
            }
        }
        $codeList = $query->select(['supplier_name', 'supplier_code'])->paginate(30)->toArray();
        foreach ($codeList['data'] as $key => $code) {
            $data[] = [
                'supplier_code' => $code['supplier_code'],
                'supplier_name' => $code['supplier_name'] . '(' . $code['supplier_code'] . ')',
            ];
        }
        $lastPage = \Arr::get($codeList, 'last_page');
        $total = \Arr::get($codeList, 'total');
        echo json_encode([
            'err_code' => 0,
            'err_msg' => 'ok',
            'total' => $total,
            'count' => $total,
            'data' => $data,
            'last_page' => $lastPage
        ]);
        exit();
    }
}
