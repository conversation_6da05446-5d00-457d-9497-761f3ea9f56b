<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Models\ClassManagementModel;
use App\Http\Services\ClassManagementService;
use App\Http\Validator\ClassManagementValidator;
use App\Exceptions\InvalidRequestException;

class ClassManagementApiController extends Controller
{
    /**
     * 获取分类管理列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getClassManagementList(Request $request)
    {
        $map = $request->all();
        $data = (new ClassManagementService())->getClassManagementList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    /**
     * 保存分类管理
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveClassManagement(Request $request)
    {
        $params = $request->only([
            'id',
            'class_id',
        ]);

        if (empty($params['id'])) {
            return $this->setError('ID不能为空');
        }

        if (empty($params['class_id'])) {
            return $this->setError('请选择分类');
        }

        try {
            // 更新分类并标记为已处理
            $result = (new ClassManagementService())->updateClassification($params['id'], $params['class_id']);

            if (!$result) {
                return $this->setError('更新分类失败');
            }

            return $this->setSuccess('更新分类成功');
        } catch (InvalidRequestException $e) {
            return $this->setError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setError('更新分类失败：' . $e->getMessage());
        }
    }

    /**
     * 更新状态
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request)
    {
        $id = $request->input('id');
        $status = $request->input('status');

        if (empty($id)) {
            return $this->setError('ID不能为空');
        }

        if (!in_array($status, [ClassManagementModel::STATUS_PENDING, ClassManagementModel::STATUS_PROCESSED])) {
            return $this->setError('状态值不正确');
        }

        try {
            $result = (new ClassManagementService())->updateStatus($id, $status);

            if ($result) {
                return $this->setSuccess('更新状态成功');
            } else {
                return $this->setError('更新状态失败');
            }
        } catch (InvalidRequestException $e) {
            return $this->setError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setError('更新状态失败：' . $e->getMessage());
        }
    }

    /**
     * 删除分类管理
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteClassManagement(Request $request)
    {
        $id = $request->input('id');

        if (empty($id)) {
            return $this->setError('ID不能为空');
        }

        try {
            $result = (new ClassManagementService())->deleteClassManagement($id);

            if ($result) {
                return $this->setSuccess('删除分类管理成功');
            } else {
                return $this->setError('删除分类管理失败');
            }
        } catch (InvalidRequestException $e) {
            return $this->setError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setError('删除分类管理失败：' . $e->getMessage());
        }
    }
}
