<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\InvalidRequestException;
use App\Exports\ShopAttrExport;
use App\Http\Controllers\Controller;
use App\Http\Models\BigData\ShopAttrMappingModel;
use App\Http\Models\BigData\ShopInfoModel;
use App\Http\Services\AlikeSpuService;
use App\Http\Services\AttrService;
use App\Http\Services\ShopAttrMappingService;
use App\Http\Services\ShopAttrService;
use App\Http\Services\ShopInfoService;
use App\Http\Validator\ShopAttrMappingValidator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Response;
use Maatwebsite\Excel\Facades\Excel;

class ShopAttrApiController extends Controller
{

    public function getShopAttrList(Request $request)
    {
        $map = $request->all();
        if ($request->input('is_export')) {
            $params = $request->input('params');
            $map = json_decode($params, true);
            return Excel::download(new ShopAttrExport($map), '第三方属性导出.xlsx');
        }
        $data = ShopAttrService::getShopAttrList($map);
        return $this->setSuccessData($data['data'], $data['total']);
    }

    public function getShopAttrMappingList(Request $request)
    {
        $map = $request->all();
        $data = ShopAttrMappingService::getShopAttrMappingList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }


    //根据平台来获取分类给xm-select使用
    public function getShopAttrByPlatform(Request $request)
    {
        $platform = $request->input('platform');
        $result = ShopAttrService::getShopAttrByPlatform($platform);
        return $this->setSuccessData($result);
    }

    public function saveShopAttrMapping(Request $request)
    {
        $isDraft = $request->input('is_draft');
        $validator = new ShopAttrMappingValidator();
        $mappingData = $validator->check($request);
        $shopClassId = $request->input('shop_class_id');
        $platform = $request->input('platform');
        ShopAttrMappingService::saveShopAttrMapping($shopClassId, $mappingData, $platform, $isDraft);
        $draftText = $isDraft ? '草稿' : '';
        return $this->setSuccess('保存映射' . $draftText . '成功');
    }

    public function deleteShopAttrMapping(Request $request)
    {
        $id = $request->input('id');
        if (empty($id)) {
            return $this->setError('需要删除的映射id不能为空');
        }
        $result = ShopAttrMappingService::deleteShopAttrMapping($id);
        if (!$result) {
            return $this->setError('删除映射失败');
        }

        return $this->setSuccess('删除映射成功');
    }

    public function getAttrsByShopClassId(Request $request)
    {
        $classId = $request->input('class_id');
        $platform = $request->input('platform');
        $data = ShopAttrService::getAttrsByShopClassId($classId, $platform);
        return $this->setSuccessData($data);
    }

    public function saveShopAttrMappingItem(Request $request)
    {
        $params = $request->only([
            'id',
            'is_required',
            'input_type',
            'attr_value_list',
            'attr_value',
            'unit'
        ]);
        $updateData = [];
        switch ($params['input_type']) {
            case 1:
                if (!empty($params['attr_value'])) {
                    $updateData['value'] = json_encode([(int)$params['attr_value']]);
                } else {
                    throw new InvalidRequestException('请选择参数值');
                }
                break;
            case 2:
                if (!empty($params['attr_value_list'][0])) {
                    $updateData['value'] = explode(',', trim($params['attr_value_list'][0], ','));
                    $updateData['value'] = array_map(function ($value) {
                        return (int)$value;
                    }, $updateData['value']);
                    $updateData['value'] = json_encode($updateData['value']);
                } else {
                    throw new InvalidRequestException('请选择参数值');
                }
                break;
            case 3:
                $updateData['value'] = $params['attr_value'];
                break;
            case 7:
                if (!is_numeric($params['attr_value'])) {
                    throw new InvalidRequestException('录入方式为数值,请填写数字');
                }
                $updateData['value'] = $params['attr_value'];
            case 10:
                if (!($params['attr_value'])) {
                    throw new InvalidRequestException('请输入参数值');
                }

                $updateData['value'] = json_encode([
                    'unit' => $params['unit'],
                    'value' => $params['attr_value']
                ]);
                break;

            default:
                $updateData['value'] = $params['attr_value'];
        }
        $updateData['update_time'] = time();
        $updateData['update_name'] = $request->user->name;
        $updateData['update_uid'] = $request->user->userId;
        $result = ShopAttrMappingModel::where('id', $params['id'])->update($updateData);
        if (!$result) {
            return $this->setError('修改映射失败');
        }
        return $this->setSuccess('修改映射成功');
    }
}
