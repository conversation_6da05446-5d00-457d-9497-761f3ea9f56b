<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\InvalidRequestException;
use App\Http\ApiHelper\ApiCode;
use App\Http\Controllers\Controller;
use App\Http\Models\BigData\ShopSkuLogModel;
use App\Http\Services\AlikeSpuService;
use App\Http\Services\BrandService;
use App\Http\Services\ShopSkuLogService;
use App\Http\Services\ThirdExportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Response;
use Maatwebsite\Excel\Facades\Excel;

class ShopSkuLogApiController extends Controller
{

    public function getShopSkuPushLogList(Request $request)
    {
        $map = $request->all();
        $data = ShopSkuLogService::getShopSkuPushLogList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    public function exportShopSkuPushLogList(Request $request)
    {
        $param = $request->input();
        $param["login_uid"] = $request->user->userId;
        $param["login_name"] = $request->user->name;

        $ExportService = new ThirdExportService();
        $data = $ExportService->exportSkuPushLogList($param);
        return $this->setSuccessData($data["data"], 0, ApiCode::API_CODE_SUCCESS, $data["err_msg"]);
    }
}
