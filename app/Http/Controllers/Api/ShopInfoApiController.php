<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Services\BrandService;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Redis;
use App\Http\Services\AlikeSpuService;
use App\Http\Services\ShopInfoService;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;
use App\Http\Models\BigData\ShopInfoModel;
use App\Exceptions\InvalidRequestException;
use App\Http\Models\BigData\ShopConfigModel;
use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Models\Cms\CmsUserIntraCodeModel;

class ShopInfoApiController extends Controller
{

    public function getShopInfoList(Request $request)
    {
        $map = $request->all();
        $data = ShopInfoService::getShopInfoList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    public function saveShopInfo(Request $request)
    {
        $map = $request->only([
            'shop_id',
            'platform',
            'shop_name',
            'shop_name_en',
            'shop_type',
            'sales_user_id',
            'sales_user_name',
            'purchase_coefficient',
            'sale_coefficient',
        ]);
        // $salesUserName = CmsUserInfoModel::where('userId', $map['sales_user_id'])->value('name');
        if (empty($map['shop_id'])) {
            ShopInfoModel::insert([
                'platform' => $map['platform'],
                'shop_name' => $map['shop_name'],
                'shop_type' => $map['shop_type'],
                'shop_name_en' => $map['shop_name_en'],
                // 'sales_user_id' => $map['sales_user_id'],
                'sales_user_name' => $map['sales_user_name'],
                'purchase_coefficient' => $map['purchase_coefficient'],
                'sale_coefficient' => $map['sale_coefficient'],
                'status' => 1,
                'create_time' => time(),
            ]);
        } else {
            ShopInfoModel::where('shop_id', $map['shop_id'])->update([
                'platform' => $map['platform'],
                'shop_name' => $map['shop_name'],
                'shop_type' => $map['shop_type'],
                'shop_name_en' => $map['shop_name_en'],
                // 'sales_user_id' => $map['sales_user_id'],
                'sales_user_name' => $map['sales_user_name'],
                'purchase_coefficient' => $map['purchase_coefficient'],
                'sale_coefficient' => $map['sale_coefficient'],
                'update_time' => time(),
            ]);
        }


        return $this->setSuccess();
    }

    public function getShopInfoListByPlatform(Request $request)
    {
        $platform = $request->input('platform');
        $shopList = ShopInfoModel::where('platform', $platform)->select(['shop_id', 'shop_name'])->get()->toArray();
        return $this->setSuccessData($shopList);
    }

    public function getShopConfigList(Request $request)
    {
        $map = $request->all();
        $data = ShopInfoService::getShopConfigList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    public function saveShopConfig(Request $request)
    {
        $map = $request->only([
            'id',
            'shop_id',
            'app_key',
            'app_secret',
            'token_redis_name',
        ]);

        //表单校验
        $validator = Validator::make($map, [
            'shop_id' => 'required|integer',
            'app_key' => 'required|string',
            'app_secret' => 'required|string',
            'token_redis_name' => 'required|string',
        ]);
        if ($validator->fails()) {
            return $this->setError($validator->errors()->first());
        }

        if (empty($map['id'])) {
            //判断shop_id是否已经存在
            $exists = ShopConfigModel::where('shop_id', $map['shop_id'])->exists();
            if ($exists) {
                return $this->setError('店铺配置已存在');
            }
            $map['create_time'] = time();
            $map['create_uid'] = request()->user->userId;
            $map['create_name'] = request()->user->name;
            $result = ShopConfigModel::insert($map);
        } else {
            $map['update_time'] = time();
            $map['update_uid'] = request()->user->userId;
            $map['update_name'] = request()->user->name;
            $result = ShopConfigModel::where('shop_id', $map['shop_id'])->update($map);
        }

        if ($result !== false) {
            $shopEnName = ShopInfoModel::where('shop_id', $map['shop_id'])->value('shop_name_en');
            //保存一份到redis
            $shopConfig = ShopConfigModel::where('shop_id', $map['shop_id'])->first()->toArray();
            $redis = Redis::connection('sku');
            $redis->hset('lie_shop_distribute_config', $shopEnName, json_encode($shopConfig));
        }
        return $this->setSuccess();
    }
}
