<?php

namespace App\Http\Controllers\Api;

use App\Exports\StandardEncapExport;
use App\Http\Controllers\Controller;
use App\Http\Services\SpuService;
use App\Http\Services\StandardEncapService;
use App\Http\Transformers\StandardEncapSpuTransformer;
use App\Http\Validator\StandardEncapValidator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class StandardEncapApiController extends Controller
{
    public function getStandardEncapList(Request $request)
    {
        $map = $request->all();
        $service = new StandardEncapService();
        $data = $service->getStandardEncapList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    public function disableStandardEncap(Request $request)
    {
        $encapId = $request->input('standard_encap_id');
        $service = new StandardEncapService();
        if ($service->checkHasMappingEncap($encapId)) {
            return $this->setError('该封装有相关的映射封装,不能被禁用');
        }
        $result = $service->disableStandardEncap($encapId);
        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('禁用失败');
        }
    }

    public function enableStandardEncap(Request $request)
    {
        $encapId = $request->input('standard_encap_id');
        $service = new StandardEncapService();
        $result = $service->enableStandardEncap($encapId);
        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('禁用失败');
        }
    }

    public function saveStandardEncap(Request $request)
    {
        $map = $request->all();
        if (empty($map['encap_name'])) {
            return $this->setError('封装名称不能为空');
        }
        $service = new StandardEncapService();
        $result = $service->saveStandardEncap($map);
        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('修改失败');
        }
    }


    public function exportStandardEncap(Request $request)
    {
        $params = $request->input('params');
        $map = json_decode($params, true);
        return Excel::download(new StandardEncapExport($map), '标准封装导出.xlsx');
    }

    public function searchStandardEncap(Request $request)
    {
        $encapName = $request->input('encap_name');
        $result = (new StandardEncapService())->searchStandardEncap($encapName);
        echo json_encode($result);
    }

    //获取单个标品的信息
    public function getStandardEncapById(Request $request)
    {
        $standardEncapId = $request->input('standard_encap_id');
        $standardEncap = (new StandardEncapService())->getStandardEncapById($standardEncapId);
        return $this->setSuccessData($standardEncap);
    }

    //根据分类id获取标准封装和普通封装
    public function getEncapByClassId(Request $request)
    {
        $classId1 = $request->input('class_id1');
        $classId2 = $request->input('class_id2');
        $classId2 = empty($classId2) ? 298 : $classId2;
        $encapList = StandardEncapService::getEncapByClassId($classId1, $classId2);
        return $this->setSuccessData($encapList);
    }
}
