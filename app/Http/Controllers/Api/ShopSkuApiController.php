<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Exports\ShopSkuExport;
use App\Http\ApiHelper\ApiCode;
use App\Http\Services\BrandService;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Services\ShopSkuService;
use Illuminate\Support\Facades\Redis;
use App\Http\Services\AlikeSpuService;
use Illuminate\Support\Facades\Response;
use App\Http\Models\BigData\ShopSkuModel;
use App\Http\Services\ThirdExportService;
use App\Http\Models\BigData\ShopInfoModel;
use App\Exceptions\InvalidRequestException;

class ShopSkuApiController extends Controller
{

    public function getShopSkuList(Request $request)
    {
        ini_set('memory_limit', '1024M');
        $map = $request->all();
        $data = ShopSkuService::getShopSkuList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    //导入商品库存
    public function importShopSkuStock(Request $request)
    {
        $file = $request->file('file');
        $result = ShopSkuService::uploadShopSku($file);
        if (!$result) {
            return $this->setError('上传处理失败,请联系管理员');
        }

        return $this->setSuccess('库存更新成功,请等待后台任务同步到对应商家');
    }

    //更新商品库存
    public function updateShopSkuStock(Request $request)
    {
        $id = $request->input('id');
        $stock = $request->input('stock');
        $result = ShopSkuService::updateShopSkuStock($id, $stock);
        if (!$result) {
            return $this->setError('修改库存失败,请联系管理员');
        }

        return $this->setSuccess('修改库存成功');
    }

    //导出商品
    public function exportShopSku(Request $request)
    {
        $param = $request->input();
        $param["login_uid"] = $request->user->userId;
        $param["login_name"] = $request->user->name;

        $ExportService = new ThirdExportService();
        $data = $ExportService->exportShopSkuList($param);
        return $this->setSuccessData($data["data"], 0, ApiCode::API_CODE_SUCCESS, $data["err_msg"]);
    }

    //拉取商品(按店铺来)
    public function fetchShopSku(Request $request)
    {
        $shopId = $request->input('shop_id');
        if (empty($shopId)) {
            return $this->setError('请选择店铺');
        }

        $redis = Redis::connection('sku');
        if ($redis->get('fetch_shop_sku_' . $shopId) && $request->user->userId != 1000) {
            return $this->setError('同一个店铺每小时内只能进行一次数据全量同步');
        } else {
            $redis->set('fetch_shop_sku_' . $shopId, 1);
            $redis->expire('fetch_shop_sku_' . $shopId, 3600);
        }
        $result = Http::post(config('website.ShopApi') . '/getAllSku', [
            'shop_id' => $shopId,
        ])->json();
        if (isset($result['code'])) {
            if ($result['code'] == 1) {
                throw new InvalidRequestException($result['msg']);
            }
        }
        return $this->setSuccess('正在同步店铺全量数据,请稍后查看');
    }

    //拉取商品信息
    public function fetchSkuInfo(Request $request)
    {
        $shopId = $request->input('shop_id');
        $shopSkuId = $request->input('shop_sku_id');
        if (empty($shopId)) {
            return $this->setError('请选择店铺');
        }
        if (empty($shopSkuId)) {
            return $this->setError('请输入商品ID');
        }
        $result = ShopSkuService::fetchSkuInfo($shopId, $shopSkuId);
        if ($result) {
            return $this->setSuccess('拉取商品信息成功');
        } else {
            return $this->setError('拉取商品信息失败');
        }
    }

    //打厂直标
    public function changeCzFlag(Request $request)
    {
        $data = json_decode($request->input('data'), true);
        $result = ShopSkuService::changeCzFlag($data);
        if (!$result) {
            return $this->setError('打厂直标失败');
        }
        return $this->setSuccess('打厂直标成功');
    }

    //导入价格系数
    public function batchUpdateSkuRate(Request $request)
    {
        $file = $request->file('file');
        $result = ShopSkuService::batchUpdateSkuRate($file);
        if (!$result) {
            return $this->setError('导入价格系数失败');
        }
        return $this->setSuccess('导入价格系数成功');
    }
}
