<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Services\EncapService;
use Illuminate\Http\Request;

class EncapApiController extends Controller
{

    public function getEncapList(Request $request)
    {
        $map = $request->all();
        $data = (new EncapService())->getEncapList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    public function searchEncap(Request $request)
    {
        $encapName = $request->input('encap_name');
        $result = (new EncapService())->searchEncap($encapName);
        echo json_encode($result);
    }

    public function saveEncap(Request $request)
    {
        $data = $request->only([
            'encap_id',
            'encap_name',
            'status',
        ]);
        $result = (new EncapService())->saveEncap($data);
        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('保存失败');
        }
    }


    public function changeEncapStatus(Request $request)
    {
        $encapId = $request->input('encap_id');
        $status = $request->input('status');
        $result = (new EncapService())->changeEncapStatus($encapId, $status);
        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('修改失败');
        }
    }

    public function getEncapListForSelect(Request $request)
    {
        $encapName = $request->input('encap_name');
        $encapName = trim($encapName);
        $result = (new EncapService())->getEncapListForSelect($encapName);
        return $this->setSuccessData($result['data'], $result['total']);
    }


    //导出品牌
    public function exportEncaps(Request $request)
    {
        $params = $request->input('params');
        $map = json_decode($params, true);
        return (new EncapService())->exportEncaps($map);
    }

    //修改是否处理
    public function updateIgnoreHandle(Request $request)
    {
        $encapId = $request->input('encap_id');
        $encapIds = $request->input('encap_ids');
        $ignoreHandle = $request->input('ignore_handle');
        $ignoreHandleReason = $request->input('ignore_handle_reason');
        if (empty($encapIds)) {
            $result = (new EncapService())->updateIgnoreHandle($encapId, $ignoreHandle, $ignoreHandleReason);
        } else {
            $result = (new EncapService())->batchUpdateIgnoreHandle($encapIds, $ignoreHandle, $ignoreHandleReason);
        }
        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('修改失败');
        }
    }
}
