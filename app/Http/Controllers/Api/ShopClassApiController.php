<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Exports\ShopClassExport;
use App\Http\Services\BrandService;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Redis;
use App\Http\Services\AlikeSpuService;
use App\Http\Services\ShopInfoService;
use App\Exports\ShopClassMappingExport;
use App\Http\Services\ShopClassService;
use Illuminate\Support\Facades\Response;
use App\Http\Models\BigData\ShopInfoModel;
use App\Exceptions\InvalidRequestException;
use App\Http\Services\ShopClassMappingService;
use App\Http\Validator\ShopClassMappingValidator;

class ShopClassApiController extends Controller
{
    public function getShopClassList(Request $request)
    {
        $map = $request->all();
        if ($request->input('is_export')) {
            $params = $request->input('params');
            $map = json_decode($params, true);
            return Excel::download(new ShopClassExport($map), '第三方分类导出.xlsx');
        }
        $data = (new ShopClassService())->getShopClassList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    public function getShopClassMappingList(Request $request)
    {
        $map = $request->all();
        if ($request->input('is_export')) {
            $params = $request->input('params');
            $map = json_decode($params, true);
            return Excel::download(new ShopClassMappingExport($map), '分类映射导出.xlsx');
        }
        $data = ShopClassMappingService::getShopClassMappingList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    //根据平台来获取分类给xm-select使用
    public function getShopClassByPlatform(Request $request)
    {
        $platform = $request->input('platform');
        $hasMapping = $request->input('has_mapping', false);
        $result = ShopClassService::getShopClassByPlatform($platform, $hasMapping);
        return $this->setSuccessData($result);
    }

    public function saveShopClassMapping(Request $request)
    {
        $params = $request->only([
            'id',
            'platform',
            'shop_class_id',
            'image',
            'class_id2',
            'is_warranty',
            'warranty_month',
            'title_jd_attr_id',
        ]);
        $validator = new ShopClassMappingValidator();
        $validator->check($params);
        $result = ShopClassMappingService::saveShopClassMapping($params);

        if (!$result) {
            return $this->setError('保存映射失败');
        }

        return $this->setSuccess('保存映射成功');

    }

    public function deleteShopClassMapping(Request $request)
    {
        $id = $request->input('id');
        if (empty($id)) {
            return $this->setError('需要删除的映射id不能为空');
        }
        $result = ShopClassMappingService::deleteShopClassMapping($id);
        if (!$result) {
            return $this->setError('删除映射失败');
        }

        return $this->setSuccess('删除映射成功');
    }

}
