<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Services\SearchForbidService;
use Illuminate\Http\Request;


class SearchForbidApiController extends Controller
{

    //获取搜索屏蔽列表
    public function getSearchForbidList(Request $request)
    {
        $map = $request->all();
        $result = (new SearchForbidService())->getSearchForbidList($map);
        return $this->setSuccessData($result['data'], $result['count']);
    }

    //新增保存搜索屏蔽列表
    public function saveSearchForbid(Request $request)
    {
        $data = $request->only([
            'id',
            'supplier_id',
            'standard_brand_id',
            'eccn',
            'eccn_file_url',
            'eccn_file_name',
            'goods_name',
            'goods_name_file_url',
            'goods_name_file_name',
            'sku_id',
            'sku_id_file_url',
            'sku_id_file_name',
            'spu_id',
            'spu_id_file_url',
            'spu_id_file_name',
        ]);
        if (empty($data['supplier_id']) && $data['supplier_id'] !== '0') {
            return $this->setError('供应商不能为空');
        }
        $result = (new SearchForbidService())->saveSearchForbid($data);
        if (!$result) {
            return $this->setError('操作搜索屏蔽失败');
        }

        return $this->setSuccess();
    }

    //新增保存搜索屏蔽列表
    public function deleteSearchForbid(Request $request)
    {
        $id = $request->input('id');
        $result = (new SearchForbidService())->deleteSearchForbid($id);
        if (!$result) {
            return $this->setError('删除搜索屏蔽失败');
        }

        return $this->setSuccess();
    }

}
