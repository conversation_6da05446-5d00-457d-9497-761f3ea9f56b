<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Services\BrandService;
use App\Http\Services\SampleClassService;
use Illuminate\Http\Request;

class SampleClassApiController extends Controller
{

    public function getSampleClassList(Request $request)
    {
        $map = $request->all();
        $data = SampleClassService::getSampleClassList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    //保存样品
    public function saveSampleClass(Request $request)
    {
        $data = $request->only(['id', 'class_name', 'sort', 'status']);
        if (empty($data['class_name'])) {
            return $this->setError('分类名称不能为空');
        }
        if (empty($data['sort'])) {
            return $this->setError('分类排序不能为空');
        }
        $result = SampleClassService::saveSampleClass($data);
        if (!$result) {
            return $this->setError('保存样品分类失败,请联系管理员');
        }

        return $this->setSuccess('保存成功');
    }

    public function deleteSampleClass(Request $request)
    {
        $isBatch = $request->input('is_batch');
        if ($isBatch == 1) {
            $ids = $request->input('ids');
            $result = SampleClassService::batchDeleteSampleClass($ids);
        } else {
            $id = $request->get('id');
            $result = SampleClassService::deleteSampleClass($id);
        }
        if (!$result) {
            return $this->setError('删除失败,请联系管理员');
        }

        return $this->setSuccess('删除成功');
    }
}
