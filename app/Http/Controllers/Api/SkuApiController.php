<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\InvalidRequestException;
use App\Http\ApiHelper\ApiCode;
use App\Http\Controllers\Controller;
use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Models\TaskLogModel;
use App\Http\Services\BrandService;
use App\Http\Services\SkuLabelService;
use App\Http\Services\SkuService;
use App\Http\Services\ThirdExportService;
use App\Http\Utils\ValidatorMsg;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;

class SkuApiController extends Controller
{

    //获取商品列表
    public function getSkuList(Request $request)
    {
        $map = $request->all();
        $data = (new SkuService())->getSkuList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    public function batchUpdateSkuStatus(Request $request)
    {
        $data = $request->only([
            'sku_ids',
            'is_long_term',
            'operate_type',
            'cp_time',
        ]);
        if ($data['operate_type'] == SkuService::OPERATE_TYPE_PUTAWAY) {
            if ($data['is_long_term'] == -1 && empty($data['cp_time'])) {
                return $this->setError('请设置上架有效期');
            }
        }

        (new SkuService())->batchUpdateSkuStatus($data);
        return $this->setSuccess('操作成功,请等待后台上下架任务完成刷新查看');
    }

    //选择sku分类
    public function getChooseSkuClass(Request $request)
    {
        $classId = $request->input('class_id');
        $Model = new PoolClassModel();
        $list = $Model->where('status', PoolClassModel::STATUS_OK)->where('parent_id', '=', $classId)->get();
        if (!$list) {
            $list = $list->toArray();
        }
        return $this->setSuccessData($list);
    }

    public function saveSku(Request $request)
    {
        $data = $request->only([
            'goods_id',
            'spu_id',
            'goods_name',
            'supplier_id',
            'goods_type',
            'canal',
            'batch_sn',
            'moq',
            'mpq',
            'stock',
            'hk_delivery_time',
            'cn_delivery_time',
            'goods_images',
            'goods_status',
            'purchases',
            'price_cn',
            'price_us',
            'cost_price',
            'alike_goods_name',
            'cn_period',
            'hk_period',
            'encoded',
            'sku_detail',
            'org_id',
            'multiple',
            'coo',
            'pack',
        ]);

        $validator = Validator::make($data, [
            'goods_id' => 'required',
            'spu_id' => 'required',
            'supplier_id' => 'required',
            'goods_status' => 'required',
            'moq' => 'required',
            'mpq' => 'required',
        ], [], [
            'supplier_id' => '供应商',
            'spu_id' => 'SPU型号',
            'goods_type' => 'SKU类别',
            'moq' => '起订量',
            'mpq' => '标准包装量',
            'cn_delivery_time' => '大陆货期',
            'hk_delivery_time' => '香港货期',
        ]);

        //只有猎芯和华云中文一定需要一个货期
        if (in_array($data['org_id'], [1, 3])) {
            if (!$data['cn_delivery_time'] && !$data['hk_delivery_time']) {
                if ($data['supplier_id'] == 17) {
                    return $this->setError('大陆货期和香港货期必选设置一个');
                }
            }
        }

        if ($validator->fails()) {
            $errors = $validator->errors();
            return $this->setError($errors->first());
        }
        $result = (new SkuService())->saveSku($data);
        if (!$result) {
            return $this->setError('修改SKU失败');
        }
        return $this->setSuccess();
    }

    public function exportSku(Request $request)
    {
        $param = $request->input();
        //分页传p,数量传offset,一次性不要传太多offset,因为有请求es和db的
        $param['p'] = 1;
        $param['offset'] = 20;
        $data = (new SkuService())->getSkuList($param);
        $skuList = \Arr::get($data, 'data');
        $count = \Arr::get($data, 'count');
        $param["login_uid"] = $request->user->userId;
        $param["login_name"] = $request->user->name;
    }

    //导出Sku
    public function exportSkuList(Request $request)
    {
        $param = $request->input();
        $param["login_uid"] = $request->user->userId;
        $param["login_name"] = $request->user->name;
      //  $param["org_id"] = $request->user->org_id ?? 1;

        $ExportService = new ThirdExportService();
        $data = $ExportService->exportSkuList($param);
        return $this->setSuccessData($data["data"], 0, ApiCode::API_CODE_SUCCESS, $data["err_msg"]);
    }

    //修改SKU库存
    public function updateSkuStock(Request $request)
    {
        $goodsId = $request->input('goods_id');
        $stock = $request->input('stock');
        if (empty($goodsId)) {
            return $this->setError('skuid不能为空');
        }
        if ($stock < 0) {
            return $this->setError('库存不能为负数');
        }
        //查出原来的库存
        $redis = Redis::connection('sku');
        $sku = $redis->hget('sku', $goodsId);
        if (empty($sku)) {
            return $this->setError('sku缓存为空');
        }
        $sku = json_decode($sku, true);
        $originalStock = \Arr::get($sku, 'stock');
        $diffStock = $stock - $originalStock;
        if (!$diffStock) {
            return $this->setSuccess('修改库存成功');
        }
        $updateType = $diffStock > 0 ? 1 : -1;
        $result = (new SkuService())->updateSkuStock($updateType, $goodsId, abs($diffStock));
        if (!$result) {
            return $this->setError('修改库存失败');
        }

        return $this->setSuccess('修改库存成功');
    }

    //sku标签信息列表
    public function getSkuLabelList(Request $request)
    {
        $data = SkuLabelService::getSkuLabelList();
        return $this->setSuccessData($data['data'], $data['count']);
    }

    //保存sku标签
    public function saveSkuLabel(Request $request)
    {
        $data = $request->only([
            'id',
            'content',
            'url',
        ]);

        SkuLabelService::saveSkuLabel($data['id'], $data);
        return $this->setSuccess('修改sku分区信息成功');
    }

    //SKU更新分类任务
    public function updateSku(Request $request)
    {
        $file = $request->file('file');
        $result = SkuService::updateSkuTask($file);
        if (!$result) {
            return $this->setError('上传处理失败,请联系管理员');
        }

        return $this->setSuccess('上传任务开始,请耐心等待处理完成');
    }

    //下载sku更新结果
    public function downloadSkuUpdateResult(Request $request)
    {
        $id = $request->input('id');
        $log = TaskLogModel::where('id', $id)->first()->toArray();
        $resultFileUrl = $log['result_file_url'];
        $filePath = $resultFileUrl;
        $path = storage_path() . '/' . 'app/' . $filePath;
        if ($path) {
            return Response::download($path);
        }

        return "找不到结果文件";
    }
}
