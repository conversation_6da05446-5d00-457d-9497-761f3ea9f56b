<?php

namespace App\Http\Controllers\Api;

use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use App\Http\Requests\ClassSave;
use App\Http\Utils\ValidatorMsg;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Requests\ClassAttrSave;
use Illuminate\Support\Facades\Redis;
use App\Http\Services\ClassAttrService;
use Illuminate\Support\Facades\Validator;
use App\Exceptions\InvalidRequestException;
use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Models\PoolClass\ClassAttrUnitModel;
use App\Http\Models\PoolClass\PoolClassAttrModel;
use App\Http\Models\PoolClass\ClassAttrValueModel;
use App\Http\Models\PoolClass\ClassAttrUnitConvertModel;
use App\Http\Models\PoolClass\ClassAttrUnitConversionModel;

class ClassAttrApiController extends Controller
{

    //参数列表
    public function classAttrList(Request $request)
    {
        $field = ['attr_name', 'class_name', 'status', 'attr_id', 'class_id', 'is_main', 'show_name', 'insert_type', 'default_unit_convert_id', 'preference'];
        $collert = $request->only($field);
        $collert = array_map(function ($value) {
            return trim($value);
        }, $collert);
        $limit = $request->input('limit', 10);
        $list = (new ClassAttrService())->getClassAttrList($collert, $limit);
        foreach ($list['data'] as &$item) {
            $item['add_time'] = $item['add_time'] ? date('Y-m-d H:i:s', $item['add_time']) : '';
            $item['insert_type_name'] = config('field.AttrInsertType')[$item['insert_type']] ?? '无';
            $item['preference_name'] = config('field.AttrPreference')[$item['preference']] ?? '无';
        }
        unset($item);
        return $this->setSuccessData($list['data'], $list['total']);
    }

    //参数列表,给选择用的
    public function getClassAttrList(Request $request)
    {
        $field = ['attr_name', 'status', 'attr_id', 'class_id', 'insert_type', 'default_unit_convert_id'];
        $collert = $request->only($field);
        $collert['status'] = 1;
        $attrId = Arr::get($collert, 'attr_id');
        unset($collert['attr_id']);

        $attrModel = new PoolClassAttrModel();

        $field = ['attr_id', 'attr_name'];
        $list = $attrModel->where($attrModel->map($collert))
            ->select($field)
            ->orderBy('attr_id', 'desc')->paginate($request->input('limit', 10))->toArray();
        $list = Arr::only($list, ['data', 'total', 'last_page']);

        if ($attrId && empty($collert['attr_name'])) {
            $attr = $attrModel->where('attr_id', '=', $attrId)->select('attr_id', 'attr_name', 'class_id')->first();
            if ($attr) {
                $attr = $attr->toArray();
                if ($attr['class_id'] == $collert['class_id']) {
                    $result = Arr::first($list['data'], function ($key, $value) use ($attr, &$keys) {
                        $keys = $key;
                        return $value['attr_id'] === $attr['attr_id'];
                    }, false);
                    if (!$result) {
                        unset($attr['class_id']);
                        $attr['selected'] = true;
                        $list['data'][-1] = $attr;
                        ksort($list['data']);
                        $list['data'] = array_values($list['data']);
                    } else {
                        $list['data'][$keys]['selected'] = true;
                    }
                }

            }
        }
        return $this->setSuccessData($list['data'], $list['total']);
    }


    //获取单位列表
    public function getClassAttrUnitList(Request $request)
    {
        $field = ['attr_unit_id', 'attr_unit_name', 'attr_id'];
        $collert = $request->only($field);
        $list = (new ClassAttrService())->getClassAttrUnitList($collert);
        return response()->json([
            'code' => 0,
            'count' => $list['total'],
            'data' => $list['data'],
            'last_page' => $list['last_page'],
        ]);
    }

    /*
     * 新增/修改参数
     */
    public function saveClassAttr(ClassAttrSave $request)
    {
        $field = [
            'attr_name',
            'show_name',
            'is_main',
            //'attr_unit_id',
            'class_id',
            'attr_id',
            'remark',
            'status',
            'eng_name',
            'preference',
            'insert_type',
            'default_unit_convert_id',
        ];
        $map = $request->only($field);
        //校验上面的字段都不能为空
        $validator = Validator::make($map, [
            'insert_type' => 'required',
        ], [], [
            'insert_type' => '录入方式',
            'default_unit_convert_id' => '默认单位',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            throw new InvalidRequestException($error);
        }
        if ($map['insert_type'] != 5) {
            if (empty($map['default_unit_convert_id'])) {
                return $this->setError('录入方式为纯文本时,默认单位才允许为空');
            }
        } else {
            $map['default_unit_convert_id'] = 0;
            $map['preference'] = 0;
        }
        //default_unit_convert_id 现在支持多选
        $attrId = $map['attr_id'];
        $attrModel = new PoolClassAttrModel();
        $redis = Redis::connection('sku');
        $originAttrName = '';
        if (empty($attrId)) {
            $find = $attrModel->where('class_id', '=', $map['class_id'])->where('attr_name', $map['attr_name'])
                ->select('attr_id')->first();
            if ($find) {
                return $this->setError('属性名称已经存在，无需重复添加');
            }
            $map['create_name'] = $request->user->name;
            $map['add_time'] = time();
            $result = $attrId = $attrModel->insertGetId($map);
        } else {
            $originAttrName = $attrModel->where('attr_id', $map['attr_id'])->value('attr_name');
            $map['update_time'] = time();
            $result = $attrModel->where('attr_id', '=', $map['attr_id'])->update($map);
            $attr = $attrModel->where('attr_id', $map['attr_id'])->first()->toArray();
            if (!empty($attr['dgk_attr_id'])) {
                //保存重要属性到redis
                $classModel = new PoolClassModel();
                $className = $classModel->where('class_id', $attr['class_id'])->value('class_name');
                $data = [
                    'class_id' => $attr['class_id'],
                    'class_name' => $className,
                    'attr_id' => $attr['attr_id'],
                    'attr_name' => $attr['attr_name'],
                    'dgk_attr_id' => (int)$attr['dgk_attr_id'],
                    'is_spec' => $attr['is_main'],
                ];
                $redis->hset('class_important_attr', (int)$attr['dgk_attr_id'], json_encode($data));
            }
        }

        $attr = $attrModel->where('attr_id', $attrId)->first()->toArray();
        $redis->hset('class_attr_by_id', $attr['attr_id'], $attr['attr_name']);
        //反向的也存一个
        if (!empty($originAttrName)) {
            $redis->hdel('class_attr_by_val', $originAttrName);
        }
        $redis->hset('class_attr_by_val', $attr['attr_name'], $attr['attr_id']);

        $offsetData = $attr['class_id'] . '_' . $attr['attr_id'];
        if (empty($map['preference'])) {
            $redis->hdel('filter_offset', $offsetData);
        } else {
            $map['preference'] = $map['preference'] == 3 ? 2 : $map['preference'];
            $redis->hset('filter_offset', $offsetData, $map['preference']);
        }


        if (!$result) {
            return $this->setError('提交失败');
        }

        return $this->setSuccess('提交成功');
    }

    /*
     * 显示隐藏这个参数
     */
    public function hideClassAttr(Request $request)
    {
        $field = ['attr_id', 'status'];

        $collert = $request->only($field);

        $classModel = new PoolClassAttrModel();
        $result = $classModel->setAttrStatus($collert['attr_id'], $collert['status']);
        if (!$result) {
            return $this->setError('修改失败');
        }
        return $this->setSuccess();
    }

    //修改分类属性
    public function updateIsMain(Request $request)
    {
        $attrId = $request->get('attr_id');
        $classId = $request->get('class_id');
        $isMain = $request->get('is_main');
        $data = [
            'class_id' => $classId,
            'is_main' => $isMain,
            'update_time' => time(),
        ];
        $classAttrModel = new PoolClassAttrModel();
        //先去判断这个分类下面的主要属性是否超过5个
//        $mainAttrCount = $classAttrModel->where('class_id', $classId)->where('is_main', 1)->count();
//        if ($mainAttrCount >= 5 && $isMain == 1) {
//            return $this->setError('一个分类下最多允许设置5个主要参数');
//        }
        $res = $classAttrModel->where('attr_id', $attrId)->update($data);
        if ($res) {
            return $this->setSuccess();
        }
        return $this->setError('更新失败');
    }

    //修改展示名称
    public function updateShowName(Request $request)
    {
        $model = new PoolClassAttrModel();
        $attrId = $request->get('attr_id');
        $showName = $request->get('show_name');
        $result = $model->where('attr_id', '=', $attrId)
            ->update(['update_time' => time(), 'show_name' => $showName]);
        if ($result) {
            return $this->setSuccess();
        } else {
            return $this->setError('修改展示名称失败');
        }
    }

    public function getClassAttrListByClassId(Request $request)
    {
        $classId = $request->input('class_id');
        $spuId = $request->input('spu_id');
        $attrList = PoolClassAttrModel::where('class_id', $classId)->get()->toArray();
        $count = count($attrList);
        $attrsExtend = [];
        if ($spuId) {
            //去mongo
            $mongo = DB::connection('mongodb');
            $spuAttrs = $mongo->table('spu_attrs2')->where('spu_id', (int)$spuId)->first();
            if ($spuAttrs) {
                $attrsExtend = !empty($spuAttrs['attrs_extend']) ? $spuAttrs['attrs_extend'] : [];
                $attrsExtend = collect($attrsExtend)->keyBy('attr_id')->toArray();
            }
        }
        $attrList = array_map(function ($attr) use ($attrsExtend) {
            $attr['insert_type_name'] = Arr::get(config('field.AttrInsertType'), $attr['insert_type'], '无');
            $attr['value_count'] = ClassAttrValueModel::where('attr_id', $attr['attr_id'])->count();
            //不是文本类型
            if ($attr['insert_type'] != 5) {
                $attr['unit_convert']['insert_type_name'] = Arr::get(config('field.AttrInsertType'), $attr['insert_type'], '无');
                $attr['unit_convert']['convert_name'] = '无';
                if(!empty($attr['default_unit_convert_id'])){
                    $defaultUnitConvertId = explode(',', $attr['default_unit_convert_id']);
                    $attr['unit_convert'] = ClassAttrUnitConvertModel::whereIn('id', $defaultUnitConvertId)->get()->toArray();
                }
            } else {
                $attr['unit_convert'] = [];
                if(!empty($attr['default_unit_convert_id'])){
                    $defaultUnitConvertId = explode(',', $attr['default_unit_convert_id']);
                    $attr['unit_convert'] = ClassAttrUnitConvertModel::whereIn('id', $defaultUnitConvertId)->get()->toArray();
                }
                if (!empty($attr['unit_convert'])) {
                    $attr['unit_convert'] = $attr['unit_convert'][0];
                    $attr['unit_convert']['standard_unit_name'] =  '';
                    $attr['unit_convert']['standard_unit_name'] = implode(',', array_column($attr['unit_convert'], 'standard_unit_name'));
                    $attr['unit_convert']['insert_type_name'] = Arr::get(config('field.AttrInsertType'), $attr['insert_type'], '无');
                } else {
                    if ($attr['insert_type'] != 5) {
                        $attr['unit_convert']['convert_name'] = '无';
                        $attr['unit_convert']['insert_type_name'] = '无';
                    }else{
                        $attr['unit_convert']['convert_name'] = '文本';
                        $attr['unit_convert']['insert_type_name'] = '文本';
                    }

                }
            }
            //如果有spu_id,还要去补充数据
            if (!empty($attrsExtend[$attr['attr_id']])) {
                $extend = $attrsExtend[$attr['attr_id']];
                $data = (new ClassAttrService())->extractAttr($attr['attr_id'], $extend['attr_value']);
                if (!is_array($data)) {
                    $data = [
                        'value' => $extend['attr_value'],
                        'value_unit' => '',
                        'origin_value' => $extend['attr_value'],
                        'unit' => '',
                    ];
                }
                $attr['valid'] = 1;
                $attr['origin_value'] = $extend['attr_value'];
                $attr = array_merge($attr, $data);
            }
            return $attr;
        }, $attrList);
        return $this->setSuccessData($attrList, $count);
    }

    //分离参数并且校验
    public function extractAttr(Request $request)
    {
        $attrId = $request->input('attr_id');
        $value = $request->input('value');
        $result = (new ClassAttrService())->extractAttr($attrId, $value);
        if (!is_array($result)) {
            return $this->setError($result);
        }

        return $this->setSuccessData($result);
    }
}


