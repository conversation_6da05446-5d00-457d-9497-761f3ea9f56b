<?php

namespace App\Http\Controllers\Api;


use App\Http\Controllers\Controller;
use App\Http\Services\ShopUnitMappingService;
use App\Http\Validator\ShopUnitMappingValidator;
use Illuminate\Http\Request;


class ShopUnitApiController extends Controller
{

    public function getShopUnitMappingList(Request $request)
    {
        $map = $request->all();
        $data = ShopUnitMappingService::getShopUnitMappingList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    //获取没有映射的单位列表
    public function getNotMappingShopUnitList(Request $request)
    {
        $map = $request->all();
        $data = ShopUnitMappingService::getNotMappingShopUnitList($map);
        return $this->setSuccessData($data['data'], $data['count']);
    }

    //根据平台来获取分类给xm-select使用
    public function getShopUnitListByPlatform(Request $request)
    {
        $platform = $request->input('platform');
        $notMapping = $request->input('not_mapping');
        $result = ShopUnitMappingService::getShopUnitListByPlatform($platform, $notMapping);
        return $this->setSuccessData($result);
    }

    public function saveShopUnitMapping(Request $request)
    {
        $params = $request->only([
            'id',
            'platform',
            'ratio',
            'attr_name',
            'unit_name',
            'standard_unit_name',
        ]);
        $validator = new ShopUnitMappingValidator();
        $validator->check($params);
        //dd($params);
        $result = ShopUnitMappingService::saveShopUnitMapping($params);

        if (!$result) {
            return $this->setError('保存单位映射失败');
        }

        return $this->setSuccess('保存单位映射成功');

    }


    public function deleteShopUnitMapping(Request $request)
    {
        $id = $request->input('id');
        if (empty($id)) {
            return $this->setError('需要删除的单位映射id不能为空');
        }
        $result = ShopUnitMappingService::deleteShopUnitMapping($id);
        if (!$result) {
            return $this->setError('删除单位映射失败');
        }

        return $this->setSuccess('删除单位映射成功');
    }


}
