<?php

namespace App\Http\Controllers\Api;

use App\Http\ApiHelper\ApiCode;
use App\Http\Controllers\Controller;
use App\Http\Models\BrandModel;
use App\Http\Models\TaskLogModel;
use App\Http\Services\AlikeSpuService;
use App\Http\Services\IdService;
use App\Http\Services\SpuLogService;
use App\Http\Services\SpuService;
use App\Http\Services\ThirdExportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Response;


class SpuApiController extends Controller
{
    //SPUl列表
    public function getSpuList(Request $request)
    {
        $service = new SpuService();
        $result = $service->getSpuList($request);
        return $this->setSuccessData($result['list'], $result['total']);
    }

    //批量下架SPU
    public function offShelf(Request $request)
    {
        $map = $request->input('spu_id');
        if (empty($map) || !is_array($map)) {
            return $this->setError('请选择要操作的SPU');
        }

        $status = $request->input('status');
        $redis = Redis::connection('sku');
        $spuRedis = Redis::connection('spu');
        $data['update_time'] = time();
        $msg = '';
        switch ($status) {
            case 'offshelf':
                $data['status'] = 3;
                $msg = '成功下架';
                break;
            case 'passed':
                $msg = '成功上架';
                $data['status'] = 1;
                break;
            case 'negative':
                $data['status'] = 2;
                $msg = '审核不通过';
                break;
        }

        $i = 0;
        foreach ($map as $k => $v) {
            $dbInfo = resolveDB($v);
            $result = DB::connection('spu')->table($dbInfo['table'])->where('spu_id', $v)->update($data);
            if ($result) {
                $info = json_decode($spuRedis->hget('spu', $v), true);
                $info['spu_id'] = $v;
                $info['status'] = $data['status'];
                $info['update_time'] = $data['update_time'];
                SpuService::handleSpu($info);
                SpuLogService::addSpuLog($v, "$msg SPU");
                $i++;
            }
        }
        //实时推送ES
        $spuIds = implode(',', $map);
        (new SpuService())->pushSpuUpdate($spuIds);
        return $this->setSuccess($msg . $i . '件SPU');
    }

    public function saveSpu(Request $request)
    {
        $params = $request->input();
        //if (empty($params['class_id2'])) {
        //    return $this->setError('请选择分类');
        //}
        if (empty($params['spu_name'])) {
            return $this->setError('请填写SPU型号');
        }
        if (empty($params['brand_id'])) {
            return $this->setError('请选择制造商');
        }

        //通过模型取出需要传递的数据，避免_url的出现
        $spuData = \Arr::only($params, [
            'spu_id',
            'class_id1',
            'class_id2',
            'brand_id',
            'spu_name',
            'status',
            'images_l',
            'images_s',
            'encap',
            'pdf',
            'spu_brief',
            'spu_title',
            'has_rohs',
            'spu_detail',
            'application_level',
            'eccn',
            'series',
            'lifecycle',
            'brand_pack',
            'mpq',
            'bussiness_area',
            'humistor',
            'attrs',

            //spu_extra
            'standard_lead_time',
            'mounting_type',
            'length',
            'width',
            'height',
            'weight',
            'reach_status',
            'customs_code',
            'en_pdf_url',
            'spu_image_list',

        ]);
        if (is_array($spuData['bussiness_area'])) {
            $spuData['bussiness_area'] = $spuData['bussiness_area'] ? implode('|',
                array_keys($spuData['bussiness_area'])) : '';
        } else {
            $spuData['bussiness_area'] = '';
        }
        $spuData['spu_image_list'] = !empty($spuData['spu_image_list']) ? json_decode($spuData['spu_image_list'], true) : [];
        $spuId = (new SpuService())->saveSpu($spuData);
        return $this->setSuccess('保存SPU成功 : ' . $spuId);
    }

    //SPU更新分类任务
    public function updateSpuClass(Request $request)
    {
        $file = $request->file('file');
        $result = SpuService::updateSpuClassTask($file);
        if (!$result) {
            return $this->setError('上传处理失败,请联系管理员');
        }

        return $this->setSuccess('上传任务开始,请耐心等待处理完成');
    }

    //下载spu更新结果
    public function downloadSpuUpdateClassResult(Request $request)
    {
        $id = $request->input('id');
        $log = TaskLogModel::where('id', $id)->first()->toArray();
        $resultFileUrl = $log['result_file_url'];
        $filePath = $resultFileUrl;
        $path = storage_path() . '/' . 'app/' . $filePath;
        if ($path) {
            return Response::download($path);
        }

        return "找不到结果文件";
    }

    //SPU更新属性任务
    public function updateSpuAttr(Request $request)
    {
        $file = $request->file('file');
        $result = SpuService::updateSpuAttrTask($file);
        if (!$result) {
            return $this->setError('上传处理失败,请联系管理员');
        }

        return $this->setSuccess('上传任务开始,请耐心等待处理完成');
    }

    //下载spu更新属性结果
    public function downloadSpuUpdateAttrResult(Request $request)
    {
        $id = $request->input('id');
        $log = TaskLogModel::where('id', $id)->first()->toArray();
        $resultFileUrl = $log['result_file_url'];
        $filePath = $resultFileUrl;
        $path = storage_path() . '/' . 'app/' . $filePath;
        if ($path) {
            return Response::download($path);
        }

        return "找不到结果文件";
    }

    //导出SPU
    public function exportSpuList(Request $request)
    {
        $param = $request->input();
        $param["login_uid"] = $request->user->userId;
        $param["login_name"] = $request->user->name;

        $ExportService = new ThirdExportService();
        $data = $ExportService->exportSpuList($param);
        return $this->setSuccessData($data["data"], 0, ApiCode::API_CODE_SUCCESS, $data["err_msg"]);
    }

    //导出SPU参数
    public function exportSpuAttr(Request $request)
    {
        $param = $request->input();
        $param["login_uid"] = $request->user->userId;
        $param["login_name"] = $request->user->name;

        $ExportService = new ThirdExportService();
        $result = $ExportService->exportSpuAttr($param);
        if (!$result) {
            return $this->setError('只有二级分类才支持参数导出');
        }

        return $this->setSuccessData($result["data"], 0, ApiCode::API_CODE_SUCCESS, $result["err_msg"]);
    }

    //导出SPU 去重导出
    public function exportSpuUniqueList(Request $request)
    {
        $param = $request->input();
        $param["login_uid"] = $request->user->userId;
        $param["login_name"] = $request->user->name;

        $rec = (new SpuService())->getSpuList($request, 3);
        if ($rec["total"] == 0) {
            return $this->setError($rec["msg"]);
        }

        $ExportService = new ThirdExportService();
        $data = $ExportService->exportSpuUniqueList($param);
        return $this->setSuccessData($data["data"], 0, ApiCode::API_CODE_SUCCESS, $data["err_msg"]);
    }


    //SPU更新属性任务
    public function updateSpuPDF(Request $request)
    {
        $spuPDFData = $request->input('spu_PDF_data');
        $lang = $request->input('lang');
        if (empty($spuPDFData)) {
            return $this->setError('上传pdf数据不能为空');
        }
        $result = SpuService::updateSpuPDFTask($spuPDFData, $lang);
        if (!$result) {
            return $this->setError('上传处理失败,请联系管理员');
        }

        return $this->setSuccess('上传任务开始,请耐心等待处理完成');
    }

    //下载spu更新属性结果
    public function downloadSpuUpdatePDFResult(Request $request)
    {
        $id = $request->input('id');
        $log = TaskLogModel::where('id', $id)->first()->toArray();
        $resultFileUrl = $log['result_file_url'];
        $filePath = $resultFileUrl;
        $path = storage_path() . '/' . 'app/' . $filePath;
        if ($path) {
            return Response::download($path);
        }

        return "找不到结果文件";
    }

    //删除spu
    public function deleteSpu(Request $request)
    {
        $spuId = $request->input('spu_id');
        if (empty($spuId)) {
            return $this->setError('请选择要操作的SPU');
        }

        $redis = Redis::connection('sku');
        $spuRedis = Redis::connection('spu');
        $data['update_time'] = time();
        $data['status'] = 4;
        $dbInfo = resolveDB($spuId);
        $result = DB::connection('spu')->table($dbInfo['table'])->where('spu_id', $spuId)->update($data);
        if ($result) {
            $info = json_decode($spuRedis->hget('spu', $spuId), true);
            $info['spu_id'] = $spuId;
            $info['status'] = 4;
            $info['update_time'] = $data['update_time'];
            SpuService::handleSpu($info);
            SpuLogService::addSpuLog($spuId, "删除SPU");
        }
        //实时推送ES
        (new SpuService())->pushSpuUpdate($spuId);
        return $this->setSuccess();
    }

    public function getSpuListForXmSelect(Request $request)
    {
        $page = $request->input('page');
        $standardBrandId = $request->input('standard_brand_id');
        $map = [];
        if ($request->input('spu_name')) {
            $map['spu_name/condition'] = $request->input('spu_name');
        }
        if ($standardBrandId) {
            $map['standard_brand_id/condition'] = $standardBrandId;
        }
        if ($page) {
            $map['p'] = $page;
        }
        $map["no_rule"] = "1122";
        $spuList = [];
        $total = 0;
        $url = config('website.search_domain_new') . '/search/es/searchSpu';
        $result = Http::get($url, $map)->json();
        $redis = Redis::connection('sku');
        $spuRedis = Redis::connection('spu');
        if (isset($result['error_code']) && $result['error_code'] === 0) {
            $total = $result['data']['total'];
            foreach ($result['data']['spu_id'] as $spuId) {
                $spuCache = $spuRedis->hget('spu', $spuId);
                $spuCache = json_decode($spuCache, true);
                $brandId = $spuCache['brand_id'];
                $brandName = $redis->hget('brand', $brandId);
                if ($request->input('ignore_spu_id')) {
                    $spuList[] = [
                        'value' => $spuCache['spu_name'],
                        'name' => $spuCache['spu_name'],
                    ];
                } else {
                    $spuList[] = [
                        'value' => $spuId,
                        'name' => $spuCache['spu_name'] . ' (' . $brandName . ')',
                    ];
                }

            }
        }
        if ($request->input('unique')) {
            $spuList = array_map('unserialize', array_unique(array_map('serialize', $spuList)));
        }
        return $this->setSuccessData($spuList, $total);
    }

    //根据spu型号获取一个spu信息
    public function getSpuBySpuNameAndStandardBrandId(Request $request)
    {
        $spuName = $request->input('spu_name');
        $standardBrandId = $request->input('standard_brand_id');
        $spu = (new SpuService())->getSpuBySpuNameAndStandardBrandId($spuName, $standardBrandId);
        if ($spu) {
            return $this->setSuccessData($spu);
        }

        return $this->setError('找不到对应的spu');
    }

    //获取单个spu信息
    public function getSpuInfo(Request $request)
    {
        $spuId = $request->input('spu_id');
        $spu = (new SpuService())->getSpuInfo($spuId);
        return $this->setSuccessData($spu);
    }
}
