<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Models\PoolClass\ClassAttrUnitConversionModel;
use App\Http\Models\PoolClass\ClassAttrUnitModel;
use App\Http\Models\PoolClass\ClassAttrValueModel;
use App\Http\Models\PoolClass\PoolClassAttrModel;
use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Requests\ClassAttrUnitSave;
use App\Http\Requests\ClassSave;
use App\Http\Services\ClassAttrService;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;

class ClassAttrUnitApiController extends Controller
{

    public function classAttrUnitList(Request $request)
    {
        $field = ['attr_unit_name', 'status'];
        $collert = $request->only($field);

        $UnitModel = new ClassAttrUnitModel();
        $field = ['attr_unit_name', 'attr_unit_id', 'remark', 'status'];
        $list = $UnitModel->where($UnitModel->map($collert))->whereIn('status', [1, 2])
            ->select($field)->orderBy('attr_unit_id', 'desc')->paginate($request->input('limit', 10))->toArray();
        $list = Arr::only($list, ['data', 'total']);
        if (!empty($list['data'])) {
            $attr_unit_id = array_column($list['data'], 'attr_unit_id');
            $ConversionModel = new ClassAttrUnitConversionModel();

            $Conversion = $ConversionModel->whereIn('status', [1, 2])->where(function ($query) use ($attr_unit_id) {
                $query->orWhere(function ($query) use ($attr_unit_id) {
                    $query->whereIn('attr_unit_id_1', $attr_unit_id);
                })->orWhere(function ($query) use ($attr_unit_id) {
                    $query->whereIn('attr_unit_id_2', $attr_unit_id);
                });
            })->select('conversion_id', 'attr_unit_id_1', 'attr_unit_id_2', 'ratio', 'status')
                ->with(['AttrUnitId1', 'AttrUnitId2'])
                ->get();

            foreach ($list['data'] as $k => &$v) {
                $v['conversion_num'] = 0;
                foreach ($Conversion as $k1 => $v1) {
                    if ($v1['attr_unit_id_1'] === $v['attr_unit_id'] || $v1['attr_unit_id_2'] === $v['attr_unit_id']) {
                        $v['conversion_num']++;
                        $v['sub'][] = $v1;
                    }
                }
            }
        }
        return $this->setSuccessData($list['data'], $list['total']);
    }

    /*
       * 参数值单位列表
       */
    public function getClassAttrUnitList(Request $request)
    {
        $field = ['attr_unit_id', 'attr_unit_name', 'attr_id'];

        $collert = $request->only($field);
        $list = (new ClassAttrService())->getClassAttrUnitList($collert);
        return response()->json([
            'code' => 0,
            'count' => $list['total'],
            'data' => $list['data'],
            'last_page' => $list['last_page'],
        ]);
    }

    /*
     * 显示/隐藏/删除参数值单位
     */
    public function hideClassAttrUnit(Request $request)
    {
        $field = ['attr_unit_id', 'status'];
        $collert = $request->only($field);

        $classModel = new ClassAttrUnitModel();

        //查找是否存在单位换算
        if ($collert['status'] == 3) {
            $conversionModel = new ClassAttrUnitConversionModel();
            $conversion = $conversionModel->where('attr_unit_id_1', '=', $collert['attr_unit_id'])
                ->orWhere('attr_unit_id_2', '=', $collert['attr_unit_id'])
                ->select('status')->first();
            if ($conversion && $conversion->status !== 3) {
                return $this->setError('这个单位下面存在换算关系。无法删除');
            }
        }

        $result = $classModel->setAttrValueUnitStatus($collert['attr_unit_id'], $collert['status']);
        if (!$result) {
            return $this->setError('修改失败');
        }
        return $this->setSuccess();
    }

    /*
     * 新增/编辑单位
     */
    public function saveClassAttrUnit(ClassAttrUnitSave $request)
    {
        $field = ['attr_unit_id', 'attr_unit_name', 'status', 'remark'];
        $collert = $request->only($field);

        $unitModel = new ClassAttrUnitModel();
        $find = $unitModel->where('attr_unit_name', '=', $collert['attr_unit_name'])
            ->whereIn('status', [1, 2])->select('attr_unit_id')->first();
        $collert['update_time'] = time();
        $attrUnitId = Arr::get($collert, 'attr_unit_id', '');
        unset($collert['attr_unit_id']);
        if (empty($attrUnitId)) {
            if ($find) {
                return $this->setError('这个单位已经存在，添加失败');
            }
            $collert['add_time'] = $collert['update_time'];
            $result = $unitModel->insert($collert);
        } else {
            if ($find && $attrUnitId != $find->attr_unit_id) {
                return $this->setError('这个单位已经存在，修改失败');
            }
            $result = $unitModel->where('attr_unit_id', '=', $attrUnitId)->update($collert);
        }

        if (!$result) {
            return $this->setError('提交失败');
        }
        return $this->setSuccess('提交成功');
    }

    /*
     * 新增/编辑单位换算
     */
    public function saveClassAttrValueUnitConversion(Request $request)
    {
        $field = ['conversion_id', 'attr_unit_id', 'to_attr_unit_id', 'ratio', 'status'];
        $collert = $request->only($field);

        $collert['update_time'] = time();
        $conversionModel = new ClassAttrUnitConversionModel();
        $attrUnitId = $collert['attr_unit_id'];
        $toAttrUnitId = $collert['to_attr_unit_id'];
        unset($collert['attr_unit_id'], $collert['to_attr_unit_id']);

        //查找换算关系
        $conversion = $conversionModel->whereIn('status', [1, 2])->where(function ($query) use (
            $attrUnitId,
            $toAttrUnitId
        ) {
            $query->orWhere(function ($query) use ($attrUnitId, $toAttrUnitId) {
                $query->where('attr_unit_id_1', '=', $attrUnitId)->where('attr_unit_id_2', '=', $toAttrUnitId);
            })->orWhere(function ($query) use ($attrUnitId, $toAttrUnitId) {
                $query->where('attr_unit_id_2', '=', $attrUnitId)->where('attr_unit_id_2', '=', $toAttrUnitId);
            });
        })->select('conversion_id', 'attr_unit_id_1', 'attr_unit_id_2', 'ratio', 'status')
            ->first();


        if (!empty($collert['conversion_id'])) {
            if ($conversion && $conversion->conversion_id != $collert['conversion_id']) {
                return $this->setError('已经存在这个换算单位');
            }

            $find = $conversionModel->where('conversion_id', '=', $collert['conversion_id'])->whereIn('status', [1, 2])
                ->select('attr_unit_id_1', 'attr_unit_id_2', 'ratio')->first();
            if (!$find) {
                return $this->setError('这个换算被删除，不能编辑');
            }

            $find = $find->toArray();

            if ($find['attr_unit_id_1'] == $attrUnitId) {
                $collert['attr_unit_id_1'] = $attrUnitId;
                $collert['attr_unit_id_2'] = $toAttrUnitId;
            } else {
                if ($find['attr_unit_id_2'] == $attrUnitId) {
                    $collert['attr_unit_id_2'] = $attrUnitId;
                    $collert['attr_unit_id_1'] = $toAttrUnitId;
                } else {
                    return $this->setError('换算关系异常，无法编辑');
                }
            }

            $result = $conversionModel->where('conversion_id', '=', $collert['conversion_id'])->update($collert);
        } else {
            if ($conversion) {
                return $this->setError('已经存在这个换算单位');
            }
            unset($collert['conversion_id']);
            $collert['attr_unit_id_1'] = $attrUnitId;
            $collert['attr_unit_id_2'] = $toAttrUnitId;
            $collert['add_time'] = $collert['update_time'];
            $result = $conversionModel->insert($collert);
        }
        if (!$result) {
            return $this->setError('提交失败');
        }
        return $this->setSuccess('提交成功');
    }

    /*
     * 显示/隐藏/删除单位换算
     */
    public function hideClassAttrUnitConversion(Request $request)
    {
        $field = ['conversion_id', 'status'];
        $collert = $request->only($field);

        $conversionModel = new ClassAttrUnitConversionModel();
        $result = $conversionModel->setAttrValueUnitConversionStatus($collert['conversion_id'], $collert['status']);
        if (!$result) {
            return $this->setError('提交失败');
        }
        return $this->setSuccess('提交成功');
    }
}


