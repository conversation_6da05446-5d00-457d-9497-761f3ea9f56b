<?php

namespace App\Http\Controllers\Api\Api\Sync;

use App\Http\Controllers\Api\Api\Controller;


class BaseSyncController extends Controller
{

    public function startTransaction(){
        \DB::connection("mysql")->beginTransaction();
    }

    public function commitTransaction(){
        \DB::connection("mysql")->commit();
    }



    public function rollBackTransaction(){
        \DB::connection("mysql")->rollBack();
    }

}
