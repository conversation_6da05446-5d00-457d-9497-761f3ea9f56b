<?php

namespace App\Http\Controllers\Api\Api\Sync;

use App\Http\Controllers\Api\Api\Controller;
use App\Http\Models\ScmOrderModel;
use App\Http\Services\Sync\ScmOrderSyncService;
use App\Http\Services\Sync\StockInSyncService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;


/*
 * 同步委托单
 */

class ScmOrderSyncController extends Controller
{

    //绑定供应链返回的信息
    //请求格式 https://www.tapd.cn/20210831/markdown_wikis/show/#1120210831001000595
    public function bindScmOrder(Request $request)
    {
        $params = $request->all();
        \Log::error(json_encode($params));
        if (\Arr::get($params, 'code') != 0) {
            $syncResult = $params['msg'];
            $syncResultData = \Arr::get($params, 'data');
            //这个是走回调接口传过去的委托单号
            $scmOrderSn = $params['search_key'];
            ScmOrderSyncService::syncScmOrderFailed($scmOrderSn, $syncResult, $syncResultData);
            return $this->setError('绑定供应链委托单失败');
        }
        $validator = Validator::make($params['data'], [
            'ID' => 'required',
//            'PTNUMBER ' => 'required',
            "ENTRYS" => 'required',
            "ENTRYS.*.ENTRYID" => 'required',
            "ENTRYS.*.SCMITEMID" => 'required',
            "ENTRYS.*.MATERILID" => 'required',
        ]);
        if ($validator->fails()) {
            return $this->setError($validator->errors()->first());
        }
        $data = $validator->validated();
        $result = ScmOrderSyncService::bindScmOrder($data);
        if (!$result) {
            return $this->setError('修改委托单信息失败');
        }

        return $this->setSuccess();

    }

    //接受委托单审单后的数据
    public function updateScmOrder(Request $request)
    {
        //为什么这里接收的是erp的物料id而不是委托详情id呢,因为在供应链那边,会将多个相同型号品牌和价格的商品合并成一个料
        //所以返回关税等信息的时候,是根据erp物料id来,不能根据委托详情id来
        $params = $request->all();
        \Log::error(json_encode($params));
        $validator = Validator::make($params, [
//            'scm_order_sn' => 'required',
            'warehouse_receipt_sn' => 'required',
            "items" => 'required',
            "items.*.erp_goods_id" => 'required',
            "items.*.erp_scm_item_id" => 'required',
            "items.*.tariff" => 'numeric',
            "items.*.tariff_rate" => 'numeric',
            "items.*.commodity_inspection" => 'max:1',
            "items.*.origin" => 'max:100',
            "items.*.origin_tax" => 'numeric',
        ]);
        if ($validator->fails()) {
            return $this->setError($validator->errors()->first());
        }
        $data = $validator->validated();
        $result = ScmOrderSyncService::updateScmOrder($data);
        if (!$result) {
            return $this->setError('修改委托单信息失败');
        }

        return $this->setSuccess();
    }

    public function confirmAddStockInToGYL(Request $request)
    {
        $params = $request->all();
        \Log::error(json_encode($params));
        if (\Arr::get($params, 'code') != 0) {
            return $this->setError('ERP回调报关状态出错,' . \Arr::get($params, 'msg'));
        }
        $erpScmItemIds = $params['search_key'];
        if (!$erpScmItemIds) {
            return $this->setError('ERP回调报关的search_key不能为空');
        }
        $erpScmItemIds = explode(',', $erpScmItemIds);
        ScmOrderSyncService::confirmAddStockInToGYL($erpScmItemIds);
        return $this->setSuccess('回调报关状态成功');
    }

    //报关成功
    public function customsSuccess(Request $request)
    {
        //报关完成后,还要生成科技金蝶的发货通知单
        $erpScmItemIds = $request->input('erp_scm_item_ids');
        $erpScmItemIds = is_string($erpScmItemIds) ? json_decode($erpScmItemIds, true) : $erpScmItemIds;
        if (!$erpScmItemIds) {
            return $this->setError('报关成功的详情id不能为空');
        }
        $result = ScmOrderSyncService::customsSuccess($erpScmItemIds);
        if (!$result) {
            return $this->setError('报关完成写入失败');
        }

        return $this->setSuccess('报关完成');

    }
}
