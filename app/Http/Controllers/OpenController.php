<?php

namespace App\Http\Controllers;

use App\Http\Models\LiexinData\GoodsModel;
use App\Map\GoodsMap;
use App\Model\RedisModel;
use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use App\Http\Models\BrandModel;
use App\Http\Services\SkuService;
use App\Http\Services\SpuService;
use Illuminate\Support\Facades\DB;
use App\Http\Services\ClassService;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use App\Http\Services\EncodedService;
use App\Http\Services\ShopSkuService;
use Illuminate\Support\Facades\Redis;
use App\Http\Models\BrandMappingModel;
use App\Http\Models\ClassManagementModel;
use App\Http\Services\SkuSeriesService;
use App\Http\Services\ShopSkuLogService;
use App\Http\Services\ShopSpuLogService;
use App\Http\Transformers\SkuTransformer;
use App\Http\Services\StandardBrandService;
use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Models\StandardBrandMappingModel;

//sku对外接口（锁库，减少库存，取消锁库 等接口操作）
class OpenController extends Controller
{
    //导出SKU
    public function exportSkuList(Request $request)
    {
        $service = new SkuService();
        $input = $request->input();
        $request->offsetSet("user", (object)["userId" => Arr::get($input, "login_uid", 0)]);
        $input["is_export"] = 1;
        $result = $service->getSkuList($input);
        $count = $result["count"] > 5000 ? 5000 : $result["count"];
        if ($request->user->userId == 1000) {
            $count = $result["count"];
        }
        return $this->setSuccessData($result["data"], $count);
    }

    //导出SPU
    public function exportSpuList(Request $request)
    {
        $service = new SpuService();
        $result = $service->getSpuList($request, 1);
        return $this->setSuccessData($result["list"], $result["total"] > 5000 ? 5000 : $result["total"]);
    }

    //导出SPU(SPU型号+标准品牌  去重导出)
    public function exportSpuUniqueList(Request $request)
    {
        $service = new SpuService();
        $result = $service->getSpuList($request, 3);
        return $this->setSuccessData($result["list"], $result["total"] > 5000 ? 5000 : $result["total"]);
    }

    //导出SPU参数
    public function exportSpuAttr(Request $request)
    {
        $service = new SpuService();
        $result = $service->getSpuList($request, 2);
        return $this->setSuccessData($result["list"], $result["total"] > 5000 ? 5000 : $result["total"]);
    }

    //对联营库存锁库等操作
    public function lockLyStock(Request $request)
    {
        $s = (new SkuService())->lockLyStock($request->input());
        return $s === true ? $this->setSuccessData('成功') : $this->setError($s);
    }

    //批量更新库存
    public function updateBatchSkuStock(Request $request)
    {
        $rawData = file_get_contents('php://input'); //原始json推送

        write_log("updateBatchSkuStock-传入参数:" . $rawData . "\n", "footstone");
        $s = (new SkuService())->updateBatchSkuStock(json_decode($rawData, true));
        return $s === true ? $this->setSuccessData('成功') : $this->setError($s);
    }

    //获取联营可购买数量
    public function getLyStock(Request $request)
    {
        $data = (new SkuService())->getLyStock($request->input("goods_id"));
        return $this->setSuccessData($data);
    }

    /*
     * erp编码查询自营信息
     */
    public function searchZy(Request $request)
    {
        write_log("searchZy-传入参数:" . json_encode($request->input()) . "\n", "footstone");
        $data = (new SkuService())->searchZy($request->input("erp_number"));
        return $this->setSuccessData($data);
    }

    /*
 * erp编码查询自营信息
 */
    public function searchLy(Request $request)
    {
        $data = (new SkuService())->searchLy(explode(",", $request->input("goods_ids")));
        return $this->setSuccessData($data);
    }

    /*
     * 更新自营编码数据
     */
    public function updateZy(Request $request)
    {
        write_log("updateZy-传入参数:" . json_encode($request->input()) . "\n", "footstone");
        $list = $request->input("list");
        if ($list) {
            foreach ($request->input("list") as $a) {
                $data = (new SkuService())->updateZy($a);
            }
        } else {
            $data = (new SkuService())->updateZy($request->input());
        }
        return $this->setSuccessData($data);
    }

    /*
     * 批量推送wms
     */
    public function pushGoodsToWms(Request $request)
    {
        write_log("pushGoodsToWms-传入参数:" . json_encode($request->input()) . "\n", "footstone");
        $goods_ids = $request->input("goods_ids");
        $data = (new SkuService())->pushGoodsToWms(explode(",", $goods_ids));
        return $this->setSuccessData($data);
    }

    public function getClassification(Request $request)
    {
        $redis = Redis::connection('sku');
        $classId = $request->input('class_id');
        if ($classId) {
            //            $classCache = $redis->hget('pool_class_info', $classId);
            $classCache = \DB::connection('class')->table('class_back')->where('class_id', $classId)->get()->toArray();
            return $this->setSuccessData($classCache);
        } else {
            $classCount = $redis->hgetAll('pool_class_info_count');
            //            $classCache = $redis->hgetall('pool_class_info');
            $classCache = \DB::connection('class')->table('class_back')->where('class_name_en', '!=', '')->get()->toArray();
            $classCache = array_map(function ($value) use ($redis, $classCount) {
                //                $value = json_decode($value, true);
                $value = (array)$value;
                $value['sku_number'] = \Arr::get($classCount, $value['class_id'], 0);
                return $value;
            }, $classCache);
            $classCache = array_values($classCache);
            return $this->setSuccessData($classCache);
        }
    }

    //获取分类的新接口,给开放平台使用的
    public function getClassList(Request $request)
    {
        $classType = $request->input('class_type');
        $classList = (new ClassService())->getClassList($classType);
        return $this->setSuccessData($classList);
    }

    //这是缓存获取
    public function getStandardBrandList(Request $request)
    {
        $standardBrandCache = Redis::connection('sku')->hgetall('standard_brand');
        $standardBrandCache = array_map(function ($value) {
            return json_decode($value, true);
        }, $standardBrandCache);
        $standardBrandCache = array_values($standardBrandCache);
        return $this->setSuccessData($standardBrandCache);
    }

    //这是db获取
    public function standardBrandList(Request $request)
    {
        $map = $request->all();
        $service = new StandardBrandService();
        $data = $service->getStandardBrandList($map);
        if (!empty($data['data'])) {
            foreach ($data['data'] as &$item) {
                $item = Arr::only($item, [
                    'standard_brand_id',
                    'brand_name',
                    'create_time'
                ]);
            }
            unset($item);
        }
        return $this->setSuccessData($data['data'], $data['count']);
    }

    public function getStandardBrandInfo(Request $request)
    {
        $brandId = $request->input('brand_id');
        $brand = Redis::connection('sku')->hget('standard_brand', $brandId);
        $brand = json_decode($brand, true);
        return $this->setSuccessData($brand);
    }


    /*
     * 深茂获取相关数据
     *    hget("sku_raw_map",$goods_id); //digikey 编码
     *    hget("standard_brand",$standard_brand_id); //获取标准品牌
     *    synchronization   //获取商品详情
     *
     */
    public function getSemourData(Request $request)
    {
        $types = $request->input("types");
        $k = $request->input("k");
        $karr = explode(",", $k);

        $redis = Redis::connection('sku');
        $spuRedis = Redis::connection('spu');
        $temp = "";
        switch ($types) {
            case "sku_raw_map":
                if (count($karr) == 1) {
                    $temp = $spuRedis->hget($types, $k);
                } else {
                    $temp = [];
                    foreach ($karr as $k => $v) {
                        $c = $spuRedis->hget($types, $v);
                        $temp[$v] = json_decode($c, true);
                    }
                }
                break;
            case "standard_brand":
            case "search_supplier_canaltopurchase":
                if (count($karr) == 1) {
                    $temp = $redis->hget($types, $k);
                } else {
                    $temp = [];
                    foreach ($karr as $k => $v) {
                        $c = $redis->hget($types, $v);
                        $temp[$v] = json_decode($c, true);
                    }
                }
                break;
            case "goods_info":
                $req_params['goods_id'] = $karr;
                $response = Http::asForm()->post(get_resource_config_section('domain', 'domain')['goods_server'] . "/synchronization", $req_params);
                $res = json_decode($response->body(), true);
                $temp = $res['data'];
        }
        return $this->setSuccessData($temp);
    }

    //根据商品系列获取所有sku列表
    public function getSkuSeriesBySkuId(Request $request)
    {
        $skuId = $request->input('sku_id');
        if (empty($skuId)) {
            return $this->setError('sku_id不能为空');
        }
        $result = (new SkuSeriesService())->getSkuSeriesBySkuId($skuId);
        return $this->setSuccessData($result);
    }

    //导出SKU推送日志
    public function exportShopSkuPushLog(Request $request)
    {
        $map = $request->all();
        $map["is_export"] = 1;
        $data = ShopSkuLogService::getShopSkuPushLogList($map);
        return $this->setSuccessData($data["data"], $data["total"]);
    }

    //导出SPU推送日志
    public function exportShopSpuPushLog(Request $request)
    {
        $map = $request->all();
        $map["is_export"] = 1;
        $data = ShopSpuLogService::getShopSpuPushLogList($map);
        return $this->setSuccessData($data["data"], $data["total"]);
    }

    //根据型号名称+标准品牌id获取spu信息
    public function getSpuBySpuNameAndStandardBrandId(Request $request)
    {
        //支持获取多个
        //直接获取json,如果是json提交,直接获取
        $itemList = $request->json();
        $spuList = [];
        if (!empty($itemList)) {
            foreach ($itemList as $item) {
                $spuName = $item['spu_name'];
                $standardBrandId = $item['standard_brand_id'];
                $spu = (new SpuService())->getSpuBySpuNameAndStandardBrandId($spuName, $standardBrandId);
                $spu['spu_extra'] = (new SpuService())->getSpuExtra($spu['spu_id']) ?? [];
                if (!empty($spu['spu_extra']['weight'])) {
                    $spu['spu_extra']['weight'] = convertToGrams($spu['spu_extra']['weight']);
                }
                $spuList[$spuName . '€' . $standardBrandId] = $spu;
            }
        }
        return $this->setSuccessData($spuList);
    }

    //导出店铺SKU列表
    public function exportShopSkuList(Request $request)
    {
        $map = $request->all();
        $map["is_export"] = 1;
        $data = ShopSkuService::getShopSkuList($map);
        return $this->setSuccessData($data["data"], $data["total"]);
    }

    /*
     * 搜索强履:
            1.型号，品牌匹配
            2.库存大于等于数量
            3.起订量小于数量
            4.上架状态
            5.渠道标签非猎芯期货

     * 参数： 型号 标准品牌 数量 非期货 上架
返回：型号 sku_id  强履 标品 采购员名称 采购员id
     */
    /*
     * 搜索强履:
            1.型号，品牌匹配
            2.库存大于等于数量
            3.起订量小于数量
            4.上架状态
            5.渠道标签非猎芯期货

     * 参数： 型号 标准品牌 数量 非期货 上架
返回：型号 sku_id  强履 标品 采购员名称 采购员id
     */
    public function searchAbilitySku(Request $request)
    {
        $input = $request->input();
        $needStock = $input["stock"];
        $supplier_id = Arr::get($input, "supplier_id", "");

        $map = [
            "goods_name_origin/eq" => $input["goods_name"],
            "standard_brand_name/eq" => strtoupper($input["standard_brand_name"]),
            "ability_level/eqs" => $input["ability_level"],
            "stock/sr" => "gte," . $needStock,
            "single_price/sr" => "gt,0", //价格大于0
            "status/eq" => 1,
            "goods_status/eq" => 1,
            "debugt" => @$input["debugt"]
        ];

        if ($supplier_id) { //存在指定供应商
            $map["supplier_id/eqs"] = $supplier_id;
        }

        $map['admin'] = 1; // 后台搜索
        $map['no_rule'] = "1122"; // 后台搜索

        $url = config('website.search_domain_new') . '/search/es/searchSku';
        $res = Http::get($url, $map)->body();
        $data = json_decode($res, true);

        if (Arr::get($map, "debugt") == 1) {
            print_r($url);
            print_r("<br/>");
            print_r(json_encode($map));
            print_r("<br/>");
            print_r($data);
            die();
        }

        $skuList = [];
        if (isset($data['error_code']) && $data['error_code'] == 0) {

            //这里是处理后的sku列表数据
            $skuList = SkuTransformer::listTransformer($data);
            $encodedService = new EncodedService();
            $encodedList = array_column($skuList, 'encoded');
            $encodedUsers = $encodedService->getEncodedUserByEncoded($encodedList);
            $skuList = array_map(function ($item) use ($encodedUsers) {
                $encoded = Arr::get($item, 'encoded', 0);
                $item['spu_id'] = (string)Arr::get($item, 'spu_id');
                $item['encoded_user_name'] = Arr::get($encodedUsers, $encoded, '');
                $item['brand_name'] = isset($item['brand_name']) && !empty($item['brand_name']) ? $item['brand_name'] : '';

                return $item;
            }, $skuList);
        }
        //排除数据
        $canal = ["********"];

        $clearSupplierIds = [];
        if (!$supplier_id) {
            $clearSupplier = [
                6 => "element14",
                3 => "rochester",
                1672 => "master",
                1675 => "corestaff",
                7 => "digikey",
                21 => "rs",
                1 => "future",
                1679 => "ti",
                4 => "tme",
                14 => "mouser",
            ];
            $clearSupplierIds = array_keys($clearSupplier);
        }

        $err_msg = [];
        $temp = [];
        foreach ($skuList as $a => $b) {
            $goods_id = (string)$b["goods_id"];

            if ($b["goods_label_name"] == "国际期货") {
                $err_msg[] = $goods_id . "：国际期货";
                continue;
            }
            if ($b["moq"] > $needStock) {
                $err_msg[] = $goods_id . "：起订量大于需求数量";
                continue;
            }

            if (in_array($b["supplier_id"], $clearSupplierIds)) {
                $err_msg[] = $goods_id . "：排除供应商id";
                continue;
            }
            if (in_array($b["canal"], $canal)) {
                $err_msg[] = $goods_id . "：排除供应商编码";
                continue;
            }

            $temp[] = [
                "goods_id" => $goods_id,
                "encoded" => $b["encoded"],
                "stock" => $b["stock"],
                "cn_delivery_time" => $b["cn_delivery_time"],
                "hk_delivery_time" => $b["hk_delivery_time"],
                "moq" => $b["moq"],
                "mpq" => $b["mpq"],
                "source" => $b["source"],
                "canal" => $b["canal"],
                "canal_id" => $b["canal_id"],
                "canal_name" => $b["canal_name"],
                "batch_sn" => $b["batch_sn"],
                "ability_level" => $b["ability_level"],
                "supplier_id" => $b["supplier_id"],
                "supplier_name" => $b["supplier_name"],
                "encoded_user_name" => $b["encoded_user_name"],
                "standard_brand_name" => $b["standard_brand_name"],
                "standard_brand_id" => $b["standard_brand_id"],
                "ladder_price" => $b["show_price"],
                "cost_ladder_price" => $b["ladder_price"],
                "goods_name" => $b["goods_name"],
            ];
        }
        if (!$temp && $err_msg) {
            return  $this->setSuccessData([], 0, 0, implode(",", $err_msg));
        }

        return $this->setSuccessData($temp);
    }


    /*
       $map =[
            "goods_name_origin/eq"=>$input["goods_name"],
            "standard_brand_name/eq"=>$input["standard_brand_name"],
            "ability_level/eqs"=>$input["ability_level"],
            "stock/sr"=>"gte,1",
            "single_price/sr"=>"gt,0", //价格大于0
            "status/eq"=>1,
            "supplier_id/eq"=>1,
            "goods_status/eq"=>1,
        ];
     (型号+标准品牌+上架状态+没有过期)sku=>(履约程度+采购员+sku_id+moq+mqp)
     */
    public function searchAbSku(Request $request)
    {
        $map = $request->input();
        $map['debugt'] = Arr::get($map, "debugt", 0); //
        $map['admin'] = 1; // 后台搜索
        $map['no_rule'] = "1122"; // 后台搜索

        $url = config('website.search_domain_new') . '/search/es/searchSku';
        $res = Http::get($url, $map)->body();
        $data = json_decode($res, true);

        if (Arr::get($map, "debugt") == 1) {
            print_r($url);
            print_r("<br/>");
            print_r(json_encode($map));
            print_r("<br/>");
            print_r($data);
            die();
        }

        $skuList = [];
        if (isset($data['error_code']) && $data['error_code'] == 0) {
            //这里是处理后的sku列表数据
            $skuList = SkuTransformer::listTransformer($data);
            $encodedService = new EncodedService();
            $encodedList = array_column($skuList, 'encoded');
            $encodedUsers = $encodedService->getEncodedUserByEncoded($encodedList, 2);

            $skuList = array_map(function ($item) use ($encodedUsers) {
                $encoded = Arr::get($item, 'encoded', 0);
                $item['spu_id'] = (string)Arr::get($item, 'spu_id');
                $item['encoded_userId'] = Arr::get(Arr::get($encodedUsers, $encoded, ''), "userId");
                $item['encoded_user_name'] = Arr::get(Arr::get($encodedUsers, $encoded, ''), "name");
                $item['brand_name'] = isset($item['brand_name']) && !empty($item['brand_name']) ? $item['brand_name'] : '';

                return $item;
            }, $skuList);
        }

        if (Arr::get($map, "debugk") == 1) {
            print_r($skuList);
            die();
        }

        $temp = [];
        foreach ($skuList as $a => $b) {
            if (!Arr::get($b,"spu_id","")){
                continue;
            }
            //返回字段：采购员、型号、品牌、履约程度、库存、成本价、批次、供应商名称、供应商编码、SKUID【后3个字段不在选择的列表展示】
            $temp[] = [
                "goods_id" => (string)$b["goods_id"],
                "stock" => $b["stock"],
                "cn_delivery_time" => $b["cn_delivery_time"],
                "hk_delivery_time" => $b["hk_delivery_time"],
                "moq" => $b["moq"],
                "mpq" => $b["mpq"],
                "batch_sn" => $b["batch_sn"],
                "ability_level" => Arr::get($b, "ability_level", ""),
                "supplier_id" => Arr::get($b, "supplier_id", 0),
                "supplier_name" => Arr::get($b, "supplier_name", "") ?: "",
                "canal" => Arr::get($b, "canal", ""),
                "canal_id" => Arr::get($b, "canal_id", 0),
                "canal_name" => Arr::get($b, "canal_name", ""),
                "encoded" => $b["encoded"],
                "encoded_userid" => $b["encoded_userId"],
                "encoded_user_name" => Arr::get($b, "encoded_user_name", ""),
                "standard_brand_name" => Arr::get($b, "standard_brand_name", ""),
                "standard_brand_id" => Arr::get($b, "standard_brand_id", ""),
                "ladder_price" => Arr::get($b, "show_price", ""), //卖价
                "cost_price" => Arr::get($b, "ladder_price", ""), //成本价
                "goods_name" => Arr::get($b, "goods_name", ""),
                "source" => Arr::get($b, "source", ""),
            ];
        }
        return $this->setSuccessData($temp);
    }


    /*
     * 搜索自营数据
       $map =[
            "goods_name_origin/eq"=>$input["goods_name"],
            "standard_brand_name/eq"=>$input["standard_brand_name"],
            "ability_level/eqs"=>$input["ability_level"],
            "stock/sr"=>"gte,1",
            "single_price/sr"=>"gt,0", //价格大于0
            "status/eq"=>1,
            "supplier_id/eq"=>1,
            "goods_status/eq"=>1,
        ];
     (型号+标准品牌+上架状态+没有过期)sku=>(履约程度+采购员+sku_id+moq+mqp)
     */
    public function searchZySku(Request $request)
    {
        #查询专营备货
        $map = $mapt = $request->input();
        $map['debugt'] = Arr::get($map, "debugt", 0); //
        $map['admin'] = 1; // 后台搜索
        $map['no_rule'] = "1122"; // 后台搜索
        $map['canal_new/eq'] = "L0018319"; //自营备货

        $url = config('website.search_domain_new') . '/search/es/searchSku';
        $res = Http::get($url, $map)->body();
        $data = json_decode($res, true);

        if (Arr::get($map, "debugt") == 1) {
            print_r($url);
            print_r("<br/>");
            print_r(json_encode($map));
            print_r("<br/>");
            print_r($data);
            die();
        }

        $skuList = [];
        if (isset($data['error_code']) && $data['error_code'] == 0) {
            //这里是处理后的sku列表数据
            $skuList = SkuTransformer::listTransformer($data);
            $encodedService = new EncodedService();
            $encodedList = array_column($skuList, 'encoded');
            $encodedUsers = $encodedService->getEncodedUserByEncoded($encodedList, 2);

            $skuList = array_map(function ($item) use ($encodedUsers) {
                $encoded = Arr::get($item, 'encoded', 0);
                $item['spu_id'] = (string)Arr::get($item, 'spu_id');
                $item['encoded_userId'] = Arr::get(Arr::get($encodedUsers, $encoded, ''), "userId");
                $item['encoded_user_name'] = Arr::get(Arr::get($encodedUsers, $encoded, ''), "name");
                $item['brand_name'] = isset($item['brand_name']) && !empty($item['brand_name']) ? $item['brand_name'] : '';

                return $item;
            }, $skuList);
        }

        $temp = [];
        foreach ($skuList as $a => $b) {
            //返回字段：采购员、型号、品牌、履约程度、库存、成本价、批次、供应商名称、供应商编码、SKUID【后3个字段不在选择的列表展示】
            $temp[] = [
                "goods_id" => (string)$b["goods_id"],
                "stock" => $b["stock"],
                "cn_delivery_time" => $b["cn_delivery_time"],
                "hk_delivery_time" => $b["hk_delivery_time"],
                "class_id1" => $b["class_id1"],
                "class_id1_name" => $b["class_id1_name"],
                "class_id2" => $b["class_id2"],
                "class_id2_name" => $b["class_id2_name"],
                "mpl" => $b["multiple"],
                "moq" => $b["moq"],
                "mpq" => $b["mpq"],
                "packing"=>$b["packing"],
                "packing_cn"=>$b["packing"],
                "pick_type"=>"",
                "pick_type_cn"=>"",
                "batch_sn" => $b["batch_sn"],
                "ability_level" => Arr::get($b, "ability_level", ""),
                "supplier_id" => Arr::get($b, "supplier_id", 0),
                "supplier_name" => Arr::get($b, "supplier_name", "") ?: "",
                "canal" => Arr::get($b, "canal", ""),
                "canal_id" => Arr::get($b, "canal_id", 0),
                "canal_name" => Arr::get($b, "canal_name", ""),
                "encoded" => $b["encoded"],
                "encoded_userid" => $b["encoded_userId"],
                "encoded_user_name" => Arr::get($b, "encoded_user_name", ""),
                "standard_brand_name" => Arr::get($b, "standard_brand_name", ""),
                "standard_brand_id" => Arr::get($b, "standard_brand_id", ""),
                "ladder_price" => json_encode(Arr::get($b, "show_price", "")), //卖价
                "cost_price" => json_encode(Arr::get($b, "ladder_price", "")), //成本价
                "goods_name" => Arr::get($b, "goods_name", ""),
                "source" => Arr::get($b, "source", ""),
            ];
        }

        #查询自营
        /*
          curl --location 'https://icso.ichunt.com/search/es/searchSku' \
        --form 'goods_name_origin/eq="MBR10200CT"' \
        --form 'no_rule="1122"' \
        --form 'p="1"' \
        --form 'offset="1"' \
        --form 'supplier_id="-2"' \ 自营数据
        --form 'is_open="1"'
         */
        #查询专营备货
        $mapt = $request->input();
        $mapt['admin'] = 1; // 后台搜索
        $mapt['no_rule'] = "1122"; // 后台搜索
        $mapt['flag'] = "88"; // 后台搜索
        $mapt['supplier_id'] = "-2"; // 后台搜索

        $url = config('website.search_domain_new') . '/search/es/searchSku';
        $res = Http::get($url, $mapt)->body();
        if (Arr::get($mapt, "debuga") == 1) {
            print_r($url);
            print_r("<br/>");
            print_r(json_encode($mapt));
            print_r("<br/>");
            print_r($data);
            print_r("<br/>结果：");
            print_r($res);
            die();
        }

        $datat = json_decode($res, true);
        $es_data = $datat["data"];
        $goods_ids = $es_data["goods_id"];
        if ($goods_ids){
            $GoodsModel = new \App\Http\Models\LiexinData\GoodsModel();
            $arr=$GoodsModel->wherein("goods_id",$goods_ids)->get()->toArray();
            $redis = Redis::connection('sku');
            $encodedList = array_column($arr, 'encoded');
            $encodedUsers = $encodedService->getEncodedUserByEncoded($encodedList, 2);

            $goodsServerUrl = config('config.goods_server_domain') . '/synchronization?goods_id=' . implode(',', $goods_ids);
           // $goodsServerUrl = config('config.goods_server_domain') . '/synchronization?goods_id=' . 10288;
            $skuServerData = Arr::get(Http::get($goodsServerUrl)->json(), 'data');

            foreach ($arr as $a =>$b){
                $sysData = Arr::get($skuServerData,$b["goods_id"]);

                $temp[] = [ #【自营id  商品型号  标准品牌  商品分类  标准包装量  包装方式 起订量  倍数  拣货方式  单价】
                    "goods_id"=>$b["goods_id"],
                    "goods_name"=>$b["goods_name"],
                    "class_id1"=>Arr::get($sysData,"class_id1",""),
                    "class_id1_name"=>Arr::get($sysData,"class_id1_name",""),
                    "class_id2"=>Arr::get($sysData,"class_id2",""),
                    "class_id2_name"=>Arr::get($sysData,"class_id2_name",""),
                    "standard_brand_id"=>Arr::get(@$sysData['standard_brand'], 'standard_brand_id',0),
                    "standard_brand_name"=>Arr::get(@$sysData['standard_brand'], 'brand_name',""),
                    "mpq"=>$b["mpq"],
                    "packing"=>$b["packing"],
                    "packing_cn"=>@GoodsMap::$packing[$b["packing"]],
                    "pick_type"=>$b["pick_type"],
                    "pick_type_cn"=>@GoodsMap::$pick_type[$b["pick_type"]],
                    "moq"=>$b["moq"],
                    "mpl"=>$b["mpl"],
                    "stock"=>$b["stock"],
                    "ladder_price"=>$b["ladder_price"],
                    "spu_id"=>$b["spu_id"],
                    //自营采购
                    "encoded"=> $b["encoded"],
                    'encoded_userid' => Arr::get(Arr::get($encodedUsers, $b["encoded"], ''), "userId",""),
                    'encoded_user_name' => Arr::get(Arr::get($encodedUsers, $b["encoded"], ''), "name",""),
                ];
            }
        }


        return $this->setSuccessData($temp);
    }

    public static function curl(Request $request)
    {
        $url = $request->input('url');
        $url = base64_decode($url);
        if (empty($url)) {
            echo "";
            die;
        }
        $data = Http::get($url)->body();
        return $data;
    }

    //获取标准品牌
    public function getStandardBrand(Request $request)
    {
        $standardBrandId = $request->input('standard_brand_id');
        $brandName = $request->input('brand_name');
        if (empty($standardBrandId) && empty($brandName)) {
            return $this->setError('standard_brand_id和brand_name不能同时为空');
        }
        if (!empty($standardBrandId)) {
            $standardBrand = (new StandardBrandService())->getStandardBrandById($standardBrandId);
            return $this->setSuccessData($standardBrand);
        }
        if (!empty($brandName)) {
            $standardBrand = (new StandardBrandService())->getStandardBrandByBrandName($brandName);
            return $this->setSuccessData($standardBrand);
        }
    }

    //获取标准品牌以及相关普通品牌
    public function getStandardBrandAndRelatedBrands(Request $request)
    {
        $brandName = $request->input('brand_name');
        $brandName = trim($brandName, ',');
        if (empty($brandName)) {
            return $this->setError('品牌名称不能为空');
        }
        $brandNameList = explode(',', $brandName);
        $data = [];
        $brandList = BrandModel::whereIn('brand_name', $brandNameList)->get()->toArray();
        foreach ($brandList as $brand) {
            $standardBrandId = StandardBrandMappingModel::where('brand_id', $brand['brand_id'])->value('standard_brand_id');
            if (empty($standardBrandId)) {
                continue;
            }
            $standardBrand = (new StandardBrandService())->getStandardBrandById($standardBrandId);
            if (empty($standardBrand)) {
                continue;
            }
            $relatedBrands = (new StandardBrandService())->getRelatedBrandsByStandardBrandId($standardBrandId);
            $item = [];
            $item['search_key'] = $brand['brand_name'];
            $item['standard_brand'] = $standardBrand;
            $item['related_brands'] = $relatedBrands;
            $data[] = $item;
        }
        $data = array_filter($data, function ($item) use ($brandNameList) {
            $brandNameList = array_map('strtoupper', $brandNameList);
            if (in_array(strtoupper($item['search_key']), $brandNameList)) {
                return true;
            }
            return false;
        });
        $data = array_values($data);
        return $this->setSuccessData($data);
    }

    //根据型号获取SPU信息
    public function getSpuClassBySpuName(Request $request)
    {
        $spuName = $request->input('spu_name');
        $needDivide = $request->input('need_divide', 0);
        if (empty($spuName)) {
            return $this->setError('spu_name不能为空');
        }

        $orderSn  = $request->input('order_sn');
        $salesName = $request->input('sales_name');
        $salesId = $request->input('sales_id');
        if ($needDivide == -1) {
            $spuNameList = [$spuName];
        } else {
            $spuNameList = explode(',', trim($spuName, ','));
            $spuNameList = array_filter($spuNameList, function ($item) {
                return !empty($item);
            });
        }

        try {
            //直接去mongo获取一个spu_id即可
            $mongo = DB::connection('mongodb');
            $spuList = $mongo->table('spu')->whereIn('spu_name', $spuNameList)->get();
            $spuList = \is_array($spuList) ? $spuList : $spuList->toArray();
        } catch (\Throwable $th) {
            $spuList = [];
        }
        if (empty($spuList)) {
            return $this->setSuccessData([]);
        }
        // $spuList = [
        //     [
        //         'spu_id' => "2174859122604227204",
        //         'spu_name' => 'TCL78955456444646464568',
        //     ],
        //     [
        //         'spu_id' => "2169459268083354304",
        //         'spu_name' => 'CND0215A',
        //     ],
        // ];
        $spuNameKeyBySpuId = \collect($spuList)->keyBy('spu_id')->pluck('spu_name', 'spu_id')->toArray();
        $spuData = [];
        //再去spu的redis获取spu信息
        $spuRedis = Redis::connection('spu');

        // Group SPUs by spu_name
        $spusGroupedByName = collect($spuList)->groupBy('spu_name');

        foreach ($spusGroupedByName as $name => $spus) {
            $bestSpu = null;
            $lastSpu = null;

            foreach ($spus as $spuInfoFromMongo) {
                $spuId = $spuInfoFromMongo['spu_id'];
                $spuJson = $spuRedis->hget('spu', $spuId);
                if (empty($spuJson)) continue;

                $currentSpu = json_decode($spuJson, true);
                $currentSpu['spu_id'] = $spuId;

                $lastSpu = $currentSpu;

                if (!empty($currentSpu['class_id1'])) {
                    $bestSpu = $currentSpu;
                    break;
                }
            }

            if (!$bestSpu) {
                $bestSpu = $lastSpu;
            }

            if ($bestSpu) {
                $classId1Name = ClassService::getClassNameFromCache($bestSpu['class_id1'] ?? 0);
                $classId2Name = ClassService::getClassNameFromCache($bestSpu['class_id2'] ?? 0);
                $bestSpu['class_id1_name'] = $classId1Name;
                $bestSpu['class_id2_name'] = $classId2Name;
                $bestSpu['class_id1'] = $bestSpu['class_id1'] ?? 0;
                $bestSpu['class_id2'] = $bestSpu['class_id2'] ?? 0;
                $spuData[$name] = $bestSpu;
            }
        }

        $redis = Redis::connection('sku');
        foreach ($spuNameList as $spuName) {
            if (!empty($spuData[$spuName]['class_id1'])) {
                continue;
            }

            //先去检查是否存在
            $exist = ClassManagementModel::where('goods_name', $spuName)->first();
            if ($exist) {
                if ($exist->class_id) {
                    $spuData[$spuName]['class_id1'] = $exist->class_id;
                    $spuData[$spuName]['class_id1_name'] = ClassService::getClassNameFromCache($exist->class_id);
                }

                continue;
            }

            if (empty($spuData[$spuName])) {
                continue;
            }
            //插入到lie_class_management表中
            $spuToInsert = $spuData[$spuName];
            $spuId = $spuToInsert['spu_id'];

            if (!isset($spuToInsert['s_brand_id'])) {
                continue;
            }
            $standardBrand = $redis->hget('standard_brand', $spuToInsert['s_brand_id']);
            $standardBrand = json_decode($standardBrand, true);
            if (empty($orderSn)) {
                continue;
            }
            $insertData = [
                'goods_name' => $spuName,
                'brand_name' => $standardBrand['brand_name'] ?? '',
                'spu_id' => $spuId,
                'order_sn' => $orderSn ?? '',
                'sales_name' => $salesName ?? '',
                'sales_id' => $salesId ?? 0,
                'create_time' => time(),
            ];
            ClassManagementModel::insert($insertData);
        }

        return $this->setSuccessData($spuData);
    }

    //根据型号获取SPU信息
    public function getSpuListBySpuName(Request $request)
    {
        // [
        //     {
        //         "spu_name":"power06",
        //         "brand_name": "JAK"
        //     }
        // ]
        $err = [];
        // 获取参数
        $orderSn = $request->input('order_sn');
        $salesName = $request->input('sales_name');
        $salesId = $request->input('sales_id');
        $params = $request->input('params');
        if (empty($params)) {
            return $this->setError('params不能为空');
        }

        // 确保params是数组
        $paramsArray = is_string($params) ? json_decode($params, true) : $params;
        if (!is_array($paramsArray)) {
            return $this->setError('params格式不正确，应为JSON数组');
        }

        // 提取spu_name列表和brand_name映射
        $spuNameList = [];
        $brandNameMap = []; // 存储传入的品牌名称映射
        foreach ($paramsArray as $item) {
            if (isset($item['spu_name'])) {
                $spuName = trim($item['spu_name']);
                $spuNameList[] = $spuName;

                // 保存传入的brand_name
                if (isset($item['brand_name'])) {
                    $brandNameMap[$spuName] = trim($item['brand_name']);
                }
            }
        }

        if (empty($spuNameList)) {
            return $this->setError('未提供有效的spu_name');
        }

        try {
            //直接去mongo获取一个spu_id即可
            $mongo = DB::connection('mongodb');
            $spuList = $mongo->table('spu')->whereIn('spu_name', $spuNameList)->get();
            $spuList = \is_array($spuList) ? $spuList : $spuList->toArray();
        } catch (\Throwable $th) {
            $spuList = [];
        }

        // 处理在Mongo中未找到的spu_name
        $foundSpuNames = collect($spuList)->pluck('spu_name')->map(function ($name) {
            return trim($name);
        })->unique()->toArray();

        $notFoundSpuNames = array_diff($spuNameList, $foundSpuNames);

        if (!empty($notFoundSpuNames)) {
            foreach ($notFoundSpuNames as $spuName) {
                // 检查是否已存在于待分类表，避免重复添加
                $exist = ClassManagementModel::where('goods_name', $spuName)->first();
                if ($exist) {
                    $err[] = "型号 '{$spuName}' 在SPU库中不存在，但已存在于待分类列表。";
                    continue;
                }

                // 从请求参数中获取品牌名称
                $brandName = $brandNameMap[$spuName] ?? '';

                $insertData = [
                    'goods_name'  => $spuName,
                    'brand_name'  => $brandName,
                    'spu_id'      => 0, // SPU不存在，spu_id设为0
                    'order_sn'    => $orderSn ?? '',
                    'sales_name'  => $salesName ?? '',
                    'sales_id'    => $salesId ?? 0,
                    'create_time' => time(),
                ];
                ClassManagementModel::insert($insertData);
                $err[] = "型号 '{$spuName}' 在SPU库中不存在，已作为新品加入待分类列表。";
            }
        }

        if (empty($spuList)) {
            printDebug($err);
            return $this->setSuccessData([]);
        }

        $spuNameKeyBySpuId = \collect($spuList)->keyBy('spu_id')->pluck('spu_name', 'spu_id')->toArray();
        $spuData = [];
        //再去spu的redis获取spu信息
        $spuRedis = Redis::connection('spu');

        // Group SPUs by spu_name
        $spusGroupedByName = collect($spuList)->groupBy('spu_name');

        foreach ($spusGroupedByName as $name => $spus) {
            $bestSpu = null;
            $lastSpu = null;

            foreach ($spus as $spuInfoFromMongo) {
                $spuId = $spuInfoFromMongo['spu_id'];
                $spuJson = $spuRedis->hget('spu', $spuId);
                if (empty($spuJson)) continue;

                $currentSpu = json_decode($spuJson, true);
                $currentSpu['spu_id'] = $spuId;

                $lastSpu = $currentSpu;

                if (!empty($currentSpu['class_id1'])) {
                    $bestSpu = $currentSpu;
                    break;
                }
            }

            if (!$bestSpu) {
                $bestSpu = $lastSpu;
            }

            if ($bestSpu) {
                $classId1Name = ClassService::getClassNameFromCache($bestSpu['class_id1'] ?? 0);
                $classId2Name = ClassService::getClassNameFromCache($bestSpu['class_id2'] ?? 0);
                $bestSpu['class_id1_name'] = $classId1Name;
                $bestSpu['class_id2_name'] = $classId2Name;
                $bestSpu['class_id1'] = $bestSpu['class_id1'] ?? 0;
                $bestSpu['class_id2'] = $bestSpu['class_id2'] ?? 0;

                // 如果传入了brand_name，则使用传入的值
                if (isset($brandNameMap[$name])) {
                    $bestSpu['brand_name'] = $brandNameMap[$name];
                }

                $spuData[$name] = $bestSpu;
            }
        }

        $redis = Redis::connection('sku');
        foreach ($spuNameList as $spuName) {
            if (!empty($spuData[$spuName]['class_id1'])) {
                $err[] = "class_id1 分类存在:".$spuName;
                continue;
            }

            //先去检查是否存在
            $exist = ClassManagementModel::where('goods_name', $spuName)->first();
            if ($exist) {
                if ($exist->class_id) {
                    $spuData[$spuName]['class_id1'] = $exist->class_id;
                    $spuData[$spuName]['class_id1_name'] = ClassService::getClassNameFromCache($exist->class_id);
                }

                continue;
            }

            if (empty($spuData[$spuName])) {
                $err[] = "mongodb-spu_name 单个找不到:".$spuName;
                continue;
            }
            //插入到lie_class_management表中
            $spuToInsert = $spuData[$spuName];
            $spuId = $spuToInsert['spu_id'];

            if (!isset($spuToInsert['s_brand_id'])) {
                $err[] = "spu_name单个找不到标准品牌:".$spuName;
                continue;
            }

            // 如果传入了brand_name，则使用传入的，否则从redis获取
            if (isset($brandNameMap[$spuName])) {
                $brandName = $brandNameMap[$spuName];
            } else {
                $standardBrand = $redis->hget('standard_brand', $spuToInsert['s_brand_id']);
                $standardBrand = json_decode($standardBrand, true);
                $brandName = $standardBrand['brand_name'] ?? '';
            }

            if (empty($orderSn)) {
                continue;
            }

            $insertData = [
                'goods_name' => $spuName,
                'brand_name' => $brandName,
                'spu_id' => $spuId,
                'order_sn' => $orderSn ?? '',
                'sales_name' => $salesName ?? '',
                'sales_id' => $salesId ?? 0,
                'create_time' => time(),
            ];
            ClassManagementModel::insert($insertData);
        }

        printDebug($err);

        return $this->setSuccessData($spuData);
    }
}
