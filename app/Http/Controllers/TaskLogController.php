<?php

namespace App\Http\Controllers;

use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use App\Http\Models\EncapModel;
use App\Http\Models\TaskLogModel;
use App\Http\Services\SpuService;
use App\Http\Models\ScmEncapModel;
use App\Http\Services\MenuService;
use App\Http\Models\StandardEncapModel;
use Illuminate\Support\Facades\Response;
use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Models\StandardEncapSpuModel;
use App\Http\Transformers\EncapTransformer;

class TaskLogController extends Controller
{
    //日志列表
    public function taskLogList(Request $request)
    {
        $admin = CmsUserInfoModel::getUserList();
        $data['adminId'] = $admin;
        return view('taskLog.taskLogList', $data);
    }
}
