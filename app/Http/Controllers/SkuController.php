<?php

namespace App\Http\Controllers;

use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use App\Http\Models\BrandModel;
use App\Http\Models\SupplierModel;
use App\Http\Services\MenuService;
use Illuminate\Support\Facades\DB;
use App\Http\Services\BrandService;
use App\Http\Services\ClassService;
use App\Http\Services\EncodedService;
use Illuminate\Support\Facades\Redis;
use App\Http\Services\SupplierService;
use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Models\BigData\ShopInfoModel;
use App\Http\Services\StandardBrandService;
use App\Http\Transformers\BrandTransformer;
use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Models\Cms\CmsUserIntraCodeModel;
use App\Http\Models\Supplier\SupplierChannelModel;
use App\Http\Services\EncapService;

class SkuController extends Controller
{

    public function skuList(Request $request)
    {

        $supplierService = new SupplierService();
        $supplierListForXmSelect = $supplierService->getSupplierListForXmSelect();
        //获取筛选框的品牌数据
        $brandSelectValue = [];
        $selectBrandIds = $request->get('brand_id/condition');
        if ($selectBrandIds) {
            $brandSelectValue = BrandModel::whereIn('brand_id', explode(',', $selectBrandIds))->get()->toArray();
            $brandSelectValue = array_map(function ($value) {
                return Arr::only($value, ['brand_name', 'brand_id']);
            }, $brandSelectValue);
        }
        $purchaseUsers = (new EncodedService())->getEncodedList();
        $shopList = ShopInfoModel::get()->toArray();

        $goodsLabelList = config('field.GoodsLabel');
        $goodsLabelListForXmSelect = [];
        foreach ($goodsLabelList as $key => $value) {
            $goodsLabelListForXmSelect[] = [
                'value' => $key,
                'name' => $value
            ];
        }

        $goodsStatusList = config('field.SkuStatus');
        $goodsStatusListForXmSelect = [];
        foreach ($goodsStatusList as $key => $value) {
            $goodsStatusListForXmSelect[] = [
                'value' => $key,
                'name' => $value
            ];
        }
        $skuSourceList = config('field.SkuSource');
        $skuSourceListForXmSelect = [];
        foreach ($skuSourceList as $key => $value) {
            $skuSourceListForXmSelect[] = [
                'value' => $key,
                'name' => $value
            ];
        }

        $purchaseUsersForXmSelect = CmsUserIntraCodeModel::getUserForXmSelect();

        $orgList = config('field.SkuOrgList');
        $orgListForXmSelect = [];
        foreach ($orgList as $key => $value) {
            $orgListForXmSelect[] = [
                'value' => $key,
                'name' => $value
            ];
        }

        $abilityLevelList = config('field.AbilityLevelRuleLevel');
        $abilityLevelListForXmSelect = [];
        foreach ($abilityLevelList as $key => $value) {
            $abilityLevelListForXmSelect[] = [
                'value' => $key,
                'name' => $value
            ];
        }

        $standardBrandAreaList = config('field.StandardBrandArea');
        $standardBrandAreaListForXmSelect = [];
        foreach ($standardBrandAreaList as $key => $value) {
            $standardBrandAreaListForXmSelect[] = [
                'value' => $key,
                'name' => $value
            ];
        }
        $encapListForXmSelect = (new EncapService())->getEncapListForXmSelect();
        $purchaseUsersForXmSelect = array_values(array_filter($purchaseUsersForXmSelect, function ($value) {
            return $value['value'] != '' && $value['name'] != '';
        }));
        $data = [
            'shopList' => $shopList,
            'supplierListForXmSelect' => $supplierListForXmSelect,
            'brandSelectValue' => json_encode($brandSelectValue),
            'classListForXmSelect' => ClassService::getAllClassListForXmSelect(),
            'topClassListForXmSelect' => ClassService::getTopClassListForXmSelect(),
            'standardBrandListForXmSelect' => StandardBrandService::getStandardBrandListForXmSelect(),
            'goodsLabelListForXmSelect' => $goodsLabelListForXmSelect,
            'goodsStatusListForXmSelect' => $goodsStatusListForXmSelect,
            'skuSourceListForXmSelect' => $skuSourceListForXmSelect,
            'purchaseUsersForXmSelect' => $purchaseUsersForXmSelect,
            'orgListForXmSelect' => $orgListForXmSelect,
            'abilityLevelListForXmSelect' => $abilityLevelListForXmSelect,
            'standardBrandAreaListForXmSelect' => $standardBrandAreaListForXmSelect,
            'encapListForXmSelect' => $encapListForXmSelect,
        ];
        return view('sku.skuList', $data);
    }


    public function chooseSkuClass(Request $request)
    {
        $list = PoolClassModel::where('parent_id', '=', 0)->where('status', '=', PoolClassModel::STATUS_OK)
            ->select()->get()->toArray();
        $data['list'] = $list;
        $data['into'] = 'no';
        return view('sku.chooseSkuClass', $data);
    }

    public function batchUpdateSkuStatus(Request $request)
    {
        return view('sku.batchUpdateSkuStatus');
    }

    public function saveSku(Request $request)
    {
        $redis = Redis::connection('sku');
        $spuName = '';
        $brandName = '';
        $ladderPrices = [];
        //查出所有供应商
        $suppliers = SupplierModel::select(['supplier_name', 'supplier_id'])->where('is_type', '=', 0)->get();
        $supplierName = '';
        $attrs = [];
        $rowCounts = 0;
        $skuId = $request->input('goods_id');
        $cnTimePeriod = $hkTimePeriod = 0;
        $skuCache = Redis::connection('sku')->hget('sku', $skuId);
        if (!$skuCache) {
            return 'SKU对应的redis不存在';
        }
        if (!empty($skuId)) {
            $db = getSpuSkuDb($skuId);
            $info = DB::connection($db['db'])->table($db['table'])->where('goods_id', $skuId)->first();
            $info = (array)$info;
            //根据spu_id查询spu_name
            $spuDb = resolveDB($info['spu_id']);
            $infoSpu = DB::connection($spuDb['db'])->table($spuDb['table'])->where(
                'spu_id',
                $info['spu_id']
            )->first();
            $infoSpu = (array)$infoSpu;
            $spuName = $infoSpu['spu_name'];
            $brandName = $redis->hget('brand', $infoSpu['brand_id']);
            //商品参数
            $spuAttrs = json_decode($infoSpu['attrs'], true);
            if (is_array($spuAttrs) && count($spuAttrs) > 0) {
                foreach ($spuAttrs as $attr) {
                    array_push($attrs, $attr['attr_name'] . ':' . $attr['attr_value']);
                }

                $attrCounts = count($spuAttrs);
                if ($attrCounts != 0) {
                    if ($attrCounts % 2 == 0) {
                        $rowCounts = $attrCounts / 2;
                    } else {
                        $rowCounts = ($attrCounts + 1) / 2;
                    }
                }
            }
            //根据supplier_id查询出 选定的供应商
            foreach ($suppliers as $supplier) {
                if ($supplier['supplier_id'] == $info['supplier_id']) {
                    $supplierName = $supplier['supplier_name'];
                    break;
                }
            }
            //阶梯价格
            $ladderPrices = json_decode($info['ladder_price'], true);
            if (empty($ladderPrices) || !is_array($ladderPrices)) {
                $ladderPrices = [];
            }
            //判断'天' 或 '周'
            $hkTimePeriod = strpos($info['hk_delivery_time'], '工作日') || strpos(
                $info['hk_delivery_time'],
                '天'
            ) !== false ? 0 : 1;
            $cnTimePeriod = strpos($info['cn_delivery_time'], '工作日') !== false ? 0 : 1;
            //去除货期中的'天'或'周'
            $info['hk_delivery_time'] = preg_replace('/([\x80-\xff]*)/i', '', $info['hk_delivery_time']);
            $info['cn_delivery_time'] = preg_replace('/([\x80-\xff]*)/i', '', $info['cn_delivery_time']);

            $spuRedis = Redis::connection('spu');
            $skuRawMap = $spuRedis->hget('sku_raw_map', $skuId);
            if (!empty($skuRawMap)) {
                $skuRawMap = json_decode($skuRawMap, true);
                $info['pack'] = $skuRawMap['pack'] ?? '';
            }
        }

        $code = '';
        $supplierModel = new SupplierChannelModel();
        $res = $supplierModel->getSupplierList();
        $saveSku = true;
        $skuDetail = '';
        if (isOnlineEnv()) {
            $skuDetail = DB::connection('mongodb')
                ->collection('sku_detail')
                ->where('sku_id', '=', (string)$skuId)
                ->first();
        }
        $skuDetail = $skuDetail ? $skuDetail['detail'] : '';
        //判断是否要存sku详情
        $info['sku_detail'] = $skuId ? $skuDetail : '';
        $encodedService = new EncodedService();
        //$encodedListCache = $redis->get('lie_encoded_list');
        //if ($encodedListCache) {
        //    $encodedList = json_decode($encodedListCache, true);
        //}else{
        //    $encodedList = $encodedService->getEncodedList();
        //    $redis->set('lie_encoded_list', json_encode($encodedList));
        //    $redis->exipred('lie_encoded_list',600);
        //}
        $encodedList = $encodedService->getEncodedList();
        if (empty($info)) {
            $info = [
                'goods_id' => '',
                'spu_id' => '',
                'old_goods_id' => '',
                'goods_name' => '',
                'goods_type' => '',
                'supplier_id' => '',
                'goods_status' => '',
                'encoded' => $code,
                'batch_sn' => '',
                'moq' => '',
                'mpq' => '',
                'attrs' => [],
                'stock' => '',
                'hk_delivery_time' => '',
                'cn_delivery_time' => '',
                'goods_details' => '',
                'ladder_price' => [],
                'single_price' => '',
                'former_price' => '',
                'sale_time' => '',
                'create_time' => '',
                'update_time' => '',
                'remark' => '',
                'goods_images' => '',
                'sku_detail' => '',
                'coo' => '',
            ];
        }

        //枚举
        $goodsTypes = [
            '自营',
            '联营',
            '专卖',
            '寄售'
        ];
        $goodsStatuses = [
            1 => '审核通过(上架)',
            3 => '下架'
        ];
        $info['ladder_price'] = json_decode($info['ladder_price']);

        $info['goods_type'] = $info['goods_type'] == 0 ? 2 : 1;

        #查下sku 额外信息
        $infoSkuExt = DB::connection("spu")->table("lie_sku_ext")->where(["goods_id"=>$skuId])->first();

        $data = [
            'info' => $info,
            'sku_ext'=>(array)$infoSkuExt,
            'goods_types' => $goodsTypes,
            'goods_statuses' => $goodsStatuses,
            'spu_name' => $spuName,
            'brand_name' => $brandName,
            'attrs' => $attrs ?: [],
            'row_counts' => $rowCounts ?: 0,
            'suppliers' => $suppliers,
            'supplier_name' => $supplierName,
            'ladder_prices' => $ladderPrices,
            'cn_time_period' => $cnTimePeriod,
            'hk_time_period' => $hkTimePeriod,
            'code' => $code,
            'encodedList' => $encodedList,
            'SaveSku' => empty($saveSku) ? false : $saveSku,
            'canal_list' => $res['canal_list']
        ];
        $data['ti_supplier_id'] = config('website.ti_supplier_id');
        return view('sku.saveSku', $data);
    }

    //商品地区列表
    public function skuLabelList(Request $request)
    {
        return view('sku.skuLabelList');
    }

    //保存商品地区信息
    public function saveSkuLabel(Request $request)
    {
        $id = $request->input('id');
        $redis = Redis::connection('sku');
        $info = $redis->hget('goods_label_remark', $id);
        if (empty($info)) {
            return '分区不存在';
        }

        $info = json_decode($info, true);
        return view('sku.saveSkuLabel', ['info' => $info]);
    }

    //更新sku属性
    public function updateSku(Request $request)
    {
        $admin = CmsUserInfoModel::getUserList();
        $data['adminId'] = $admin;
        return view('sku.updateSku', $data);
    }
}
