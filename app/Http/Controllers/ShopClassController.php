<?php

namespace App\Http\Controllers;

use App\Http\Models\BigData\ShopAttrMappingModel;
use App\Http\Models\BigData\ShopAttrModel;
use App\Http\Models\BigData\ShopClassMappingModel;
use App\Http\Models\BigData\ShopClassModel;
use App\Http\Models\BigData\ShopInfoModel;
use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Models\ScmEncapModel;
use App\Http\Models\StandardEncapSpuModel;
use App\Http\Services\MenuService;
use App\Http\Models\EncapModel;
use App\Http\Services\ShopClassService;
use App\Http\Services\SpuService;
use App\Http\Transformers\EncapTransformer;
use App\Http\Models\StandardEncapModel;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class ShopClassController extends Controller
{

    public function shopClassList(Request $request)
    {
        return view('shopClass.shopClassList');
    }

    public function shopClassMappingList(Request $request)
    {
        return view('shopClass.shopClassMappingList');
    }

    public function shopClassMapping(Request $request)
    {
        $id = $request->input('id');
        $shopList = ShopInfoModel::pluck('shop_name', 'shop_id')->toArray();
        $platformList = config('field.ShopPlatform');
        $hasMappingAttr = false;

        //获取可以设置的京东参数列表
        $jdAttrList = [];

        if ($id) {
            $classInitValue = ShopClassService::getShopClassMappingInitValue($id);
            $mapping = ShopClassMappingModel::where('id', $id)->first()->toArray();
            $platform = $mapping['platform'];
            $shopClassData = ShopClassService::getShopClassByPlatform($platform);
            $classId = ShopClassModel::where('id', $mapping['shop_class_id'])->value('class_id');
            $allShopAttrIds = ShopAttrModel::where('class_id', $classId)->pluck('id')->toArray();
            $hasMappingAttr = ShopAttrMappingModel::whereIn('shop_attr_id', $allShopAttrIds)->exists();

            //找出这个分类下面映射的shop_attr_id
            $mappingShopAttrIds = ShopAttrMappingModel::whereIn('shop_attr_id', $allShopAttrIds)->pluck('shop_attr_id')->toArray();
            $jdAttrList = ShopAttrModel::whereIn('id', $mappingShopAttrIds)->where('platform', ShopAttrModel::PLATFORM_JD)
                ->select(DB::raw('attr_id as value'), DB::raw('attr_name as text'))->get()->toArray();
        }
        $lieClassList = PoolClassModel::whereIn('class_type', [1, 3])->where('parent_id', '!=', 0)->get()->toArray();
        $classList = [];
        foreach ($lieClassList as $key => $value) {
            $classList[$value['class_id']] = $value['class_name'] . ' ( ' . (config('field.ClassType')[$value['class_type']] ?? '无') . ')';
        }



        return view('shopClass.shopClassMapping', [
            'shopList' => $shopList,
            'platformList' => $platformList,
            'classInitValue' => $classInitValue ?? [],
            'shopClassData' => $shopClassData ?? [],
            'mapping' => $mapping ?? [],
            'lieClassList' => $classList ?? [],
            'hasAttrMapping' => $hasMappingAttr,
            'jdAttrList' => $jdAttrList ?? [],
        ]);
    }
}
