<?php

namespace App\Http\Controllers;

use App\Http\Services\BrandService;
use App\Http\Services\MenuService;
use App\Http\Models\BrandModel;
use App\Http\Transformers\BrandTransformer;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class BrandController extends Controller
{

    //品牌列表
    public function brandList(Request $request)
    {
        return view('brand.brandList');
    }

    public function saveBrand(Request $request)
    {
        $brandId = $request->input('brand_id');
        $brand = [];
        if ($brandId) {
            $service = new BrandService();
            $brand = $service->getBrand($brandId);
        }
        $data['brand'] = $brand;
        return view('brand.saveBrand', $data);
    }

    //映射标准品牌数据
    public function mappingStandardBrand(Request $request)
    {
        $brandId = $request->input('brand_id');
        $brand = (new BrandService())->getBrand($brandId);
        return view('brand.mappingStandardBrand',compact('brand'));
    }
}
