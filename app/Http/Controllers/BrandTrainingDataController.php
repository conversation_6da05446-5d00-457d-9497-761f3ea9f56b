<?php

namespace App\Http\Controllers;

use App\Http\Models\TrainingDataModel;
use Illuminate\Http\Request;

class BrandTrainingDataController extends Controller
{
    // 品牌训练数据列表
    public function brandTrainingDataList(Request $request)
    {
        return view('brandTrainingData.brandTrainingDataList');
    }

    // 保存品牌训练数据
    public function saveBrandTrainingData(Request $request)
    {
        $trainingDataId = $request->input('id');
        $data = [];
        if (!empty($trainingDataId)) {
            $model = new TrainingDataModel();
            $trainingData = $model->where('id', $trainingDataId)->first();
            $data['trainingData'] = $trainingData ? $trainingData->toArray() : [];
        }
        return view('brandTrainingData.saveBrandTrainingData', $data);
    }
}
