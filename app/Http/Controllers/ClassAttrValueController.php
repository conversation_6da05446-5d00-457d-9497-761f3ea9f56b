<?php

namespace App\Http\Controllers;

use App\Http\Models\PoolClass\ClassAttrUnitConversionModel;
use App\Http\Models\PoolClass\ClassAttrUnitModel;
use App\Http\Models\PoolClass\ClassAttrValueModel;
use App\Http\Models\PoolClass\PoolClassAttrModel;
use App\Http\Models\PoolClass\PoolClassModel;
use Illuminate\Http\Request;

class ClassAttrValueController extends Controller
{
    /*
     * 参数列表
     */
    public function classAttrValueList(Request $request)
    {
        return view('classAttrValue.classAttrValueList');
    }

    /*
     * 编辑参数值
     */
    public function saveClassAttrValue(Request $request)
    {
        $attrValueId = $request->input('attr_value_id');
        $classModel = new PoolClassModel();
        if ($attrValueId) {
            $attrValueModel = new ClassAttrValueModel();
            $field = ['attr_value_id', 'value', 'attr_id', 'attr_unit_id', 'status', 'remark'];
            $data['info'] = $attrValueModel->where('attr_value_id', '=', $attrValueId)
                ->select($field)->with('AttrInfo')->first()->toArray();
            $data['classList'] = $classModel->where('parent_id', '=',
                $data['info']['attr_info']['class_info']['parent_id'])
                ->pluck('class_name', 'class_id')->toArray();
        }

        $data['parentClassList'] = $classModel->where('parent_id', '=', 0)
            ->pluck('class_name', 'class_id')->toArray();
        return view('classAttrValue.saveClassAttrValue', $data);
    }

}
