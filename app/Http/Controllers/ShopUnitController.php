<?php

namespace App\Http\Controllers;

use App\Http\Models\BigData\ShopAttrModel;
use App\Http\Models\BigData\ShopBrandMappingModel;
use App\Http\Models\BigData\ShopUnitMappingModel;
use App\Http\Models\BigData\ShopInfoModel;
use App\Http\Models\PoolClass\ClassAttrUnitConvertModel;
use App\Http\Models\StandardBrandModel;
use App\Http\Models\SupplierModel;
use App\Http\Services\ShopUnitMappingService;
use Illuminate\Http\Request;

class ShopUnitController extends Controller
{

    public function shopUnitMappingList(Request $request)
    {
        return view('shopUnitMapping.shopUnitMappingList');
    }

    public function notMappingShopUnitList(Request $request)
    {
        return view('shopUnitMapping.notMappingShopUnitList');
    }

    public function saveShopUnitMapping(Request $request)
    {
        $id = $request->input('id');
        if (!empty($id)) {
            $mapping = ShopUnitMappingModel::with('lie_unit_convert')->find($id)->toArray();
            //找出没有映射的
            $shopUnitNameList = ShopUnitMappingService::getNotMappingUnit($id);
        }

        $standardUnitNameList = ClassAttrUnitConvertModel::pluck('standard_unit_name', 'standard_unit_name')->toArray();

        //把猎芯自己的展示名称和第三方的展示名称输出到前端,让它们直接获取到对应的值

        return view('shopUnitMapping.saveShopUnitMapping', [
            'mapping' => $mapping ?? [],
            'shopUnitNameList' => $shopUnitNameList ?? [],
            'standardUnitNameList' => $standardUnitNameList ?? [],
            'convertNameMap' => ClassAttrUnitConvertModel::pluck('convert_name', 'standard_unit_name')->toArray(),
            'attrNameMap' => ShopAttrModel::where('default_unit', '!=', '')->where('platform', $mapping['platform'] ?? 1)
                ->select(['default_unit', 'attr_name'])->distinct()->pluck('attr_name', 'default_unit')->toArray(),
        ]);
    }
}
