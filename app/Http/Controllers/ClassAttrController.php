<?php

namespace App\Http\Controllers;

use App\Http\Models\PoolClass\ClassAttrUnitConvertModel;
use App\Http\Models\PoolClass\ClassAttrUnitModel;
use App\Http\Models\PoolClass\PoolClassAttrModel;
use App\Http\Models\PoolClass\PoolClassModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ClassAttrController extends Controller
{
    /*
     * 参数列表
     */
    public function classAttrList(Request $request)
    {
        $data['unitConvertList'] = ClassAttrUnitConvertModel::select([DB::raw('concat(convert_name," | ",standard_unit_name) as value'), 'id'])
            ->get()->pluck('value', 'id')->toArray();
        return view('classAttr.classAttrList', $data);

    }

    /*
     * 新增/编辑属性
     */
    public function saveClassAttr(Request $request)
    {
        $attrId = $request->input('attr_id');
        $attrModel = new PoolClassAttrModel();
        $classModel = new PoolClassModel();
        if (!empty($attrId)) {
            $data['info'] = $attrModel->where('attr_id', $attrId)->with('ClassInfo')->first()->toArray();
            $data['classList'] = $classModel->where('parent_id', '=',
                $data['info']['class_info']['parent_id'])
                ->pluck('class_name', 'class_id')->toArray();
        }

        $data['parentClassList'] = $classModel->where('parent_id', '=', 0)
            ->pluck('class_name', 'class_id')->toArray();
        if ($request->get('parent_id')) {
            $data['classList'] = $classModel->where('parent_id', '=',
                $request->get('parent_id'))
                ->pluck('class_name', 'class_id')->toArray();
        }
        //$unitConvertList
        $data['unitConvertList'] = ClassAttrUnitConvertModel::select([DB::raw('concat(convert_name," | ",standard_unit_name) as value'), 'id'])
            ->where('status', 1)
            ->get()->pluck('value', 'id')->toArray();

        $data['unitConvertListForXmSelect'] = ClassAttrUnitConvertModel::select([DB::raw('concat(convert_name," | ",standard_unit_name) as name'), 'id as value'])
            ->get()->toArray();
        $data['attrUnitList'] = ClassAttrUnitModel::getAllClassAttrUnitsForSelect();

        return view('classAttr.saveClassAttr', $data);
    }

}
