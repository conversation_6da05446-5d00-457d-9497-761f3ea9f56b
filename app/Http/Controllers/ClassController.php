<?php

namespace App\Http\Controllers;

use App\Exports\ClassExport;
use App\Http\Models\PoolClass\ClassAttrUnitConversionModel;
use App\Http\Models\PoolClass\ClassAttrUnitModel;
use App\Http\Models\PoolClass\ClassAttrValueModel;
use App\Http\Models\PoolClass\PoolClassAttrModel;
use App\Http\Models\PoolClass\PoolClassModel;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class ClassController extends Controller
{

    /*
     * 新版联营分类列表
     */
    public function classList(Request $request)
    {
        return view('class.classList');
    }

    /*
     *新版新增/编辑联营分类
     */
    public function saveClass(Request $request)
    {
        $class_id = $request->input('class_id');
        $classModel = new PoolClassModel();
        if (!empty($class_id)) {
            $field = [
                'class_id',
                'show_name',
                'class_name',
                'sign',
                'parent_id',
                'status',
                'class_icon',
                'class_brief',
                'sort',
                'class_type'
            ];
            $class = $classModel->where('class_id', '=', $class_id)->select($field)->first()->toArray();
            $data['info'] = $class;
            $data['classList'] = $classModel->where('parent_id', '=',
                $data['info']['parent_id'])
                ->pluck('class_name', 'class_id')->toArray();
        }

        $parentClassList = $classModel->where('parent_id', '=', 0)
            ->select(['class_name', 'class_id', 'class_type'])->get()->toArray();
        $parentList = [];
        foreach ($parentClassList as $parentClass) {
            $parentList[$parentClass['class_id']] = $parentClass['class_name'] . " (" . (config('field.ClassType')[$parentClass['class_type']] ?? '无') . ") ";
        }
        $data['parentClassList'] = $parentList;

        return view('class.saveClass', $data);
    }

    private function choice_pool_class(Request $request)
    {
        $Model = new PoolClassModel();
        $list = $Model->where('parent_id', '=', 0)->where('status', '=', 1)->select()->get()->toArray();
        $this->data['list'] = $list;
        $this->data['into'] = 'no';
        return $this->view('分类列表');
    }

    /**
     * 导出分类列表
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function exportClass(Request $request)
    {
        $fileName = '分类列表_' . date('YmdHis') . '.xlsx';
        return Excel::download(new ClassExport($request->all()), $fileName);
    }

}
