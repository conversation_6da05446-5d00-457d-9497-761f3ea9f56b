<?php

namespace App\Http\Controllers;

use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Models\Cms\CmsUserIntraCodeModel;
use App\Http\Models\SeoElementModel;
use App\Http\Models\SupplierExtraModel;
use App\Http\Models\SupplierRatioModel;
use App\Http\Services\SupplierService;
use App\Http\Services\MenuService;
use App\Http\Models\SupplierModel;
use App\Http\Transformers\BrandTransformer;
use Http;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class SupplierController extends Controller
{

    //品牌列表
    public function supplierList(Request $request)
    {
        return view('supplier.supplierList');
    }

    public function saveSupplier(Request $request)
    {
        $skuOptionalBatchForXmSelect = config('field.SkuOptionalBatch');
        foreach ($skuOptionalBatchForXmSelect as $k => &$v) {
            $v = [
                'value' => $k,
                'name' => $v
            ];
        }
        unset($v);
        $supplierId = $request->input('supplier_id');
        //获取魔方的配置信息
        $rule = Http::get(config('website.cube_domain') . '/open/rule/getSupplierFeeRule?supplier_id=' . $supplierId);
        $rule = json_decode($rule->body(), true);
        $rule = $rule['data'] ?? [];
        $data = (new SupplierService())->getSaveSupplierData($supplierId);
        $data['rule'] = $rule;
        $data['skuOptionalBatchForXmSelect'] = array_values($skuOptionalBatchForXmSelect);
        return view('supplier.saveSupplier', $data);
    }

    //保存附加费
    public function saveSuppExtendFee(Request $request)
    {
        $supplierModel = new SupplierModel();
        $supplierExtra = $supplierModel->where('supplier_id', $request->input('supplier_id'))
            ->select(['supplier_id', 'supplier_name', 'supp_extend_fee'])->first()->toArray();
        if (!empty($supplierExtra)) {
            $supplierExtra['supp_extend_fee'] = json_decode($supplierExtra['supp_extend_fee'], true);
        }
        if (empty($supplierExtra['supp_extend_fee']['cn']) && empty($supplierExtra['supp_extend_fee']['hk'])) {
            $supplierExtra['supp_extend_fee'] = [
                'cn' => $supplierExtra['supp_extend_fee']
            ];
        }
        $data = [
            'info' => $supplierExtra
        ];

        return view('supplier.saveSuppExtendFee', $data);
    }

    public function supplierRatioList(Request $request)
    {
        $data['Status'] = [
            1 => '正常',
            2 => '禁用'
        ];
        $supplierModel = new SupplierModel();
        $data['SupplierList'] = $supplierModel->getSuppliersForSelect();
        foreach ($data['SupplierList'] as $k => &$v) {
            if ($k === 17) {
                unset($v);
            }
        }

        return view('supplier.supplierRatioList', $data);
    }

    public function saveSupplierRatio(Request $request)
    {
        $data['Status'] = [
            1 => '正常',
            2 => '禁用'
        ];
        $supplierModel = new SupplierModel();
        $data['SupplierList'] = $supplierModel->getSuppliersForSelect();
        foreach ($data['SupplierList'] as $k => &$v) {
            if ($k === 17) {
                unset($v);
            }
        }
        $SupplierRatioModel = new SupplierRatioModel();
        $data['tax'] = config('field.Tax');
        $data['info'] = $SupplierRatioModel->getSupplierRatio($request->input('id'));
        return view('supplier.saveSupplierRatio', $data);
    }
}
