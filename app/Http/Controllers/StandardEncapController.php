<?php

namespace App\Http\Controllers;

use App\Http\Models\ScmEncapModel;
use App\Http\Models\StandardEncapSpuModel;
use App\Http\Services\MenuService;
use App\Http\Models\EncapModel;
use App\Http\Services\SpuService;
use App\Http\Transformers\EncapTransformer;
use App\Http\Models\StandardEncapModel;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class StandardEncapController extends Controller
{

    //委托单列表
    public function standardEncapList(Request $request)
    {
        return view('standardEncap.standardEncapList');
    }

    //保存标准封装
    public function saveStandardEncap(Request $request)
    {
        $standardEncapId = $request->input('standard_encap_id');
        $data = [];
        if (!empty($standardEncapId)) {
            $model = new StandardEncapModel();
            $encap = $model->where('standard_encap_id', $standardEncapId)->first()->toArray();
            $data['encap'] = $encap;
        }
        return view('standardEncap.saveStandardEncap', $data);
    }
}
