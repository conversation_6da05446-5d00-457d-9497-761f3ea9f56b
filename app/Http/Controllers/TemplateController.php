<?php

namespace App\Http\Controllers;

use App\Http\Models\ScmBrandModel;
use App\Http\Services\BrandService;
use App\Http\Services\MenuService;
use App\Http\Models\BrandModel;
use App\Http\Services\ScmBrandService;
use App\Http\Transformers\BrandTransformer;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;

class TemplateController extends Controller
{
    //品牌列表
    public function templateList(Request $request)
    {
        $name = $request->input('name');
        if (!empty($name)) {
            return response()->download(storage_path('app/Template/' . $name), $name);
        }

        $list = [
            '批量上传SKU' => 'UploadSkuV5.0.csv',
            '自营批量上传素材（图片/pdf/简短描述/供应商库存/内部编码/静态成本）' => 'UploadSourceV1.4.0.csv',
            '自营批量价格处理' => 'UploadpriceV1.0.0.csv',
            '批量上传品牌' => 'UploadBrandV1.0.0.xls',
            '批量上传分类' => 'UploadClassV1.0.0.csv',
            '自营报价上传' => 'self_offer_templetV1.0.0.xlsx',
            '自营样品上传' => 'sample_templet.v1.0.0.xlsx',
            '批量上传相似物料' => 'alike_goods_templet.v1.0.0.csv',
            '自营商品限购上传' => 'self_goods_quota_templet_v1.0.0.csv',
            '自营品牌映射' => 'supplier_brand_mapping_templetV1.0.0.csv',
            '自营分类映射' => 'supplier_class_mapping_templetV1.0.0.csv',
        ];
        foreach ($list as $k => $v) {
            $time = Storage::disk('public')->lastModified('template/' . $v);
            unset($list[$k]);
            $list[] = [
                'describe' => $k,
                'file_name' => $v,
                'time' => date('Y-m-d H:i', $time)
            ];
        }
        $data['fileList'] = $list;
        return view('template.templateList', $data);
    }

    public function downloadTemplate(Request $request)
    {
        $name = $request->input('name');
        if (!empty($name)) {
            return response()->download(public_path('template/' . $name), $name);
        }
    }

}
