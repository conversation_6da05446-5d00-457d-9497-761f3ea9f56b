<?php

namespace App\Http\Controllers;

use App\Http\Models\SampleClassModel;
use App\Http\Models\SampleModel;
use App\Http\Services\BrandService;
use App\Http\Services\MenuService;
use App\Http\Models\BrandModel;
use App\Http\Transformers\BrandTransformer;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Maatwebsite\Excel\Facades\Excel;

//第三方商店相关
class ShopSpuLogController extends Controller
{
    public function shopSpuLogList(Request $request)
    {

        $platformList = [];
        foreach (config('field.ShopPlatform') as $key => $value) {
            $platformList[] = [
                'name' => $value,
                'value' => $key,
            ];
        }

        $pushStatusList = [];
        foreach (config('field.ShopSpuPushStatus') as $key => $value) {
            $pushStatusList[] = [
                'name' => $value,
                'value' => $key,
            ];
        }

        return view('shopSpuLog.shopSpuPushLogList', [
            'platformList' => $platformList,
            'pushStatusList' => $pushStatusList,
        ]);
    }
}
