<?php

namespace App\Http\Controllers;

use App\Exports\ShopSkuExport;
use App\Exports\StandardBrandExport;
use App\Http\Models\BigData\ShopInfoModel;
use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Models\SampleClassModel;
use App\Http\Models\SampleModel;
use App\Http\Services\BrandService;
use App\Http\Services\MenuService;
use App\Http\Models\BrandModel;
use App\Http\Transformers\BrandTransformer;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Maatwebsite\Excel\Facades\Excel;

//第三方商店相关
class ShopSkuLogController extends Controller
{
    public function shopSkuLogList(Request $request)
    {

        $platformList = [];
        foreach (config('field.ShopPlatform') as $key => $value) {
            $platformList[] = [
                'name' => $value,
                'value' => $key,
            ];
        }

        $pushStatusList = [];
        foreach (config('field.ShopSkuPushStatus') as $key => $value) {
            $pushStatusList[] = [
                'name' => $value,
                'value' => $key,
            ];
        }

        $auditStatusList = [];
        foreach (config('field.ShopSkuPushAuditStatus') as $key => $value) {
            $auditStatusList[] = [
                'name' => $value,
                'value' => $key,
            ];
        }

        $shopListDb = ShopInfoModel::get()->toArray();
        $shopList = [];
        foreach ($shopListDb as $shop) {
            $shopList[] = [
                'name' => $shop['shop_name'],
                'value' => $shop['shop_id'],
            ];
        }

        return view('shopSkuLog.shopSkuPushLogList', [
            'platformList' => $platformList,
            'pushStatusList' => $pushStatusList,
            'auditStatusList' => $auditStatusList,
            'shopList' => $shopList,
        ]);
    }
}
