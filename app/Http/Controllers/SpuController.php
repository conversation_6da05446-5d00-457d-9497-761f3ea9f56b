<?php

namespace App\Http\Controllers;

use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use App\Http\Models\BrandModel;
use Illuminate\Support\Facades\DB;
use App\Http\Services\ClassService;
use Illuminate\Support\Facades\Redis;
use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Services\ClassAttrUnitService;
use App\Http\Models\PoolClass\PoolClassModel;
use App\Http\Models\PoolClass\PoolClassAttrModel;
use App\Http\Models\PoolClass\ClassAttrValueModel;

class SpuController extends Controller
{
    //spu列表
    public function spuList(Request $request)
    {
        $data['data']['goods_id'] = [];
        //获取筛选框的品牌数据
        $brandSelectValue = [];
        $selectBrandIds = $request->get('brand_id/condition');
        if ($selectBrandIds) {
            $brandModel = new BrandModel();
            $brandSelectValue = $brandModel->whereIn('brand_id', explode(',', $selectBrandIds))->get();
            $brandSelectValue = $brandSelectValue ? $brandSelectValue->toArray() : [];
            $brandSelectValue = array_map(function ($value) {
                return Arr::only($value, ['brand_name', 'brand_id']);
            }, $brandSelectValue);
        }
        $data = [
            'classListForXmSelect' => ClassService::getAllClassListForXmSelect(),
            'brandSelectValue' => json_encode($brandSelectValue),
        ];
        return view('spu.spuList', $data);
    }

    public function saveSpu(Request $request)
    {
        $spuId = $request->input('spu_id');
        if (!empty($spuId) && !is_numeric($spuId)) {
            return '非法spu_id';
        }
        $classModel = new PoolClassModel();
        $data['parentClassList'] = $classModel->where('parent_id', '=', 0)
            ->pluck('class_name', 'class_id')->toArray();

        if (!empty($spuId)) {
            $dbInfo = resolveDB($spuId);
            $spu = DB::connection($dbInfo['db'])->table($dbInfo['table'])->where('spu_id', $spuId)->first();
            $spu = (array)$spu;
            //提取选中的应用领域
            $chosenBussinessAreasStr = $spu['bussiness_area'];
            $chosenBussinessAreas = explode('|', $chosenBussinessAreasStr);
            if ($spu['class_id1']) {
                $data['classList'] = $classModel->where('parent_id', '=',
                    $spu['class_id1'])
                    ->pluck('class_name', 'class_id')->toArray();
            }
            $redis = Redis::connection('sku');
            $spu['brand_name'] = $redis->hget('brand', $spu['brand_id']);
            $spu['bussiness_area_value'] = $chosenBussinessAreas;
            $spuExtra = DB::connection('mongodb')->table('spu_extra')->where('spu_id', $spuId)->first();
            $spu['spu_image_list'] = isset($spuExtra['image_list']) ? $spuExtra['image_list'] : [];
            if (is_array($spuExtra)) {
                $spu = array_merge($spu, $spuExtra);
            }
            $data['spu'] = $spu;
        }
        return view('spu.saveSpu', $data);
    }

    public function updateSpuClass(Request $request)
    {
        $admin = CmsUserInfoModel::getUserList();
        $data['adminId'] = $admin;
        return view('spu.updateSpuClass', $data);
    }

    public function updateSpuAttr(Request $request)
    {
        $admin = CmsUserInfoModel::getUserList();
        $data['adminId'] = $admin;
        return view('spu.updateSpuAttr', $data);
    }

    public function updateSpuPDF(Request $request)
    {
        $admin = CmsUserInfoModel::getUserList();
        $data['adminId'] = $admin;
        return view('spu.updateSpuPDF', $data);
    }

}
