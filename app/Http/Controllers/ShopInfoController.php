<?php

namespace App\Http\Controllers;

use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use App\Exports\ShopSkuExport;
use App\Http\Models\BrandModel;
use App\Http\Models\SampleModel;
use App\Http\Services\MenuService;
use App\Http\Services\BrandService;
use App\Exports\StandardBrandExport;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Models\SampleClassModel;
use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Models\BigData\ShopInfoModel;
use App\Http\Transformers\BrandTransformer;
use App\Http\Models\BigData\ShopConfigModel;
use App\Http\Models\BigData\ShopSettingModel;
use App\Http\Models\Cms\CmsUserIntraCodeModel;

//第三方商店相关
class ShopInfoController extends Controller
{
    public function shopInfoList(Request $request)
    {
        return view('shopInfo.shopInfoList');
    }

    public function saveShopInfo(Request $request)
    {

        $userList = CmsUserIntraCodeModel::getUserForSelect();
        $shopId = $request->input('shop_id');

        if (!empty($shopId)) {
            $shop = ShopInfoModel::where('shop_id', $shopId)->first()->toArray();
        }

        return view('shopInfo.saveShopInfo', [
            'shop' => $shop ?? [],
            'userList' => $userList ?? []
        ]);
    }

    //获取店铺配置列表
    public function shopConfigList(Request $request)
    {
        $shopList = ShopInfoModel::select(['shop_id', 'shop_name'])->pluck('shop_name', 'shop_id')->toArray();
        return view('shopInfo.shopConfigList', [
            'shopList' => $shopList ?? []
        ]);
    }

    //保存店铺配置
    public function saveShopConfig(Request $request)
    {
        $shopId = $request->input('shop_id');
        if (!empty($shopId)) {
            $shopConfig = ShopConfigModel::where('shop_id', $shopId)->first()->toArray();
        }
        $shopList = ShopInfoModel::select(['shop_id', 'shop_name'])->pluck('shop_name', 'shop_id')->toArray();
        return view('shopInfo.saveShopConfig', [
            'shopConfig' => $shopConfig ?? [],
            'shopList' => $shopList ?? []
        ]);
    }
}
