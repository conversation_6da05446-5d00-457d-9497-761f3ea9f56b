<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Models\SupplierModel;
use App\Http\Models\DistributePriceModel;
use App\Http\Services\StandardBrandService;
use App\Http\Models\Supplier\SupplierChannelModel;

class DistributePriceController extends Controller
{
    public function distributePriceList(Request $request)
    {
        $data['supplierListForXmSelect'] = (new SupplierModel())->getSupplierListForXmSelect();
        $data['canalListForXmSelect'] = SupplierChannelModel::getCanalListForXmSelect();
        return view('distributePrice.distributePriceList', $data);
    }

    public function saveDistributePrice(Request $request)
    {
        $id = $request->input('id');
        if (!empty($id)) {
            $data['rule'] = DistributePriceModel::find($id)->toArray() ?? [];
        }
        $data['supplierList'] = (new SupplierModel())->getSuppliersForSelect();
        $data['canalListForXmSelect'] = SupplierChannelModel::getCanalListForXmSelect();
        $data['standardBrandListForXmSelect'] = StandardBrandService::getStandardBrandListForXmSelect();
        return view('distributePrice.saveDistributePrice', $data);
    }
}
