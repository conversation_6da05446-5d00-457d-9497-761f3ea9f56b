<?php

namespace App\Http\Controllers;

use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use App\Http\Models\BrandModel;
use App\Http\Services\SpuService;
use App\Http\Models\ScmBrandModel;
use App\Http\Services\MenuService;
use App\Http\Services\BrandService;
use App\Http\Services\ClassService;
use App\Http\Models\AgentBrandModel;
use App\Http\Services\ScmBrandService;
use App\Http\Models\AgentBrandSpuModel;
use App\Http\Services\AgentBrandService;
use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Transformers\BrandTransformer;
use App\Http\Models\Cms\CmsUserIntraCodeModel;
use App\Http\Models\Supplier\SupplierChannelModel;

class AgentBrandController extends Controller
{
    //品牌列表
    public function agentBrandList(Request $request)
    {
        $data['brandLevelForXmSelect'] = config('field.AgentBrandLevel');
        foreach ($data['brandLevelForXmSelect'] as $key => $value) {
            $data['brandLevelForXmSelect'][$key] = [
                'name' => $value,
                'value' => $key,
            ];
        }
        $data['brandLevelForXmSelect'] = array_values($data['brandLevelForXmSelect']);
        $data['brandAreaForXmSelect'] = config('field.StandardBrandArea');
        foreach ($data['brandAreaForXmSelect'] as $key => $value) {
            $data['brandAreaForXmSelect'][$key] = [
                'name' => $value,
                'value' => $key,
            ];
        }
        $data['brandAreaForXmSelect'] = array_values($data['brandAreaForXmSelect']);
        $data['applicationAreaForXmSelect'] = config('field.SpuApplicationArea');
        foreach ($data['applicationAreaForXmSelect'] as $key => $value) {
            $data['applicationAreaForXmSelect'][$key] = [
                'name' => $value,
                'value' => $key,
            ];
        }
        $data['applicationAreaForXmSelect'] = array_values($data['applicationAreaForXmSelect']);
        $data['purchaseUsers'] = (new CmsUserInfoModel())->getPurchaseUserList();
        $data['supplierCodeList'] = (new SupplierChannelModel())->pluck('supplier_code')->toArray();
        return view('agentBrand.agentBrandList', $data);
    }

    //编辑代理品牌
    public function saveAgentBrand(Request $request)
    {
        $data['purchaseUsers'] = (new CmsUserInfoModel())->getPurchaseUserList();
        if (!empty($request->input('id'))) {
            $data['brand'] = (new AgentBrandService())->getAgentBrandInfo($request->input('id'));
            $data['brand']['certificate_effective_time'] = $data['brand']['certificate_effective_time'] ? date('Y-m-d', $data['brand']['certificate_effective_time']) : '';
        }
        $data['brandLevelForXmSelect'] = config('field.AgentBrandLevel');
        foreach ($data['brandLevelForXmSelect'] as $key => $value) {
            $data['brandLevelForXmSelect'][$key] = [
                'name' => $value,
                'value' => $key,
            ];
        }
        $data['brandAreaForXmSelect'] = config('field.StandardBrandArea');
        foreach ($data['brandAreaForXmSelect'] as $key => $value) {
            $data['brandAreaForXmSelect'][$key] = [
                'name' => $value,
                'value' => $key,
            ];
        }
        $data['applicationAreaForXmSelect'] = config('field.AgentBrandApplicationArea');
        foreach ($data['applicationAreaForXmSelect'] as $key => $value) {
            $data['applicationAreaForXmSelect'][$key] = [
                'name' => $value,
                'value' => $key,
            ];
        }
        $data['mainProductClassList'] = (new ClassService())->getParentClassForXmSelect();

        $data['agentBrandListForXmSelect'] = config('field.AgentBrandClass');
        foreach ($data['agentBrandListForXmSelect'] as $key => $value) {
            $data['agentBrandListForXmSelect'][$key] = [
                'name' => $value,
                'value' => $key,
            ];
        }
        $data['agentBrandListForXmSelect'] = array_values($data['agentBrandListForXmSelect']);

        return view('agentBrand.saveAgentBrand', $data);
    }

    //编辑代理品牌产品
    public function saveAgentBrandSpu(Request $request)
    {
        $id = $request->input('id');
        $data = [];
        if (!empty($id)) {
            $agentSpu = AgentBrandSpuModel::where('id', $id)->first()->toArray();
            $standardBrandId = AgentBrandModel::where('id', $agentSpu['agent_brand_id'])->value('standard_brand_id');
            $spu = (new SpuService())->getSpuBySpuNameAndStandardBrandId($agentSpu['spu_name'], $standardBrandId);
            $data['spu'] = array_merge($spu, $agentSpu);
        }
        return view('agentBrand.saveAgentBrandSpu', $data);
    }
}
