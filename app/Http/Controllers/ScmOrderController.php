<?php

namespace App\Http\Controllers;

use App\Http\Models\StockInItemModel;
use App\Http\Services\MenuService;
use App\Http\Services\PurchasePlanService;
use App\Http\Services\PurchaseUserService;
use App\Http\Services\ScmOrderTemporaryService;
use Illuminate\Http\Request;

class ScmOrderController extends Controller
{

    //委托单列表
    public function scmOrderList(Request $request)
    {
        $data['menu_list'] = MenuService::getMenu();
        $data['create_users'] = ScmOrderTemporaryService::getScmOrderTemporaryCreateUsers();
        $data['customs_status'] = config('field.CustomsStatus');
        $data['scm_order_status'] = config('field.ScmOrderStatus');
        return view('scmOrder.scmOrderList', $data);
    }

    //获取委托单的供应商名称列表
    public function getScmOrderSupplierList(Request $request)
    {
        $params = $request->only(['limit', 'page', 'keyword']);
        $result = ScmOrderTemporaryService::getScmOrderTemporarySupplierList($params);
        return $this->setSuccessData(['list' => $result]);
    }


    //新增报关
    public function addCustoms(Request $request)
    {
        $data['menu_list'] = MenuService::getMenu();
        return view('scmOrder.addCustoms', $data);
    }

    //委托入库明细
    public function scmOrderStockInList(Request $request)
    {
        $data['menu_list'] = MenuService::getMenu();
        $data['status'] = StockInItemModel::$ITEM_STATUS;
        return view('scmOrder.scmOrderStockInList', $data);
    }
}
