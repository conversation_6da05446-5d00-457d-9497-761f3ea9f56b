<?php

namespace App\Http\Controllers;


use App\Http\Models\SearchForbidModel;
use App\Http\Services\MenuService;
use App\Http\Services\SkuService;
use App\Http\Services\SupplierService;
use Illuminate\Http\Request;

class SearchForbidController extends Controller
{
    public function searchForbidList(Request $request)
    {
        $menu_list = MenuService::getMenu();
        $supplierService = new SupplierService();
        $supplierList = $supplierService->getBaseSupplier();
        $supplierList = collect($supplierList)->pluck('supplier_name', 'supplier_id')->toArray();
        return view('searchForbid.searchForbidList', compact(['menu_list', 'supplierList']));
    }

    public function saveSearchForbid(Request $request)
    {
        $id = $request->input('id');
        $menu_list = MenuService::getMenu();
        $supplierService = new SupplierService();
        $supplierList = $supplierService->getBaseSupplier();
        $supplierList = collect($supplierList)->pluck('supplier_name', 'supplier_id')->toArray();
        $supplierList['0'] = '全渠道';
        ksort($supplierList);
        $forbid = [];
        if (!empty($id)) {
            $forbid = SearchForbidModel::where('id', $id)->first()->toArray();
        }
        return view('searchForbid.saveSearchForbid', compact(['menu_list', 'supplierList', 'forbid']));
    }

}
