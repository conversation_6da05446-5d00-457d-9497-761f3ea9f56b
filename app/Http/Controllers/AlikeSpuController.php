<?php

namespace App\Http\Controllers;

use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use App\Exports\AlikeSpuExport;
use App\Http\Models\AlikeSpuModel;
use App\Http\Services\MenuService;
use App\Http\Services\BrandService;
use App\Http\Models\StandardBrandModel;
use App\Http\Models\AlikeSpuCenterModel;
use Illuminate\Support\Facades\Response;
use App\Http\Models\Cms\CmsUserInfoModel;

class AlikeSpuController extends Controller
{

    public function alikeSpuList(Request $request)
    {
        $admin = CmsUserInfoModel::getUserList();
        $data['admin'] = $admin;
        return view('alikeSpu.alikeSpuList', $data);
    }


    public function alikeSpuItems(Request $request)
    {
        $mainId = $request->input('main_id');
        $alikeSpu = AlikeSpuModel::where('id', $mainId)->first()->toArray();
        $alikeSpuCenter = AlikeSpuCenterModel::where('spu_name', $alikeSpu['spu_name'])
        ->where('standard_brand_name', $alikeSpu['brand_name'])
        ->first();
        $alikeStandardBrandName = $alikeSpu['alike_brand_name'];
        $alikeStandardBrand = StandardBrandModel::where('brand_name', $alikeStandardBrandName)->first();
        $alikeSpuCenter = $alikeSpuCenter?$alikeSpuCenter->toArray():[];
        $alikeStandardBrand = $alikeStandardBrand?$alikeStandardBrand->toArray():[];
        return view('alikeSpu.alikeSpuItems', ['alikeSpu' => $alikeSpu, 'alikeSpuCenter' => $alikeSpuCenter,'alikeStandardBrand' => $alikeStandardBrand]);
    }

    public function alikeSpuUploadLog(Request $request)
    {
        $admin = CmsUserInfoModel::getUserList();
        $data['adminId'] = $admin;
        $data['status'] = config('field.UploadStatus');
        return view('alikeSpu.alikeSpuUploadLog', $data);
    }

}
