<?php

namespace App\Http\Controllers;

use App\Http\Models\BigData\ShopBrandMappingModel;
use App\Http\Models\BigData\ShopDistributeRuleModel;
use App\Http\Models\BigData\ShopInfoModel;
use App\Http\Models\StandardBrandModel;
use App\Http\Models\Supplier\SupplierChannelModel;
use App\Http\Models\SupplierModel;
use App\Http\Services\ShopDistributeRuleService;
use Illuminate\Http\Request;

class ShopDistributeRuleController extends Controller
{

    public function shopDistributeRuleList(Request $request)
    {
        return view('shopDistributeRule.shopDistributeRuleList');
    }

    public function saveShopDistributeRule(Request $request)
    {
        $isCopy = $request->input('is_copy', 0);
        $id = $request->input('id');
        if (!empty($id)) {
            $rule = ShopDistributeRuleModel::find($id)->toArray();
            $shopInfoList = ShopInfoModel::where('platform', $rule['platform'])->select(['shop_id', 'shop_name'])->get()->toArray();
            $canalInitValue = self::getCanalInitValue($rule['canals']);
        }
        $supplierList = SupplierModel::where('is_type', 0)->select([
            \DB::raw('supplier_name as name'),
            \DB::raw('supplier_id as value')])
            ->get()->toArray();
        //还要找出已经映射的品牌
        //$lieBrandIds = ShopBrandMappingModel::pluck('lie_brand_id')->toArray();
        $brandList = StandardBrandModel::select([
            \DB::raw('brand_name as name'),
            \DB::raw('standard_brand_id as value')])
            ->get()->toArray();
        return view('shopDistributeRule.saveShopDistributeRule', [
            'supplierList' => $supplierList,
            'brandList' => $brandList,
            'rule' => $rule ?? [],
            'shopInfoList' => $shopInfoList ?? [],
            'isCopy' => $isCopy,
            'canal_init_value' => $canalInitValue ?? [],
        ]);
    }

    public static function getCanalInitValue($canals)
    {
        if (!trim($canals, ',')) {
            return [];
        }
        $canals = explode(',', $canals);
        $canalList = SupplierChannelModel::whereIn('supplier_code', $canals)->select([
            'supplier_code',
            'supplier_name',
        ])->get();
        $canalList = !empty($canalList) ? $canalList->toArray() : [];
        return $canalList;
    }
}
