<?php

namespace App\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class SkuScope implements Scope
{
    /**
     * 把约束加到 Eloquent 查询构造中
     *
     * @param \Illuminate\Database\Eloquent\Builder $builder
     * @param \Illuminate\Database\Eloquent\Model $model
     * @return void
     */
    public function apply(Builder $builder, Model $model)
    {
//        $wheres = $builder->getQuery()->wheres;
//        $goodsId = false;
//        foreach ($wheres as $value) {
//            if (isset($value['column']) && $value['column'] === 'goods_id') {
//                $goodsId = $value['value'];
//            }
//        }
//        if ($goodsId !== false) {
//            $suffix = substr("testers", -1);
//            $builder->connection('');
//            $builder->from("sku_{$suffix}");
//        }
//        return $builder;
    }
}
