<?php

namespace App\Listeners;

use Faker\Provider\DateTime;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Events\QueryExecuted;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;

class QueryListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  QueryExecuted $event
     * @return void
     */
    public function handle(QueryExecuted $event)
    {
        try {
            if (Config('app.debug') == true) {
                $sql = str_replace("?", "'%s'", $event->sql);
                foreach ($event->bindings as $i => $binding) {
                    if ($binding instanceof DateTime) {
                        $event->bindings[$i] = $binding->format('\'Y-m-d H:i:s\'');
                    } else {
                        if (is_string($binding)) {
                            $event->bindings[$i] = "'$binding'";
                        }
                    }
                }
                $log = vsprintf($sql, $event->bindings);
                $log = $log . '  [ RunTime:' . $event->time . 'ms ] ';
                $fileName = sprintf("logs/log/sql-%s.log",date("Y-m-d"));
                \Log::channel("sql")->info(sprintf($log));
//                (new Logger('sql'))->pushHandler(new StreamHandler(storage_path($fileName)))->info($log);
            }
        } catch (\Exception $exception) {
            \Log::channel("sql")->info(
                sprintf(
                    "错误：请求参数 %s,错误原因:%s",
                    print_r(request()->all(),true),
                    json_encode($exception->getMessage(),JSON_UNESCAPED_UNICODE)
                )
            );
//            (new Logger('sql'))->pushHandler(new StreamHandler(storage_path($fileName)))->info($exception->getMessage());
        }
    }
}
