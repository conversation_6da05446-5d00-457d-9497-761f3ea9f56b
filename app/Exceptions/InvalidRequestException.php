<?php

namespace App\Exceptions;

use App\Http\ApiHelper\Response;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/*
 *无效请求异常
 */

class InvalidRequestException extends \Exception
{

    public function __construct($message = "", $code = 200)
    {
        parent::__construct($message, $code);
    }

    public function render(Request $request)
    {
        $err_info = [
            'domain' => $_SERVER['HTTP_HOST'],
            'interface' => parse_url($_SERVER['REQUEST_URI'])['path'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'],
            'ip' => request()->getClientIp(),
            'time' => time(),
            'other' => '',
            'request_params' => $_REQUEST,
            'msg' => $this->getMessage(),
            "code" => $this->getCode(),
        ];
        Log::error(json_encode($err_info, JSON_UNESCAPED_UNICODE));
        return response()->json(json_decode(Response::setError($this->message), true));
    }
}
