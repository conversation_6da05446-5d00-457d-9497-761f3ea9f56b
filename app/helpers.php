<?php

function printDebug($data,$pre = "pre",$debug_k="dubugc"){
    if (request()->input($debug_k)== 1){
        print_r("----$pre----".date("Y-m-d H:i:s").":<br/>".(is_array($data) ? json_encode($data,JSON_UNESCAPED_UNICODE):$data)." <br/>");
    }
}

//记录日志
function write_log($data,$pre="request_log",$is_print = false){
//设置路径目录信息
    $url = base_path() ."/storage/".date("Ymd")."_".$pre.".txt";
    $dir_name=dirname($url);
//目录不存在就创建
    if(!file_exists($dir_name)){
        $res = mkdir(iconv("UTF-8", "GBK", $dir_name),0777,true);
    }
    $fp = fopen($url,"a");//打开文件资源通道 不存在则自动创建
    if (is_array($data)){
        $data["data"] = mb_substr(json_encode(@$data["data"]), 0, 1000, 'UTF-8');  //限制日志数量
    }

    $s = date("Y-m-d H:i:s")."----".unicode2Chinese(json_encode($data))."\r\n";
    if ($is_print){
        print_r($s);
    }
    fwrite($fp,$s);//写入文件
    fclose($fp);//关闭资源通道
}

function unicode2Chinese($str)
{
    return preg_replace_callback("#\\\u([0-9a-f]{4})#i",
        function ($r) {return iconv('UCS-2BE', 'UTF-8', pack('H4', $r[1]));},
        $str);
}

/*
 * php $_REQUEST / $_GET 获取特殊值
 */
function getUrlData($k)
{
    return rawurldecode(urlencode(urldecode($_REQUEST[$k])));
}

/*
 * 解析SPU和SKU的ID
 * @$id  sku 或者 spu id
 * @return  array   db 数据库，table 表名
 */
function getSpuSkuDb($id)
{
    $data['db'] = 'sku_0';
    $data['table'] = 'sku_0';
    $type = substr($id, 0, 1);
    $dbcode = substr($id, -2, 1);
    $tablecode = substr($id, -1);
    if ($type == 1) {
        $data['db'] = 'sku_' . $dbcode;
        $data['table'] = 'sku_' . $tablecode;
    }
    if ($type == 2) {
        $data['db'] = 'spu';
        $data['table'] = 'spu_' . $tablecode;
    }
    return $data;
}

//构造SPU和SKU的ID
function createSpuSkuId($type = 'spu')
{
    if ($type == 'sku') {
        $id = '1';
        $db = rand(0, 9) . rand(0, 9);
    } else {
        $id = '2';
        $db = '0' . rand(0, 9);
    }
    $id .= time() . random(6, true) . $db;
    return $id;
}

/**
 * 是否为多维数组
 * @param array $arr
 * @return bool
 */
function isMultipleArray(array &$arr): bool
{
    if (count($arr) <= 0) {
        return false;
    }

    if (count($arr) == count($arr, COUNT_RECURSIVE)) {
        foreach ($arr as $tempArr) {
            if (is_array($tempArr)) {
                return true;
            }
        }
        return false;
    }

    return true;
}

//判断是否有对应的权限
//request()->perms是Permission中间件过来的
function checkPerm($perm): bool
{
    $perms = request()->perms ?: [];
    return in_array($perm, $perms);
}


/**
 * 价格格式化
 * @param  [type]  $price [description]
 * @param integer $sign [description]
 * @param integer $num [description]
 * @return [type]         [description]
 */
function price_format($price, $sign = 0, $num = 2, $sep = '')
{
    $minus = $price < 0 ? '-' : '';
    $price = number_format(abs($price), $num, '.', $sep);
    switch ($sign) {
        case 1:
            $sign = '¥';
            break;
        case 2:
            $sign = '$';
            break;
        default:
            $sign = '';
            break;
    }
    if (!empty($sign)) {
        $price = $sign . $price;
    }
    return $minus . $price;
}


/*
 * 判断变量是否存在 并且 设置默认值
 */
function issetOrReturnDefault($arr, $key, $default = "", $func = "")
{
    if (isset($arr[$key])) {
        if ($func) {
            try {
                return $func($arr[$key]);
            } catch (\Exception $e) {
                return $arr[$key];
            }
        } else {
            return $arr[$key];
        }
    } else {
        return $default;
    }
}

/*
 * 构建时间查询
 */
function buildQueryTimeRange($time = "")
{
    $time = explode("~", $time);
    $buildTimeQueryData["begin_time"] = isset($time[0]) ? $time[0] : "";
    $buildTimeQueryData["end_time"] = isset($time[1]) ? $time[1] : "";
    return $buildTimeQueryData;
}


/*
 * 排序
 */
//function _arraySort($array,$keys,$sort='asc') {
//    $newArr = $valArr = array();
//    foreach ($array as $key=>$value) {
//        $valArr[$key] = $value[$keys];
//    }
//    ($sort == 'asc') ?  asort($valArr) : arsort($valArr);
//    reset($valArr);
//    foreach($valArr as $key=>$value) {
//        $newArr[$key] = $array[$key];
//    }
//    return $newArr;
//}

function arraySort($list, $keys, $sort = "asc")
{
    if ($sort == "asc") {
        $sort = SORT_ASC;
    } else {
        $sort = SORT_DESC;
    }
    array_multisort(array_column($list, $keys), $sort, $list);
    return $list;
}


/*
 * 去重数组
 * 过滤数组
 */
function array_filter_unique($arr)
{
    return array_unique(array_filter($arr));
}


/*
 * 截取字符串
 */
//如果字符串长度超过10，则截取并以省略号结尾
function truncStr($str, $len = 100, $endStr = "")
{
    $str = (string)$str;
    if (mb_strlen($str, 'utf-8') > $len) {
        return mb_substr($str, 0, $len, 'utf-8') . $endStr;
    } else {
        return $str;
    }
}

/*
 * 获取登录者的信息
 */
function getAdminUser()
{
    $admin = request()->get("user");
    if (!$admin) {
        throw new \App\Exceptions\InvalidRequestException("没找到登录相关信息,请先登录~_~");
    }
    $arr = [];
    $arr["userId"] = $admin->userId;
    $arr["name"] = $admin->name;
    $arr["email"] = $admin->email;
    $arr["engName"] = $admin->engName;
    return $arr;
}

/*
 * 获取登录者用户id
 */
function getAdminUserId()
{
    $admin = request()->get("user");
    if (!$admin) {
        throw new \App\Exceptions\InvalidRequestException("没找到登录相关信息,请先登录~_~");
    }
    return $admin->userId;
}

/*
 * 获取登录者的名字
 */
function getAdminUserName()
{
    $admin = request()->get("user");
    if (!$admin) {
        throw new \App\Exceptions\InvalidRequestException("没找到登录相关信息,请先登录~_~");
    }
    return $admin->name;
}


/*
 * 遍历数组中某个字段的值 作为 键   返回新数组
 */
function arrayChangeKeyByField($list, $searchKey)
{
    $arr = [];
    if (!$searchKey) {
        return $list;
    }
    foreach ($list as $k => $v) {
        if (isset($v[$searchKey])) {
            $arr[$v[$searchKey]] = $v;
        }
    }
    return $arr ? $arr : $list;
}


/*
 * 把数组中null的字符串转为空
 */
function conversionArray($arr)
{
    if (empty($arr)) {
        return $arr;
    }

    foreach ($arr as $k => $v) {
        if (is_array($v)) {
            $arr[$k] = conversionArray($v);
        } else {
            if ($v === null) {
                $arr[$k] = "";
            }
        }
    }
    return $arr;
}

function dateDefault($time)
{
    $time = intval($time);
    if ($time) {
        return date("Y-m-d H:i:s", $time);
    }
    return "";
}

/*
 * 格式化数字 保留两位小数
 */
if (!function_exists('sprintf2')) {
    function sprintf2($amount)
    {
        $amount = floatval(strval($amount));
        $tmp = number_format($amount, 2, ".", "");
        return $tmp;
    }
}


/*
 * 格式化数字 保留6位小数
 */
if (!function_exists('sprintf6')) {
    function sprintf6($amount)
    {
        $amount = floatval(strval($amount));
        $tmp = number_format($amount, 6, ".", "");
        return $tmp;
    }
}

/*
 * 格式化数字 保留6位小数
 */
if (!function_exists('sprintf4')) {
    function sprintf4($amount)
    {
        $amount = floatval(strval($amount));
        $tmp = number_format($amount, 4, ".", "");
        return $tmp;
    }
}

if (!function_exists('printJson')) {
    function printJson($data)
    {
        print_r(is_array($data) ? json_encode($data) : $data);
        die;
    }
}

if (!function_exists('TrimX')) {
    //过滤数据
    function TrimX($data = '', $empty = true, $arr = false)
    {
        if (empty($data)) {
            $data = Request::only($arr);
        }
        if (is_array($data)) {
            foreach ($data as $k => $v) {
                if (!empty($v) && $v != '0' && !is_array($v)) {
                    $data[$k] = trim($v);
                    $data[$k] = trim($v, ' ');
                }
                if ($empty == true) {
                    if ((empty($v) && $v != 0) || $v == 'undefined' || $v == null) {
                        unset($data[$k]);
                    }
                }
                if (is_array($arr) && count($arr) > 0 && count($data) > 0) {
                    if (!in_array($k, $arr)) {
                        unset($data[$k]);
                    }
                }
            }
        } else {
            $data = trim($data);
            $data = trim($data, ' ');
        }
        return $data;
    }
}

if (!function_exists('Autograph')) {
    //上传文件接口签名
    function Autograph($exts = 'png|jpg|gif', $url = '')
    {
        if (!$url) {
            $url = Config('website.UploadUrl');
        }
        $data['k1'] = time();
        $data['k2'] = MD5(MD5($data['k1']) . Config('website.UploadKey'));
        echo '<script>
            k1="' . $data['k1'] . '";
            k2="' . $data['k2'] . '";
            UploadImgUrl="' . $url . '";
            exts = "' . $exts . '";
        </script>';
    }
}

if (!function_exists('resolveDB')) {
    //解析SPU和SKU的数据库ID
    function resolveDB($id)
    {
        $data['db'] = 'sku_0';
        $data['table'] = 'lie_sku_0';
        $type = substr($id, 0, 1);
        $dbCode = substr($id, -2, 1);
        $tableCode = substr($id, -1);
        if ($type == 1) {
            $data['db'] = 'sku_' . $dbCode;
            $data['table'] = 'lie_sku_' . $tableCode;
        }
        if ($type == 2) {
            $data['db'] = 'spu';
            $data['table'] = 'lie_spu_' . $tableCode;
        }
        return $data;
    }
}

/**
 * 生成随机数
 *
 * @param $length   int      随机数长度
 * @param $numeric  boolean  是否为纯数字
 * @return string  随机数
 */
if (!function_exists('random')) {
    function random($length, $numeric = 0)
    {
        $seed = base_convert(md5(microtime() . $_SERVER['DOCUMENT_ROOT']), 16, $numeric ? 10 : 35);
        $seed = $numeric ? (str_replace('0', '', $seed) . '012340567890') : ($seed . 'zZ' . strtoupper($seed));
        if ($numeric) {
            $hash = '';
        } else {
            $hash = chr(rand(1, 26) + rand(0, 1) * 32 + 64);
            $length--;
        }
        $max = strlen($seed) - 1;
        for ($i = 0; $i < $length; $i++) {
            $hash .= $seed[mt_rand(0, $max)];
        }
        return $hash;
    }
}

if (!function_exists('dtcurl')) {
    function dtcurl($url, $data = null)
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        if (!empty($data)) {
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type: application/json;charset=UTF-8'));
        }
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $output = curl_exec($curl);
        curl_close($curl);
        return json_decode($output, true);
    }
}

if (!function_exists('sendRobot')) {
    function sendRobot($webhook = '', $message, $at = false)
    {
        if (empty($webhook)) {
            $webhook = "https://oapi.dingtalk.com/robot/send?access_token=d1e3ec2e48d250721276accbadb8defcf28ba8e6fd8644ee3f4c3ea4cf417bca";
        }
        $data = array('msgtype' => 'text', 'text' => array('content' => $message . "\r\n" . Config('website.data')));
        if ($at === true) {
            $data['at'] = ['atMobiles' => ['13510507993']];
        }
        $data_string = json_encode($data);
        $result = dtcurl($webhook, $data_string, true);
        return $result;
    }
}

if (!function_exists('getFile')) {
    function getFile($url, $save_dir = '', $filename = '', $type = 0)
    {
        if (trim($url) == '') {
            return false;
        }
        if (trim($save_dir) == '') {
            $save_dir = '.' . DIRECTORY_SEPARATOR;
        }
        if (0 !== strrpos($save_dir, DIRECTORY_SEPARATOR)) {
            $save_dir .= DIRECTORY_SEPARATOR;
        }
        //创建保存目录
        if (!file_exists($save_dir) && !mkdir($save_dir, 0777, true)) {
            return false;
        }
        //获取远程文件所采用的方法
        if ($type) {
            $ch = curl_init();
            $timeout = 5;
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
            $content = curl_exec($ch);
            curl_close($ch);
        } else {
            ob_start();
            readfile($url);
            $content = ob_get_contents();
            ob_end_clean();
        }
        //echo $content;
        $size = strlen($content);
        //文件大小
        $fp2 = @fopen($save_dir . $filename, 'a');
        fwrite($fp2, $content);
        fclose($fp2);
        unset($content, $url);
        return array(
            'file_name' => $filename,
            'save_path' => $save_dir . $filename,
            'file_size' => $size
        );
    }

    if (!function_exists('arraySequence')) {
        function arraySequence($array, $field, $sort = 'SORT_DESC')
        {
            $arrSort = array();
            foreach ($array as $uniqid => $row) {
                foreach ($row as $key => $value) {
                    $arrSort[$key][$uniqid] = $value;
                }
            }
            array_multisort($arrSort[$field], constant($sort), $array);
            return $array;
        }
    }

    if (!function_exists('SendAlarm')) {
        function SendAlarm($e, $url = '')
        {
            if (stripos($e->getFile(), 'RouteCollection.php')) {
                return false;
            }
            $message = '时间：' . date('Y-m-d H:i:s') . "\r\n" . '任务：' . $e->getFile() . "\r\n" . '错误信息：' . $e->getMessage() . "\r\n" . '行号：' . $e->getLine() . $url;
            if (!empty(request()->cookie('oa_user_id'))) {
                $message .= "\r操作用户ID:" . request()->cookie('oa_user_id');
            }
            if (!empty(request()->input())) {
                $message .= "\r参数：" . json_encode(request()->input());
            }
            SendRobot('', $message);
        }
    }

    if (!function_exists('checkHasChineseString')) {
        function checkHasChineseString($string)
        {
            return preg_match('/[\x{4e00}-\x{9fa5}]/u', $string) > 0;
        }
    }

    if (!function_exists('arraySort')) {
        function arraySort($array, $keys, $sort = SORT_ASC)
        {
            $keysValue = [];
            foreach ($array as $k => $v) {
                $keysValue[$k] = $v[$keys];
            }
            array_multisort($keysValue, $sort, $array);
            return $array;
        }
    }

    if (!function_exists('isTwoDimensionalArray')) {
        function isTwoDimensionalArray($array)
        {
            if (!is_array($array)) {
                return false;
            }

            foreach ($array as $value) {
                if (is_array($value)) {
                    return true;
                }
            }

            return false;
        }
    }

    if (!function_exists('compareTwoDimensionalArrays')) {
        function compareTwoDimensionalArrays($array1, $array2)
        {
            $result = [];

            // 遍历第一个数组，查找不同或不存在的元素
            foreach ($array1 as $key => $value) {
                if (!array_key_exists($key, $array2)) {
                    $result[$key] = $value;
                } else {
                    foreach ($value as $subKey => $subValue) {
                        if (!isset($array2[$key][$subKey]) || $array2[$key][$subKey] !== $subValue) {
                            $result[$key][$subKey] = $subValue;
                        }
                    }
                }
            }

            // 遍历第二个数组，查找在第一个数组中不存在的元素
            foreach ($array2 as $key => $value) {
                if (!array_key_exists($key, $array1)) {
                    $result[$key] = $value;
                } else {
                    foreach ($value as $subKey => $subValue) {
                        if (!isset($array1[$key][$subKey]) || $array1[$key][$subKey] !== $subValue) {
                            $result[$key][$subKey] = $subValue;
                        }
                    }
                }
            }

            return $result;
        }
    }

    if (!function_exists('isJsonDeserializable')) {
        function isJsonDeserializable($string)
        {
            json_decode($string);
            return (json_last_error() === JSON_ERROR_NONE);
        }
    }

    if (!function_exists('isOnlineEnv')) {
        function isOnlineEnv()
        {
            $hostParts = explode('.', request()->getHost());
            $mainDomain = implode('.', array_slice($hostParts, -2));
            if ($mainDomain != 'ichunt.net') {
                return false;
            }
            return true;
        }
    }

    if (!function_exists('convertToGrams')) {
        function convertToGrams(string $input)
        {
            // 清洗数据：去除左右空格，去除中间空格
            $input = str_replace(' ', '', trim($input));
            // 分离数字和单位
            $numStr = '';
            $unit = '';
            $len = strlen($input);
            for ($i = 0; $i < $len; $i++) {
                $char = $input[$i];
                if (is_numeric($char) || $char === '.') {
                    $numStr .= $char;
                } else {
                    $unit = substr($input, $i);
                    break;
                }
            }

            // 将数字部分转换为浮点数
            if (!is_numeric($numStr)) {
                return 0;
            }
            $num = floatval($numStr);

            // 将单位转换为小写，方便匹配
            $unit = strtolower($unit);

            // 根据单位返回对应的克数
            switch ($unit) {
                case 'kg':
                case '千克':
                    return $num * 1000;
                case 'g':
                case '克':
                    return $num;
                case 't':
                case '吨':
                    return $num * 1000000;
                case 'mg':
                case '毫克':
                    return $num / 1000;
                default:
                   return 0;
            }
        }
    }
}
