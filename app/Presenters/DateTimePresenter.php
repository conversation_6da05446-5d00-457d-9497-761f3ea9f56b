<?php


namespace App\Presenters;


class DateTimePresenter
{
    public function render($name, $text, $value)
    {
        $time = $value;
        return <<<EOF
                 <label class="layui-form-label">$text</label>
                            <div class="layui-input-inline">
                                <input type="text" id="$name" name="$name"
                                       placeholder="请输入$text"
                                       class="layui-input"
                                       value="$time">
                            </div>
                   <script>
                    window.onload = function(){
                    layui.use('laydate', function(){
                       let laydate = layui.laydate;
                       laydate.render({ 
                         elem: 'input[name=$name]'
                           ,type: 'datetime'
                           ,format: 'yyyy-MM-dd H:i:s'
                       });
                     });
                    }
                    </script>
EOF;
    }
}