<?php

namespace App\Presenters;

//百度编辑器模板插件
class TinymcePresenter
{
    public function render($name, $text, $value = null, $option = [])
    {
        $uploadUrl = config('website.UploadUrl');
        $isRequired = \Arr::get($option, 'required', false);
        $requiredHtml = $isRequired ? '<span style="color: red">*</span>' : "";
        $height = \Arr::get($option, 'height', 600);
        $value = e($value);
        $html = <<<EOF
                 <label class="layui-form-label">$requiredHtml $text</label>
                                <input type="hidden" name="$name" id="$name" value='$value'>
                                       <div class="layui-input-block" style="min-width: 1200px">
                            <textarea id="$name-container">$value</textarea>
                        </div>

<script>
                           tinymce.init({
                            selector: '#$name-container',
                            language: 'zh_CN',//注意大小写
                            plugins: 'print preview code searchreplace autolink directionality visualblocks visualchars fullscreen image link media template code codesample table charmap hr pagebreak nonbreaking anchor insertdatetime advlist lists wordcount imagetools textpattern help emoticons autosave   autoresize  axupimgs',
                            toolbar: 'code undo redo code  restoredraft | forecolor backcolor bold italic underline strikethrough link anchor | alignleft aligncenter alignright alignjustify outdent indent | \
    styleselect formatselect fontselect fontsizeselect image axupimgs| bullist numlist | blockquote subscript superscript removeformat | \
    table  media charmap emoticons hr pagebreak insertdatetime print preview | fullscreen | lineheight',
                            height: $height, //编辑器高度
                            min_height: $height,
                            setup: function (editor) {
                                editor.on('change', function () {
                                    console.log('Content was updated. Current content:', editor.getContent());
                                    $('#$name').val(editor.getContent());
                                });
                            },
                            images_upload_handler: function (blobInfo, succFun, failFun) {
                                var xhr, formData;
                                var file = blobInfo.blob();//转化为易于理解的file对象
                                xhr = new XMLHttpRequest();
                                xhr.withCredentials = false;
                                xhr.open('POST', getImageServerDomain() + '/uploadImage?sys_type=5&create_uid=' + getCookie('oa_user_id'));
                                xhr.onload = function () {
                                    var json;
                                    if (xhr.status !== 200) {
                                        failFun('HTTP Error: ' + xhr.status);
                                        return;
                                    }
                                    json = JSON.parse(xhr.responseText);
                                    if (json.code !== 0) {
                                        failFun('上传图片失败.'+json.message);
                                        return;
                                    }
                                    succFun(json.data.oss_image_url);
                                };
                                formData = new FormData();
                                formData.append('file', file, file.name);
                                // formData.append('k1', k1);
                                // formData.append('k2', k2);
                                // formData.append('source', 1);
                                xhr.send(formData);
                            },

                        });

</script>
EOF;
        return $html;
    }
}
