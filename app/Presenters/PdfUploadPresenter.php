<?php

namespace App\Presenters;

//单图上传展示模板组件
class PdfUploadPresenter
{
    public function render($name, $text, $value = null, $data = [], $option = [])
    {
        $isRequired = \Arr::get($option, 'required', false);
        $ext = \Arr::get($option, 'ext', []);
        $ext = json_encode($ext);
        $requiredHtml = $isRequired ? '<span style="color: #ff0000">*</span>' : "";
        $uploadJsField = Autograph();
        $hide = !$value ? "layui-hide" : '';
        $html = <<<EOF
                    $uploadJsField
                    <label class="layui-form-label">
                    $requiredHtml
                      $text
                     </label>
                     <input type="hidden" id="$name" name="$name" value="$value">
                     <div class="layui-upload-drag" id="${name}UploadDIv">

                      <div class="$hide" id="${name}ViewDiv">
                        <img src="$value" alt="上传成功后渲染" style="max-width: 196px">
                      </div>
                      <br>
                      <i class="layui-icon"></i>
                     <p>点击上传，或将文件拖拽到此处</p>
                    </div>
                    <script>
               window.onload=function(){
                    layui.use('upload', function(){
                       var upload = layui.upload;
                       let uploadIns = upload.render({
                          elem: '#${name}UploadDIv',
                          url: UploadImgUrl, //改成您自己的上传接口
                          field:'upload',
                          exts: ['pdf'],
                          size: 50000,
                          data: {
                            k1:k1,
                            k2:k2,
                            source:1
                          },
                          done: function(res){
                            layer.msg('上传成功');
                            $('#$name').val(res.data[0]);
                            layui.$('#${name}ViewDiv').removeClass('layui-hide').find('img').attr('src', res.data[0]);
                            layui.$('#${name}UploadDIv').find('i').remove();
                            layui.$('#${name}UploadDIv').find('p').remove();
                          }
                        });
                       uploadIns.reload();
                     });
                    }
                    </script>
EOF;

        return $html;
    }
}
