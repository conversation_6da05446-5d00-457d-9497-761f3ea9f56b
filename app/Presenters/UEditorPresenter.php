<?php

namespace App\Presenters;

//百度编辑器模板插件
class UEditorPresenter
{
    public function render($name, $text, $value = null, $data = [], $option = [])
    {
        $isRequired = \Arr::get($option, 'required', false);
        $requiredHtml = $isRequired ? '<span style="color: red">*</span>' : "";
        $html = <<<EOF
                   <script type="text/javascript" charset="utf-8" src="/assets/libs/editor/ueditor.config.js"></script>
                   <script type="text/javascript" charset="utf-8" src="/assets/libs/editor/ueditor.all.min.js"></script>
                   <script type="text/javascript" charset="utf-8" src="/assets/libs/editor/lang/zh-cn/zh-cn.js"></script>
                    <label class="layui-form-label">
                   $requiredHtml
                     $text
                    </label>
                     <div class="layui-input-inline">
                                    <script type="text/plain" id="$name"
                                            name="$name">$value</script>
                                    <script type="text/javascript">UE.getEditor('$name');</script>
                    </div>
EOF;
        return $html;
    }
}
