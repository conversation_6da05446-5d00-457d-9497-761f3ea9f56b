<?php


namespace App\Presenters;


class TimeIntervalPresenter
{
    public function render($name, $text)
    {
        $time = request()->get($name);
        $html = <<<EOF
                    <label class="layui-form-label">$text</label>
                    <div class="layui-input-inline" style="min-width: 280px">
                        <input type="text" name="{$name}" placeholder="请选择时间区间" autocomplete="off" class="layui-input">
                    </div>
                   <script>
                        $(document).ready(function(){
                    layui.use('laydate', function(){
                       let laydate = layui.laydate;
                       laydate.render({
                         elem: 'input[name=$name]'
                           ,type: 'datetime'
                           ,trigger:'click'
                           ,range: '~' //或 range: '~' 来自定义分割字符
                           ,value: '$time'
                       });
                     });
                     });
                    </script>
EOF;
        return $html;
    }
}
