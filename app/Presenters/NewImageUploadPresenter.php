<?php

namespace App\Presenters;

//单图上传展示模板组件
class NewImageUploadPresenter
{
    public function render($name, $text, $value = null, $data = [], $option = [])
    {
        $isRequired = \Arr::get($option, 'required', false);
        $requiredHtml = $isRequired ? '<span style="color: #ff0000">*</span>' : "";
        $uploadJsField = Autograph();
        $hide = !$value ? "layui-hide" : '';
        $html = <<<EOF
                    $uploadJsField
                    <label class="layui-form-label">
                    $requiredHtml
                      $text
                     </label>
                     <input type="hidden" id="$name" name="$name" value="$value">
                     <div class="layui-upload-drag" id="${name}UploadDIv">

                      <div class="$hide" id="${name}ViewDiv">
                        <img src="$value" alt="上传成功后渲染" style="max-width: 196px">
                      </div>
                      <br>
                      <i class="layui-icon"></i>
                     <p>点击上传，或将文件拖拽到此处</p>
                    </div>
                    <script>
                    layui.use('upload', function(){
                       var upload = layui.upload;
                       let uploadIns = upload.render({
                          elem: '#${name}UploadDIv',
                          url: getImageServerDomain() + '/uploadImage?sys_type=5&create_uid=' + getCookie("oa_user_id"),
                          field:'file',
                          accept:'file',
                          done: function(res){
                              if (res.code==0){
                                   layer.msg('上传成功');
                                   $('#$name').val(res.data.oss_image_url);
                                   layui.$('#${name}ViewDiv').removeClass('layui-hide').find('img').attr('src', res.data.oss_image_url);
                                   layui.$('#${name}UploadDIv').find('i').remove();
                                   layui.$('#${name}UploadDIv').find('p').remove();
                              }else{
                                  layer.msg(res.data);
                              }

                          }
                        });
                      uploadIns.reload();
                     });
                    </script>
EOF;

        return $html;
    }
}
