<?php


namespace App\Presenters;


class BrandSelectorPresenter
{
    public function render($name, $text, $value = null, $data = [0 => '禁用', 1 => '启用'], $option = [])
    {
        $isRequired = \Arr::get($option, 'required', false);
        $requiredHtml = $isRequired ? '<span style="color: red">*</span>' : "";
        $html = <<<EOF
           <label class="layui-form-label">
              $requiredHtml
              $text</label>
              <div class="layui-input-inline">
              <div id="$name" class="layui-input-inline" value="$value" style="width: 700px;">
              </div>
         <input type="hidden" name="$name" id="$name">
          </div>
         <script>
             //渲染品牌多选
         let brandUrl = '/api/common/getBrandList';
         let brandSelector = xmSelect.render({
             el: '#$name',
             name: '$name',
             searchTips: '请输入要查找的制造商',
             paging: true,
             empty: '没有查找到数据',
             prop: {
                 name: 'brand_name',
                 value: 'brand_id'
             },
             height: "1300px",
             remoteSearch: true,
             autoRow: true,
             pageRemote: true,
             pageSize: 10,
             filterable: true,
             remoteMethod: function (val, cb, show, pageIndex) {
                 //val: 搜索框的内容, 不开启搜索默认为空, cb: 回调函数, show: 当前下拉框是否展开, pageIndex: 当前第几页
                 $.ajax({
                     url: brandUrl,
                     type: 'post',
                     data: {
                         brand_ids: $('#$name').val(),
                         brand_name: val,
                         page: pageIndex
                     },
                     dataType: 'json',
                     timeout: 10000,
                     success: function (res) {
                         if (!res) return layer.msg('网络错误，请重试', {icon: 5});
                         if (res.code === 0) {
                             cb(res.data, res.count/10);
                         }
                     },
                     error: function () {
                         return layer.msg('网络错误，请重试', {icon: 5});
                     }
                 });
             },
             on: function (data) {
                 let brandIds = '';
                 for (let x in data.arr)  // x 为属性名
                 {
                     brandIds = brandIds + data.arr[x].brand_id + ',';
                 }
                 $("#brand_ids").val(brandIds);
             }
         });
         let brandIds = $('#brand_selector').attr('value');
         brandSelector.setValue(brandIds.split(','));
         </script>
EOF;
        return $html;
    }

}
