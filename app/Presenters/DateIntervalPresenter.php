<?php


namespace App\Presenters;


class DateIntervalPresenter
{
    public function render($name, $text)
    {
        $time = request()->get($name);
        $html = <<<EOF
                    <label class="layui-form-label">$text</label>
                    <div class="layui-input-inline" style="min-width: 140px">
                        <input type="text" name="{$name}" placeholder="请选择时间区间" autocomplete="off" class="layui-input">
                    </div>
                   <script>
                    window.onload = function(){
                    layui.use('laydate', function(){
                       let laydate = layui.laydate;
                       laydate.render({
                         elem: 'input[name=$name]'
                           ,type: 'date'
                           ,trigger:'click'
                           ,range: '~' //或 range: '~' 来自定义分割字符
                           ,value: '$time'
                       });
                     });
                    }
                    </script>
EOF;
        return $html;
    }
}
