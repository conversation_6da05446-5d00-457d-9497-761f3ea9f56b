<?php

namespace App\Presenters;

//百度编辑器模板插件
class WangEditorPresenter
{
    public function render($name, $text, $value = null, $option = [])
    {
        $uploadUrl = config('website.UploadUrl');
        $isRequired = \Arr::get($option, 'required', false);
        $requiredHtml = $isRequired ? '<span style="color: red">*</span>' : "";
        $editorName = $name . 'Editor';
        $html = <<<EOF
                 <label class="layui-form-label">$requiredHtml $text:</label>
                                <input type="hidden" name="$name" id="$name" value='$value'>
                                <div class="layui-input-block">
                                    <div id="$name-container">
                                        <div>
                                            $value
                                        </div>
                                    </div>
                                </div>
<script src="/layui_exts/wangEditor.min.js"></script>
<script>
    const $name = window.wangEditor
    const $editorName = new $name('#$name-container');
    {$editorName}.config.customUploadImg = function (resultFiles, insertImgFn) {
        // resultFiles 是 input 中选中的文件列表
        // insertImgFn 是获取图片 url 后，插入到编辑器的方法
        $.each(resultFiles, function (index, item) {
            var formData = new FormData();
            formData.append('upload', item);
            formData.append('k1', k1);
            formData.append('k2', k2);
            formData.append('source', 1);
            $.ajax({
                type: "post",
                url: "$uploadUrl",
                data: formData,
                contentType: false,
                //默认文件类型application/x-www-form-urlencoded  设置之后multipart/form-data
                processData: false,
                // 默认情况下会对发送的数据转化为对象 不需要转化的信息
                success: function (res) {
                    if (res.code === 200) {
                        insertImgFn(res.data[0]);
                    } else {
                        layer.msg('上传失败,' + res.message);
                    }
                },
            });
        });
    }
    {$editorName}.config.onchange = function (newHtml) {
       $('#$name').val(newHtml);
    };
     {$editorName}.create();
</script>
EOF;
        return $html;
    }
}
