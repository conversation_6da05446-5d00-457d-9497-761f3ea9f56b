<?php


namespace App\Presenters;


use Arr;

class MultiSelectorPresenter
{
    public function render($name, $text, $value = null, $data = [], $option = [])
    {
        $data = $data ?: [['name' => '启用', 'value' => 1], ['name' => '禁用', 'value' => -1]];
        $isRequired = Arr::get($option, 'required', false);
        $radio = Arr::get($option, 'radio', false);
        $radio = $radio ? "true" : "false";
        $width = Arr::get($option, 'width', '157px');
        $disable = Arr::get($option, 'disable', false);
        $disable = $disable ? "true" : "false";
        $requiredHtml = $isRequired ? '<span style="color: red">*</span>' : "";
        $elemId = $name . "Selector";
        $data = json_encode($data);
        $html = <<<EOF
           <label class="layui-form-label">
              $requiredHtml
              $text</label>
           <div class="layui-input-inline" style="margin-top: 0">
              <div id="$elemId" class="layui-input-inline" value="$value" style="width: $width;">
              </div>
              <input type="hidden" name="$name" id="$name" value="$value">
          </div>
         <script>
        layui.use(['xmSelect', 'element'], function () {
    let xmSelect = layui.xmSelect;
    //渲染多选
    let $elemId = xmSelect.render({
        el: '#$elemId',
        name: `$name`,
        searchTips: '请输入要查找的$text',
        paging: true,
        empty: '没有数据',
        prop: {
            name: 'name',
            value: 'value'
        },
        radio: $radio,
        disabled: $disable,
	    size: 'mini',
        direction: 'down',
        data : $data,
        height: "1300px",
        autoRow: true,
        pageSize: 10,
        filterable: true,
        on: function (data) {
            let values = [];
            for (let x in data.arr)  // x 为属性名
            {
                values.push(data.arr[x].value);
            }
            $("#$name").val(values.join(','));
        }
    });
    let values = $('#$elemId').attr('value');
    $elemId.setValue(values.split(','));
});
         </script>
EOF;
        return $html;
    }
}
