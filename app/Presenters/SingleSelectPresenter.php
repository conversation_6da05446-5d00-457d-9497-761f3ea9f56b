<?php


namespace App\Presenters;


class SingleSelectPresenter
{
    public function render($name, $text, $value = null, $data = [0 => '禁用', 1 => '启用'], $option = [])
    {
        $isRequired = \Arr::get($option, 'required', false);
        $requiredHtml = $isRequired ? '<span style="color: red">*</span>' : "";
        $html = <<<EOF
                    <label class="layui-form-label">
                   $requiredHtml
                     $text
                    </label>
                     <div class="layui-input-block">
                        {$this->itemRender($data,$name, $value)}
                    </div>
EOF;
        return $html;
    }

    public function itemRender($data,$name, $value)
    {
        $checked = '';
        $itemsHtml = '';
        foreach ($data as $v => $item) {
            if ($value !== null) {
                $checked = ($v == $value) ? "checked" : '';
            }
            $itemsHtml = $itemsHtml . "<input type='radio' lay-filter='${name}' name='${name}' value='${v}' title='${item}' $checked>";
        }
        return $itemsHtml;
    }
}
