<?php


namespace App\Presenters;


class StatusPresenter
{
    public function render($name, $text, $status = null, $data = [0 => '禁用', 1 => '启用'], $option = [])
    {
        $isRequired = \Arr::get($option, 'required', false);
        $isDisable = \Arr::get($option, 'disable') === true ? 'disabled' : '';
        $width = \Arr::get($option, 'width', '170px');
        $noName = \Arr::get($option, 'noName');
        $name = $noName ? '' : $name;
        $requiredHtml = $isRequired ? '<span style="color: red">*</span>' : "";
        $html = <<<EOF
                    <label class="layui-form-label">
                   $requiredHtml
                     $text
                    </label>
                     <div class="layui-input-inline" style="width: $width">
                        <select name="$name" id="$name" lay-filter="$name" $isDisable  lay-search="">
                        {$this->optionsRender($data, $status)}
                        </select>
                    </div>
EOF;
        return $html;
    }

    public function optionsRender($data, $status)
    {
        $optionsHtml = ' <option value="">请选择</option>';
        $checked = '';
        foreach ($data as $key => $value) {
            if ($status !== '' && $status != null) {
                $checked = ($key == $status) ? "selected='selected'" : '';
            }
            if ($status === 0 || $status === '') {
                $checked = ($key === $status) ? "selected='selected'" : '';
            }
            $optionsHtml = $optionsHtml . "<option value='$key' $checked>$value</option>";
        }
        return $optionsHtml;
    }
}
