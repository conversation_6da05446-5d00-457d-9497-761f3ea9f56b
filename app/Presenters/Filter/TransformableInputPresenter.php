<?php


namespace App\Presenters\Filter;


class TransformableInputPresenter
{
    public function render($map = [])
    {
        $unique = "unique_" . mt_rand(1, 100000);
        $defaultKey = array_keys($map)[0];
        $html = <<<EOF
               <div class="layui-row" style="width:268px;margin-bottom: 3px;">
                    <div class="layui-col-md5">
                        <div class="layui-input-inline" style="width: 100px;margin-left: 10px">
                            <select lay-filter="$unique">
                             {$this->optionsRender($map)}
                            </select>
                        </div>
                    </div>
                    <div class="layui-col-md7" style="margin-left: -3px">
                        <div class="layui-input-inline">
                            <input type="text" name="$defaultKey" placeholder="请输入" class="layui-input"
                                   style="width: 158px">
                        </div>
                    </div>
                </div>
                <script>
                $(function() {
                    layui.use(['form', 'element'], function () {
                                let form = layui.form;
                                 form.on('select($unique)', function(data){
                                     data.othis.parent().parent().parent().find('input').attr('name',data.value);
                                     form.render();
                                 });
                    });
                });
                  
                </script>
EOF;
        return $html;
    }

    public function optionsRender($data)
    {
        $optionsHtml = '';
        foreach ($data as $key => $value) {
            $optionsHtml = $optionsHtml . "<option value='$key'>$value</option>";
        }
        return $optionsHtml;
    }
}