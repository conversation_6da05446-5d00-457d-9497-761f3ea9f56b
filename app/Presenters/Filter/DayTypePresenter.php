<?php


namespace App\Presenters\Filter;


class DayTypePresenter
{
    public function render($relateObjName = '')
    {
        $unique = "unique_" . mt_rand(1, 100000);
        $html = <<<EOF
               <div class="layui-row" style="width:268px;margin-bottom: 3px;margin-top: 5px">
                    <div class="layui-col-md2" style="margin-left: 34px">
                     <button type="button" class="layui-btn layui-btn-primary layui-btn-sm day_type_button" id="${unique}_today">今日</button>
                     </div>
                      <div class="layui-col-md2">
                      <button type="button" class="layui-btn layui-btn-primary layui-btn-sm day_type_button" id="${unique}_last_days">近三天</button>
                     </div>
                      <div class="layui-col-md2">
                      <button type="button" class="layui-btn layui-btn-primary layui-btn-sm day_type_button" style="margin-left: 11px" id="${unique}_this_week">本周</button>
                     </div>
                      <div class="layui-col-md2">
                      <button type="button" class="layui-btn layui-btn-primary layui-btn-sm day_type_button"  style="margin-left: 10px" id="${unique}_this_month">本月</button>
                     </div>
                      <div class="layui-col-md3">
                     </div>
                </div>
                <script>
                    layui.use(['form', 'element'], function () {
                               $('.day_type_button').click(function() {
                                   $('.day_type_button').attr('class','layui-btn layui-btn-primary layui-btn-sm day_type_button');
                                   $(this).attr('class','layui-btn layui-btn-sm layui-btn-normal day_type_button');
                               });
                    });
                </script>
EOF;
        return $html;
    }

    public function optionsRender($data)
    {
        $optionsHtml = '';
        foreach ($data as $key => $value) {
            $optionsHtml = $optionsHtml . "<option value='$key'>$value</option>";
        }
        return $optionsHtml;
    }
}