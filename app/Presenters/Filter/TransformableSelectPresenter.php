<?php


namespace App\Presenters\Filter;

//左边可以下拉,右边也可以下拉
class TransformableSelectPresenter
{
    public function render($map = [], $data = [])
    {
        $defaultKey = array_keys($map)[0];
        $unique = "unique_" . mt_rand(1, 100000);
        $selectUnique = "unique_" . mt_rand(1, 100000);
        $html = <<<EOF
                <div class="layui-row" style="width:268px;margin-bottom: 3px;">
                    <div class="layui-col-md5">
                        <div class="layui-input-inline" style="width: 100px;margin-left: 10px">
                            <select lay-filter="$unique">
                             {$this->optionsRender($map)}
                            </select>
                        </div>
                    </div>
                    <div class="layui-col-md7" style="margin-left: -3px">
                        <div class="layui-input-inline">
                           <select class="value_select" name="$defaultKey" lay-filter="$selectUnique" lay-search="">
                             {$this->selectOptionsRender($data)}
                            </select>
                        </div>
                    </div>
                </div>
                <script>
                $(function() {
                               layui.use(['form', 'element'], function () {
                                let form = layui.form;
                                 form.on('select($unique)', function(data){
                                     data.othis.parent().parent().parent().find('.value_select').attr('name',data.value);
                                 });
                    });
                });
       
                </script>
EOF;
        return $html;
    }

    public function optionsRender($data)
    {
        $optionsHtml = '';
        foreach ($data as $key => $value) {
            $optionsHtml = $optionsHtml . "<option value='$key'>$value</option>";
        }
        return $optionsHtml;
    }

    public function selectOptionsRender($data)
    {
        $optionsHtml = ' <option value="">请选择</option>';
        foreach ($data as $key => $value) {
            $optionsHtml = $optionsHtml . "<option value='$key'>$value</option>";
        }
        return $optionsHtml;
    }
}