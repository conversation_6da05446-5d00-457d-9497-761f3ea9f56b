<?php


namespace \Arr::getFilter;

//左边数据可以多种,右边数据也可以多种,而且右边数据跟着左边来变
class MultiTransformableSelectPresenter
{
    public function render($map = [], $data = [])
    {
        $defaultKey = array_keys($map)[0];
        $unique = "unique_" . mt_rand(1, 100000);
        $selectUnique = "unique_" . mt_rand(1, 100000);
        //默认的第一项数据
        $firstDataItemKey = array_first(array_keys($data));
        $html = <<<EOF
                <div class="layui-row" style="width:268px;margin-bottom: 3px;">
                    <div class="layui-col-md5">
                        <div class="layui-input-inline" style="width: 100px;margin-left: 10px">
                            <select lay-filter="$unique">
                             {$this->optionsRender($map)}
                            </select>
                        </div>
                    </div>
                    <div class="layui-col-md7" style="margin-left: -3px">
                        <div class="layui-input-inline">
                           <select class="value_select" name="$defaultKey" id="$selectUnique" lay-filter="$selectUnique" lay-search="">
                            </select>
                        </div>
                    </div>
                    {$this->selectOptionsRender($data)}
                </div>
                <script>
                $(function() {
                               layui.use(['form', 'element'], function () {
                                let form = layui.form;
                                //初始化渲染
                                let optionsHtml = $('#$firstDataItemKey').html();
                                $('#$selectUnique').html(optionsHtml);
                                form.render();

                                //变更渲染
                                form.on('select($unique)', function(data){
                                    console.log(data.value)
                                    optionsHtml = $('#'+data.value).html();
                                    $('#$selectUnique').html(optionsHtml);
                                    data.othis.parent().parent().parent().find('.value_select').attr('name',data.value);
                                    form.render();
                                });
                    });
                });

                </script>
EOF;
        return $html;
    }

    public function optionsRender($data)
    {
        $optionsHtml = '';
        foreach ($data as $key => $value) {
            $optionsHtml = $optionsHtml . "<option value='$key'>$value</option>";
        }
        return $optionsHtml;
    }

    public function selectOptionsRender($data)
    {
        $html = '';
        foreach ($data as $key => $value) {
            //第二层
            $optionsHtml = ' <option value="">请选择</option>';
            foreach ($value as $k => $v) {
                $optionsHtml = $optionsHtml . "<option value='$k'>$v</option>";
            }
            $html .= <<<EOF
            <div id="$key" style="display: none" >
            $optionsHtml;
            </div>
EOF;

        }
        return $html;
    }
}
