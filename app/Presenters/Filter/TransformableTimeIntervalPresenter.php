<?php


namespace App\Presenters\Filter;


use Carbon\Carbon;

class TransformableTimeIntervalPresenter
{
    public function render($map = [])
    {
        $unique = "unique_" . mt_rand(1, 100000);
        $defaultKey = array_keys($map)[0];
        $todayTimeInterval = Carbon::now()->startOfDay()->toDateString() . ' ~ ' . Carbon::now()->addDay(1)->toDateString();
        $threeDayTimeInterval = Carbon::now()->subDay(2)->toDateString() . ' ~ ' . Carbon::now()->addDay(1)->toDateString();
        $thisWeekTimeInterval = Carbon::now()->startOfWeek()->toDateString() . ' ~ ' . Carbon::now()->addDay(1)->toDateString();
        $thisMonthTimeInterval = Carbon::now()->startOfMonth()->toDateString() . ' ~ ' . Carbon::now()->addDay(1)->toDateString();
        $html = <<<EOF
              <div class="layui-row" style="margin-bottom: 3px;">
                             <div class="layui-col-md6">
                   <div class="layui-col-md5">
                        <div class="layui-input-inline" style="width: 100px;margin-left: 10px">
                            <select lay-filter="$unique">
                             {$this->optionsRender($map)}
                            </select>
                        </div>
                        </div>
                <div class="layui-col-md7" style="margin-left: -15px">
                    <div class="layui-input-inline" style="min-width: 150px;width: 158px">
                        <input type="text" name="$defaultKey" id="$unique" placeholder="请选择时间区间" autocomplete="off" class="layui-input">
                    </div>
                    </div>
                    </div>
             <div class="layui-col-md6">
             <div style="margin-top: 5px;">
                    <div class="layui-col-md2" style="margin-left: -18px">
                     <button type="button" class="layui-btn layui-btn-primary layui-btn-sm day_type_button"
                       dateInterval="$todayTimeInterval"
                       id="${unique}_today">今日</button>
                     </div>
                      <div class="layui-col-md2">
                      <button type="button" class="layui-btn layui-btn-primary layui-btn-sm day_type_button"
                       dateInterval="$threeDayTimeInterval"
                       id="${unique}_last_days">近三天</button>
                     </div>
                      <div class="layui-col-md2">
                      <button type="button" class="layui-btn layui-btn-primary layui-btn-sm day_type_button" style="margin-left: 11px" 
                      dateInterval="$thisWeekTimeInterval"
                      id="${unique}_this_week">本周</button>
                     </div>
                      <div class="layui-col-md2">
                      <button type="button" class="layui-btn layui-btn-primary layui-btn-sm day_type_button"  style="margin-left: 10px" 
                      dateInterval="$thisMonthTimeInterval"
                      id="${unique}_this_month">本月</button>
                     </div>
                      <div class="layui-col-md3">
                     </div>
                     </div>
                </div>
</div>
                   <script>
                   $(function() {
                      layui.use(['form', 'element','laydate'], function(){
                       let laydate = layui.laydate;
                        let form = layui.form;
                                 form.on('select($unique)', function(data){
                                     data.othis.parent().parent().parent().find('input').attr('name',data.value);
                                 });
                       laydate.render({
                         elem: '#$unique'
                           ,type: 'date'
                           ,trigger:'click'
                           ,range: '~' //或 range: '~' 来自定义分割字符
                           ,change: function (value, date, endDate) {
                             alert(123)
                           }
                       });
                         $('.day_type_button').click(function() {
                                   $('.day_type_button').attr('class','layui-btn layui-btn-primary layui-btn-sm day_type_button');
                                   $(this).attr('class','layui-btn layui-btn-sm layui-btn-normal day_type_button');
                                    laydate.render({
                                        elem: '#$unique'
                                          ,type: 'date'
                                          ,value: $(this).attr('dateInterval')
                                          ,trigger:'click'
                                          ,range: '~' //或 range: '~' 来自定义分割字符
                                      });
                               });
                    });
                   })
                   
                    </script>
EOF;

        return $html;
    }

    public function optionsRender($data)
    {
        $optionsHtml = '';
        foreach ($data as $key => $value) {
            $optionsHtml = $optionsHtml . "<option value='$key'>$value</option>";
        }
        return $optionsHtml;
    }
}