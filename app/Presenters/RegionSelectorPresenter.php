<?php

namespace App\Presenters;


class RegionSelectorPresenter
{
    public function render($name, $text, $status = null, $data = [0 => '禁用', 1 => '启用'], $option = [])
    {
        $isRequired = \Arr::get($option, 'required', false);
        $requiredHtml = $isRequired ? '<span style="color: red">*</span>' : "";
        return <<<EOF
                    <label class="layui-form-label">
                     $requiredHtml
                     $text
                    </label>
                     <div class="layui-input-inline" style="width: 400px">
              <select name="city" lay-verify="">
  <option value="">请选择一个城市</option>
  <option value="010">北京</option>
  <option value="021">上海</option>
  <option value="0571">杭州</option>
</select>
<select name="city" lay-verify="">
  <option value="">请选择一个城市</option>
  <option value="010">北京</option>
  <option value="021">上海</option>
  <option value="0571">杭州</option>
</select>
                     </div>

                    <script>
                      layui.use(['form'], function () {
                             let $ = layui.$;
                             let form = layui.form;

                          });
                     </script>
EOF;
    }
}
