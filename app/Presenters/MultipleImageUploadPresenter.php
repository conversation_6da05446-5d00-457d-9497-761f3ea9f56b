<?php

namespace App\Presenters;

//百度编辑器模板插件
class MultipleImageUploadPresenter
{
    public function render($name, $text, $value = null, $option = [])
    {
        $uploadUrl = config('website.UploadUrl');
        $isRequired = \Arr::get($option, 'required', false);
        $requiredHtml = $isRequired ? '<span style="color: red">*</span>' : "";
        $editorName = $name . 'Editor';
        $html = <<<EOF
<script src="https://unpkg.com/dropzone@5/dist/min/dropzone.min.js"></script>
<link rel="stylesheet" href="https://unpkg.com/dropzone@5/dist/min/dropzone.min.css" type="text/css" />
                 <label class="layui-form-label">$requiredHtml $text:</label>
                                <input type="hidden" name="$name" id="$name" value='$value'>
                                <div class="layui-input-block">
                                    <div id="$name-container">
                                      <input type="file" id="$name" class="$name" name="$name"/>
                                    </div>
                                </div>
<script>
 $(function(){

    // First register any plugins
    // $.fn.filepond.registerPlugin('https://img.ichunt.com/images/cms/202309/07/16cb3953606bf33f045adb720983b0f8.png');

    // Turn input element into a pond
    $('#$name').filepond();

    // Set allowMultiple property to true
    $('#$name').filepond('allowMultiple', true);

    // Listen for addfile event
    //$('#$name').on('FilePond:addfile', function(e) {
    //    console.log('file added event', e);
    //});

    // Manually add a file using the addfile method
    //$('#$name').first().filepond('addFile', 'index.html').then(function(file){
    //  console.log('file added', file);
    //});

  });
</script>
EOF;
        return $html;
    }
}
