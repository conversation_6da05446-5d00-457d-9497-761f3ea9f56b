<?php

namespace App\Imports;

use App\Http\Models\AlikeSpuModel;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Concerns\WithProgressBar;
use PhpOffice\PhpSpreadsheet\IOFactory;

class ClassAttrImport implements ToCollection, WithHeadingRow, WithProgressBar
{

    public $filePath;

    public $sheetNames;

    public function __construct($filePath)
    {
        $this->filePath = $filePath;
    }

    use Importable;
    public function collection(Collection $rows)
    {
        $reader = IOFactory::createReader('Xlsx');
        $reader->setReadDataOnly(TRUE);
        $spreadsheet = $reader->load($this->filePath);
        $this->sheetNames = $spreadsheet->getSheetNames();
    }
}
