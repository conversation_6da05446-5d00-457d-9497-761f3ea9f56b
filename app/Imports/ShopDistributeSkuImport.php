<?php

namespace App\Imports;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class ShopDistributeSkuImport implements ToCollection, WithChunkReading,ShouldQueue
{
    public function collection($row)
    {
        return [
            $row[0],
            $row[1],
            $row[2],
            $row[3],
            $row[4],
            $row[5],
        ];
    }

    public function chunkSize() : int
    {
        return 1000; // Adjust the chunk size as needed
    }

    public function batchSize() : int
    {
        return 1000; // Adjust the batch size as needed
    }


}
