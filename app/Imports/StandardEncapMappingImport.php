<?php

namespace App\Imports;

use App\Http\Models\AlikeSpuModel;
use App\Http\Models\EncapModel;
use App\Http\Models\StandardEncapMappingModel;
use App\Http\Models\StandardEncapModel;
use http\Exception\InvalidArgumentException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\ToModel;

class StandardEncapMappingImport implements ToArray
{
    public function array($array)
    {
        DB::transaction(function () use ($array) {
             // 处理数组数据
            foreach ($array as $key => $row) {
                if ($key == 0) {
                    if ($row[0] != '普通封装' || $row[1] != '标准封装') {
                        throw new InvalidArgumentException('上传模板不正确,请重新下载对应的模板');
                    }
                    continue;
                }


                $encap = trim($row[0]);
                $standardEncap = trim($row[1]);

                //先去找标准封装是否存在,存在的话,再去判断普通封装是否存在,存在的话判断是否有映射
                //映射也有的话直接跳过
                $standardEncapId = StandardEncapModel::where('encap_name', $standardEncap)->value('standard_encap_id');
                if ($standardEncapId) {
                    $encapId = EncapModel::where('encap_name', $encap)->value('encap_id');
                    if ($encapId) {
                        if (StandardEncapMappingModel::where('encap_id', $encapId)->where('standard_encap_id', $standardEncapId)->exists()) {
                            continue;
                        } else {
                            //检查这个普通封装id是否已经存在映射,存在的话,删除之前的映射
                            StandardEncapMappingModel::where('encap_id', $encapId)->delete();
                            StandardEncapMappingModel::insert([
                                'standard_encap_id' => $standardEncapId,
                                'encap_id' => $encapId,
                                'create_time' => time(),
                                'update_time' => time(),
                                'admin_id' => request()->user->userId,
                                'admin_name' => request()->user->name,
                                'status' => 1
                            ]);
                        }
                    } else {
                        //不存在普通封装id,那么先新建一个,然后直接插入映射即可
                        $encapId = EncapModel::insertGetId([
                            'encap_name' => $encap,
                            'create_time' => time(),
                            'create_uid' => request()->user->userId,
                            'create_name' => request()->user->name,
                            'status' => 1
                        ]);
                        StandardEncapMappingModel::insert([
                            'standard_encap_id' => $standardEncapId,
                            'encap_id' => $encapId,
                            'create_time' => time(),
                            'admin_id' => request()->user->userId,
                            'admin_name' => request()->user->name,
                            'status' => 1
                        ]);
                    }
                } else {
                    //如果标准封装不存在,新建标准封装,判断普通封装是否存在,存在直接映射(先判断是否已经有映射,有的话删除之前的映射)
                    $standardEncapId = StandardEncapModel::insertGetId([
                        'encap_name' => $standardEncap,
                        'create_time' => time(),
                        'create_uid' => request()->user->userId,
                        'create_name' => request()->user->name,
                        'status' => 1
                    ]);
                    $encapId = EncapModel::where('encap_name', $encap)->value('encap_id');
                    if ($encapId) {
                        StandardEncapMappingModel::insert([
                            'standard_encap_id' => $standardEncapId,
                            'encap_id' => $encapId,
                            'create_time' => time(),
                            'admin_id' => request()->user->userId,
                            'admin_name' => request()->user->name,
                            'status' => 1
                        ]);
                    } else {
                        //不存在普通封装id,那么先新建一个,然后直接插入映射即可
                        $encapId = EncapModel::insertGetId([
                            'encap_name' => $encap,
                            'create_time' => time(),
                            'create_uid' => request()->user->userId,
                            'create_name' => request()->user->name,
                            'status' => 1
                        ]);
                        StandardEncapMappingModel::insert([
                            'standard_encap_id' => $standardEncapId,
                            'encap_id' => $encapId,
                            'create_time' => time(),
                            'admin_id' => request()->user->userId,
                            'admin_name' => request()->user->name,
                            'status' => 1
                        ]);
                    }
                }
            }
        });
    }

}
