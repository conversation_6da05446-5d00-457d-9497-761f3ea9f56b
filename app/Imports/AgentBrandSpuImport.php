<?php

namespace App\Imports;

use App\Http\Models\AlikeSpuModel;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\ToModel;

class AgentBrandSpuImport implements ToArray
{
    protected $data = [];

    public function array($rows)
    {
        foreach ($rows as $row) {
            // 在这里处理每一行的数据
            $this->data[] = $row;
        }
    }

    public function getData()
    {
        return $this->data;
    }
}
