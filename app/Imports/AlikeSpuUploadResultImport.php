<?php

namespace App\Imports;

use App\Http\Models\AlikeSpuModel;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\ToModel;

class AlikeSpuUploadResultImport implements ToArray
{
    public function array($row)
    {
        return [
            $row[0],
            $row[1],
            $row[2],
            $row[3],
        ];
    }
}
