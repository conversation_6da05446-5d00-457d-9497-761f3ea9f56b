<?php

namespace App\Imports;

use App\Http\Models\AlikeSpuModel;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\BeforeImport;

class UpdateSpuClassImport implements ToArray, WithEvents, WithChunkReading
{
    use RegistersEventListeners;


    public function __construct()
    {
        $options = LIBXML_COMPACT | LIBXML_PARSEHUGE;
        \PhpOffice\PhpSpreadsheet\Settings::setLibXmlLoaderOptions($options);
    }

    public static function beforeImport(BeforeImport $event)
    {
        $options = LIBXML_COMPACT | LIBXML_PARSEHUGE;
        \PhpOffice\PhpSpreadsheet\Settings::setLibXmlLoaderOptions($options);
    }


    public function registerEvents(): array
    {
        return [
            // Handle by a closure.
            BeforeImport::class => function(BeforeImport $event) {
                $options = LIBXML_COMPACT | LIBXML_PARSEHUGE;
                \PhpOffice\PhpSpreadsheet\Settings::setLibXmlLoaderOptions($options);
            },
        ];
    }


    public function chunkSize(): int
    {
        return 10000;
    }

    public function array($row)
    {
        return [
            $row[0],
            $row[1],
            $row[2],
            $row[3],
            $row[4],
            $row[5],
            $row[6],
            $row[7],
            $row[8],
            $row[9],
            $row[10],
        ];
    }
}
