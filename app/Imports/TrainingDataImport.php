<?php

namespace App\Imports;

use App\Http\Models\TrainingDataModel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Concerns\ToArray;

class TrainingDataImport implements ToArray
{
    protected $type;

    public function __construct($type)
    {
        $this->type = $type;
    }

    public function array($array)
    {
        $hasInserted = false;
        $insertedContents = [];

        DB::transaction(function () use ($array, &$hasInserted, &$insertedContents) {
            foreach ($array as $key => $row) {
                if ($key == 0) {
                    // 跳过表头
                    if ($row[0] != '内容') {
                        throw new \InvalidArgumentException('上传模板不正确,请重新下载对应的模板');
                    }
                    continue;
                }

                $content = trim($row[0] ?? '');
                if (empty($content)) {
                    continue;
                }

                // 检查是否已存在相同内容和类型的数据
                $exists = TrainingDataModel::where('content', $content)
                    ->where('type', $this->type)
                    ->exists();

                if (!$exists) {
                    // 检查内容是否在其他集合中已存在
                    $existsInOther = $this->checkContentExistsInOtherCollections($content);
                    if ($existsInOther) {
                        throw new \InvalidArgumentException("内容「{$content}」已在{$existsInOther}训练数据中存在，不能重复添加");
                    }

                    TrainingDataModel::insert([
                        'content' => $content,
                        'type' => $this->type,
                        'create_time' => time(),
                        'create_name' => request()->user->name ?? 'admin',
                        'update_time' => time(),
                        'update_name' => request()->user->name ?? 'admin',
                    ]);
                    $hasInserted = true;
                    $insertedContents[] = $content;
                }
            }
        });

        // 如果有数据插入，同步到MongoDB和设置需要重新训练状态
        if ($hasInserted) {
            $this->syncToMongo($insertedContents);
            $redis = Redis::connection('sku');
            $redis->set('need_retraining_bom_data_status', 1);
        }
    }

    /**
     * 同步导入的数据到MongoDB
     */
    private function syncToMongo($contents)
    {
        $collectionName = $this->getMongoCollectionName();
        $fieldName = $this->getMongoFieldName();

        if (!$collectionName || !$fieldName) {
            return;
        }

        $mongo = DB::connection('mongodb');

        foreach ($contents as $content) {
            // 检查MongoDB中是否已存在
            $exists = $mongo->table($collectionName)
                ->where($fieldName, $content)
                ->exists();

            if (!$exists) {
                $mongo->table($collectionName)->insert([
                    $fieldName => $content,
                    'is_manual' => 1
                ]);
            }
        }
    }

    /**
     * 获取MongoDB集合名称
     */
    private function getMongoCollectionName()
    {
        switch ($this->type) {
            case 1: // 品牌
                return 'bom_brand_train_data';
            case 2: // 型号
                return 'bom_gn_train_data';
            case 3: // 分类
                return 'bom_cate_train_data';
            default:
                return null;
        }
    }

    /**
     * 获取MongoDB字段名称
     */
    private function getMongoFieldName()
    {
        switch ($this->type) {
            case 1: // 品牌
                return 'brand';
            case 2: // 型号
                return 'gn';
            case 3: // 分类
                return 'cate';
            default:
                return null;
        }
    }

    /**
     * 获取所有需要检查的MongoDB集合配置
     */
    private function getAllMongoCollections()
    {
        return [
            ['collection' => 'bom_brand_train_data', 'field' => 'brand'],
            ['collection' => 'bom_gn_train_data', 'field' => 'gn'],
            ['collection' => 'bom_cate_train_data', 'field' => 'cate'],
            ['collection' => 'bom_params_train_data', 'field' => 'param'],
        ];
    }

    /**
     * 获取除当前类型外的其他MongoDB集合配置
     */
    private function getOtherMongoCollections()
    {
        $allCollections = $this->getAllMongoCollections();
        $currentCollection = $this->getMongoCollectionName();

        return array_filter($allCollections, function($config) use ($currentCollection) {
            return $config['collection'] !== $currentCollection;
        });
    }

    /**
     * 检查内容是否在其他集合中已存在
     */
    private function checkContentExistsInOtherCollections($content)
    {
        $mongo = DB::connection('mongodb');
        $otherCollections = $this->getOtherMongoCollections();

        foreach ($otherCollections as $config) {
            $exists = $mongo->table($config['collection'])
                ->where($config['field'], $content)
                ->exists();

            if ($exists) {
                // 返回存在的集合信息
                $collectionNames = [
                    'bom_brand_train_data' => '品牌',
                    'bom_gn_train_data' => '型号',
                    'bom_cate_train_data' => '分类',
                    'bom_params_train_data' => '参数',
                ];
                return $collectionNames[$config['collection']] ?? '其他';
            }
        }

        return false;
    }
}
