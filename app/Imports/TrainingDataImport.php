<?php

namespace App\Imports;

use App\Http\Models\TrainingDataModel;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ToArray;

class TrainingDataImport implements ToArray
{
    protected $type;

    public function __construct($type)
    {
        $this->type = $type;
    }

    public function array($array)
    {
        DB::transaction(function () use ($array) {
            foreach ($array as $key => $row) {
                if ($key == 0) {
                    // 跳过表头
                    if ($row[0] != '内容') {
                        throw new \InvalidArgumentException('上传模板不正确,请重新下载对应的模板');
                    }
                    continue;
                }

                $content = trim($row[0] ?? '');
                if (empty($content)) {
                    continue;
                }

                // 检查是否已存在相同内容和类型的数据
                $exists = TrainingDataModel::where('content', $content)
                    ->where('type', $this->type)
                    ->exists();

                if (!$exists) {
                    TrainingDataModel::insert([
                        'content' => $content,
                        'type' => $this->type,
                        'create_time' => time(),
                        'create_name' => request()->user->name ?? 'admin',
                        'update_time' => time(),
                        'update_name' => request()->user->name ?? 'admin',
                    ]);
                }
            }
        });
    }
}
