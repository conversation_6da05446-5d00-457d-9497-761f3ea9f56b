{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.2.5|^8.0", "ext-http": "*", "ext-json": "*", "ext-libxml": "*", "ext-soap": "*", "artisaninweb/laravel-soap": "********", "barryvdh/laravel-dompdf": "^0.9.0", "dcat/laravel-wherehasin": "^0.8.0", "douyasi/laravel-wang-editor": "^2.4", "fideloper/proxy": "^4.4", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "^6.3.1|^7.0.1", "hprose/hprose": "^2.0", "jenssegers/mongodb": "3.7.3", "laravel/framework": "^7.29", "laravel/tinker": "^2.5", "maatwebsite/excel": "^3.1", "overtrue/laravel-lang": "~5.0", "php-amqplib/php-amqplib": "2.12.3", "predis/predis": "^1.1", "tucker-eric/eloquentfilter": "^3.0"}, "require-dev": {"facade/ignition": "^2.0", "fakerphp/faker": "^1.9.1", "itsgoingd/clockwork": "^5.1", "mockery/mockery": "^1.3.1", "nunomaduro/collision": "^4.3", "phpunit/phpunit": "^8.5.8|^9.3.3"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "platform-check": false}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"], "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}