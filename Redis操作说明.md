# 训练数据Redis操作说明

## 概述

在训练数据管理模块中，所有涉及数据改动的操作（增删改查中的增删改）都会自动设置Redis中的 `need_retraining_bom_data_status` 键为 1，用于标识需要重新训练BOM数据。

## 触发Redis操作的场景

### 1. 新增训练数据
- **触发位置**: `TrainingDataService::saveTrainingData()` 方法
- **操作**: 当新增数据成功时，设置 `need_retraining_bom_data_status = 1`

### 2. 修改训练数据
- **触发位置**: `TrainingDataService::saveTrainingData()` 方法
- **操作**: 当更新数据成功时，设置 `need_retraining_bom_data_status = 1`

### 3. 删除训练数据
- **触发位置**: `TrainingDataService::deleteTrainingData()` 方法
- **操作**: 当删除数据成功时，设置 `need_retraining_bom_data_status = 1`

### 4. 批量导入训练数据
- **触发位置**: 
  - `TrainingDataService::importTrainingData()` 方法（导入成功后）
  - `TrainingDataImport::array()` 方法（实际插入数据后）
- **操作**: 当有新数据插入时，设置 `need_retraining_bom_data_status = 1`

## 技术实现

### Redis连接配置
使用项目中已有的Redis连接配置：
```php
$redis = Redis::connection('sku');
```

### 设置操作
```php
$redis->set('need_retraining_bom_data_status', 1);
```

### 实现位置

#### 1. TrainingDataService.php
```php
/**
 * 设置需要重新训练BOM数据状态
 */
private function setNeedRetrainingStatus()
{
    $redis = Redis::connection('sku');
    $redis->set('need_retraining_bom_data_status', 1);
}
```

#### 2. 在数据操作方法中调用
- `saveTrainingData()` - 新增/修改后调用
- `deleteTrainingData()` - 删除后调用
- `importTrainingData()` - 导入成功后调用

#### 3. TrainingDataImport.php
在批量导入的实际数据插入处也添加了Redis操作，确保数据变动时能及时设置状态。

## 适用页面

此Redis操作适用于所有三个训练数据管理页面：
- 品牌训练数据管理页面
- 型号训练数据管理页面  
- 分类训练数据管理页面

## 注意事项

1. **只在数据实际变动时触发**: 只有当数据库操作成功时才会设置Redis状态
2. **导入去重处理**: 批量导入时，只有在实际插入新数据时才会设置Redis状态
3. **事务安全**: 导入操作在数据库事务中进行，Redis操作在事务成功后执行
4. **连接复用**: 使用项目统一的Redis连接配置，保持一致性

## 验证方法

可以通过以下方式验证Redis操作是否正常：

1. **查看Redis状态**:
```bash
redis-cli -h [host] -p [port] -a [password]
GET need_retraining_bom_data_status
```

2. **测试数据操作**:
- 新增一条训练数据
- 修改一条训练数据  
- 删除一条训练数据
- 批量导入数据

每次操作后检查Redis中的 `need_retraining_bom_data_status` 值是否为 1。
