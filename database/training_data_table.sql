-- 训练数据表创建脚本
CREATE TABLE lie_training_data
(
    id          int(10) auto_increment comment '主键'
        primary key,
    content     text                   null comment '内容',
    type        tinyint(2)  default 0  not null comment '类型,1是品牌,2是型号,3是分类',
    create_time int(10)     default 0  not null comment '创建时间',
    create_name varchar(10) default '' not null comment '创建人',
    update_time int(10)     default 0  not null comment '创建时间',
    update_name varchar(10) default '' not null comment '更新人'
)
    comment '训练数据';

-- 创建索引
CREATE INDEX idx_type ON lie_training_data(type);
CREATE INDEX idx_create_time ON lie_training_data(create_time);
CREATE INDEX idx_content ON lie_training_data(content(100));
