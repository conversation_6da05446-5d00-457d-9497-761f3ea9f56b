@include('css')
<style>
    .layui-table-cell {
        height: auto;
        overflow: visible;
        text-overflow: inherit;
        white-space: normal;
    }
</style>

<div class="layui-card">
    <div class="layui-card-body" style="padding-top: 20px">
        <div class="layui-collapse" style="margin-bottom: 10px;padding-top: 25px">

            <form class="layui-form" action="">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">替代型号</label>
                        <div class="layui-input-inline">
                            <input type="text" name="alike_spu_name" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">替代品牌</label>
                        <div class="layui-input-inline">
                            <input type="text" name="alike_brand_name" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">被替代型号</label>
                        <div class="layui-input-inline">
                            <input type="text" name="spu_name" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">被替代品牌</label>
                        <div class="layui-input-inline">
                            <input type="text" name="brand_name" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('pin_to_pin','pin to pin',request()->get('pin_to_pin'), config('field.PinToPinMap')) !!}
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">参数匹配度</label>
                        <div class="layui-input-inline">
                            <div class="layui-row">
                                 <div class="layui-col-xs6" style="width: 94px">
                                    <select name="match_rate_compare_type">
                                        <option value="">请选择</option>
                                        <option value="eq"> 等于 </option>
                                        <option value="neq"> 不等于 </option>
                                        <option value="lt"> 小于 </option>
                                        <option value="lte"> 小于等于 </option>
                                        <option value="gt"> 大于 </option>
                                        <option value="gte"> 大于等于 </option>
                                    </select>
                                </div>
                                <div class="layui-col-xs6" style="width: 64px;">
                                    <input type="text" value="" name="match_rate" placeholder="百分比" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('admin_id','创建人',request()->get('admin_id'),$admin) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('timePresenter','App\Presenters\TimeIntervalPresenter')
                        {!! $timePresenter->render('add_time','创建时间') !!}
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-sm" lay-submit lay-filter="Reload" style="margin-left: 50px">
                            查询
                        </button>
                        <button type="button" class="layui-btn layui-btn-sm" id="reset">
                            重置
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="layui-inline" style="margin-left: 20px">
            <button class="layui-btn layui-btn-sm" id="export_alike_spu">导出</button>
            <button class="layui-btn layui-btn-sm" id="batch_delete_alike_spu">批量删除</button>
            <button class="layui-btn layui-btn-sm" id="upload_alike_spu">上传替代型号</button>
        </div>
        <table class="layui-table" id="list" lay-filter="Reload"></table>

    </div>
</div>


<script type="text/html" id="cz">
    <a class="layui-btn layui-btn-xs layui-btn-outline layui-btn-primary" lay-event="mapping"><strong>映射管理</strong></a>
</script>

<script type="text/html" id="spu_id">
    <a href="/manage/spulist?spu_id%2Fcondition=@{{ d.spu_id }}" target="_blank">@{{ d.spu_id }}</a>
</script>
<script type="text/html" id="alike_spu_id">
    <a href="/manage/spulist?spu_id%2Fcondition=@{{ d.alike_spu_id }}" target="_blank">@{{ d.alike_spu_id }}</a>
</script>

<div style="display: none" id="upload_div">
    <div class="layui-form" style="margin-top: 40px">
        <div class="layui-form-item" style="margin-left: 70px">
            <label class="layui-form-label">文件上传 : </label>
            <div class="layui-input-inline">
                <button type="button" class="layui-btn layui-btn-sm" id="upload_button">
                    <i class="layui-icon">&#xe67c;</i>上传映射型号文件
                </button>
                <div id="msgTip"></div>
            </div>
        </div>
        <div class="layui-form-item" style="margin-left: 70px">
            <label class="layui-form-label">模板文件 : </label>
            <div class="layui-input-inline">
                <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" id="download_template">
                    <i class="layui-icon">&#xe67c;</i>点击下载模板文件
                </button>
            </div>
        </div>
        <input type="hidden" id="hasFile" value="0">
        <div class="layui-form-item" style="margin-left: 65px">
            <label class="layui-form-label">选择类型</label>
            <div class="layui-input-block">
                <input type="radio" name="type" value="1" title="">
                <input type="radio" name="type" value="2" title="国外">
            </div>
        </div>
        <div class="layui-form-item" style="text-align: center;margin-top: 10px">
            <button class="layui-btn" lay-submit lay-filter="confirmUpload" id="confirmUpload">确定上传</button>
        </div>
    </div>
</div>

<script type="text/html" id="operation">
    <a ew-href="/web/alikeSpu/alikeSpuItems?main_id=@{{ d.id }}&spu_name=@{{ d.spu_name }}&alike_spu_name=@{{ d.alike_spu_name }}" ew-title="替代明细 : @{{ d.alike_spu_name }} - @{{ d.spu_name }} "
       class="layui-btn layui-btn-xs" value="@{{ d.id }}"><strong>替代明细</strong></a>
    <button class="layui-btn layui-btn-xs" value="@{{ d.id }}" lay-event="reload_data"><strong>重跑数据</strong></button>
    <button class="layui-btn layui-btn-xs layui-btn-danger delete_alike_spu" value="@{{ d.id }}" lay-event="delete">
        <strong>删除</strong></button>
</script>
@include('js')
<script type="text/javascript" src="/js/web/alikeSpu/alikeSpuList.js?v={{time()}}"></script>
