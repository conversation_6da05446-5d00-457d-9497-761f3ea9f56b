@include('css')
<style>
    .layui-table-cell {
        height: auto;
        overflow: visible;
        text-overflow: inherit;
        white-space: normal;
    }
</style>

<div class="layui-card">
    <div class="layui-card-body" style="padding-top: 20px">
        <b style="font-size: 20px;">替代型号</b>
        <hr>
        <div style="min-height: 350px">
            <form class="layui-form" action="" style="margin-bottom: -25px">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">SKUID</label>
                        <div class="layui-input-inline">
                            <input type="text" name="goods_id/condition" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">制造商</label>
                        <div class="layui-input-inline">
                            <input type="text" name="brand_name/condition" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('goods_status/condition','状态','',config('field.SkuStatus')) !!}
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">供应商编码</label>
                        <div class="layui-input-inline">
                            <input type="text" name="canal/condition" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-sm" lay-submit lay-filter="getAlikeSpuSkuList" style="margin-left: 50px">
                            查询
                        </button>
                        <button class="layui-btn layui-btn-sm" type="button" lay-submit lay-filter="restAlikeSpuSkuList">
                            重置
                        </button>
                    </div>
                </div>
            </form>
            <button class="layui-btn layui-btn-xs layui-btn-danger" style="margin-top: 10px" id="batchDeleteAlikeSpuSku">批量删除</button>
            <table class="layui-table" id="alikeSpuSkuList" lay-filter="alikeSpuSkuList"></table>
        </div>
        <div>
            <b style="font-size: 20px;">被替代型号</b>
            <hr>
            <form class="layui-form" action="" style="margin-bottom: -25px">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">SKUID</label>
                        <div class="layui-input-inline">
                            <input type="text" name="goods_id/condition" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">制造商</label>
                        <div class="layui-input-inline">
                            <input type="text" name="brand_name/condition" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('goods_status/condition','状态','',config('field.SkuStatus')) !!}
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">供应商编码</label>
                        <div class="layui-input-inline">
                            <input type="text" name="canal/condition" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-sm" lay-submit lay-filter="getSpuSkuList" style="margin-left: 50px">
                            查询
                        </button>
                        <button class="layui-btn layui-btn-sm" type="button" lay-submit lay-filter="restSpuSkuList">
                            重置
                        </button>
                    </div>
                </div>
            </form>
            <button class="layui-btn layui-btn-xs layui-btn-danger" style="margin-top: 10px" id="batchDeleteSpuSku">批量删除</button>
            <table class="layui-table" id="spuSkuList" lay-filter="spuSkuList"></table>
        </div>
    </div>
</div>
<input type="hidden" id="mainId" value="{{request()->get('main_id',0)}}">
<input type="hidden" id="spu_name" value="{{$alikeSpu['spu_name']}}">
<input type="hidden" id="standard_brand_id" value="{{$alikeSpuCenter['standard_brand_id'] ?? 0}}">
<input type="hidden" id="alike_spu_name" value="{{$alikeSpu['alike_spu_name']}}">
<input type="hidden" id="alike_standard_brand_id" value="{{$alikeStandardBrand['standard_brand_id'] ?? 0}}">

<script type="text/html" id="cz">
    <a class="btn btn-xs btn-outline btn-primary" lay-event="mapping"><strong>映射管理</strong></a>
</script>

<script type="text/html" id="spuSkuListOperation">
   <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</button>
</script>
<script type="text/html" id="alikeSpuSkuListOperation">
    <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</button>
</script>
<script type="text/html" id="sku_id">
    <a style="color: blue" ew-href="/web/sku/skuList?search=1&goods_id/condition=@{{ d.goods_id }}" ew-title="商品列表">@{{ d.goods_id }}</a>
</script>

@include('js')
<script type="text/javascript" src="/js/web/alikeSpu/alikeSpuItems.js?v={{time()}}"></script>

