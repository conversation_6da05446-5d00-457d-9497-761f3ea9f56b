@include('css')
<style>
    .layui-table-cell {
        height: auto;
        overflow: visible;
        text-overflow: inherit;
        white-space: normal;
    }

    .layui-input {
        height: 30px;
        margin-top: 2px;
    }

    .layui-form-label {
        margin-left: -25px;
    }
</style>
<div class="lay-box">
    <div class="layui-card" style="font-size: 12px;">
        <div class="layui-card-body" style="padding-top: 15px">
            <form class="layui-form" action="" lay-filter="standard_brand_filter_form">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">品牌名称</label>
                        <div class="layui-input-inline">
                            <input type="text" name="brand_name" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">精确品牌名称</label>
                        <div class="layui-input-inline">
                            <input type="text" name="brand_name_raw" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">标准品牌ID</label>
                        <div class="layui-input-inline">
                            <input type="text" name="standard_brand_id" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">英文全称</label>
                        <div class="layui-input-inline">
                            <input type="text" name="brand_name_en" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">中文全称</label>
                        <div class="layui-input-inline">
                            <input type="text" name="brand_name_cn" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('status', '状态', request()->get('status'), config('field.StandardBrandStatus')) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                    </div>
                    <div class="layui-inline">
                        {!! $statusPresenter->render('brand_area', '品牌地区', request()->get('brand_area'), config('field.StandardBrandArea')) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('has_picture', '是否有图片', request()->get('has_picture'), [1=>'有',-1=>'没有']) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('is_show', '网站展示', request()->get('is_show'), [1=>'是',-1=>'否']) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('first_letter', '品牌首字母:', !empty($brand) ? $brand['first_letter'] : '', config('field.Letters')) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('has_brand_brief', '官网链接', request()->get('has_brand_brief'), [1=>'有',-1=>'无']) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('has_brand_desc', '品牌描述', request()->get('has_brand_desc'), [1=>'有',-1=>'无']) !!}
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-sm" lay-submit lay-filter="list" style="margin-left: 20px">
                            查询
                        </button>
                        <button class="layui-btn layui-btn-primary layui-btn-sm reset-button" lay-submit
                                lay-filter="list" style="margin-left: 18px">
                            重置
                        </button>
                    </div>
                </div>
            </form>
            <div class="layui-inline" style="margin-left: 1px">
                <button type="button" class="layui-btn layui-btn-sm"
                        id="save_standard_brand">新增标准品牌
                </button>
                {{--            <button class="layui-btn layui-btn-sm" id="add_standard_brand_mapping">新增标准映射</button>--}}
{{--                <a href="/api/standardBrand/exportStandardBrand" target="_blank"--}}
{{--                   class="layui-btn layui-btn-sm layui-btn-primary" id="export_standard_brand_mapping">导出标准品牌</a>--}}
                <button type="button" class="layui-btn layui-btn-sm"
                        id="export_standard_brand">导出标准品牌
                </button>
            </div>
            <table class="layui-table" id="list" lay-filter="list"></table>

        </div>
    </div>


    <script type="text/html" id="cz">
        <a class="btn btn-xs btn-outline btn-primary" lay-event="mapping"><strong>映射管理</strong></a>
    </script>

    <script type="text/html" id="spu_id">
        <a href="/manage/spulist?spu_id%2Fcondition=@{{ d . spu_id }}" target="_blank">@{{ d . spu_id }}</a>
    </script>
    <script type="text/html" id="alike_spu_id">
        <a href="/manage/spulist?spu_id%2Fcondition=@{{ d . alike_spu_id }}" target="_blank">@{{ d . alike_spu_id }}</a>
    </script>

    <div style="display: none" id="upload_div">
        <div class="layui-form" style="margin-top: 40px">
            <div class="layui-form-item" style="margin-left: 70px">
                <label class="layui-form-label">文件上传 : </label>
                <div class="layui-input-inline">
                    <button type="button" class="layui-btn" id="upload_button">
                        <i class="layui-icon">&#xe67c;</i>上传映射型号文件
                    </button>
                    <div id="msgTip"></div>
                </div>
            </div>
            <div class="layui-form-item" style="margin-left: 70px">
                <label class="layui-form-label">模板文件 : </label>
                <div class="layui-input-inline">
                    <button type="button" class="layui-btn layui-btn-primary" id="download_template">
                        <i class="layui-icon">&#xe67c;</i>点击下载模板文件
                    </button>
                </div>
            </div>
            <input type="hidden" id="hasFile" value="0">
            <div class="layui-form-item" style="margin-left: 65px">
                <label class="layui-form-label">选择类型</label>
                <div class="layui-input-block">
                    <input type="radio" name="type" value="1" title="国产">
                    <input type="radio" name="type" value="2" title="国外">
                </div>
            </div>
            <div class="layui-form-item" style="text-align: center">
                <button class="layui-btn" lay-submit lay-filter="confirmUpload" id="confirmUpload">确定上传</button>
            </div>
        </div>
    </div>
</div>
<script type="text/html" id="operation">
    <button type="button" class="layui-btn layui-btn-xs layui-btn save_standard_brand"
            value="@{{ d.standard_brand_id }}"
            lay-event="save_standard_brand"><strong>编辑</strong></button>
    @{{# if(d.status != 3){ }}
    <button class="layui-btn layui-btn-xs layui-btn manage_standard_brand_mapping" value="@{{ d.standard_brand_id }}"
            lay-event="manage_standard_brand_mapping"><strong>管理品牌映射</strong></button>
    @{{# } }}

    <button class="layui-btn layui-btn-xs layui-btn shop_brand_mapping" value="@{{ d.standard_brand_id }}"
            lay-event="shop_brand_mapping"><strong>外部品牌映射</strong></button>

    @{{# if(d.status == 3){ }}
    <button type="button" class="layui-btn layui-btn-xs layui-btn-warning enable_standard_brand"
            value="@{{ d.standard_brand_id }}"
            lay-event="disable_standard_brand"><strong>启用</strong></button>
    @{{# }else{ }}
    <button type="button" class="layui-btn layui-btn-xs layui-btn-danger disable_standard_brand"
            value="@{{ d.standard_brand_id }}"
            lay-event="disable_standard_brand"><strong>禁用</strong></button>
    @{{# } }}
</script>
@include('js')
<script type="text/javascript" src="/js/web/standardBrand/standardBrandList.js?v={{time()}}"></script>
