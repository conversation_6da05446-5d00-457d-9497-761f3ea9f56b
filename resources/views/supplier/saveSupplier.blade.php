@include('css')
@include('js')
<style>
    .red {
        color: red;
    }

    .layui-form-label {
        width: 120px;
    }

    .input-width {
        width: 500px !important;
    }

</style>
<div class="row">
    <div class="col-lg-12">
        <div class="ibox float-e-margins ibox-content" style="margin: 15px">
            <div class="row mapping-rows" style="overflow:hidden;margin: 10px">

                <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
                    <ul class="layui-tab-title">
                        <li @if(empty($_GET['tab'])) class="layui-this" @endif>基础信息</li>
                        <li @if(!empty($_GET['tab']) && $_GET['tab']=='seo' ) class="layui-this" @endif>SEO三要素</li>
                        <li @if(!empty($_GET['tab']) && $_GET['tab']=='rule' ) class="layui-this" @endif>供应商起订、运费、其他费用规则</li>
                    </ul>
                    <div class="layui-tab-content">
                        <div class="layui-tab-item @if(empty($_GET['tab'])) layui-show @endif" style="min-height: 500px">
                            <form class="layui-form">
                                <input type="hidden" name="supplier_id" value="{{$supplier['supplier_id'] ?? ''}}">

                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label">供应商名字
                                            <span class="red">*</span>
                                        </label>
                                        <div class="layui-input-inline">
                                            <input type="text" name="supplier_name" value="{{$supplier['supplier_name'] ?? ''}}" required lay-verify="required" @if(request()->user->userId!=1000) disabled @endif placeholder="请输入供应商名字"
                                            autocomplete="off" class="layui-input layui-disabled">
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        @inject('multiSelectorPresenter','App\Presenters\MultiSelectorPresenter')
                                        {!! $multiSelectorPresenter->render('sku_optional_batch', 'SKU可选批次', $supplier['sku_optional_batch'] ?? '', $skuOptionalBatchForXmSelect,['width'=>'300px']) !!}
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label">供应商类别
                                            <span class="red">*</span>
                                        </label>
                                        <div class="layui-input-inline">
                                            <select name="type_id" lay-verify="required" lay-search @if(!empty($supplier['goods_id'])) disabled @endif>
                                                @if(!empty($type_id))
                                                @foreach($type_id as $k=>$v)
                                                <option value="{{$k}}" @if(!empty($supplier['type_id']) && $supplier['type_id']==$k) selected @endif>{{$v}}</option>
                                                @endforeach
                                                @endif
                                            </select>
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                                        {!! $statusPresenter->render('channel_type', '渠道类型', $supplier['channel_type'] ?? 0, [1=>'代购',0=>'非代购']) !!}
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label">供应商状态
                                            <span class="red">*</span>
                                        </label>
                                        <div class="layui-input-inline">
                                            <select name="status" lay-verify="required" lay-search>
                                                @if(!empty($status))
                                                @foreach($status as $k=>$v)
                                                <option value="{{$k}}" @if(!empty($supplier['status']) && $supplier['status']==$k) selected @endif>{{$v}}</option>
                                                @endforeach
                                                @endif
                                            </select>
                                        </div>
                                    </div>
                                </div>


                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        {!! $statusPresenter->render('purchase_uid', '采购员', $supplier['purchase_uid'] ?? '', $purchaseUsers) !!}
                                    </div>
                                    <div class="layui-inline">
                                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                                        {!! $statusPresenter->render('supplier_channel_code', '供应商编码', $supplier['supplier_channel_code'] ?? '', $supplierChannelCodes) !!}
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label">大陆货期
                                            <span class="red">*</span>
                                        </label>
                                        <div class="layui-input-inline">
                                            <input type="text" name="cn_delivery" value="{{$supplier['cn_delivery'] ?? ''}}" required placeholder="请输入大陆货期" autocomplete="off" class="layui-input">
                                        </div>
                                    </div>

                                    <div class="layui-inline">
                                        <label class="layui-form-label">香港货期
                                            <span class="red">*</span>
                                        </label>
                                        <div class="layui-input-inline">
                                            <input type="text" name="hk_delivery" value="{{$supplier['hk_delivery'] ?? ''}}" required placeholder="请输入香港货期" autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label">排序</label>
                                        <div class="layui-input-inline ">
                                            <input type="text" name="sort" value="{{$supplier['sort'] ?? ''}}" placeholder="请输入描述文案" autocomplete="off" class="layui-input">
                                        </div>
                                    </div>

                                    <div class="layui-inline">
                                        <label class="layui-form-label">供应商昵称</label>
                                        <div class="layui-input-inline">
                                            <input type="text" name="supplier_nickname" value="{{$supplier['supplier_nickname'] ?? ''}}" placeholder="请输入供应商昵称" autocomplete="off" class="layui-input">
                                        </div>
                                        <div class="layui-form-mid layui-word-aux">仅开发填写</div>
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <label class="layui-form-label">运营宣传文案</label>
                                    <div class="layui-input-inline input-width">
                                        <input type="text" name="ad_text" value="{{$supplier['ad_text'] ?? ''}}" required placeholder="请输入宣传文案" autocomplete="off" class="layui-input">
                                    </div>
                                    <div class="layui-form-mid layui-word-aux">50字以内</div>
                                </div>

                                <div class="layui-form-item">
                                    <label class="layui-form-label">文案跳转</label>
                                    <div class="layui-input-inline input-width">
                                        <input type="text" name="ad_url" value="{{$supplier['ad_url'] ?? ''}}" required placeholder="请输入文案跳转" autocomplete="off" class="layui-input">
                                    </div>
                                </div>

                                <div class="layui-form-item layui-form-text">
                                    <label class="layui-form-label">描述文案
                                        <span class="red">*</span>
                                    </label>
                                    <div class="layui-input-inline input-width">
                                        <textarea name="describe" lay-verify="required|describe" placeholder="请输入描述文案" class="layui-textarea">{{$supplier['describe'] ?? ''}}</textarea>
                                    </div>
                                    <div class="layui-form-mid layui-word-aux">500字以内</div>
                                </div>
                                <div class="layui-form-item layui-form-text">
                                    <label class="layui-form-label">费用说明
                                    </label>
                                    <div class="layui-input-inline input-width">
                                        <textarea name="fee_des" placeholder="请输入费用说明" class="layui-textarea">{{$supplier['fee_des'] ?? ''}}</textarea>
                                    </div>
                                    <div class="layui-form-mid layui-word-aux">255字以内</div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">供应商图片</label>
                                    <div class="layui-upload-drag upload-img" preview="supplier_logo_preview" data-obj="supplier_logo" style="width:200px;height: 100px;padding: 0px">
                                        @if(!empty($supplier['supplier_logo']))
                                        <img src="{{$supplier['supplier_logo'] ?? ''}}" id="supplier_logo_preview" width=200" height="100">
                                        @else
                                        <div style="padding: 30px">
                                            <i class="layui-icon"></i>
                                            <p>点击上传，或将文件拖拽到此处</p>
                                        </div>
                                        @endif
                                    </div>
                                    <div class="layui-inline" style="width:50%">
                                        <div class="layui-form-mid layui-word-aux">
                                            支持扩展名png或者jpg，不超过2M,宽高：200*100
                                        </div>
                                        <br>
                                        <div class="layui-form-mid layui-word-aux" style="width:100%">
                                            <input type="text" id="supplier_logo" name="supplier_logo" class="layui-input" value="{{$supplier['supplier_logo'] ?? ''}}">
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-form-item" style="margin-top: 30px">
                                    <div class="layui-col-md3">.</div>
                                    <div class="layui-col-md6">
                                        <div class="layui-input-block">
                                            <button class="layui-btn" lay-submit lay-filter="*">立即提交</button>
                                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                        </div>
                                    </div>
                                    <div class="layui-col-md3"></div>

                                </div>
                            </form>
                        </div>

                        <div class="layui-tab-item @if(!empty($_GET['tab']) && $_GET['tab']=='seo') layui-show @endif" style="min-height: 500px">
                            <form class="layui-form">
                                <input type="hidden" name="key_id" value="{{$seo['key_id'] ?? ''}}">
                                <input type="hidden" name="type" value="{{$seo['type'] ?? ''}}">

                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label">{{$seo['field'] ?? ''}}
                                            <span class="red">*</span>
                                        </label>
                                        <div class="layui-input-inline">
                                            <input type="text" value="{{$seo['value'] ?? ''}}" disabled autocomplete="off" class="layui-input layui-disabled">
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <label class="layui-form-label">title
                                        <span class="red">*</span>
                                    </label>
                                    <div class="layui-input-inline input-width">
                                        <input type="text" name="title" value="{{$seo['title'] ?? ''}}" required lay-verify="required" placeholder="请输入title" autocomplete="off" class="layui-input">
                                    </div>
                                </div>

                                <div class="layui-form-item layui-form-text">
                                    <label class="layui-form-label">keywords
                                        <span class="red">*</span>
                                    </label>
                                    <div class="layui-input-inline input-width">
                                        <textarea name="keywords" lay-verify="required" placeholder="请输入keywords" class="layui-textarea">{{$seo['keywords'] ?? ''}}</textarea>
                                    </div>
                                </div>

                                <div class="layui-form-item layui-form-text">
                                    <label class="layui-form-label">description
                                        <span class="red">*</span>
                                    </label>
                                    <div class="layui-input-inline input-width">
                                        <textarea name="description" lay-verify="required" placeholder="请输入description" class="layui-textarea">{{$seo['description'] ?? ''}}</textarea>
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <div class="layui-input-block">
                                        <button class="layui-btn" lay-submit lay-filter="seo">立即提交</button>
                                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                    </div>
                                </div>

                            </form>
                        </div>
                        <div class="layui-tab-item @if(!empty($_GET['rule']) && $_GET['tab']=='rule') layui-show @endif" style="min-height: 500px">
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>规则名称</th>
                                        <th>规则详情</th>
                                    </tr>
                                    </tr>
                                <tbody>
                                    <tr>
                                        <td>起订规则</td>
                                        <td>{!! $rule['minOrderRule'] ?? '无' !!}</td>
                                    </tr>
                                    <tr>
                                        <td>运费规则</td>
                                        <td>{!! $rule['shippingRule'] ?? '无' !!}</td>
                                    </tr>
                                    <tr>
                                        <td>其他费用规则</td>
                                        <td>
                                            {!! $rule['otherFeeRule']['rolling_rule'] ?? '无' !!}
                                            <br>
                                            {!! $rule['otherFeeRule']['operation_rule'] ?? '无' !!}
                                        </td>
                                    </tr>
                                </tbody>
                                </thead>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{Autograph()}}
<script type="text/javascript" src="/js/web/supplier/saveSupplier.js?v={{time()}}"></script>
