@include('css')

<style>
    label {
        box-sizing: content-box;
    }
</style>
<div class="wrapper wrapper-content">
    <div class="lay-box">

        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-content">
                        <div>
                            <div class="layui-form">
                                <div class="layui-form-item" style="margin-top: 25px;">
                                    <div class="layui-inline">
                                        <label class="layui-form-label">供应商ID</label>
                                        <div class="layui-input-inline">
                                            <input type="text" name="supplier_id" placeholder="请输入供应商ID"
                                                   autocomplete="off" class="layui-input">
                                        </div>
                                    </div>

                                    <div class="layui-inline">
                                        <label class="layui-form-label">供应商名字</label>
                                        <div class="layui-input-inline">
                                            <input type="text" name="supplier_name" placeholder="请输入供应商名字"
                                                   autocomplete="off" class="layui-input">
                                        </div>
                                    </div>

                                    <div class="layui-inline">
                                        <label class="layui-form-label">供应商类型</label>
                                        <div class="layui-input-inline">
                                            <select name="type_id">
                                                <option value="">全部</option>
                                                <option value="1">联营</option>
                                                <option value="2">专卖</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="layui-inline">
                                        <div class="layui-inline">
                                            <label class="layui-form-label">状态</label>
                                            <div class="layui-input-inline">
                                                <select name="status">
                                                    <option value="">全部</option>
                                                    <option value="0">待审核</option>
                                                    <option value="1">启用</option>
                                                    <option value="2">禁用</option>
                                                    <option value="3">删除</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="layui-inline">
                                        <button class="layui-btn layui-btn-sm load" lay-submit lay-filter="load">搜索</button>
                                    </div>
                                </div>
                            </div>
                            <table class="layui-table" id="list" lay-filter="list"></table>

                        </div>
                    </div>
            </div>
        </div>
    </div>
</div>
<script type="text/html" id="operation">
    <a href="/manage/SaveGoods?goods_id=@{{ d.goods_id }}" target="_blank"
       class="layui-btn layui-btn-xs layui-btn-outline "><strong>编辑</strong></a>
</script>
<script type="text/html" id="goods_images_preview">
    @{{# if(d.goods_images){ }}
    <button class="layui-btn layui-btn-xs layui-btn-outline  goods_images_preview" data-url="@{{ d.goods_images }}">
        <strong>预览</strong></button>
    @{{# } }}
</script>
<script type="text/html" id="add_time">
    @{{ date('Y-m-d H:i',d.add_time) }}
</script>
<script type="text/html" id="update_time">
    @{{ date('Y-m-d H:i',d.update_time) }}
</script>
<script type="text/html" id="type">
    <a class="layui-btn layui-btn-xs layui-btn-outline
    @{{# if(d.is_pre_sale == 1){ }}
            layui-btn-warning
    @{{# }else if(d.is_pre_sale == 0 && d.lock_type==0){  }}

    @{{# }else if(d.is_pre_sale == 0 && d.lock_type==1){  }}
            layui-btn-success
    @{{# }else{ }}
            layui-btn-danger
    @{{# } }}
            " style="pointer-events: none;">
        <strong>
            @{{# if(d.is_pre_sale == 1){ }}
            预售
            @{{# }else if(d.is_pre_sale == 0 && d.lock_type==0){ }}
            现货
            @{{# }else if(d.is_pre_sale == 0 && d.lock_type==1){ }}
            退供应商
            @{{# }else{ }}
            未知
            @{{# } }}
        </strong>
    </a>
</script>
<script type="text/html" id="type_id">
    <a class="layui-btn layui-btn-xs layui-btn-outline @{{# if(d.type_id == 1){ }}  @{{# }else if(d.type_id==2){  }} layui-btn-success @{{# }else{  }} layui-btn-danger  @{{# } }} "
       style="pointer-events: none;">
        <strong>
            @{{# if(d.type_id == 1){ }} 联营 @{{# }else if(d.type_id==2){ }} 专卖 @{{# }else{ }} 其他 @{{# } }}
        </strong>
    </a>

</script>
<script type="text/html" id="status">
    <a class="layui-btn layui-btn-xs layui-btn-outline @{{# if(d.status == 0){ }} layui-btn-danger @{{# }else if(d.status == 1){ }}  @{{# }else if(d.status==2){  }} layui-btn-danger @{{# }else if(d.status==3){ }} layui-btn-danger @{{# } }}"
       style="pointer-events: none;">
        <strong>
            @{{# if(d.status == 0){ }} 待审核 @{{# }else if(d.status == 1){ }} 启用 @{{# }else if(d.status==2){ }} 禁用 @{{#
            }else if(d.status==3){ }} 删除 @{{# } }}
        </strong>
    </a>
</script>
<script type="text/html" id="cz">
    <button type="button" value="@{{ d.supplier_id }}" class="layui-btn layui-btn-xs layui-btn-warning saveSupplier"><strong>编辑</strong></button>
    @{{# if(d.type_id==1 || d.type_id==2){ }}
    {{--        <a href="/footstone/modSupplierPrice?supplier_id=@{{ d.supplier_id }}" class="layui-btn layui-btn-xs layui-btn-outline layui-btn-danger"><strong>价格系数修改</strong></a>--}}
    <button class="layui-btn layui-btn-xs layui-btn-success saveSuppExtendFee" value="@{{ d.supplier_id }}"><strong>附加费设置</strong></button>
    @{{# } }}
</script>
@include('js')
<script type="text/javascript" src="/js/web/supplier/supplierList.js?v={{time()}}"></script>
