@include('css')
<style>
    .layui-table-cell {
        height: auto;
        overflow: visible;
        text-overflow: inherit;
        white-space: normal;
    }

    .layui-input {
        height: 30px;
        margin-top: 2px;
    }

    .layui-form-label {
        margin-left: -25px;
    }
</style>
<div class="lay-box">
    <div class="layui-card" style="font-size: 12px;">
        <div class="layui-card-body" style="padding-top: 15px">
            <form class="layui-form" action="" lay-filter="brand_filter_form">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">品牌名称</label>
                        <div class="layui-input-inline">
                            <input type="text" name="brand_name" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">精确品牌名称</label>
                        <div class="layui-input-inline">
                            <input type="text" name="brand_name_raw" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">品牌id</label>
                        <div class="layui-input-inline">
                            <input type="text" name="brand_id" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('is_unusual', '异常状态', request()->get('is_unusual'), [0=>'正常',1=>'异常']) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('insert_type', '创建来源', request()->get('insert_type'), config('field.BrandInsertType')) !!}
                    </div>
                    <div class="layui-inline">
                        {!! $statusPresenter->render('brand_area', '品牌地区', request()->get('brand_area'), config('field.BrandArea')) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('has_mapping', '是否有映射', request()->get('has_mapping'), [1=>'有',0=>'没有',-1=>'不处理']) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('has_picture', '是否有图片', request()->get('has_picture'), [1=>'有',0=>'没有']) !!}
                    </div>

                    <div class="layui-inline">
                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('has_order', '是否有订单', request()->get('has_order'), [1=>'有',-1=>'没有']) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('has_put_away_sku', '是否有上架商品', request()->get('has_put_away_sku'), [1=>'有',-1=>'没有']) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('ignore_handle', '是否忽略处理', request()->get('ignore_handle'), [1=>'是',-1=>'否']) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('sku_upload_need_deal', '上传待处理', request()->get('sku_upload_need_deal'), [1=>'是',0=>'否']) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('datePresenter','App\Presenters\DateIntervalPresenter')
                        {!! $datePresenter->render('create_time','创建时间') !!}
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-sm" lay-submit lay-filter="list" style="margin-left: 20px">
                            查询
                        </button>
                        <button class="layui-btn layui-btn-primary layui-btn-sm reset-button" lay-submit
                                lay-filter="list" style="margin-left: 18px">
                            重置
                        </button>
                    </div>
                </div>
            </form>
{{--            <div class="layui-inline" style="margin-left: 1px">--}}
{{--                <button type="button" class="layui-btn layui-btn-sm"--}}
{{--                        id="save_brand">新增品牌--}}
{{--                </button>--}}
{{--                <button type="button" class="layui-btn layui-btn-sm"--}}
{{--                        id="export_brand">导出--}}
{{--                </button>--}}
{{--                <button type="button" class="layui-btn layui-btn-sm"--}}
{{--                        id="batch_ignore_handle">不处理--}}
{{--                </button>--}}
{{--            </div>--}}
            <table class="layui-table" id="list" lay-filter="list"></table>

        </div>
    </div>


    <script type="text/html" id="cz">
        <a class="btn btn-xs btn-outline btn-primary" lay-event="mapping"><strong>映射管理</strong></a>
    </script>

    <script type="text/html" id="spu_id">
        <a href="/manage/spulist?spu_id%2Fcondition=@{{ d . spu_id }}" target="_blank">@{{ d . spu_id }}</a>
    </script>
    <script type="text/html" id="alike_spu_id">
        <a href="/manage/spulist?spu_id%2Fcondition=@{{ d . alike_spu_id }}" target="_blank">@{{ d . alike_spu_id }}</a>
    </script>

    <div style="display: none" id="upload_div">
        <div class="layui-form" style="margin-top: 40px">
            <div class="layui-form-item" style="margin-left: 70px">
                <label class="layui-form-label">文件上传 : </label>
                <div class="layui-input-inline">
                    <button type="button" class="layui-btn" id="upload_button">
                        <i class="layui-icon">&#xe67c;</i>上传映射型号文件
                    </button>
                    <div id="msgTip"></div>
                </div>
            </div>
            <div class="layui-form-item" style="margin-left: 70px">
                <label class="layui-form-label">模板文件 : </label>
                <div class="layui-input-inline">
                    <button type="button" class="layui-btn layui-btn-primary" id="download_template">
                        <i class="layui-icon">&#xe67c;</i>点击下载模板文件
                    </button>
                </div>
            </div>
            <input type="hidden" id="hasFile" value="0">
            <div class="layui-form-item" style="margin-left: 65px">
                <label class="layui-form-label">选择类型</label>
                <div class="layui-input-block">
                    <input type="radio" name="type" value="1" title="国产">
                    <input type="radio" name="type" value="2" title="国外">
                </div>
            </div>
            <div class="layui-form-item" style="text-align: center">
                <button class="layui-btn" lay-submit lay-filter="confirmUpload" id="confirmUpload">确定上传</button>
            </div>
        </div>
    </div>
</div>
<script type="text/html" id="toolbar">
    <button type="button" class="layui-btn layui-btn-sm"
            id="save_brand">新增品牌
    </button>
    <button type="button" class="layui-btn layui-btn-sm"
            id="export_brand">导出
    </button>
    <button type="button" class="layui-btn layui-btn-sm"
            id="batch_ignore_handle">不处理
    </button>
</script>
<script type="text/html" id="operation">
    @{{# if(d.ignore_handle == -1){ }}
    @if(checkPerm('api_standardBrand_saveStandardBrand'))
    <button type="button" class="layui-btn layui-btn-xs layui-btn save_brand"
            value="@{{ d.brand_id }}"
            lay-event="save_brand"><strong>编辑</strong></button>
    @endif
    @if(checkPerm('api_standardBrand_disableStandardBrand'))
    @{{# if(d.status == 1){ }}
    <button class="layui-btn layui-btn-danger layui-btn-xs change_brand_status"
            status="0" value="@{{ d.brand_id }}">隐藏
    </button>
    @{{# }else{ }}
    <button class="layui-btn layui-btn-info layui-btn-xs change_brand_status"
            status="1" value="@{{ d.brand_id }}">显示
    </button>
    @{{# } }}
    @endif
    @if(checkPerm('api_sku_getSkuList'))
    <a ew-href="/web/sku/skuList?brand_id/condition=@{{ d.brand_id }}&brand_name/condition=@{{ d.brand_name }}"
       class="layui-btn layui-btn layui-btn-xs">SKU</a>
    @endif
    @if(checkPerm('api_standardBrand_getMainSpuList'))
    <a ew-href="/web/spu/spuList?brand_id/condition=@{{ d.brand_id }}&brand_name/condition=@{{ d.brand_name }}"
       class="layui-btn layui-btn layui-btn-xs">SPU</a>
    @endif
    @if(checkPerm('api_standardBrandMapping_addStandardBrandMapping'))
    @{{# if(d.standard_brand_name == ''){ }}
    <button class="layui-btn layui-btn-info layui-btn-xs mapping_standard_brand"
            value="@{{ d.brand_id }}">标品映射
    </button>
    @{{# } }}
    @endif
    @{{# } }}
    @{{# if(d.ignore_handle == 1){ }}
    <button class="layui-btn layui-btn-danger layui-btn-xs recover_handle"
            status="0" value="@{{ d.brand_id }}">恢复处理
    </button>
    @{{# }else{ }}
    @{{# if(!d.standard_brand_name){ }}
    <button class="layui-btn layui-btn-info layui-btn-xs ignore_handle"
            status="1" value="@{{ d.brand_id }}">不处理
    </button>
    @{{# } }}
    @{{# } }}
</script>
@include('js')
<script type="text/javascript" src="/js/web/brand/brandList.js?v={{time()}}"></script>
