@include('css')
@include('js')
<div class="lay-box">
    <div class="layui-card">
        <div class="layui-card-body" style="padding-top: 20px">
            <form class="layui-form" action="" lay-filter="ability_level_rule_filter_form">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        @inject('canalPresenter', 'App\Presenters\MultiSelectorPresenter')
                        {!! $canalPresenter->render('supplier_id', '猎芯渠道', request()->get('supplier_id'), $supplierListForXmSelect , ['radio' => true,'width' => '200']) !!}
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">供应商编码</label>
                        <div class="layui-input-inline">
                            <input type="text" name="supplier_code" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter', 'App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('level', '履约程度', request()->get('level'), config('field.AbilityLevelRuleLevel')) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter', 'App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('status', '状态', request()->get('status'), [1 => '启用', -1 => '禁用']) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('timePresenter','App\Presenters\TimeIntervalPresenter')
                        {!! $timePresenter->render('create_time','创建时间') !!}
                    </div>
                </div>
                <div class="layui-form-item" style="text-align: center;">
                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-sm" lay-submit lay-filter="search" style="margin-left: 50px">
                            查询
                        </button>
                        <button type="button" class="layui-btn layui-btn-sm" lay-submit lay-filter="reset">
                            重置
                        </button>
                    </div>
                </div>
            </form>
            <div>
                <div class="layui-col-md8">
                    <button type="button" class="layui-btn layui-btn-sm" id="saveAbilityLevelRule">新增履约能力规则
                    </button>
                </div>
                <div class="layui-col-md4" style="text-align: right;">
                    <span style="color: orange;font-size: 12px;">
                    <p>未配置履约程度的供应商下的SKU默认为弱履约；</p>
                        <p>配置后将在次日凌晨刷新历史SKU数据的履约程度</p>
                    </span>
                </div>
            </div>
            <table class="layui-table" id="list" lay-filter="list"></table>
        </div>
    </div>
</div>

<script type="text/html" id="operation">
    <button class="layui-btn layui-btn-xs" value="@{{ d.id }}" lay-event="update"><strong>修改</strong></button>
    <button class="layui-btn layui-btn-xs" value="@{{ d.id }}" lay-event="operationLog"><strong>操作日志</strong></button>
    @if(request()->user->userId == 1000)
        <button class="layui-btn layui-btn-xs layui-btn-danger" value="@{{ d.id }}" lay-event="delete">
        <strong>删除</strong></button>
    @endif

</script>

<script type="text/html" id="statusTpl">
    <input type="checkbox" name="status" value="@{{d.id}}" lay-skin="switch" lay-text="启用|禁用" lay-filter="statusSwitch" @{{ d.status == 1 ? 'checked' : '' }}>

</script>

<script type="text/javascript" src="/js/web/abilityLevelRule/abilityLevelRuleList.js?v={{time()}}"></script>
