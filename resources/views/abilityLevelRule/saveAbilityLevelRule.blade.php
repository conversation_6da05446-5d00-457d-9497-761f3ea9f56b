@include('css')
@include('js')
<section class="layui-card" style="margin-top: 10px;padding-bottom: 30px">
    <form class="layui-form" action="" lay-filter="saveForm">
        <input type="hidden" name="id" value="{{ $rule['id'] ?? '' }}">
        <div class="layui-form-item" style="margin-top: 20px">
            <div class="layui-form-item">
                <div class="layui-inline">
                    @inject('statusPresenter', 'App\Presenters\StatusPresenter')
                    {!! $statusPresenter->render('supplier_id', '渠道 :', $rule['supplier_id'] ?? '', $supplierList,['required' => true,'disable' => !empty($rule['supplier_id'])]) !!}
                </div>
            </div>
            <div id="supplierCodeSelector" @if(($rule['supplier_id']??0) !=17) style="display: none;" @endif>
                <div class="layui-form-item">
                         <input type="hidden" id="canal_init_value"
                   value="{{!empty($canal_init_value) ? json_encode($canal_init_value): ''}}">
                      <label class="layui-form-label">
                    供应商编码</label>
                <div class="layui-input-inline">
                    <div id="canal_selector" class="layui-input-inline" value="" style="width: 300px;">
                    </div>
                    <input type="hidden" name="supplier_code" value="{{$rule['canals'] ?? ''}}" id="supplier_code">
                </div>
            </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        @inject('sourcePresenter', 'App\Presenters\MultiSelectorPresenter')
                        {!! $sourcePresenter->render('source', '来源', $rule['source'] ?? '', $sourceListForXmSelect, ['width' => '300']) !!}
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    @inject('statusPresenter', 'App\Presenters\StatusPresenter')
                    {!! $statusPresenter->render('level', '履约等级', $rule['level'] ?? '', config('field.AbilityLevelRuleLevel'),['required' => true]) !!}
                </div>
            </div>
        </div>
        <br>
        <br>
        <div class="layui-form-item" style="margin-left: 330px;padding-bottom: 10px;margin-top: 20px;text-align: left">
            <button type="button" class="layui-btn" lay-submit lay-filter="saveForm">保存</button>
            <button type="button" class="layui-btn layui-btn-primary" lay-submit lay-filter="closeForm">取消
            </button>
        </div>
        <br>
    </form>
</section>

<script type="text/javascript" src="/js/web/abilityLevelRule/saveAbilityLevelRule.js?v={{ time() }}"></script>
