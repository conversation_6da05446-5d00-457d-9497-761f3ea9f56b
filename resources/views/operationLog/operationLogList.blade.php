@include('css')
<style>
    .layui-table-cell {
        height: auto;
        overflow: visible;
        text-overflow: inherit;
        white-space: normal;
    }
</style>

<div class="layui-card">
    <div class="layui-card-body" style="padding-top: 20px">
        {{-- <div class="layui-collapse" style="margin-bottom: 10px;padding-top: 25px">
            <form class="layui-form" action="">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('admin_id','上传人','',$adminId) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('timePresenter','App\Presenters\TimeIntervalPresenter')
                        {!! $timePresenter->render('create_time','创建时间') !!}
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-sm" lay-submit lay-filter="Reload" style="margin-left: 50px">
                            查询
                        </button>
                        <button class="layui-btn layui-btn-sm"  type="button" id="reset">
                            重置
                        </button>
                    </div>

                </div>
            </form>
        </div> --}}
        <div class="layui-inline" style="margin-bottom: -15px">
        </div>
        <table class="layui-table" id="list" lay-filter="Reload"></table>

    </div>
</div>


{{-- <script type="text/html" id="operation">
    <a href="/alike_spu/alike_spu_items?main_id=@{{ d.id }}" target="_blank"
       class="layui-btn layui-btn-xs" value="@{{ d.id }}"><strong>下载结果</strong></a>
</script> --}}
@include('js')
<script type="text/javascript" src="/js/web/operationLog/operationLogList.js?v={{time()}}"></script>
