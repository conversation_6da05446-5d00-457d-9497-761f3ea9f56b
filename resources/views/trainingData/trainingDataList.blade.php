@include('css')
<style>
    .layui-table-cell {
        height: auto !important;
        white-space: normal;
    }
</style>
<div>
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>训练数据管理</h3>
        </div>
        <div class="layui-card-body">
            <form class="layui-form" lay-filter="training_data_filter_form" id="training_data_filter_form">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">内容:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="content" placeholder="请输入内容" autocomplete="off"
                                   class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">类型:</label>
                        <div class="layui-input-inline">
                            <select name="type">
                                <option value="">请选择类型</option>
                                <option value="1">品牌</option>
                                <option value="2">型号</option>
                                <option value="3">分类</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">创建人:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="create_name" placeholder="请输入创建人" autocomplete="off"
                                   class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" lay-submit lay-filter="list">搜索</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    </div>
                </div>
            </form>
            <div style="margin-bottom: 10px;">
                <button type="button" class="layui-btn layui-btn-sm" id="save_training_data">新增训练数据</button>
                <button type="button" class="layui-btn layui-btn-sm" id="export_training_data">导出训练数据</button>
                <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="import_brand_data">导入品牌数据</button>
                <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="import_model_data">导入型号数据</button>
                <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="import_category_data">导入分类数据</button>
                <a class="layui-btn layui-btn-sm layui-btn-warm" target="_blank" href="/template/training_data_brand_template.csv">下载品牌模板</a>
                <a class="layui-btn layui-btn-sm layui-btn-warm" target="_blank" href="/template/training_data_model_template.csv">下载型号模板</a>
                <a class="layui-btn layui-btn-sm layui-btn-warm" target="_blank" href="/template/training_data_category_template.csv">下载分类模板</a>
            </div>
            <table class="layui-table" id="list" lay-filter="list"></table>
        </div>
    </div>
</div>

<script type="text/html" id="operation">
    <button type="button" class="layui-btn layui-btn-xs edit_training_data" value="@{{ d.id }}"
            lay-event="edit_training_data"><strong>编辑</strong></button>
    <button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete_training_data"
            value="@{{ d.id }}"
            lay-event="delete_training_data"><strong>删除</strong></button>
</script>
@include('js')
<script type="text/javascript" src="/js/web/trainingData/trainingDataList.js?v={{time()}}"></script>
