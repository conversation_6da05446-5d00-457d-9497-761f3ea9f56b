@include('css')
<style>
    .layui-form-label {
        width: 120px;
    }
    .layui-input-block {
        margin-left: 150px;
    }
</style>
<div>
    <div class="layui-card">
        <form class="layui-form" action="">
            <div class="layui-tab layui-tab-brief" style="margin: 10px 40px 40px;">
                <ul class="layui-tab-title">
                    <li class="layui-this">训练数据信息</li>
                </ul>
                <div class="layui-tab-content">
                    <div class="layui-tab-item layui-show">
                        <div class="layui-card-body" style="padding-top: 10px">
                            <input type="hidden" name="id" value="{{ $trainingData['id'] ?? '' }}">
                            <div class="layui-form-item">
                                <label class="layui-form-label"><span style="color: red">* </span>内容:</label>
                                <div class="layui-input-block">
                                    <textarea name="content" placeholder="请输入内容" class="layui-textarea" lay-verify="required">{{ $trainingData['content'] ?? '' }}</textarea>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label"><span style="color: red">* </span>类型:</label>
                                <div class="layui-input-block">
                                    <select name="type" lay-verify="required">
                                        <option value="">请选择类型</option>
                                        <option value="1" {{ isset($trainingData['type']) && $trainingData['type'] == 1 ? 'selected' : '' }}>品牌</option>
                                        <option value="2" {{ isset($trainingData['type']) && $trainingData['type'] == 2 ? 'selected' : '' }}>型号</option>
                                        <option value="3" {{ isset($trainingData['type']) && $trainingData['type'] == 3 ? 'selected' : '' }}>分类</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-submit lay-filter="SaveTrainingData" id="SaveTrainingData">立即提交</button>
                                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@include('js')
<script type="text/javascript" src="/js/web/trainingData/saveTrainingData.js?v={{time()}}"></script>
