@include('css')
@include('js')
<div class="lay-box">
    <div class="layui-card">
        <div class="layui-card-body" style="padding-top: 20px">
            <form class="layui-form" action="" lay-filter="distribute_price_filter_form">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        @inject('canalPresenter', 'App\Presenters\MultiSelectorPresenter')
                        {!! $canalPresenter->render('supplier_id', '猎芯渠道', request()->get('supplier_id'), $supplierListForXmSelect , ['radio' => false,'width' => '200']) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter', 'App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('status', '状态', request()->get('status'), config('field.DistributePriceStatus')) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('timePresenter', 'App\Presenters\TimeIntervalPresenter')
                        {!! $timePresenter->render('create_time', '创建时间') !!}
                    </div>
                    <div class="layui-inline">
                        @inject('canalPresenter', 'App\Presenters\MultiSelectorPresenter')
                        {!! $canalPresenter->render('canal', '供应商编码', request()->get('canal'), $canalListForXmSelect , ['radio' => true,'width' => '300']) !!}
                    </div>
                </div>
                <br>
                <div class="layui-form-item" style="text-align: center;">
                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-sm" lay-submit lay-filter="list" style="margin-left: 20px">
                            查询
                        </button>
                        <button class="layui-btn layui-btn-primary layui-btn-sm reset-button" lay-submit lay-filter="list" style="margin-left: 18px">
                            重置
                        </button>
                    </div>
                </div>
            </form>
            <div class="layui-inline" style="margin-left: 1px">
                <button class="layui-btn layui-btn-sm" id="add_distribute_price">新增分配价格</button>
                </button>
            </div>
            <table class="layui-table" id="list" lay-filter="list"></table>

        </div>
    </div>

    <div style="display: none" id="upload_div">
        <div class="layui-form" style="margin-top: 40px">
            <div class="layui-form-item" style="margin-left: 70px">
                <label class="layui-form-label">文件上传 : </label>
                <div class="layui-input-inline">
                    <button type="button" class="layui-btn" id="upload_button">
                        <i class="layui-icon">&#xe67c;</i>上传映射型号文件
                    </button>
                    <div id="msgTip"></div>
                </div>
            </div>
            <div class="layui-form-item" style="margin-left: 70px">
                <label class="layui-form-label">模板文件 : </label>
                <div class="layui-input-inline">
                    <button type="button" class="layui-btn layui-btn-primary" id="download_template">
                        <i class="layui-icon">&#xe67c;</i>点击下载模板文件
                    </button>
                </div>
            </div>
            <input type="hidden" id="hasFile" value="0">
            <div class="layui-form-item" style="margin-left: 65px">
                <label class="layui-form-label">选择类型</label>
                <div class="layui-input-block">
                    <input type="radio" name="type" value="1" title="国产">
                    <input type="radio" name="type" value="2" title="国外">
                </div>
            </div>
            <div class="layui-form-item" style="text-align: center">
                <button class="layui-btn" lay-submit lay-filter="confirmUpload" id="confirmUpload">确定上传</button>
            </div>
        </div>
    </div>
</div>
<script type="text/html" id="operation">
    <button type="button" class="layui-btn layui-btn-xs edit_distribute_price" value="@{{ d.id }}" lay-event="edit_distribute_price">编辑</button>
    <button class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete_distribute_price">删除</button>
    @{{# if(d.status == 1){ }}
        <button class="layui-btn layui-btn-xs layui-btn-danger" lay-event="disable_distribute_price">禁用</button>
        @{{# } else { }}
            <button class="layui-btn layui-btn-xs" lay-event="enable_distribute_price">启用</button>
            @{{# } }}
                <button class="layui-btn layui-btn-xs" lay-event="copy_distribute_price">复制</button>
</script>
</div>
<script type="text/javascript" src="/js/web/distributePrice/distributePriceList.js?v={{time()}}"></script>
