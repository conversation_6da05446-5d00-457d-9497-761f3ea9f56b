@include('css')
@include('js',['layuiVersion'=>'27'])
<style>
    .p-l {
        margin: 20px;
        background-color: rgba(255, 249, 242, 0.02);
        height: auto;
    }

    .layui-form-item {
        margin-bottom: 0px;
    }

    .p-l label {
        margin-left: 30px;
    }

    .div-b {
        margin-top: 20px;
    }

    label {
        box-sizing: content-box;
    }

    .custom .layui-layer-content .table-status {
        position: relative;
        margin: 0 !important;
        z-index: 2;
    }

    .custom .layui-layer-content .table-status th {
        white-space: nowrap;
    }

    .layui-layer-content {
        padding: 3px 3px !important;
    }

    .custom .layui-layer-content .table-status td,
    .custom .layui-layer-content .table-status th {
        padding: 3px 3px !important;
    }
</style>
<div style="height: 10px"></div>
<div class="lay-box">
    <div class="layui-card">
        <div class="layui-card-body" style="padding-bottom: 0px;">
            <div class="layui-collapse">
                <form class="layui-form layui-box" style="padding-top: 20px" lay-filter="sku_list_form">
                    <input type="hidden" name="prev_sku" value="1">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">商品型号</label>
                            <div class="layui-input-inline" style="width: 150px">
                                <input type="text" value="{{request()->get('goods_name_origin/eqs') ?? ''}}"
                                       name="goods_name_origin/eqs" placeholder="商品型号,逗号分隔支持多个" autocomplete="off"
                                       class="layui-input">
                            </div>
                            <button class="layui-btn layui-btn-sm" type="button" id="batchGoodsNameSearch">批量</button>
                        </div>
                        <div class="layui-inline" style="margin-left: -25px">
                            <label class="layui-form-label">商品名称</label>
                            <div class="layui-input-inline">
                                <input type="text" value="{{request()->get('sku_name/condition') ?? ''}}"
                                       name="sku_name/condition" placeholder="请输入商品名称" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                           @inject('multiSelectPresenter','App\Presenters\MultiSelectorPresenter')
                           {!! $multiSelectPresenter->render('supplier_id','供应商',request()->get('supplier_id'),$supplierListForXmSelect,['width'=>'168px']) !!}
                        </div>
                        <div class="layui-inline">
                            @inject('multiSelectPresenter','App\Presenters\MultiSelectorPresenter')
                            {!! $multiSelectPresenter->render('goods_label','渠道标签',request()->get('goods_label'),$goodsLabelListForXmSelect,['width'=>'168px']) !!}
                        </div>

                        <div class="layui-inline">
                            @inject('multiSelectPresenter','App\Presenters\MultiSelectorPresenter')
                            {!! $multiSelectPresenter->render('goods_status','SKU状态',request()->get('goods_status'),$goodsStatusListForXmSelect,['width'=>'168px']) !!}
                        </div>

                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">品牌</label>
                            <div class="layui-input-inline">
                                <div id="brandSelect" style="width: 200px"></div>
                                <input type="hidden" id="brand_id_condition"
                                       select_value="{{$brandSelectValue}}"
                                       name="brand_id/eqs"
                                       value=""
                                       class="layui-input"/>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">SKUID</label>
                            <div class="layui-input-inline">
                                <input type="text" value="{{request()->get('goods_id/condition') ?? ''}}"
                                       name="goods_id/condition" placeholder="请输入型号" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>

                        <div class="layui-inline">
                            <label class="layui-form-label">供应商编码</label>
                            <div class="layui-input-inline">
                                <input type="text" name="canal/condition" placeholder="请输入供应商编码"
                                       value="{{request()->get('canal/condition') ?? ''}}" class="layui-input"/>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">SKU分类</label>
                            <div class="layui-input-inline">
                                <div id="classSelector" style="width: 168px"></div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">ECCN</label>
                            <div class="layui-input-inline">
                                <input type="text" name="eccn/eq" placeholder="请输入ECCN,精确搜索"
                                       value="{{request()->get('eccn/eq') ?? ''}}" class="layui-input"/>
                            </div>
                        </div>
                        <div class="layui-inline">
                            @inject('multiSelectPresenter','App\Presenters\MultiSelectorPresenter')
                            {!! $multiSelectPresenter->render('source','来源',request()->get('source'),$skuSourceListForXmSelect,['width'=>'168px']) !!}
                        </div>

                        <div class="layui-inline">
                            @inject('multiSelectPresenter','App\Presenters\MultiSelectorPresenter')
                            {!! $multiSelectPresenter->render('encoded','SKU采购',request()->get('encoded'),$purchaseUsersForXmSelect,['width'=>'168px']) !!}
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">SPUID</label>
                            <div class="layui-input-inline">
                                <input type="text" value="{{request()->get('spu_id/condition') ?? ''}}"
                                       name="spu_id/condition" placeholder="请输入SPUID" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">原ID</label>
                            <div class="layui-input-inline">
                                <input type="text" value="{{request()->get('old_goods_id/eq') ?? ''}}"
                                       name="old_goods_id/condition" placeholder="原ID" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            @inject('statusPresenter','App\Presenters\StatusPresenter')
                            {!! $statusPresenter->render('is_expire','是否过期',request()->get('is_expire'),[-1=>'否',1=>'是']) !!}
                        </div>

                        <div class="layui-inline">
                            @inject('multiSelectPresenter','App\Presenters\MultiSelectorPresenter')
                            {!! $multiSelectPresenter->render('org_id','所属组织',request()->get('org_id'),$orgListForXmSelect,['width'=>'168px']) !!}
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">录入时间</label>
                            <div class="layui-input-inline" style="min-width: 280px">
                                <input type="text" name="create_time" placeholder="请选择时间区间" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            @inject('timePresenter','App\Presenters\TimeIntervalPresenter')
                            {!! $timePresenter->render('update_time','更新时间') !!}
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            @inject('statusPresenter','App\Presenters\StatusPresenter')
                            {!! $statusPresenter->render('is_temp','是否寄售',request()->get('is_temp'),[-1=>'否',1=>'是']) !!}
                        </div>
                         <div class="layui-inline">
                           @inject('statusPresenter','App\Presenters\StatusPresenter')
                            {!! $statusPresenter->render('has_attr','是否有参数',request()->get('has_attr'),[-1=>'否',1=>'是']) !!}
                        </div>
                         <div class="layui-inline">
                           @inject('statusPresenter','App\Presenters\StatusPresenter')
                            {!! $statusPresenter->render('has_class','是否有分类',request()->get('has_class'),[-1=>'否',1=>'是']) !!}
                        </div>
                        <div class="layui-inline">
                            @inject('statusPresenter','App\Presenters\StatusPresenter')
                            {!! $statusPresenter->render('has_stock','是否有库存',request()->get('has_stock'),[-1=>'否',1=>'是']) !!}
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">库存数量</label>
                            <div class="layui-input-inline">
                                <div class="layui-row">
                                     <div class="layui-col-xs6" style="width: 94px">
                                        <select name="stock_compare_type">
                                            <option value="">请选择</option>
                                            <option value="eq"> 等于 </option>
                                            <option value="neq"> 不等于 </option>
                                            <option value="lt"> 小于 </option>
                                            <option value="lte"> 小于等于 </option>
                                            <option value="gt"> 大于 </option>
                                            <option value="gte"> 大于等于 </option>
                                        </select>
                                    </div>
                                    <div class="layui-col-xs6" style="width: 64px;">
                                        <input type="text" value="" name="stock_num" placeholder="数量" autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                            </div>
                        </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                @inject('multiSelectPresenter','App\Presenters\MultiSelectorPresenter')
                                {!! $multiSelectPresenter->render('class_id1','一级分类',request()->get('class_id1'),$topClassListForXmSelect,['width'=>'168px']) !!}
                            </div>
                            <div class="layui-inline">
                                @inject('multiSelectPresenter','App\Presenters\MultiSelectorPresenter')
                                {!! $multiSelectPresenter->render('encap','封装',request()->get('encap'),$encapListForXmSelect,['width'=>'168px']) !!}
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">标准品牌</label>
                                <div class="layui-input-inline">
                                    <div id="standardBrandAreaSelector" style="width: 168px"></div>
                                </div>
                                <div class="layui-input-inline">
                                    <div id="standardBrandSelector" style="width: 168px"></div>
                                </div>

                            </div>
                            {{-- <div class="layui-inline">
                                @inject('multiSelectPresenter','App\Presenters\MultiSelectorPresenter')
                                {!! $multiSelectPresenter->render('standard_brand_ids','标准品牌',request()->get('standard_brand_ids'),$standardBrandListForXmSelect,['width'=>'168px']) !!}
                            </div> --}}
                        </div>
                        <div class="layui-form-item">
                        <div class="layui-inline">
                            @inject('multiSelectPresenter','App\Presenters\MultiSelectorPresenter')
                            {!! $multiSelectPresenter->render('ability_level','履约程度',request()->get('ability_level'),$abilityLevelListForXmSelect,['width'=>'168px']) !!}
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">精确型号搜索</label>
                            <div class="layui-input-inline">
                                <input type="text" value="{{request()->get('goods_name/eq') ?? ''}}"
                                       name="goods_name/eq" placeholder="精确型号搜索" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            @inject('statusPresenter','App\Presenters\StatusPresenter')
                            {!! $statusPresenter->render('single_price/sort','成本价排序',request()->get('single_price/sort'),['desc'=>'降序','asc'=>'升序']) !!}
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">关键词</label>
                            <div class="layui-input-inline" style="width: 270px;">
                                <input type="text" value="{{request()->get('keyattr') ?? ''}}"
                                       name="keyattr" placeholder="请输入任意关键词，支持范围，参数等" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>
                        <div class="layui-btn-group" style="margin-bottom: 10px;margin-left: 15px">
                            <button lay-submit lay-filter="load" class="layui-btn layui-btn-sm" id="skuListSearch"
                                    data-type="reload">
                                搜索
                            </button>
                            <a href="" class="layui-btn layui-btn-sm layui-btn-warm">重置</a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div style="margin-left: 15px;margin-right: 15px;">
            <table class="layui-table" id="skuList" lay-filter="skuList" lay-data="{id:'skuList'}"></table>
        </div>
        <div class="layui-btn-group demoTable" style="margin-left: 15px;margin-bottom: 15px;">
            <button class="layui-btn layui-btn-sm" id="selectAll">全选</button>
            @if (!request()->input('is_distribute'))
                <button class="layui-btn layui-btn-sm layui-btn-normal" id="batchPutAway">上架</button>
                <button class="layui-btn layui-btn-sm layui-btn-danger" id="batchUnShelve">下架</button>
            @endif

        </div>
    </div>
</div>
<script type="text/html" id="operate">
        {{--    <a href="{{config('website.MainDomain')}}/goods_@{{ d.goods_id }}.html"--}}
        {{--       target="_blank"--}}
        {{--       class="layui-btn layui-btn-xs layui-btn-outline layui-btn-info"><strong>预览</strong></a>--}}
        <button class="layui-btn layui-btn-xs" lay-event="saveSku">编辑SKU</button>
        <button class="layui-btn layui-btn-xs" lay-event="saveSpu">编辑SPU</button>
        {{--    <a href="{{url('footstone/addsku')}}?sku_id=@{{ d.goods_id }}"--}}
        {{--       class="layui-btn layui-btn-xs layui-btn-outline layui-btn-primary"><strong>编辑</strong></a>--}}
        @{{# if(d.goods_status ==3 || d.goods_status == 0 || d.goods_status ==2 ){ }}
        {{--    <button class="layui-btn layui-btn-xs layui-btn-outline layui-btn-success only_shelf" lay-event="putAway">--}}
        {{--        <strong>上架</strong></button>--}}
        @{{# }else{  }}
        {{--    <button class="layui-btn layui-btn-xs layui-btn-outline layui-btn-warning only_shelf" lay-event="unShelve"><strong>下架</strong>--}}
        {{--    </button>--}}
        @{{# }  }}
</script>

<script type="text/html" id="toolbar">
    <div class="layui-btn-container">
        @if (!request()->input('is_distribute'))
        <button class="layui-btn layui-btn-sm" id="exportSkuList">导出SKU</button>
        <button class="layui-btn layui-btn-sm" id="batchUpdateItemSeries">绑定系列</button>
        @endif
        <button class="layui-btn layui-btn-sm" id="batchSaveShopDistributeSku">勾选推送选品池</button>
        <button class="layui-btn layui-btn-sm layui-btn-normal" id="viewSelectedFilters">批量推送选品池</button>
    </div>
</script>

<script>
    let userId = {{request()->user->userId}};

    let classListForXmSelect = {!! json_encode($classListForXmSelect) !!};

    let standardBrandListForXmSelect = {!! json_encode($standardBrandListForXmSelect) !!};

    let shopList = {!! json_encode($shopList) !!};

    let standardBrandAreaListForXmSelect = {!! json_encode($standardBrandAreaListForXmSelect) !!};

</script>
<script type="text/javascript" src="/js/web/sku/skuPrevList.js?v={{time()}}"></script>
