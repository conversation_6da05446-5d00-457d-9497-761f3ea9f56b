<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
@include('css')
@include('js')
<script type="text/javascript" charset="utf-8" src="/js/plugins/tinymce/tinymce.min.js"></script>
<style>
    .red {
        color: red;
        margin-right: 5px;
    }

    .layui-form-select dl {
        top: auto;
        bottom: 36px;
    }
</style>
<form class="layui-form" style="margin-top: 30px">
    <div class="layui-form">
        <div>
            <div class="layui-form-item">
                <label class="layui-form-label">SPU型号：</label>
                <div class="layui-input-inline" style="width: 590px">
                    <input type="hidden" name="spu_id" id="spu_id" value="{{$info['spu_id'] ?? ''}}">
                    <input type="hidden" id="spu_name" value="{{$spu_name ?? ''}}">
                    <input type="hidden" id="brand_name" value="{{$brand_name ?? ''}}">
                    <div class="layui-input-inline" id="spu_id_selector" style="width: 500px">
                    </div>
                </div>
                <label class="layui-form-label">SKU型号</label>
                <div class="layui-input-inline">
                    <input type="text" name="goods_name" value="{{$info['goods_name'] ?? ''}}" placeholder="请输入SKU型号"
                           @if(request()->get('from')=='supplier') disabled @endif
                           autocomplete="off"
                           class="layui-input @if(request()->get('from')=='supplier') layui-disabled @endif">
                </div>
                @if(!empty($info['goods_id']))
                    <label class="layui-form-label" style="margin-left: 60px">SPUID</label>
                    <div class="layui-input-inline">
                        <div style="padding-top: 10px">
                            <a type="text" id="spu_id"
                               href="http://{{config('website.data')}}/footstone/addspu?spu_id={{$info['spu_id']}}">{{$info['spu_id']}}</a>
                        </div>
                    </div>
                @endif
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="red">*</span>SKU类别</label>
                <div class="layui-input-inline">
                    <select name="goods_type" lay-verify="required" lay-filter="goods_type" lay-verType="tips"
                            @if(!empty($info['goods_type'])) disabled @endif>
                        <option value="">请选择SKU类别</option>
                        <option value="1" @if(!empty($info['goods_type']) && $info['goods_type']==1) selected @endif>联营
                        </option>
                        <option value="2" @if(!empty($info['goods_type']) && $info['goods_type']==2) selected @endif>专卖
                        </option>
                    </select>
                </div>

                <label class="layui-form-label"><span class="red">*</span>供应商</label>
                <div class="layui-input-inline">
                    @if(empty($info['goods_id']))
                        <select name="supplier_id" lay-verify="required" lay-verType="tips" lay-filter="supplier_id">
                            <option value="">请先选择SKU类别</option>
                        </select>
                    @else
                        <input type="hidden" name="goods_id" value="{{$info['goods_id'] ?? ''}}">
                        <input type="hidden" name="supplier_id" value="{{$info['supplier_id'] ?? ''}}">
                        <input type="text" class="layui-input form-control layui-disabled" value="{{ $supplier_name }}"
                               disabled="disabled"/>
                    @endif
                </div>
                <div>
                    <label class="layui-form-label" style="width: 170px">供应商编码</label>
                    <div class="layui-input-inline" style="width: 230px">
                        <select name="canal" lay-search lay-verType="tips"
                                @if(!empty($info['canal']) || (!empty($info['goods_type']) && $info['goods_type']==1)) disabled @endif>
                            <option value="">请先选择SKU类别</option>
                            @if(!empty($canal_list) && is_array($canal_list))
                                @foreach($canal_list as $k=>$v)
                                    <option value="{{$k}}"
                                            @if(!empty($info['canal']) && $info['canal']==$k) selected @endif>{{$k}}</option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                </div>
                <div>
                    <label class="layui-form-label" style="width: 100px">采购员</label>
                    <div class="layui-input-inline" style="width: 230px">
                        <select name="encoded" lay-search lay-verType="tips"
                                @if(!empty($info['encoded']) || (!empty($info['goods_type']) && $info['goods_type']==1)) disabled @endif>
                            <option value="">请先选择供应商编码</option>
                            @if(!empty($encodedList) && is_array($encodedList))
                                @foreach($encodedList as $k=>$v)
                                    <option value="{{$k}}"
                                            @if(!empty($info['encoded']) && $info['encoded']==$k) selected @endif>{{$v}}</option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label"><span class="red">*</span>SKU状态</label>
                <div class="layui-input-inline" style="width: 230px">
                    @foreach($goods_statuses as $k=>$v)
                        <input type="radio" name="goods_status" value="{{ $k }}" title="{{$v}}"
                               @if(!empty($info['goods_status']) && $info['goods_status'] == $k) checked
                               @elseif(empty($info['goods_status']) && $k==1) checked
                            @endif />
                    @endforeach
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    @inject('statusPresenter','App\Presenters\StatusPresenter')
                    {!! $statusPresenter->render('org_id','SKU组织',$info['org_id']??'猎芯',config('field.SkuOrgList'),[
                         'disable' => true
                     ]) !!}
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-inline">
                    <div class="layui-form-item">
                        @inject('imageUploadPresenter','App\Presenters\ImageUploadPresenter')
                        {!! $imageUploadPresenter->render('goods_images', 'SKU图片:', !empty($info['goods_images']) ? $info['goods_images'] : '') !!}
                    </div>
                    <p style="margin-left: 110px;" class="layui-form-mid layui-word-aux">
                        支持拓展名png和jpg,建议大小不超过1M,宽高：220*220</p>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label"><span class="red">*</span>起订量</label>
                <div class="layui-input-inline">
                    <input type="text" name="moq" placeholder="请输入起订量" autocomplete="off" class="layui-input"
                           value="{{$info['moq'] ?? ''}}" lay-verify="required" lay-verType="tips">
                </div>
                <label class="layui-form-label"><span class="red">*</span>库存</label>
                <div class="layui-input-inline">
                    <input type="text" name="stock" placeholder="请输入库存量" autocomplete="off" class="layui-input"
                           value="{{$info['stock'] ?? ''}}" lay-verify="required" lay-verType="tips">
                </div>
                <label class="layui-form-label">产地</label>
                <div class="layui-input-inline">
                    <input type="text" name="coo" placeholder="请输入产地" autocomplete="off" class="layui-input"
                           value="{{$info['coo'] ?? ''}}">
                </div>
                <label class="layui-form-label">包装方式</label>
                <div class="layui-input-inline">
                    <input type="text" name="pack" placeholder="请输入包装方式" autocomplete="off" class="layui-input"
                           value="{{$info['pack'] ?? ''}}">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="red">*</span>标准包装量</label>
                <div class="layui-input-inline">
                    <input type="text" name="mpq" placeholder="请输入标准包装量" autocomplete="off" class="layui-input"
                           value="{{$info['mpq'] ?? ''}}" lay-verify="required" lay-verType="tips">
                </div>
                <label class="layui-form-label">批次</label>
                <div class="layui-input-inline">
                    <input type="text" name="batch_sn" placeholder="请输入批次" autocomplete="off" class="layui-input"
                           value="{{$info['batch_sn'] ?? ''}}">
                </div>
                <label class="layui-form-label">递增量</label>
                <div class="layui-input-inline">
                    <input type="text" name="multiple" placeholder="请输入递增量" autocomplete="off" class="layui-input"
                           value="{{$info['multiple'] ?? ''}}">

                </div>
                <div class="layui-input-inline">
                    <span style="color: #999;">为0则是会取moq或者mpq的值</span>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="red">*</span>香港货期</label>
                <div class="layui-input-inline">
                    <input type="text" name="hk_delivery_time" placeholder="香港货期" autocomplete="off"
                           class="layui-input"
                           value="{{$info['hk_delivery_time'] ?? ''}}">
                </div>
                <div class="layui-input-inline">
                    <select name="hk_period" lay-verify="required">
                        <option value="工作日" @if($hk_time_period==0) selected @endif >工作日</option>
                        <option value="周" @if($hk_time_period==1) selected @endif >周</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="red">*</span>大陆货期</label>
                <div class="layui-input-inline">
                    <input type="text" name="cn_delivery_time" placeholder="大陆货期" autocomplete="off"
                           class="layui-input"
                           value="{{$info['cn_delivery_time'] ?? ''}}">
                </div>
                <div class="layui-input-inline">
                    <select name="cn_period" lay-verify="required">
                        <option value="工作日" @if($cn_time_period==0) selected @endif >工作日</option>
                        <option value="周" @if($cn_time_period==1) selected @endif >周</option>
                    </select>
                </div>
                <div class="layui-form-mid layui-word-aux">
                    注：大陆及香港货期必需填写一个(上架爱智英文站可不填货期)，必须是以'数字-数字'格式，'-'为英文字符。eg:10-20
                </div>
            </div>
            <div class="layui-form-item alike_goods_name layui-form-text"
                 style="@if(empty($info['supplier_id'])) display: none @endif margin-left:47px">
                <div class="layui-form-item">
                    <div class="layui-input-inline" style="margin-left: -47px">
                        @inject('tinymcePresenter','App\Presenters\TinymcePresenter')
                        {!! $tinymcePresenter->render('sku_detail', '详细描述:', $info['sku_detail'] ?? '')!!}
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-col-sm8" style="margin-top: 20px">
                        <p style="size: 20px;margin-bottom: 10px;">阶梯价格</p>
                        <table class="layui-table layui-table-striped layui-table-bordered layui-table-hover"
                               id="table_ladder_price" style="margin-left: 60px">
                            <thead>
                            <tr role="row" style="background-color: darkgrey">
                                <th style="width:100px;text-align: center;">数量</th>
                                <th style="width:120px;text-align: center;">国内含税(￥)</th>
                                <th style="width:200px;text-align: center;">香港交货($)</th>
                                <th style="width:200px;text-align: center;">成本价($,仅Mouser供应商有效)</th>
                                <th style="width:100px;text-align: center;">操作</th>
                            </tr>
                            </thead>
                            <tbody id="tbody_ladder_price">
                            @foreach($ladder_prices as $ladder_p)
                                <tr align="center" class="tr_ladder_price">
                                    <td><input style="text-align: center" type="number" name="purchases[]"
                                               value="{{ $ladder_p['purchases']}}"/></td>
                                    <td><input style="text-align: center" type="number" name="price_cn[]"
                                               value="{{ $ladder_p['price_cn'] ?? ''}}"/></td>
                                    <td><input style="text-align: center" type="number" name="price_us[]"
                                               value="{{ $ladder_p['price_us'] ?? ''}}"/></td>
                                    <td><input style="text-align: center" type="number" name="cost_price[]"
                                               value="{{ $ladder_p['cost_price'] ?? ''}}"/></td>
                                    {{--                                        <td><i class="layui-icon delete_price">&#xe640;</i></td>--}}
                                    <td>
                                        <button type="button"
                                                class="layui-btn layui-btn-xs layui-btn-danger delete_price">删除
                                        </button>
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                    {{--添加一个阶梯价格 & 保存按钮--}}
                    <div class="layui-col-sm10"
                         @if(!empty($info) && isset($info['goods_type'])&& $info['goods_type']==1 && $SaveSku===false) style="display: none;margin-top: 20px" @endif>
                        <div style="text-align: center">
                            <button type="button" class="layui-btn layui-btn-primary" id="add_ladder_price">添加价格
                            </button>
                            <button type="submit" class="layui-btn " lay-submit lay-filter="load">提交</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
</form>
</body>
</html>
{{Autograph()}}
<script type="text/javascript" src="/js/web/sku/saveSku.js?v={{time()}}"></script>
