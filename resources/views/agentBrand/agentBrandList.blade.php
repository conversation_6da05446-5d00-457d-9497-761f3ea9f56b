@include('css')
@include('js')
<style>
    .layui-table-cell {
        height: auto;
        overflow: visible;
        text-overflow: inherit;
        white-space: normal;
    }

    .layui-input {
        height: 30px;
        margin-top: 2px;
    }

    .layui-form-label {
        margin-left: -25px;
    }

</style>
<div class="lay-box" x-data="agentBrandList">
    <div class="layui-card" style="font-size: 12px;">
        <div class="layui-card-body" style="padding-top: 15px">
            <form class="layui-form" action="" lay-filter="agent_brand_filter_form">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">代理品牌名称</label>
                        <div class="layui-input-inline">
                            <input type="text" name="standard_brand_name" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('pm_user_id', 'PM负责人', '', $purchaseUsers) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('status', '状态', '', [1=>'启用',-1=>'禁用']) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('multiSelectorPresenter','App\Presenters\MultiSelectorPresenter')
                        {!! $multiSelectorPresenter->render('agent_brand_level', '品牌等级', '', $brandLevelForXmSelect) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('multiSelectorPresenter','App\Presenters\MultiSelectorPresenter')
                        {!! $multiSelectorPresenter->render('brand_area', '品牌区域', '', $brandAreaForXmSelect) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('multiSelectorPresenter','App\Presenters\MultiSelectorPresenter')
                        {!! $multiSelectorPresenter->render('application_area', '应用领域', '', $applicationAreaForXmSelect) !!}
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">供应商编码</label>
                        <div class="layui-input-inline">
                            <input type="text" name="supplier_code" id="supplier_code" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        @inject('timePresenter','App\Presenters\TimeIntervalPresenter')
                        {!! $timePresenter->render('create_time','创建时间') !!}
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-sm" lay-submit lay-filter="list" style="margin-left: 20px">
                            查询
                        </button>
                        <button class="layui-btn layui-btn-primary layui-btn-sm reset-button" lay-submit lay-filter="list" style="margin-left: 18px">
                            重置
                        </button>
                    </div>
                </div>
            </form>
            <script type="text/html" id="toolbar">
                <button type="button" class="layui-btn layui-btn-sm" lay-event="add_brand">新增
                </button>
                <button type="button" class="layui-btn layui-btn-sm" id="export_brand">导出
                </button>

            </script>
            <table class="layui-table" id="list" lay-filter="list"></table>

        </div>
    </div>
</div>
<script type="text/html" id="operation">
    <button class="layui-btn layui-btn-xs" value="@{{ d.id }}" lay-event="edit">
        <strong>编辑</strong></button>

    @{{# if(d.status == 1){ }}
        <button class="layui-btn layui-btn-xs layui-btn-danger" value="@{{ d.id }}" lay-event="disable">
            <strong>禁用</strong></button>
        @{{# }else{ }}
            <button class="layui-btn layui-btn-xs" value="@{{ d.id }}" lay-event="enable">
                <strong>启用</strong></button>
            @{{# } }}

</script>

<script>
    let supplierCodeList = @json($supplierCodeList);
</script>

<script type="text/javascript" src="/js/web/agentBrand/agentBrandList.js?v={{time()}}"></script>
