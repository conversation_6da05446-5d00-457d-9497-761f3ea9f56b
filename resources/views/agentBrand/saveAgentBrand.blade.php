@include('css')
@include('js')
<style>
    .layui-form-select dl {
        z-index: 9999;
    }

</style>
<div style="padding: 10px 30px 30px 30px" x-data="agentBrandForm">
    <div class="layui-tab layui-tab-brief">
        <ul class="layui-tab-title">
            <li class="layui-this">代理品牌信息</li>
            @if(!empty($brand))
            <li>代理产品库</li>
            @endif
        </ul>
        <div class="layui-tab-content">
            <div class="layui-tab-item layui-show">
                <div class="layui-card">
                    <form class="layui-form" action="">
                        <div class="layui-form-item" style="margin-top: 10px">
                            <div class="layui-inline">
                                <label class="layui-form-label"><span style="color: red">*</span>选择标准品牌 </label>
                                <div class="layui-input-inline">
                                    <div id="brandSelect" style="width: 200px"></div>
                                    <input type="hidden" id="standard_brand_id" name="standard_brand_id" value="{{ $brand['standard_brand_id'] ?? 0 }}" x-model.fill="standard_brand_id" class="layui-input" />
                                </div>
                            </div>
                        </div>
                        <input type="hidden" id="id" x-model.fill="id" name="id" value="{{$brand['id']??0}}">
                        <input type="hidden" id="brand_name" x-model.fill="brand_name" value="{{$brand['brand_name'] ?? ''}}">
                        <div>
                            <div x-show="show_brand_info">
                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label">英文全称</label>
                                        <div class="layui-input-inline">
                                            <label class="layui-form-label" style="text-align: left;width: 400px" x-text="brand_name_en"> {{$brand['brand_name_en'] ?? ''}}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label">中文全称</label>
                                        <div class="layui-input-inline">
                                            <label class="layui-form-label" style="text-align: left;width: 400px" x-text="brand_name_cn"> {{$brand['brand_name_cn'] ?? ''}}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label">品牌官网链接</label>
                                        <div class="layui-input-inline">
                                            <label class="layui-form-label" style="text-align: left;width: 400px" x-text="brand_brief"> {{$brand['brand_brief'] ?? ''}}</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">应用领域</label>
                                <div class="layui-input-block">
                                    <input type="hidden" name="application_area" value="{{$brand['application_area'] ?? ''}}">
                                    @foreach(config('field.SpuApplicationArea') as $k=>$type)
                                    <input type="checkbox" name="application_area[{{$k}}]" {{--                                               lay-skin="primary"--}} title="{{$type}}" @if (!empty($brand) && in_array($k,explode(',',$brand['application_area']))) checked @endif>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">
                                <span style="color: red">*</span>代理证:
                            </label>
                            <input type="hidden" id="agent_certificate" name="agent_certificate" value="{{$brand['agent_certificate']??''}}">
                            <div class="layui-upload-drag" id="upload_agent_certificate">
                                <div id="agent_certificate_ViewDiv">
                                    <img class="{{$brand['agent_certificate']??'layui-hide'}}" src="{{!empty($brand['agent_certificate']) ? (strpos($brand['agent_certificate'],'.pdf' )!==false ?'/assets/images/pdf.png':$brand['agent_certificate']) : ''}}" alt="上传成功后渲染" style="max-width: 50px">
                                    <br>
                                    <i class="layui-icon"></i>
                                    <p>点击上传，或将文件拖拽到此处</p>
                                </div>
                            </div>
                            <p style="margin-left: 110px;font-weight: bolder">
                                <input type="hidden" name="agent_certificate_name" id="agent_certificate_name" x-model.fill="agent_certificate_name" value="{{$brand['agent_certificate_name'] ?? ''}}">
                                支持上传文件格式为图片或者PDF，文件大小为50MB以内。
                                @if(!empty($brand['agent_certificate']))
                                <a href="{{$brand['agent_certificate']}}" target="_blank" style="color: #00a0e9" x-text="agent_certificate_name"></a>
                                @endif
                            </p>
                        </div>
                        <br>

                        <div class="layui-form-item">
                            <div>
                                <label class="layui-form-label"> <span style="color: red">*</span>代理证有效时间:</label>
                                <div class="layui-input-inline" style="padding-top: 4px">
                                    <input type="text" x-bind:class="disableEffectTime ? 'layui-disabled layui-input' : 'layui-input'" x-bind:disabled="disableEffectTime" x-bind:style="disableEffectTime ? 'background: #DFE1E5':''" x-model.fill="certificate_effective_time" id="certificate_effective_time" name="certificate_effective_time" value="{{ $brand['certificate_effective_time'] ?? '' }}" placeholder="请选择时间">
                                </div>
                                <div style="max-height: 40px;">
                                    <input type="checkbox" lay-filter="is_certificate_permanent" name="is_certificate_permanent" title="长期有效" value="1" x-model.fill="is_certificate_permanent" {{!empty($brand['is_certificate_permanent'])?'checked':''}}>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    @inject('statusPresenter','App\Presenters\StatusPresenter')
                                    {!! $statusPresenter->render('pm_user_id', 'PM负责人', $brand['pm_user_id'] ?? '', $purchaseUsers,['required'=>true]) !!}
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">
                                    <span style="color: red">*</span>供应商编码
                                </label>
                                <div class="layui-input-inline">
                                    <input type="text" name="supplier_code" class="layui-input" value="{{$brand['supplier_code'] ?? ''}}">
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                @inject('statusPresenter','App\Presenters\StatusPresenter')
                                {!! $statusPresenter->render('agent_brand_level', '品牌等级', $brand['agent_brand_level'] ?? '', config('field.AgentBrandLevel'),['required'=>true]) !!}
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                @inject('multiSelectorPresenter','App\Presenters\MultiSelectorPresenter')
                                {!! $multiSelectorPresenter->render('main_product_class_ids', '主营产品分类', $brand['main_product_class_ids'] ?? '', $mainProductClassList,['required'=>true,'width'=>'500px']) !!}
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                @inject('multiSelectorPresenter','App\Presenters\multiSelectorPresenter')
                                {!! $multiSelectorPresenter->render('agent_class_id', '专题页所属分类', $brand['agent_class_id'] ?? '', $agentBrandListForXmSelect,['required'=>true,'width'=>'700px']) !!}
                            </div>
                        </div>
                        <div class="layui-form-item" style="text-align: center">
                            <button type="button" class="layui-btn layui-btn-sm" lay-submit lay-filter="saveAgentBrand">
                                确定
                            </button>
                            <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm" lay-submit lay-filter="cancel">
                                取消
                            </button>
                        </div>
                        <br>
                        <br>
                    </form>
                </div>
            </div>
            <div class="layui-tab-item">
                <button type="button" style="display: none" class="layui-btn layui-btn-sm" id="import_agent_brand_spu">导入</button>
                <script type="text/html" id="toolbar">
                    <button type="button" class="layui-btn layui-btn-sm" lay-event="add_spu">添加</button>
                    <button type="button" class="layui-btn layui-btn-sm" lay-event="batch_delete_spu">批量删除</button>
                    <button type="button" class="layui-btn layui-btn-sm" lay-event="import_agent_brand_spu">导入
                    </button>
                    <span style="margin-left: 20px;"><a href="/template/代理产品库导入模板.xlsx" target="_blank">导入模板下载</a></span>

                </script>
                <table class="layui-table" id="spuList" lay-filter="spuList"></table>
            </div>
        </div>
    </div>
</div>


<script type="text/html" id="operation">
    <button type="button" class="layui-btn layui-btn-xs" lay-event="edit">编辑</button>
    <button type="button" class="layui-btn layui-btn-xs layui-btn-danger " lay-event="delete">删除</button>

</script>

{{Autograph()}}
<script type="text/javascript" src="/js/web/agentBrand/saveAgentBrand.js?v={{time()}}"></script>
