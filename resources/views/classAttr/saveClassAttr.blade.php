@include('css')
@include('js')
<div class="layui-card" style="margin-top: 10px;padding-bottom: 20px;">
    <form class="layui-form" action="">
        <input type="hidden" name="attr_id" value="{{$info['attr_id'] ?? ''}}">
        <input type="hidden" id="attr_unit_id" value="{{$info['attr_unit_id'] ?? ''}}">
        <div class="layui-form-item">
            <div class="layui-form-item">
                <label class="layui-form-label">中文名称：</label>
                <div class="layui-input-inline" style="width: 20%">
                    <div class="layui-input-inline">
                        <input type="text" name="attr_name" autocomplete="off" class="layui-input"
                               value="{{$info['attr_name'] ?? ''}}">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">展示名称：</label>
                <div class="layui-input-inline" style="width: 20%">
                    <div class="layui-input-inline">
                        <input type="text" name="show_name" autocomplete="off" class="layui-input"
                               value="{{$info['show_name'] ?? ''}}">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">英文名称：</label>
                <div class="layui-input-inline" style="width: 20%">
                    <div class="layui-input-inline">
                        <input type="text" name="eng_name" autocomplete="off" class="layui-input"
                               value="{{$info['eng_name'] ?? ''}}">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('insert_type', '录入方式', $info['insert_type']??0, config('field.AttrInsertType'),['required'=>true]) !!}
            </div>

            <div class="layui-form-item" id="preference_div">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('preference', '偏向', $info['preference']??0, config('field.AttrPreference')) !!}
            </div>

            <div class="layui-form-item" id="unit_convert_id_div"
                 @if(!empty($info)&&$info['insert_type']==5) style="display: none" @endif
            >
                <div class="layui-inline">
                    <div class="layui-inline">
                        @inject('multiSelectorPresenter', 'App\Presenters\MultiSelectorPresenter')
                        {!! $multiSelectorPresenter->render('default_unit_convert_id', '默认单位', $info['default_unit_convert_id']??'', $unitConvertListForXmSelect, ['width' => '200']) !!}
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">核心参数：</label>
                <div class="layui-input-block">
                    <input type="radio" name="is_main" value="1" title="是"
                           @if(empty($info['is_main']) || $info['is_main'] === 1) checked @endif>
                    <input type="radio" name="is_main" value="0" title="否"
                           @if(isset($info['is_main']) && $info['is_main'] === 0) checked @endif>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">所属分类：</label>
                <div class="layui-input-block">
                    <div class="layui-input-inline" style="width: 230px">
                        <select name="parent_id" lay-filter="parent_id" lay-search>
                            <option value="">无</option>
                            @if(!empty($parentClassList))
                                @foreach($parentClassList as $k=>$v)
                                    <option value="{{$k}}"
                                            @if((!empty($info['class_info']['parent_id']) && $k == $info['class_info']['parent_id']) ||
                                            (!empty($_GET['parent_id'])&&$k == $_GET['parent_id'])) selected @endif>{{$v}}</option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                    <div class="layui-input-inline" style="width: 300px">
                        <select name="class_id" lay-search>
                            <option value="">无</option>
                            @if(!empty($classList))
                                @foreach($classList as $k=>$v)
                                    <option value="{{$k}}"
                                            @if((!empty($info['class_id']) && $k == $info['class_id']) ||
                                            (!empty($_GET['class_id'])&&$k == $_GET['class_id'])) selected @endif>{{$v}}</option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">属性状态：</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="显示"
                           @if(empty($info['status']) || $info['status'] === 1) checked @endif>
                    <input type="radio" name="status" value="2" title="隐藏"
                           @if(!empty($info['status']) && $info['status'] === 2) checked @endif>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">备注:</label>
                <div class="layui-input-block" style="padding-right: 20px">
                    <input type="text" name="remark" class="layui-input"
                           value="{{$info['remark'] ?? ''}}">
                </div>
            </div>

        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="button" class="layui-btn" lay-submit lay-filter="load">立即提交</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript" src="/js/web/classAttr/saveClassAttr.js?v={{time()}}"></script>
