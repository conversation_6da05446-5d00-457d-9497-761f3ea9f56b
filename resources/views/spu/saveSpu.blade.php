<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
@include('css')
@include('js')
<script type="text/javascript" charset="utf-8" src="/js/plugins/tinymce/tinymce.min.js"></script>

<style>
    .layui-form-select dl {
        z-index: 9999;
    }

    .layui-form-checkbox {
        max-height: 30px;
    }

    .scroll-button {
        position: fixed;
        top: 20px;
        right: 10px;
        z-index: 1000;
        opacity: 0.7;
    }
</style>
<div class="scroll-button">
    <button class="layui-btn layui-btn-normal" style="transform: rotate(-90deg);" onclick="scrollToBottom()">
        <i class="layui-icon  layui-icon-prev" style="font-size: 20px; color: white;"></i>
    </button>
</div>
<div class="layui-card" style="margin-top: 20px">
    <form class="layui-form" lay-filter="save_spu_form" x-data="spuForm">
        <div class="layui-card-body" style="padding-top: 10px">
            <input type="hidden" id="spu_id" name="spu_id" value="{{$spu['spu_id'] ?? ''}}"/>
            <div class="layui-form-item">
                <label class="layui-form-label">所属分类：</label>
                <div class="layui-input-block">
                    <div class="layui-input-inline" style="width: 230px">
                        <select name="class_id1" lay-filter="parent_id" lay-search>
                            <option value="">无</option>
                            @if(!empty($parentClassList))
                                @foreach($parentClassList as $k=>$v)
                                    <option value="{{$k}}"
                                            @if((!empty($spu['class_id1']) && $k == $spu['class_id1']) ||
                                            (!empty($_GET['class_id1'])&&$k == $_GET['class_id1'])) selected @endif>{{$v}}</option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                    <div class="layui-input-inline" style="width: 300px">
                        <input type="hidden" id="class_id2" value="{{$spu['class_id2'] ?? ''}}"
                               x-model.fill="class_id2">
                        <select name="class_id2" lay-filter="class_id2_select" lay-search>
                            <option value="">无</option>
                            @if(!empty($classList))
                                @foreach($classList as $k=>$v)
                                    <option value="{{$k}}"
                                            @if((!empty($spu['class_id2']) && $k == $spu['class_id2']) ||
                                            (!empty($_GET['class_id2'])&&$k == $_GET['class_id2'])) selected @endif>{{$v}}</option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                    <button type="button" id="jump_to_class" class="layui-btn layui-btn-sm"
                            style="@if(empty($spu['class_id2'])) display: none @endif">跳转分类列表
                    </button>
                </div>
            </div>


            <div class="layui-form-item">
                <label class="layui-form-label"><span style="color: red">* </span>SPU型号:</label>
                <div class="layui-input-block">
                    <input type="text" name="spu_name" value="{{ $spu['spu_name'] ?? '' }}"
                           placeholder="请输入SPU型号" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><span style="color: red">* </span>品牌：</label>
                <div class="layui-input-inline">
                    <input type="hidden" id="brand_id" value="{{$spu['brand_id'] ?? ''}}">
                    <input type="hidden" id="brand_name" value="{{$spu['brand_name'] ?? ''}}">
                    <div class="layui-input-inline" id="BrandSelector" style="width: 230px">

                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                @inject('singleSelectPresenter','App\Presenters\SingleSelectPresenter')
                {!! $singleSelectPresenter->render('status', 'SPU状态:',  isset($spu['status']) ? $spu['status'] : 1,config('field.SpuStatus')) !!}
            </div>
            <div class="layui-form-item">
                @inject('singleSelectPresenter','App\Presenters\SingleSelectPresenter')
                {!! $singleSelectPresenter->render('has_rohs', 'RoHS:',  isset($spu['has_rohs']) ? $spu['has_rohs'] : 0,[1=>'是',0=>'否']) !!}
            </div>

            <div class="layui-form-item">
                <div class="layui-col-md3">
                    @inject('imageUploadPresenter','App\Presenters\NewImageUploadPresenter')
                    {!! $imageUploadPresenter->render('images_l', 'SPU图片(大图)', !empty($spu['images_l']) ? $spu['images_l'] : '') !!}
                </div>
                <div class="layui-col-md9" style="padding-left:110px">
                    @if (!empty($spu)&&$spu['images_s'])
                        {!! $imageUploadPresenter->render('images_s', 'SPU图片(小图)', !empty($spu['images_s']) ? $spu['images_s'] : '') !!}
                    @else
                        <input type="hidden" name="images_s">
                    @endif
                </div>
                <p style="margin-left: 110px;font-weight: bolder">
                    支持扩展名png或者jpg，建议尺寸不要超过2M,猎芯网宽高：220x220，爱智平台宽高750x750</p>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">封装:</label>
                <div class="layui-input-block">
                    <input type="text" name="encap" value="{{ $spu['encap'] ?? '' }}"
                           placeholder="请输入SPU封装" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">商品名称:</label>
                <div class="layui-input-block">
                    <input type="text" name="spu_title" value="{{ $spu['spu_title'] ?? '' }}"
                           placeholder="请输入商品名称" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item" style="">
                <div class="layui-col-md4">
                    <label class="layui-form-label">
                        中文PDF:
                    </label>
                    <input type="hidden" id="pdf" name="pdf"
                           value="{{$spu['pdf']??''}}">
                    <div class="layui-upload-drag" id="upload_pdf">
                        <div id="pdfViewDiv">
                            <img class="{{$spu['pdf']??'layui-hide'}}"
                                 id="pdf_img"
                                 src="{{!empty($spu['pdf'])?'/assets/images/pdf.png':''}}"
                                 style="max-width: 80px">
                            <br>
                            <i class="layui-icon"></i>
                            <p>点击上传，或将文件拖拽到此处</p>
                        </div>
                    </div>
                    <p style="margin-left: 110px;font-weight: bolder">
                        支持上传文件格式为PDF，文件大小为50MB以内。
                        <span id="previewPDFLink">
                        @if(!empty($spu['pdf']))
                                <button class="layui-btn layui-btn-xs">
                                <a href="{{$spu['pdf']}}" style="color: white" target="_blank">点击查看PDF</a>
                                </button>
                            @endif
                    </span>
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-danger deletePdf"
                                id="deletePdfButton" style="margin-left: 7px;
                                @if(empty($spu['pdf']))
                                display:none
                                @endif
                                ">删除
                        </button>
                    </p>
                </div>
                <div class="layui-col-md6" style="margin-left: -10px">
                    <label class="layui-form-label">
                        英文PDF:
                    </label>
                    <input type="hidden" id="en_pdf_url" name="en_pdf_url"
                           value="{{$spu['en_pdf_url']??''}}">
                    <div class="layui-upload-drag" id="upload_pdf_en">
                        <div id="pdfEnViewDiv">
                            <img class="{{$spu['en_pdf_url']??'layui-hide'}}"
                                 id="pdf_en_img"
                                 src="{{!empty($spu['en_pdf_url'])?'/assets/images/pdf.png':''}}"
                                 style="max-width: 80px">
                            <br>
                            <i class="layui-icon"></i>
                            <p>点击上传，或将文件拖拽到此处</p>
                        </div>
                    </div>
                    <p style="margin-left: 110px;font-weight: bolder">
                        支持上传文件格式为PDF，文件大小为50MB以内。
                        <span id="previewPDFEnLink">
                        @if(!empty($spu['en_pdf_url']))
                                <button class="layui-btn layui-btn-xs">
                                <a href="{{$spu['en_pdf_url']}}" target="_blank"
                                   style="color: white">点击查看PDF</a>
                                </button>
                            @endif
                    </span>
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-danger deletePdfEn"
                                id="deletePdfEnButton" style="margin-left: 7px;
                                           @if(empty($spu['en_pdf_url']))
                                           display:none
                                           @endif
                                        ">删除
                        </button>
                    </p>
                </div>

            </div>
            <br>
            <div class="layui-form-item">
                {{--                    @inject('wangEditorPresenter','App\Presenters\WangEditorPresenter')--}}
                {{--                    {!! $wangEditorPresenter->render('spu_brief', '商品描述:', $spu['spu_brief'] ?? '')!!}--}}
                @inject('tinymcePresenter','App\Presenters\TinymcePresenter')
                {!! $tinymcePresenter->render('spu_brief', '商品描述:', $spu['spu_brief'] ?? '',['height'=> 400])!!}
            </div>

            <div class="layui-form-item">
                {{--                    @inject('wangEditorPresenter','App\Presenters\WangEditorPresenter')--}}
                {{--                    {!! $wangEditorPresenter->render('spu_detail', '详细描述:', $spu['spu_detail'] ?? '')!!}--}}
                @inject('tinymcePresenter','App\Presenters\TinymcePresenter')
                {!! $tinymcePresenter->render('spu_detail', '详细描述:', $spu['spu_detail'] ?? '',['height'=> 400])!!}
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">应用领域:</label>
                    <div class="layui-input-block">
                        <input type="hidden" name="bussiness_area"
                               value="{{$spu['bussiness_area'] ?? ''}}">
                        @foreach(config('field.SpuApplicationArea') as $k=>$type)
                            <input type="checkbox" name="bussiness_area[{{$k}}]"
                                   lay-skin="primary"
                                   title="{{$type}}" @if (!empty($spu) && in_array($k,$spu['bussiness_area_value']))
                                       checked
                                @endif>
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">应用级别:</label>
                <div class="layui-input-block">
                    <input type="text" name="application_level" value="{{ $spu['application_level'] ?? '' }}"
                           placeholder="请输入应用级别" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">ECCN:</label>
                <div class="layui-input-block">
                    <input type="text" name="eccn" value="{{ $spu['eccn'] ?? '' }}"
                           placeholder="请输入ECCN" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">湿敏等级:</label>
                <div class="layui-input-block">
                    <input type="text" name="humistor" value="{{ $spu['humistor'] ?? '' }}"
                           placeholder="请输入湿敏等级" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">系列:</label>
                <div class="layui-input-block">
                    <input type="text" name="series" value="{{ $spu['series'] ?? '' }}"
                           placeholder="请输入系列" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">生命周期:</label>
                <div class="layui-input-block">
                    <input type="text" name="lifecycle" value="{{ $spu['lifecycle'] ?? '' }}"
                           placeholder="请输入生命周期" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">制造商包装:</label>
                <div class="layui-input-block">
                    <input type="text" name="brand_pack" value="{{ $spu['brand_pack'] ?? '' }}"
                           placeholder="请输入制造商包装" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">标准包装量:</label>
                <div class="layui-input-block">
                    <input type="text" name="mpq" value="{{ $spu['mpq'] ?? '' }}"
                           placeholder="请输入标准包装量" autocomplete="off" class="layui-input">
                </div>
            </div>

            {{--spu多图--}}
            <div class="layui-form-item">
                <label class="layui-form-label">SPU图片:</label>

                <div class="layui-input-block">
                    <div id="myDropzone" class="dropzone"></div>
                    <input type="hidden" name="spu_image_list" id="spu_image_list"
                           value='{!!!empty($spu)? json_encode($spu['spu_image_list']) : ''!!}'>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">原厂标准交货期:</label>
                <div class="layui-input-block">
                    <input type="text" name="standard_lead_time" value="{{ $spu['standard_lead_time'] ?? '' }}"
                           placeholder="请输入原厂标准交货期" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">安装类型:</label>
                <div class="layui-input-block">
                    <input type="text" name="mounting_type" value="{{ $spu['mounting_type'] ?? '' }}"
                           placeholder="请输入安装类型" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">长度:</label>
                <div class="layui-input-block">
                    <input type="text" name="length" value="{{ $spu['length'] ?? '' }}"
                           placeholder="请输入长度" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">宽度:</label>
                <div class="layui-input-block">
                    <input type="text" name="width" value="{{ $spu['width'] ?? '' }}"
                           placeholder="请输入宽度" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">高度:</label>
                <div class="layui-input-block">
                    <input type="text" name="height" value="{{ $spu['height'] ?? '' }}"
                           placeholder="请输入高度" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">商品重量:</label>
                <div class="layui-input-block">
                    <input type="text" name="weight" value="{{ $spu['weight'] ?? '' }}"
                           placeholder="请输入商品重量" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">REACH状态:</label>
                <div class="layui-input-block">
                    <input type="text" name="reach_status" value="{{ $spu['reach_status'] ?? '' }}"
                           placeholder="请输入REACH状态" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">海关编码:</label>
                <div class="layui-input-block">
                    <input type="text" name="customs_code" value="{{ $spu['customs_code'] ?? '' }}"
                           placeholder="请输入海关编码" autocomplete="off" class="layui-input">
                </div>
            </div>
        </div>


        {{--参数管理,这里是用了alpine.js的,不懂可以去学习下,这里不详细说了--}}
        <div class="layui-row" style="padding-left: 50px;margin-top: 30px">
            <span style="font-size: 20px;margin-top: 10px;margin-left: -10px"><b>分类参数值管理</b></span>
        </div>
        <div id="attrs_manage_div" style="margin-left: 12px;margin-top: 30px">
            <div id="attr_list">
                <template x-for="(attr, index) in attrs" :key="index">
                    <div class="layui-col-md6">
                        <label class="layui-form-label" style="width: 100px">
                            <strong x-text="attr.attr_name"></strong>
                        </label>
                        <div class="layui-input-block">
                            <div class="layui-col-md4">
                                <input type="text"
                                       x-bind:style="attr.origin_value && attr.valid ? '' : (attr.origin_value?'border: 1px solid red':'')"
                                       x-model="attr.origin_value" x-on:blur="handleAttrValueChange($event,index)"
                                       :data-id="attr.attr_id"
                                       x-ref="inputRefs[index]"
                                       placeholder="请输入参数值" class="layui-input">
                            </div>
                            <div class="layui-col-md4" style="padding-left: 10px;padding-top: 3px">
                                <b>录入方式</b> : <span
                                    x-text="attr.insert_type_name"></span>
                            </div>
                            <div class="layui-col-md4" style="margin-left: -20px;padding-top: 3px">
                                <b>默认单位</b> : <span
                                    x-text="attr.unit_convert && Array.isArray(attr.unit_convert) ? attr.unit_convert.map(item => item.standard_unit_name).join(' | ') : ''"></span>
                            </div>
                        </div>
                        <p x-text="attr.valid_msg" style="height: 25px;margin-top: -5px;color: red"></p>
                    </div>
                </template>
            </div>
        </div>

        <div class="layui-form-item" style="padding-bottom: 30px;padding-top: 30px">
            <div class="layui-col-md4">_</div>
            <div class="layui-col-md4">
                <div class="layui-input-block">
                    <button type="button" id="saveSpu" x-bind:disabled="!canSubmit"
                            x-bind:class="canSubmit?'layui-btn':'layui-btn layui-btn-disabled'" lay-submit
                            lay-filter="saveSpu">立即提交
                    </button>
                    <button type="button" lay-submit lay-filter="cancel" class="layui-btn layui-btn-primary">取消
                    </button>
                </div>
            </div>
            <div class="layui-col-md4"></div>

        </div>
    </form>
</div>

</body>
</html>
<script type="text/html" id="operation">
    <button type="button" class="layui-btn layui-btn-xs updateSpu" lay-event="edit">编辑</button>
</script>
<script>
    function scrollToBottom() {
        window.scrollTo({
            top: document.body.scrollHeight,
            behavior: 'smooth'
        });
    }
</script>

<link href="/js/plugins/dropzone/dropzone.min.css" rel="stylesheet"/>
<script src="/js/plugins/dropzone/dropzone.min.js"></script>
<script type="text/javascript" src="/js/web/spu/saveSpu.js?v={{time()}}"></script>
