@include('css')
@include('js')
<div class="lay-box">
    <div class="layui-card">
        <div class="layui-card-body" style="padding-top: 20px">
            <form class="layui-form" action="" lay-filter="distribute_user_price_filter_form">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">客户名称：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="user_name" autocomplete="off" class="layui-input" placeholder="请输入客户名称">
                        </div>
                    </div>
                    <div class="layui-inline">
                        @inject('statusPresenter', 'App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('status', '状态', request()->get('status'), config('field.DistributeUserPriceStatus')) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('canalPresenter', 'App\Presenters\MultiSelectorPresenter')
                        {!! $canalPresenter->render('supplier_id', '猎芯渠道', request()->get('supplier_id'), $supplierListForXmSelect , ['radio' => false,'width' => '200']) !!}
                    </div>
                    <div class="layui-inline">
                        @inject('timePresenter', 'App\Presenters\TimeIntervalPresenter')
                        {!! $timePresenter->render('create_time', '创建时间') !!}
                    </div>
                </div>
                <br>
                <div class="layui-form-item" style="text-align: center;">
                    <div class="layui-inline">
                        <button class="layui-btn layui-btn-sm" lay-submit lay-filter="list" style="margin-left: 20px">
                            查询
                        </button>
                        <button class="layui-btn layui-btn-primary layui-btn-sm reset-button" lay-submit lay-filter="list" style="margin-left: 18px">
                            重置
                        </button>
                    </div>
                </div>
            </form>
            <div class="layui-inline" style="margin-left: 1px">
                <button class="layui-btn layui-btn-sm" id="add_distribute_user_price">新增客户价格分发</button>
                </button>
            </div>
            <table class="layui-table" id="list" lay-filter="list"></table>

        </div>
    </div>
</div>
<script type="text/html" id="operation">
    <button type="button" class="layui-btn layui-btn-xs edit_distribute_user_price" value="@{{ d.id }}" lay-event="edit_distribute_user_price">编辑</button>
    <button class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete_distribute_user_price">删除</button>
    @{{# if(d.status == 1){ }}
        <button class="layui-btn layui-btn-xs layui-btn-danger" lay-event="disable_distribute_user_price">禁用</button>
        @{{# } else { }}
            <button class="layui-btn layui-btn-xs" lay-event="enable_distribute_user_price">启用</button>
            @{{# } }}
            <button class="layui-btn layui-btn-xs" lay-event="copy_distribute_user_price">复制</button>
</script>
</div>
<script type="text/javascript" src="/js/web/distributeUserPrice/distributeUserPriceList.js?v={{time()}}"></script>
