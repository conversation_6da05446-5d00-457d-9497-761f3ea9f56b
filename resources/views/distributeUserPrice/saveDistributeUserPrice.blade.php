@include('css')
@include('js')
<style>
    .layui-form-label {
        width: 110px;
    }

    xm-select {
        height: auto !important;
    }

    .required-field {
        color: red;
    }

    .table-container {
        margin-top: 20px;
        margin-bottom: 20px;
    }

    .table-container table {
        width: 100%;
        border-collapse: collapse;
    }

    .table-container th,
    .table-container td {
        border: 1px solid #e6e6e6;
        padding: 10px;
        text-align: center;
    }

    .table-container th {
        background-color: #f2f2f2;
    }

    .action-buttons {
        text-align: center;
        margin-top: 20px;
    }

    .action-buttons .layui-btn {
        margin: 0 10px;
    }

</style>
<section class="section-page" style="margin-top: 20px">
    <form class="layui-form" action="" lay-filter="saveDistributeUserPriceForm">
        <input type="hidden" name="id" value="{{ $user['id'] ?? '' }}">

        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label"><span class="required-field">*</span>客户名称：</label>
                <div class="layui-input-inline">
                    <input type="text" name="user_name" value="{{ $user['user_name'] ?? '' }}" autocomplete="off" class="layui-input {{ !empty($user['id']) && !request()->input('copy') ? 'layui-disabled' : '' }}" placeholder="请填写客户名称" {{ !empty($user['id']) && !request()->input('copy') ? 'readonly' : '' }}>
                </div>
            </div>

            <div class="layui-inline">
                @inject('multiSelectorPresenter', 'App\Presenters\MultiSelectorPresenter')
                {!! $multiSelectorPresenter->render('price_type', '对接价格 : ',$user['price_type'] ?? '',$priceTypeList,['required'=>true]) !!}
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">APP_KEY</label>
                <div class="layui-input-inline">
                    <input type="text" name="app_key" value="{{ $user['app_key'] ?? '' }}" autocomplete="off" class="layui-input {{ request()->user->userId != 1000 && !request()->input('copy') ? 'layui-disabled' : '' }}" placeholder="请填写APP_KEY" {{  request()->user->userId != 1000 && !request()->input('copy') ? 'readonly' : '' }}>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">APP_SECRET</label>
                <div class="layui-input-inline">
                    <input type="text" name="app_secret" value="{{ $user['app_secret'] ?? '' }}" autocomplete="off" class="layui-input {{ request()->user->userId != 1000 && !request()->input('copy') ? 'layui-disabled' : '' }}" placeholder="请填写API_SECRET" {{  request()->user->userId != 1000 && !request()->input('copy') ? 'readonly' : '' }}>
                </div>
            </div>
        </div>
        <div class="table-container" style="padding-left: 50px;padding-right: 50px">
            <table id="priceRuleTable">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>操作</th>
                        <th><span class="required-field">*</span>猎芯渠道</th>
                        <th><span class="required-field">*</span>销售协议价系数</th>
                        <th><span class="required-field">*</span>销售指导价系数</th>
                    </tr>
                </thead>
                <tbody>
                    @if(empty($user))
                    <tr class="rule-row">
                        <td class="row-number">1</td>
                        <td>
                            <button type="button" class="btn-add layui-btn layui-btn-sm">新增</button>
                            <button type="button" class="btn-delete layui-btn layui-btn-sm layui-btn-danger">删除</button>
                        </td>
                        <td>
                            <select name="supplier_id[]" id="supplier_id" class="supplier-select" lay-filter="supplier_id">
                                <option value="">请选择</option>
                                @foreach($supplierList as $key => $value)
                                <option value="{{ $value['supplier_id'] }}" agreement-value="{{ $value['agreement_price_coefficient'] }}" guide-value="{{ $value['guide_price_coefficient'] }}">{{ $value['supplier_name'] }}</option>
                                @endforeach
                            </select>
                        </td>
                        <td>
                            <div style="display: flex; align-items: center;">
                                <input type="number" name="agreement_price_coefficient[]" class="layui-input agreement-price" lay-verify="number" value='' placeholder="">
                                <span style="margin-left: 5px;">%</span>
                            </div>
                        </td>
                        <td>
                            <div style="display: flex; align-items: center;">
                                <input type="number" name="guide_price_coefficient[]" class="layui-input guide-price" lay-verify="number" value='' placeholder="">
                                <span style="margin-left: 5px;">%</span>
                            </div>
                        </td>
                    </tr>
                    @else
                    @foreach($priceList as $key => $value)
                    <tr class="rule-row">
                        <td class="row-number">{{ $key + 1 }}</td>
                        <td>
                            <button type="button" class="btn-add layui-btn layui-btn-sm">新增</button>
                            <button type="button" class="btn-delete layui-btn layui-btn-sm layui-btn-danger">删除</button>
                        </td>
                        <td>
                            <input type="hidden" name="price_id[]" value="{{ $value['id'] }}">
                            <select name="supplier_id[]" id="supplier_id" class="supplier-select" lay-filter="supplier_id">
                                <option value="">请选择</option>
                                @foreach($supplierList as $key => $supplier)
                                <option value="{{ $supplier['supplier_id'] }}" agreement-value="{{ $supplier['agreement_price_coefficient'] }}" guide-value="{{ $supplier['guide_price_coefficient'] }}" {{ ($supplier['supplier_id'] == $value['supplier_id'] || $supplier['supplier_id'] == $value['supplier_code']) ? 'selected' : '' }}>{{ $supplier['supplier_name'] }}</option>
                                @endforeach
                            </select>
                        </td>
                        <td>
                            <div style="display: flex; align-items: center;">
                                <input type="number" name="agreement_price_coefficient[]" class="layui-input agreement-price" lay-verify="number" value="{{ $value['agreement_price_coefficient'] }}" placeholder="">
                                <span style="margin-left: 5px;">%</span>
                            </div>
                        </td>
                        <td>
                            <div style="display: flex; align-items: center;">
                                <input type="number" name="guide_price_coefficient[]" class="layui-input guide-price" lay-verify="number" value="{{ $value['guide_price_coefficient'] }}" placeholder="">
                                <span style="margin-left: 5px;">%</span>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                    @endif

                </tbody>
            </table>
        </div>

        <div class="action-buttons">
            <button type="button" class="layui-btn" lay-submit lay-filter="saveForm">保存</button>
            <button type="button" class="layui-btn layui-btn-primary" id="cancelBtn">取消</button>
        </div>
    </form>
</section>

<script type="text/javascript" src="/js/web/distributeUserPrice/saveDistributeUserPrice.js?v={{ time() }}"></script>
