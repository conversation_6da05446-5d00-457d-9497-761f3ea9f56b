@include('css')
@include('js')

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-collape">
                <form class="layui-form layui-box" action="" lay-filter="searchForm">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">创建时间</label>
                            <div class="layui-input-inline">
                                <input type="text" name="create_time" id="create_time" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">状态</label>
                            <div class="layui-input-inline">
                                <select name="status" lay-filter="status">
                                    <option value="">全部</option>
                                    <option value="0">待处理</option>
                                    <option value="1">已处理</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">型号</label>
                            <div class="layui-input-inline">
                                <input type="text" name="goods_name" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <button class="layui-btn layui-btn-sm" lay-submit lay-filter="searchForm">查询</button>
                            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" lay-submit lay-filter="resetBtn">重置</button>
                            {{-- <button type="button" class="layui-btn layui-btn-normal layui-btn-sm" id="addBtn">新增</button> --}}
                        </div>
                    </div>
                </form>
            </div>
            <table id="dataTable" lay-filter="dataTable"></table>

            <script type="text/html" id="toolbar">
                <div class="layui-btn-container">
                    <button class="layui-btn layui-btn-sm" lay-event="edit">编辑</button>
                    @{{# if(d.status == 0){ }}
                        <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="process">标记为已处理</button>
                        @{{# } else { }}
                            <button class="layui-btn layui-btn-sm layui-btn-warm" lay-event="pending">标记为待处理</button>
                            @{{# } }}
                                <button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="delete">删除</button>
                </div>

            </script>
        </div>
    </div>
</div>

<script type="text/javascript" src="/js/web/classManagement/classManagementList.js?v={{ time() }}"></script>
