@include('css')
@include('js')
<section class="layui-card" style="margin-top: 10px;padding-bottom: 30px">
    <form class="layui-form" action="" lay-filter="saveForm">
        <input type="hidden" name="id" value="{{ $classManagement['id'] ?? '' }}">
        <div class="layui-form-item" style="margin-top: 20px">
            <div class="layui-row">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">型号：</label>
                        <div class="layui-form-mid">{{ $goods_name }}</div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">品牌：</label>
                        <div class="layui-form-mid">{{ $brand_name }}</div>
                    </div>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">SPU：</label>
                        <div class="layui-form-mid">{{ $spu_id }}</div>
                    </div>
                </div>
                {{-- <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">SKU：</label>
                        <div class="layui-form-mid">{{ $sku_id }}</div>
                    </div>
                </div> --}}
            </div>
            <div class="layui-row">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">当前分类：</label>
                        <div class="layui-form-mid">{{ $current_class }}</div>
                    </div>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">更新分类：</label>
                        <div class="layui-input-inline">
                            <select name="class_id" lay-filter="class_id">
                                <option value="">请选择</option>
                                @foreach($classList as $class)
                                    <option value="{{ $class['id'] }}">{{ $class['name'] }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
                </div>
        </div>
        <div class="layui-form-item" style="text-align: center; margin-top: 30px;">
            <button type="button" class="layui-btn layui-btn-normal layui-btn-sm" lay-submit lay-filter="saveForm">更新分类</button>
            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" style="margin-left: 10px;" lay-submit lay-filter="closeForm">取消</button>
        </div>
    </form>
</section>

<script type="text/javascript" src="/js/web/classManagement/saveClassManagement.js?v={{ time() }}"></script>
