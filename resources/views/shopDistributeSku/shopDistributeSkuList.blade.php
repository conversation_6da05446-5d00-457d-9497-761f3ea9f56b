@include('css')
@include('js')

<style>
    .layui-table-cell {
        height: auto;
        overflow: visible;
        text-overflow: inherit;
        white-space: normal;
    }

    .custom .layui-layer-content .table-status {
        position: relative;
        margin: 0 !important;
        z-index: 2;
    }

    .custom .layui-layer-content .table-status th {
        white-space: nowrap;
    }

    .layui-layer-content {
        padding: 3px 3px !important;
    }

    .custom .layui-layer-content .table-status td,
    .custom .layui-layer-content .table-status th {
        padding: 3px 3px !important;
    }

</style>
<div class="lay-box">

    <div class="layui-card">
        <div class="layui-card-body" style="">
            <form class="layui-form" action="" lay-filter="sku_list_form">
                <div class="layui-collapse" style="margin-bottom: 10px;padding-top: 11px">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">ID</label>
                            <div class="layui-input-inline">
                                <input type="text" name="id" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            @inject('statusPresenter','App\Presenters\StatusPresenter')
                            {!! $statusPresenter->render('shop_id', '店铺', '', $shopList) !!}
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">SKUID</label>
                            <div class="layui-input-inline">
                                <input type="text" name="sku_id" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            @inject('statusPresenter','App\Presenters\StatusPresenter')
                            {!! $statusPresenter->render('status', '状态', '', config('field.ShopDistributeSkuStatus')) !!}
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">型号</label>
                            <div class="layui-input-inline">
                                <input type="text" name="spu_name" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            @inject('multiSelectPresenter','App\Presenters\MultiSelectorPresenter')
                            {!! $multiSelectPresenter->render('supplier_id', '渠道', '', $supplierList) !!}
                        </div>
                        <div class="layui-inline">
                            @inject('multiSelectPresenter','App\Presenters\MultiSelectorPresenter')
                            {!! $multiSelectPresenter->render('standard_brand_id', '标准品牌', '', $standardBrandList) !!}
                        </div>
                        <div class="layui-inline">
                            @inject('multiSelectPresenter','App\Presenters\MultiSelectorPresenter')
                            {!! $multiSelectPresenter->render('class_id2', '分类', '', $classList) !!}
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">库存数量</label>
                            <div class="layui-input-inline">
                                <div class="layui-row">
                                    <div class="layui-col-xs6" style="width: 94px">
                                        <select name="stock_compare_type">
                                            <option value="">请选择</option>
                                            <option value="eq"> 等于 </option>
                                            <option value="neq"> 不等于 </option>
                                            <option value="lt"> 小于 </option>
                                            <option value="lte"> 小于等于 </option>
                                            <option value="gt"> 大于 </option>
                                            <option value="gte"> 大于等于 </option>
                                        </select>
                                    </div>
                                    <div class="layui-col-xs6" style="width: 64px;">
                                        <input type="text" value="" name="stock_num" placeholder="数量" autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">供应商编码</label>
                            <div class="layui-input-inline">
                                <input type="text" name="supplier_code" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            @inject('statusPresenter','App\Presenters\StatusPresenter')
                            {!! $statusPresenter->render('is_invalid_title', '商品标题标红数据', '', [1=>'标红',-1=>'未标红']) !!}
                        </div>
                        <div class="layui-inline">
                            @inject('timePresenter','App\Presenters\TimeIntervalPresenter')
                            {!! $timePresenter->render('create_time','创建时间') !!}
                        </div>
                    </div>
                </div>
                <div class='layui-form-item'>

                    <div class="layui-form-item" style="text-align: center;">
                        <div class="layui-inline">
                            <button class="layui-btn layui-btn-sm" lay-submit lay-filter="shopDistributeSkuList" style="margin-left: 50px">
                                查询
                            </button>
                            <button type="button" class="layui-btn layui-btn-sm" lay-submit lay-filter="reset">
                                重置
                            </button>
                        </div>
                    </div>
                </div>
            </form>

            <div class="layui-inline" style="margin-left: 1px">
                <button type="button" class="layui-btn layui-btn-sm" id="batchSaveShopDistributeSku">点击去新增推送选品
                </button>
                <button type="button" class="layui-btn layui-btn-sm" id="exportShopDistributeSku">导出
                </button>

                <button type="button" class="layui-btn layui-btn-sm" id="importShopDistributeSku">导入
                </button>
                <button type="button" class="layui-btn layui-btn-sm" id="viewImportShopDistributeSkuLog">查看导入日志
                </button>
                <a href="/template/分发池SKU导入.xlsx" class="layui-btn layui-btn-sm layui-btn-primary" download="分发池SKU导入.xlsx">下载导入模板</a>

            </div>
            <table class="layui-table" id="shopDistributeSkuList" lay-filter="shopDistributeSkuList"></table>

        </div>
    </div>
</div>

<script type="text/html" id="operation">
    <button class="layui-btn layui-btn-xs layui-btn-danger" value="@{{ d.id }}" lay-event="delete_sku">
        <strong>删除</strong></button>

</script>

<script type="text/html" id="statusTpl">
    <input type="checkbox" name="status" value="@{{d.id}}" lay-skin="switch" lay-text="启用|禁用" lay-filter="statusSwitch" @{{ d.status == 1 ? 'checked' : '' }}>

</script>

<script type="text/javascript" src="/js/web/shopDistributeSku/shopDistributeSkuList.js?v={{time()}}"></script>
