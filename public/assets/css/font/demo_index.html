<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i2/O1CN01ZyAlrn1MwaMhqz36G_!!6000000001499-73-tps-64-64.ico" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01EYTRnJ297D6vehehJ_!!6000000008020-55-tps-64-64.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=3146917" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe668;</span>
                <div class="name">cnav7</div>
                <div class="code-name">&amp;#xe668;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe669;</span>
                <div class="name">cnav6</div>
                <div class="code-name">&amp;#xe669;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66a;</span>
                <div class="name">cnav9</div>
                <div class="code-name">&amp;#xe66a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66b;</span>
                <div class="name">cnav10</div>
                <div class="code-name">&amp;#xe66b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66c;</span>
                <div class="name">cnav1</div>
                <div class="code-name">&amp;#xe66c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66d;</span>
                <div class="name">cnav8</div>
                <div class="code-name">&amp;#xe66d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66e;</span>
                <div class="name">cnav4</div>
                <div class="code-name">&amp;#xe66e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66f;</span>
                <div class="name">cnav3</div>
                <div class="code-name">&amp;#xe66f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe670;</span>
                <div class="name">cnav5</div>
                <div class="code-name">&amp;#xe670;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe671;</span>
                <div class="name">cnav2</div>
                <div class="code-name">&amp;#xe671;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe661;</span>
                <div class="name">uploadx</div>
                <div class="code-name">&amp;#xe661;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe657;</span>
                <div class="name">downx</div>
                <div class="code-name">&amp;#xe657;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe658;</span>
                <div class="name">editx</div>
                <div class="code-name">&amp;#xe658;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe659;</span>
                <div class="name">nav3</div>
                <div class="code-name">&amp;#xe659;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65a;</span>
                <div class="name">nav2</div>
                <div class="code-name">&amp;#xe65a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65b;</span>
                <div class="name">errx</div>
                <div class="code-name">&amp;#xe65b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65c;</span>
                <div class="name">nav6</div>
                <div class="code-name">&amp;#xe65c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65d;</span>
                <div class="name">nav8</div>
                <div class="code-name">&amp;#xe65d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65e;</span>
                <div class="name">nav5</div>
                <div class="code-name">&amp;#xe65e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65f;</span>
                <div class="name">nav7</div>
                <div class="code-name">&amp;#xe65f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe660;</span>
                <div class="name">nav4</div>
                <div class="code-name">&amp;#xe660;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe662;</span>
                <div class="name">qrtkx</div>
                <div class="code-name">&amp;#xe662;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe663;</span>
                <div class="name">subx</div>
                <div class="code-name">&amp;#xe663;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe664;</span>
                <div class="name">nav1</div>
                <div class="code-name">&amp;#xe664;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe665;</span>
                <div class="name">detailx</div>
                <div class="code-name">&amp;#xe665;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe666;</span>
                <div class="name">delx</div>
                <div class="code-name">&amp;#xe666;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe667;</span>
                <div class="name">coppx</div>
                <div class="code-name">&amp;#xe667;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1644817575926') format('woff2'),
       url('iconfont.woff?t=1644817575926') format('woff'),
       url('iconfont.ttf?t=1644817575926') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-cnav7"></span>
            <div class="name">
              cnav7
            </div>
            <div class="code-name">.icon-cnav7
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-cnav6"></span>
            <div class="name">
              cnav6
            </div>
            <div class="code-name">.icon-cnav6
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-cnav9"></span>
            <div class="name">
              cnav9
            </div>
            <div class="code-name">.icon-cnav9
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-cnav10"></span>
            <div class="name">
              cnav10
            </div>
            <div class="code-name">.icon-cnav10
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-cnav1"></span>
            <div class="name">
              cnav1
            </div>
            <div class="code-name">.icon-cnav1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-cnav8"></span>
            <div class="name">
              cnav8
            </div>
            <div class="code-name">.icon-cnav8
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-cnav4"></span>
            <div class="name">
              cnav4
            </div>
            <div class="code-name">.icon-cnav4
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-cnav3"></span>
            <div class="name">
              cnav3
            </div>
            <div class="code-name">.icon-cnav3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-cnav5"></span>
            <div class="name">
              cnav5
            </div>
            <div class="code-name">.icon-cnav5
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-cnav2"></span>
            <div class="name">
              cnav2
            </div>
            <div class="code-name">.icon-cnav2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-uploadx"></span>
            <div class="name">
              uploadx
            </div>
            <div class="code-name">.icon-uploadx
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-downx"></span>
            <div class="name">
              downx
            </div>
            <div class="code-name">.icon-downx
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-editx"></span>
            <div class="name">
              editx
            </div>
            <div class="code-name">.icon-editx
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-nav3"></span>
            <div class="name">
              nav3
            </div>
            <div class="code-name">.icon-nav3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-nav2"></span>
            <div class="name">
              nav2
            </div>
            <div class="code-name">.icon-nav2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-errx"></span>
            <div class="name">
              errx
            </div>
            <div class="code-name">.icon-errx
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-nav6"></span>
            <div class="name">
              nav6
            </div>
            <div class="code-name">.icon-nav6
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-nav8"></span>
            <div class="name">
              nav8
            </div>
            <div class="code-name">.icon-nav8
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-nav5"></span>
            <div class="name">
              nav5
            </div>
            <div class="code-name">.icon-nav5
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-nav7"></span>
            <div class="name">
              nav7
            </div>
            <div class="code-name">.icon-nav7
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-nav4"></span>
            <div class="name">
              nav4
            </div>
            <div class="code-name">.icon-nav4
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qrtkx"></span>
            <div class="name">
              qrtkx
            </div>
            <div class="code-name">.icon-qrtkx
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-subx"></span>
            <div class="name">
              subx
            </div>
            <div class="code-name">.icon-subx
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-nav1"></span>
            <div class="name">
              nav1
            </div>
            <div class="code-name">.icon-nav1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-detailx"></span>
            <div class="name">
              detailx
            </div>
            <div class="code-name">.icon-detailx
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-delx"></span>
            <div class="name">
              delx
            </div>
            <div class="code-name">.icon-delx
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-coppx"></span>
            <div class="name">
              coppx
            </div>
            <div class="code-name">.icon-coppx
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cnav7"></use>
                </svg>
                <div class="name">cnav7</div>
                <div class="code-name">#icon-cnav7</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cnav6"></use>
                </svg>
                <div class="name">cnav6</div>
                <div class="code-name">#icon-cnav6</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cnav9"></use>
                </svg>
                <div class="name">cnav9</div>
                <div class="code-name">#icon-cnav9</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cnav10"></use>
                </svg>
                <div class="name">cnav10</div>
                <div class="code-name">#icon-cnav10</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cnav1"></use>
                </svg>
                <div class="name">cnav1</div>
                <div class="code-name">#icon-cnav1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cnav8"></use>
                </svg>
                <div class="name">cnav8</div>
                <div class="code-name">#icon-cnav8</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cnav4"></use>
                </svg>
                <div class="name">cnav4</div>
                <div class="code-name">#icon-cnav4</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cnav3"></use>
                </svg>
                <div class="name">cnav3</div>
                <div class="code-name">#icon-cnav3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cnav5"></use>
                </svg>
                <div class="name">cnav5</div>
                <div class="code-name">#icon-cnav5</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cnav2"></use>
                </svg>
                <div class="name">cnav2</div>
                <div class="code-name">#icon-cnav2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-uploadx"></use>
                </svg>
                <div class="name">uploadx</div>
                <div class="code-name">#icon-uploadx</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-downx"></use>
                </svg>
                <div class="name">downx</div>
                <div class="code-name">#icon-downx</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-editx"></use>
                </svg>
                <div class="name">editx</div>
                <div class="code-name">#icon-editx</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-nav3"></use>
                </svg>
                <div class="name">nav3</div>
                <div class="code-name">#icon-nav3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-nav2"></use>
                </svg>
                <div class="name">nav2</div>
                <div class="code-name">#icon-nav2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-errx"></use>
                </svg>
                <div class="name">errx</div>
                <div class="code-name">#icon-errx</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-nav6"></use>
                </svg>
                <div class="name">nav6</div>
                <div class="code-name">#icon-nav6</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-nav8"></use>
                </svg>
                <div class="name">nav8</div>
                <div class="code-name">#icon-nav8</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-nav5"></use>
                </svg>
                <div class="name">nav5</div>
                <div class="code-name">#icon-nav5</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-nav7"></use>
                </svg>
                <div class="name">nav7</div>
                <div class="code-name">#icon-nav7</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-nav4"></use>
                </svg>
                <div class="name">nav4</div>
                <div class="code-name">#icon-nav4</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qrtkx"></use>
                </svg>
                <div class="name">qrtkx</div>
                <div class="code-name">#icon-qrtkx</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-subx"></use>
                </svg>
                <div class="name">subx</div>
                <div class="code-name">#icon-subx</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-nav1"></use>
                </svg>
                <div class="name">nav1</div>
                <div class="code-name">#icon-nav1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-detailx"></use>
                </svg>
                <div class="name">detailx</div>
                <div class="code-name">#icon-detailx</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-delx"></use>
                </svg>
                <div class="name">delx</div>
                <div class="code-name">#icon-delx</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-coppx"></use>
                </svg>
                <div class="name">coppx</div>
                <div class="code-name">#icon-coppx</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
