@font-face {
  font-family: "iconfont"; /* Project id 3146917 */
  src: url('iconfont.woff2?t=1644817575926') format('woff2'),
       url('iconfont.woff?t=1644817575926') format('woff'),
       url('iconfont.ttf?t=1644817575926') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-cnav7:before {
  content: "\e668";
}

.icon-cnav6:before {
  content: "\e669";
}

.icon-cnav9:before {
  content: "\e66a";
}

.icon-cnav10:before {
  content: "\e66b";
}

.icon-cnav1:before {
  content: "\e66c";
}

.icon-cnav8:before {
  content: "\e66d";
}

.icon-cnav4:before {
  content: "\e66e";
}

.icon-cnav3:before {
  content: "\e66f";
}

.icon-cnav5:before {
  content: "\e670";
}

.icon-cnav2:before {
  content: "\e671";
}

.icon-uploadx:before {
  content: "\e661";
}

.icon-downx:before {
  content: "\e657";
}

.icon-editx:before {
  content: "\e658";
}

.icon-nav3:before {
  content: "\e659";
}

.icon-nav2:before {
  content: "\e65a";
}

.icon-errx:before {
  content: "\e65b";
}

.icon-nav6:before {
  content: "\e65c";
}

.icon-nav8:before {
  content: "\e65d";
}

.icon-nav5:before {
  content: "\e65e";
}

.icon-nav7:before {
  content: "\e65f";
}

.icon-nav4:before {
  content: "\e660";
}

.icon-qrtkx:before {
  content: "\e662";
}

.icon-subx:before {
  content: "\e663";
}

.icon-nav1:before {
  content: "\e664";
}

.icon-detailx:before {
  content: "\e665";
}

.icon-delx:before {
  content: "\e666";
}

.icon-coppx:before {
  content: "\e667";
}

