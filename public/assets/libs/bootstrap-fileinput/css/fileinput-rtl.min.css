/*!
 * bootstrap-fileinput v5.2.7
 * http://plugins.krajee.com/file-input
 *
 * <PERSON><PERSON><PERSON> RTL (Right To Left) default styling for bootstrap-fileinput.
 *
 * Author: <PERSON><PERSON><PERSON>
 * Copyright: 2014 - 2021, <PERSON><PERSON><PERSON>, Krajee.com
 *
 * Licensed under the BSD-3-Clause
 * https://github.com/kartik-v/bootstrap-fileinput/blob/master/LICENSE.md
 */.kv-rtl .close,.kv-rtl .krajee-default .file-actions,.kv-rtl .krajee-default .file-other-error{float:left}.kv-rtl .krajee-default .file-drag-handle,.kv-rtl .krajee-default .file-upload-indicator,.kv-rtl .krajee-default.file-preview-frame{float:right}.kv-rtl .file-error-message pre,.kv-rtl .file-error-message ul,.kv-rtl .file-zoom-dialog{text-align:right}.kv-rtl .file-zoom-dialog .kv-desc-hide{float:left}.kv-rtl{direction:rtl}.kv-rtl .floating-buttons{left:10px;right:auto}.kv-rtl .floating-buttons .btn-kv{margin-left:0;margin-right:3px}.kv-rtl .file-caption-icon{left:auto;padding:.5rem;right:4px}.kv-rtl .file-drop-zone{margin:12px 12px 12px 15px}.kv-rtl .btn-kv-prev{right:0;left:auto}.kv-rtl .btn-kv-next{left:0;right:auto}.kv-rtl .float-right,.kv-rtl .pull-right{float:left!important}.kv-rtl .float-left,.kv-rtl .pull-left{float:right!important}.kv-rtl .kv-zoom-title{direction:ltr}.kv-rtl .krajee-default.file-preview-frame{box-shadow:-1px 1px 5px 0 #a2958a}.kv-rtl .krajee-default.file-preview-frame:not(.file-preview-error):hover{box-shadow:-3px 3px 5px 0 #333}.kv-rtl .kv-zoom-actions .btn-kv{margin-left:0;margin-right:3px}.kv-rtl .file-caption.icon-visible .file-caption-name{padding-left:0;padding-right:30px}.kv-rtl .input-group>.input-group-btn:last-child>.btn:last-child{border-radius:4px 0 0 4px!important}.kv-rtl .input-group>.input-group-btn:first-child>.btn:first-child{border-radius:0 4px 4px 0!important}.kv-rtl .input-group>.btn:last-child,.kv-rtl .input-group>.form-control:last-child,.kv-rtl .input-group>.input-group-append:last-child>.btn:last-child{border-radius:.25rem 0 0 .25rem!important}.kv-rtl .input-group>.form-control:first-child,.kv-rtl .input-group>.input-group-prepend:first-child>.btn:first-child,.kv-rtl .input-group>.input-group>.btn:first-child{border-radius:0 .25rem .25rem 0!important}.kv-rtl .input-group>.file-caption-icon:first-child~.form-control:last-child{border-radius:.25rem!important}.kv-rtl .btn-file input[type=file]{left:auto;right:0;text-align:left;background:100% 0 none}