//获取地址栏参数
function getRequest(value) {
  var url = decodeURI(location.search);
  var object = {};
  if (url.indexOf("?") != -1) {
    var str = url.substr(1);
    var strs = str.split("&");
    for (var i = 0; i < strs.length; i++) {
      object[strs[i].split("=")[0]] = strs[i].split("=")[1]
    }
  }
  return object[value];
}

//单击行勾选checkbox事件
$(document).on("click", ".layui-table-body table.layui-table tbody tr", function () {
  var index = $(this).attr('data-index');
  var tableBox = $(this).parents('.layui-table-box');
  //存在固定列
  if (tableBox.find(".layui-table-fixed.layui-table-fixed-l").length > 0) {
    tableDiv = tableBox.find(".layui-table-fixed.layui-table-fixed-l");
  } else {
    tableDiv = tableBox.find(".layui-table-body.layui-table-main");
  }
  var CheckLength = tableDiv.find("tr[data-index=" + index + "]").find(
    "td div.layui-form-checked").length;

  var checkCell = tableDiv.find("tr[data-index=" + index + "]").find(
    "td div.laytable-cell-checkbox div.layui-form-checkbox I");
  if (checkCell.length > 0) {
    checkCell.click();
  }

});

$(document).on("click", "td div.laytable-cell-checkbox div.layui-form-checkbox", function (e) {
  e.stopPropagation();
});

var check_arr = [];
var check_arr1 = [];

//处理列表多选
function checkGo(table, tag, table_data) {
  check_arr = []
  table.on('checkbox(' + tag + ')', function (obj) {
    if (obj.checked == true) {
      if (obj.type == 'one') {
        check_arr.push(obj.data);
      } else {
        for (var i = 0; i < table_data.length; i++) {
          check_arr = JSON.parse(JSON.stringify(table_data));
        }
      }
    } else {
      if (obj.type == 'one') {
        for (var i = 0; i < check_arr.length; i++) {
          if (check_arr[i].id == obj.data.id) {
            check_arr.splice(i, 1);
          }
        }
      } else {
        check_arr = []
      }
    }
    //拉黑就不用变色了
    if (obj.checked === true && obj.type === 'all') {
      //点击全选,拉黑的不用选上
      $('.layui-table-body table.layui-table tbody tr ').addClass('layui-table-click');
    } else if (obj.checked === false && obj.type === 'all') {
      //点击全不选
      $('.layui-table-body table.layui-table tbody tr').removeClass('layui-table-click');
    } else if (obj.checked === true && obj.type === 'one') {
      //点击单行
      if (obj.checked === true) {
        obj.tr.addClass('layui-table-click');
      } else {
        obj.tr.removeClass('layui-table-click');
      }
    } else if (obj.checked === false && obj.type === 'one') {
      //点击全选之后点击单行
      if (obj.tr.hasClass('layui-table-click')) {
        obj.tr.removeClass('layui-table-click');
      }
    }
  })


}

//处理列表多选
function checkGo1(table, tag, table_data) {
  check_arr1 = []
  table.on('checkbox(' + tag + ')', function (obj) {
    if (obj.checked == true) {
      if (obj.type == 'one') {
        check_arr1.push(obj.data);
      } else {
        for (var i = 0; i < table_data.length; i++) {
          check_arr1 = JSON.parse(JSON.stringify(table_data));
        }
      }
    } else {
      if (obj.type == 'one') {
        for (var i = 0; i < check_arr1.length; i++) {
          if (check_arr1[i].id == obj.data.id) {
            check_arr1.splice(i, 1);
          }
        }
      } else {
        check_arr1 = []
      }
    }
    //拉黑就不用变色了
    if (obj.checked === true && obj.type === 'all') {
      //点击全选,拉黑的不用选上
      $('.layui-table-body table.layui-table tbody tr ').addClass('layui-table-click');
    } else if (obj.checked === false && obj.type === 'all') {
      //点击全不选
      $('.layui-table-body table.layui-table tbody tr').removeClass('layui-table-click');
    } else if (obj.checked === true && obj.type === 'one') {
      //点击单行
      if (obj.checked === true) {
        obj.tr.addClass('layui-table-click');
      } else {
        obj.tr.removeClass('layui-table-click');
      }
    } else if (obj.checked === false && obj.type === 'one') {
      //点击全选之后点击单行
      if (obj.tr.hasClass('layui-table-click')) {
        obj.tr.removeClass('layui-table-click');
      }
    }
  })


}

//处理模糊匹配
function mhpp(ele, isRever, callback) {
  //模糊匹配
  $(ele).each(function () {
    var tag_ = $(this).attr("guid");
    var self = $(this);
    var type = $(this).attr('type');

    var skuSelect = xmSelect.render({
      el: '#' + tag_,
      autoRow: false,
      radio: true,
      toolbar: { show: false },
      remoteSearch: true,
      filterable: true,
      clickClose: true,
      data: [],
      remoteMethod: function (val, cb, show) {
        //这里如果val为空, 则不触发搜索
        if (type != 'auto') {
          if (!val)
            return cb([]);
        }

        $.ajax({
          url: self.attr("urls"),
          type: 'GET',
          data: {
            keyword: val,
          },
          dataType: 'json',
          timeout: 1000,
          success: function (res) {
            var a = [];
            if (res.code == 0) {
              var arr_ = res.data.list;
              for (var i = 0; i < arr_.length; i++) {
                a.push({
                  name: arr_[i].name,
                  value: arr_[i].id
                })
              }
            }
            cb(a);
          },
          error: function () {
            return layer.msg('网络错误，请重试', { icon: 5 });
          }
        });
      },
      on: function (data) {
        var arr = data.arr
        if (arr.length == 0) {
          console.log("数组为空");
          $("input[name='" + tag_ + "']").val("").attr("guid", "");
        } else {
          var json = [];
          var j = {};
          j.value = data.arr[0].value;
          j.name = data.arr[0].name;
          json.push(j);
          typeof callback == 'function' && callback(j.value);
          //默认需要ID
          $("input[name='" + tag_ + "']").val(json[0].value).attr("guid", json[0].name);
          //需要名字
          if (isRever) {
            $("input[name='" + tag_ + "']").val(json[0].name).attr("guid", json[0].value);
          }
        }
      }
    })
  })
}



//输入框不能输入负数--
$("body").on("keypress", "input[type='number']", function (event) {
  if($(this).hasClass("disxk")){return}
  if (event.keyCode == 45) {
    event.preventDefault();
  }
})
$("body").on("input propertychange", "input[type='number']", function (event) {
  if($(this).hasClass("disxk")){return}
  if($(this).val()<0){
    $(this).val(0)
  }
  
})

$("body").on("keypress", "input", function (event) {
  if (event.keyCode == 13) {
    $(".pur-search").click()
  }
})