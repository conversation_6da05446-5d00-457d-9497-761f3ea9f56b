layui.config({
    version: true,
    closeFooter: true,
    tabAutoRefresh: false,
    base: getProjectUrl() + 'assets/module/'
}).extend({
    steps: 'steps/steps',
    notice: 'notice/notice',
    cascader: 'cascader/cascader',
    dropdown: 'dropdown/dropdown',
    fileChoose: 'fileChoose/fileChoose',
    Split: 'Split/Split',
    Cropper: 'Cropper/Cropper',
    soulTable: 'soulTable/soulTable',
    tagsInput: 'tagsInput/tagsInput',
    citypicker: 'city-picker/city-picker',
    introJs: 'introJs/introJs',
    zTree: 'zTree/zTree'
}).use(['layer', 'admin', 'table', 'index'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    var admin = layui.admin;
    var table = layui.table;
    var index = layui.index;

    //关闭当前页面 跳到指定页面
    window.closeCurrentPageJumpOne = function (titlex, urlx, time) {
        //不传 时间 为0
        var time = time ? time : 0;
        setTimeout(function () {
            index.openTab({
                title: titlex,
                url: urlx
            });
        }, time)
        setTimeout(function () {
            index.closeTab(window.location.pathname + window.location.search);
        }, (time + 500))
    }

    //点击重置刷新当前页面
    $('.reset-button').click(function () {
        location.reload();
    });
});

/**
 * 获取当前项目的根路径，通过获取layui.js全路径截取assets之前的地址
 * */
function getProjectUrl() {
    var layuiDir = layui.cache.dir;
    if (!layuiDir) {
        var js = document.scripts, last = js.length - 1, src;
        for (var i = last; i > 0; i--) {
            if (js[i].readyState === 'interactive') {
                src = js[i].src;
                break;
            }
        }
        var jsPath = src || js[last].src;
        layuiDir = jsPath.substring(0, jsPath.lastIndexOf('/') + 1);
    }
    return layuiDir.substring(0, layuiDir.indexOf('assets'));
}

/**
 *  获取参数
 * @param variable
 * @returns {string}
 */
function getQueryVariable(variable) {
    let query = window.location.search.substring(1);
    let vars = query.split("&");
    for (let i = 0; i < vars.length; i++) {
        let pair = vars[i].split("=");
        if (pair[0] == variable) {
            return pair[1];
        }
    }
    return '';
}

/**
 * 判断数组内元素是否全相同
 * @param array
 * @returns {boolean}
 */
function isAllEqual(array) {
    if (array.length > 0) {
        return !array.some(function (value, index) {
            return value !== array[0];
        });
    } else {
        return true;
    }
}

/**
 *
 * @param url
 * @param type
 * @param param
 * @param callback
 * @param isload
 * @constructor
 */
function Request(url, type, param, callback, isload) {
    if (isload == undefined) {
        var index = layer.load(1);
    }

    //设置携带cookie
    $.ajaxSetup({
        xhrFields: {
            withCredentials: true
        }
    });

    var params = $.extend({}, param);

    $.ajax({
        type: type,
        url: url,
        data: params,
        xhrFields: {withCredentials: true},
        timeout: 10000,
        success: function (data) {
            typeof callback == 'function' && callback(data);
            if (isload == undefined) {
                layer.closeAll('loading');
            }
        },
        error: function () {
            layer.closeAll('loading');
            layer.msg('网络出现问题，请重试！');
        }
    });
}

function LayUiTableParseData(res) {
    if (res.code == 0) {
        return {
            code: res.code,
            msg: "",
            count: res.data.total ? res.data.total : 0,
            data: res.data.list
        }
    } else {
        return {
            code: 0,
            msg: "",
            count: 0,
            data: []
        }
    }
}

/**
 * 判断数组中是否包含某个值
 * @param arr 数组
 * @param str 值
 * @returns {boolean}
 */
function contains(arr, str) {
    var i = arr.length;
    while (i--) {
        if (arr[i] == str) {
            return true;
        }
    }
    return false;
}

function ajaxCommon(url, data) {
    var result = false;
    $.ajax({
        url: url,
        type: 'post',
        data: data,
        async: false,
        dataType: 'json',
        timeout: 10000,
        success: function (resp) {
            if (resp) {
                result = resp;
            }
        }
    });
    return result;
}

function getCookie(name) //取cookies函数
{
  var arr = document.cookie.match(new RegExp("(^| )" + name + "=([^;]*)(;|$)"));
  if (arr != null) return unescape(arr[2]);
  return null;
}

function getImageServerDomain() {
    var defaultDomain = "http://image.liexindev.net";
    if (document.domain.indexOf("ichunt") !== -1) {
        defaultDomain = "https://image.ichunt.net";
    }
    return defaultDomain;
}

function getOssUploadDomain() {
    var defaultDomain = "http://file.liexindev.net";
    if (document.domain.indexOf("ichunt") !== -1) {
        defaultDomain = "https://files.ichunt.net";
    }
    return defaultDomain;
}

function getSupplierDomain() {
    var defaultDomain = "http://supplier.liexin.net";
    if (document.domain.indexOf("ichunt") !== -1) {
        defaultDomain = "https://supplier.ichunt.net";
    }
    return defaultDomain;
}

//数组去重
function array_unique(arr) {
    var res = [];
    for (var i = 0, len = arr.length; i < len; i++) {
        var item = arr[i];
        for (var j = 0, jLen = res.length; j < jLen; j++) {
            if (item == res[j]) break;
        }
        if (j == jLen) res.push(item);
    }
    return res;
}

function array_remove_empty(arr) {
    for (var i = 0; i < arr.length; i++) {
        if (arr[i] == "" || typeof (arr[i]) == "undefined") {
            arr.splice(i, 1);
            i = i - 1; // i - 1 ,因为空元素在数组下标 2 位置，删除空之后，后面的元素要向前补位
        }
    }
    return arr;
}
