
var strVar = "";
strVar += "<style>";
strVar += "    .navxboxp{position:fixed;left:50%;top:0px;width:130px;height:50px;background:#fff;z-index:999;font-size:12px;margin-left:-65px;}";
strVar += "    .navxboxp .namexp{background:#4b4e55;border-radius:0 0 5px  5px ;color:#fff;text-align:center;height:26px;line-height:26px;cursor: pointer;}";
strVar += "    .navxboxp:hover ul{";
strVar += "        top:26px;";
strVar += "    }";
strVar += "    ";
strVar += "    .navxboxp:hover ul::-webkit-scrollbar {";
strVar += "        width: 2px;height: 10px;background: transparent;"
strVar += "    }";
strVar += "    ";
strVar += "    .navxboxp ul{box-shadow:0px 0px 10px #ccc;position:fixed;transition:all 0.5s;border-radius:5px ;top:-1000px;width:130px;max-height:200px;overflow-y:auto;background: #fff;}";
strVar += "    .navxboxp li a{background:#fff;height:24px;line-height:24px;display:block;border-bottom:1px solid #f3f3f3;text-align:center;cursor: pointer;}";
strVar += "    .navxboxp ul li a:hover {color:#009688;}";
strVar += "<\/style>";
strVar += "<div  class=\"navxboxp\">";
strVar += "    <div class=\"namexp\">系统导航<\/div>";
strVar += "    <ul>";

var strVarEnd = "    <\/ul><\/div>";
//ajax封装
function Ajaxxs(type, url, data, success, failed) {
    // 创建ajax对象
    var xhr = null;
    if (window.XMLHttpRequest) {
        xhr = new XMLHttpRequest();
    } else {
        xhr = new ActiveXObject('Microsoft.XMLHTTP')
    }

    var type = type.toUpperCase();
    // 用于清除缓存
    var random = Math.random();

    if (typeof data == 'object') {
        var str = '';
        for (var key in data) {
            str += key + '=' + data[key] + '&';
        }
        //method=get&url=
        data = str.replace(/&$/, '');
    }

    if (type == 'GET') {
        if (data) {
            xhr.open('GET', url + '?' + data, true);
        } else {
            xhr.open('GET', url + '?t=' + random, true);
        }
        xhr.send();

    } else if (type == 'POST') {
        xhr.open('POST', url, true);
        // 如果需要像 html 表单那样 POST 数据，请使用 setRequestHeader() 来添加 http 头。
        xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        xhr.send(data);
    }

    // 处理返回数据
    xhr.onreadystatechange = function () {
        if (xhr.readyState == 4) {
            if (xhr.status == 200) {
                success(JSON.parse(xhr.responseText));
            } else {
                if (failed) {
                    failed(xhr.status);
                }
            }
        }
    }
}
function getCookiexs(objName) {//获取指定名称的cookie的值 
    var arrStr = document.cookie.split("; ");
    for (var i = 0; i < arrStr.length; i++) {
        var temp = arrStr[i].split("=");
        if (temp[0] == objName) return unescape(temp[1]);
    }
}
function insertAfters(newElement, targetElement) { // newElement是要追加的元素 targetElement 是指定元素的位置 
    var parent = targetElement.parentNode; // 找到指定元素的父节点 
    if (parent.lastChild == targetElement) { // 判断指定元素的是否是节点中的最后一个位置 如果是的话就直接使用appendChild方法 
        parent.appendChild(newElement, targetElement);
    } else {
        parent.insertBefore(newElement, targetElement.nextSibling);
    };
};
if(getCookiexs("oa_user_id")){
    Ajaxxs('get', 'http://perm.ichunt.net/api/ajax/getUserPermList', { userId: getCookiexs("oa_user_id") }, function (res) {
    var data_ = res.data || [];
    if (data_.length > 0) {
        var hl_ = ""; html_ = ";"
        for (var i = 0; i < data_.length; i++) {
            hl_ += '<li><a href="' + data_[i].url + '" target="_blank" title="' + data_[i].business_name + '">' + data_[i].business_name + '</a></li>'
        }
        html_ = strVar + hl_ + strVarEnd;
        var body_ = document.body;
        var div = document.createElement("div");
        div.innerHTML = html_
        insertAfters(div, body_); 
       

    }

})
}








