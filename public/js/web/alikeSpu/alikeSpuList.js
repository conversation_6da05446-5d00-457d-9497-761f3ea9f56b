layui.use(['table', 'form', 'laydate', 'layer', 'upload', 'index', 'admin'], function () {
    var table = layui.table;
    var laydate = layui.laydate;
    var form = layui.form;
    var index = layui.index;
    var admin = layui.admin;
    var upload = layui.upload;

    //自定义格式
    laydate.render({
        elem: 'input[name=start_time]',
        type: 'date',
        format: 'yyyy-MM-dd'
    });
    laydate.render({
        elem: 'input[name=end_time]',
        type: 'date',
        format: 'yyyy-MM-dd'
    });
    let currentPage = 1;
    table.render({
        elem: '#list',
        url: '/api/alikeSpu/getAlikeSpuList',
        method: 'post',
        request: {
            pageName: 'page',//页码的参数名称，默认：page
            limitName: 'limit' //每页数据量的参数名，默认：limit
        },
        limit: 15,
        loading: true,
        size: 'sm',
        cols: [[
            {type: 'checkbox'},
            {field: 'no', title: '序号', width: 60},
            {field: 'alike_spu_name', title: '替代型号', width: 150},
            {field: 'alike_brand_name', title: '替代品牌', width: 150},
            {field: 'spu_name', title: '被替代型号', width: 150},
            {field: 'brand_name', title: '被替代品牌', width: 150},
            {field: 'pin_to_pin', title: 'pin to pin', width: 40},
            {field: 'match_rate', title: '参数匹配度', width: 100},
            {field: 'effective_alike_spu_num', title: '有效替代商品数', width: 130},
            {field: 'effective_spu_num', title: '有效被替代商品数', width: 130},
            {field: 'remark', title: '备注',width: 150},
            {field: 'add_time', title: '创建时间', width: 200},
            {field: 'admin_name', title: '创建人', width: 150},
            {field: 'cz', fixed: 'right', title: '操作', templet: '#operation', width: 220}
        ]],
        limits: [15, 30, 50, 100],
        id: 'Reload',
        page: {},
        done: function (res, curr, count) {
            currentPage = curr;
        }
    });

    form.on('submit(Reload)', function (data) {
        //执行重载
        table.reload('Reload', {
            page: {
                curr: 1 //重新从第 1 页开始
            },
            where: data.field
        });
        return false;
    });

    $('#reset').click(function () {
        // window.open('/web/alikeSpu/alikeSpuList')
        window.location.href = '/web/alikeSpu/alikeSpuList';
    });


    $('#upload_alike_spu').click(function () {
        index.openTab({
            title: '上传国产替代型号',
            url: '/web/alikeSpu/alikeSpuUploadLog?view=iframe',
            end: function () {
                // insTb.reload();
            }
        });
    });

    $('#export_alike_spu').click(function () {
        window.open('/api/alikeSpu/export', '_blank')
    });

    $('#upload_list').click(function () {
        layer.open({
            title: "上传相似型号",
            area: ['1500px', '700px'],
            type: 2,
            content: '/web/alikeSpu/alikeSpuUploadLog'
        });
    });

    //批量删除
    $(document).on('click', '#batch_delete_alike_spu', function () {
        let checkStatus = table.checkStatus('Reload');
        let data = checkStatus.data;
        let ids = [];
        $.each(data, function (index, value) {
            ids.push(value.id);
        });
        if (ids.length === 0) {
            layer.msg('请选择要操作的数据', {icon: 5});
            return false;
        }
        layer.confirm('确定要批量删除选中的替代关系吗？', {
            btn: ['确定', '取消'] //按钮
        }, function () {
            $.ajax({
                url: '/api/alikeSpu/deleteAlikeSpu',
                type: 'post',
                data: {
                    id: ids,
                    is_batch: 1
                },
                dataType: 'json',
                timeout: 10000,
                success: function (res) {
                    if (res.code !== 0) {
                        layer.msg(res.msg, {icon: 5});
                    } else {
                        layer.msg(res.msg, {icon: 6});
                        table.reload('Reload', {
                            page: {
                                curr: currentPage //重新从第 1 页开始
                            },
                            where: data.field
                        });
                        layer.closeAll();
                    }
                },
                error: function () {
                    return layer.msg('网络错误，请重试', {icon: 5});
                }
            });
        });

    });

    //执行实例
    upload.render({
        elem: '#upload_button', //绑定元素
        url: '/api/alikeSpu/uploadAlikeSpu', //上传接口
        accept: 'file',
        bindAction: '#confirmUpload',
        auto: false,
        data: {
            type: function () {
                return $("input[name='type']:checked").val();
            }
        },
        before: function (obj) {
            if (!$("input[name='type']:checked").val()) {
                layer.msg("请选择类型", {icon: 5});
                layer.stopPropagation();
            }
        },
        choose: function (obj) {
            $('#hasFile').val(1);
            if (!$("input[name='type']:checked").val()) {
                layer.msg("已经选择文件,请选择映射商品类型", {offset: "tb", area: ['400px', '50px']});
            }
        },
        done: function (res) {
            //上传完毕回调
            layer.closeAll();
            if (res.code === 0) {
                layer.msg(res.msg, {icon: 6});
            } else {
                layer.msg(res.msg, {icon: 5});
            }
            location.reload();
        }, error: function () {
            //请求异常回调
            layer.msg('请求异常', {icon: 5});
            layer.closeAll();
        }
    });

    $('#confirmUpload').click(function () {
        if ($('#hasFile').val() === '0') {
            layer.msg("上传文件不能为空", {icon: 5});
        }
        $(this).attr('class', 'layui-btn layui-btn-disabled');
    });


    table.on('tool(Reload)', function (obj) {
        let data = obj.data;
        if (obj.event === "reload_data") {
            $.ajax({
                url: '/api/alikeSpu/reloadData',
                type: 'post',
                data: {
                    id: data.id
                },
                dataType: 'json',
                timeout: 10000,
                success: function (res) {
                    if (res.code !== 0) {
                        layer.msg(res.msg, {icon: 5});
                    } else {
                        layer.msg(res.msg, {icon: 6});
                    }
                },
                error: function () {
                    return layer.msg('网络错误，请重试', {icon: 5});
                }
            });
        }
        if (obj.event === 'delete') {
            layer.confirm('确定删除这个映射关系吗？', {
                btn: ['确定', '取消'] //按钮
            }, function () {
                let id = $(this).attr('value');
                $.ajax({
                    url: '/api/alikeSpu/deleteAlikeSpu',
                    type: 'post',
                    data: {
                        id: data.id
                    },
                    dataType: 'json',
                    timeout: 10000,
                    success: function (res) {
                        if (res.code !== 0) {
                            layer.msg(res.msg, {icon: 5});
                        } else {
                            layer.msg(res.msg, {icon: 6});
                            table.reload('Reload', {
                                page: {
                                    curr: currentPage //重新从第 1 页开始
                                },
                                where: data.field
                            });
                            layer.closeAll();
                        }
                    },
                    error: function () {
                        return layer.msg('网络错误，请重试', {icon: 5});
                    }
                });
            });
        }
    });
});
