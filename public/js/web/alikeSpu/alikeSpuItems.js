layui.use(['table', 'form', 'laydate', 'layer', 'upload'], function () {
    var table = layui.table;
    var laydate = layui.laydate;
    var form = layui.form;
    var upload = layui.upload;

    //自定义格式
    laydate.render({
        elem: 'input[name=start_time]',
        type: 'date',
        format: 'yyyy-MM-dd'
    });
    laydate.render({
        elem: 'input[name=end_time]',
        type: 'date',
        format: 'yyyy-MM-dd'
    });
    let currentPage = 1;
    const mainId = $('#mainId').val();
    const spuName = $('#spu_name').val();
    const alikeSpuName = $('#alike_spu_name').val();
    const standardBrandId = $('#standard_brand_id').val();
    const alikeStandardBrandId = $('#alike_standard_brand_id').val();
    // const spuName = getQueryVariable('spu_name');
    // const alikeSpuName = getQueryVariable('alike_spu_name');


    table.render({
        elem: '#alikeSpuSkuList',
        url: '/api/alikeSpu/getAlikeSpuItems?alike_spu_name=' + alikeSpuName + '&standard_brand_id=' + alikeStandardBrandId,
        method: 'post',
        request: {
            pageName: 'page',//页码的参数名称，默认：page
            limitName: 'limit' //每页数据量的参数名，默认：limit
        },
        size: 'sm',
        loading: true,
        cols: [[
            {type: 'checkbox'},
            {field: 'goods_id', title: 'SKUID', width: 180, templet: "#sku_id"},
            {field: 'goods_name', title: '型号',width: 200},
            {field: 'brand_name', title: '制造商',width: 200},
            {field: 'standard_brand_name', title: '标准品牌',width: 200},
            {field: 'is_agent_brand_spu', title: '授权代理产品',width: 150},
            {field: 'agent_brand_spu_type_name', title: '产品类型',width: 100},
            {field: 'status_name', title: '状态', width: 100},
            // {field: 'goods_status_name', title: '创建时间', width: 100},
            {field: 'canal', title: '供应商编码', width: 100},
            {field: 'canal_name', title: '供应商名称', width: 250},
            {field: 'update_time', title: '更新时间', width: 150},
            {field: 'cz', title: '操作', templet: '#alikeSpuSkuListOperation', width: 100}
        ]],
        id: 'alikeSpuSkuList',
        page: {
            // layout: ['prev', 'page', 'next', 'count',  'skip'] //自定义分页布局
        },
        done: function (res, curr, count) {
            //得到当前页码
            currentPage = curr;
        }
    });


    form.on('submit(getAlikeSpuSkuList)', function (data) {
        //执行重载
        table.reload('alikeSpuSkuList', {
            page: {
                curr: 1 //重新从第 1 页开始
            },
            where: data.field
        });
        return false;
    });

    //批量删除
    $(document).on('click', '#batchDeleteAlikeSpuSku', function () {
        let checkStatus = table.checkStatus('alikeSpuSkuList');
        let data = checkStatus.data;
        let skuIds = [];
        $.each(data, function (index, value) {
            skuIds.push(value.goods_id);
        });
        if (skuIds.length === 0) {
            layer.msg('请选择要操作的sku', {icon: 5});
            return false;
        }
        layer.confirm('确定要删除选中的数据吗', function (index) {
            $.post('/api/alikeSpu/deleteAlikeSpuSku', {
                'main_id': mainId,
                'sku_ids': skuIds.join(','),
                'spu_name': spuName
            }, function (res) {
                if (res.code === 0) {
                    layer.msg(res.msg, {icon: 6});
                } else {
                    layer.msg(res.msg, {icon: 5});
                    return false;
                }
            });
        });
    });

    table.on('tool(alikeSpuSkuList)', function (obj) {
        let data = obj.data;
        if (obj.event === 'delete') {
            layer.confirm('确定删除这个商品吗？', {
                btn: ['确定', '取消'] //按钮
            }, function () {
                $.post('/api/alikeSpu/deleteAlikeSpuSku', {
                    'main_id': mainId,
                    'sku_ids': data.goods_id,
                    'spu_name': spuName
                }, function (res) {
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 6});
                    } else {
                        layer.msg(res.msg, {icon: 5});
                        return false;
                    }
                });
            });
        }
    });

    form.on('submit(restAlikeSpuSkuList)', function (data) {
        table.reload('alikeSpuSkuList', {
            page: {
                curr: 1 //重新从第 1 页开始
            }
        });
    });

    //----------------------------------------------------------------------------------------------------------------


    table.render({
        elem: '#spuSkuList',
        url: '/api/alikeSpu/getAlikeSpuItems?spu_name=' + spuName + '&standard_brand_id=' + standardBrandId,
        method: 'post',
        request: {
            pageName: 'page',//页码的参数名称，默认：page
            limitName: 'limit' //每页数据量的参数名，默认：limit
        },
        size: 'sm',
        loading: true,
        cols: [[
            {type: 'checkbox'},
            {field: 'goods_id', title: 'SKUID', width: 180, templet: "#sku_id"},
            {field: 'goods_name', title: '型号',width: 200},
            {field: 'brand_name', title: '制造商',width: 200},
            {field: 'standard_brand_name', title: '标准品牌',width: 200},
            {field: 'is_agent_brand_spu', title: '授权代理产品',width: 150},
            {field: 'agent_brand_spu_type_name', title: '产品类型',width: 100},
            {field: 'status_name', title: '状态', width: 100},
            // {field: 'goods_status_name', title: '创建时间', width: 100},
            {field: 'canal', title: '供应商编码', width: 100},
            {field: 'canal_name', title: '供应商名称', width: 250},
            {field: 'update_time', title: '更新时间', width: 150},
            {field: 'cz', title: '操作', templet: '#spuSkuListOperation', width: 100}
        ]],
        id: 'spuSkuList',
        page: {
            // layout: ['prev', 'page', 'next', 'count',  'skip'] //自定义分页布局
        },
        done: function (res, curr, count) {
            //得到当前页码
            currentPage = curr;
        }
    });

    form.on('submit(getSpuSkuList)', function (data) {
        //执行重载
        table.reload('spuSkuList', {
            page: {
                curr: 1 //重新从第 1 页开始
            },
            where: data.field
        });
        return false;
    });


    //批量删除
    $(document).on('click', '#batchDeleteSpuSku', function () {
        let checkStatus = table.checkStatus('spuSkuList');
        let data = checkStatus.data;
        let skuIds = [];
        $.each(data, function (index, value) {
            skuIds.push(value.goods_id);
        });
        if (skuIds.length === 0) {
            layer.msg('请选择要操作的sku', {icon: 5});
            return false;
        }
        layer.confirm('确定要删除选中的数据吗', function (index) {
            $.post('/api/alikeSpu/deleteSpuSku', {
                'main_id': mainId,
                'sku_ids': skuIds.join(','),
                'spu_name': spuName
            }, function (res) {
                if (res.code === 0) {
                    layer.msg(res.msg, {icon: 6});
                } else {
                    layer.msg(res.msg, {icon: 5});
                    return false;
                }
            });
        });
    });

    table.on('tool(spuSkuList)', function (obj) {
        let data = obj.data;
        if (obj.event === 'delete') {
            layer.confirm('确定删除这个商品吗？', {
                btn: ['确定', '取消'] //按钮
            }, function () {
                $.post('/api/alikeSpu/deleteSpuSku', {
                    'main_id': mainId,
                    'sku_ids': data.goods_id,
                    'spu_name': spuName
                }, function (res) {
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 6});
                    } else {
                        layer.msg(res.msg, {icon: 5});
                        return false;
                    }
                });
            });
        }
    });

    form.on('submit(restSpuSkuList)', function (data) {
        table.reload('spuSkuList', {
            page: {
                curr: 1 //重新从第 1 页开始
            }
        });
    });
});
