layui.use(['table', 'form', 'laydate', 'layer', 'upload', 'index'], function () {
    var table = layui.table;
    var laydate = layui.laydate;
    var form = layui.form;
    var index = layui.index;
    var upload = layui.upload;

    let currentPage = 1;
    table.render({
        elem: '#shopDistributeSkuList',
        url: '/api/shopDistributeSku/getShopDistributeSkuList',
        method: 'post',
        request: {
            pageName: 'page',//页码的参数名称，默认：page
            limitName: 'limit' //每页数据量的参数名，默认：limit
        },
        limit: 15,
        loading: true,
        size: 'sm',
        cols: [[
            { type: 'checkbox' },
            { field: 'id', title: 'ID', width: 60 },
            { field: 'shop_id', title: '店铺ID', width: 60 },
            {
                field: 'shop', title: '店铺名称', width: 120, templet: function (d) {
                    return d.shop.shop_name;
                }
            },
            { field: 'sku_id', title: 'SKUID', width: 150 },
            {
                field: 'spu_name', title: '型号', width: 270, templet: function (d) {
                    return d.sku.spu_name;
                }
            },
            {
                field: 'standard_brand_name', title: '标准品牌', width: 100, templet: function (d) {
                    return d.sku.standard_brand_name??'';
                }
            },

            {
                field: 'stock', title: '库存', width: 80, templet: function (d) {
                    return d.sku.stock;
                }
            },
            {
                field: 'class_name', title: '分类', width: 200, templet: function (d) {
                    return d.sku.class_name1 ? d.sku.class_name1 + ' / ' + d.sku.class_name2 : '';
                }
            },
            {
                field: 'ladder_price', title: '成本价', align: 'center', width: 80, templet: function (data) {
                    return data.ladder_price ? '<span style="color: dodgerblue" class="view_ladder_price">移入查看</span>' : '';
                }
            },
            {
                field: 'mpq', title: '标准包装量', width: 100, templet: function (d) {
                    return d.mpq;
                }
            },
            {
                field: 'moq',
                title: '起订量',
                width: 80,
                edit: 'text',
                style: 'background-color:white',
                templet: function (d) {
                    d.LAY_OLD_MOQ = d.moq; // 保存原始库存值
                    return d.moq;
                }
            },
            {
                field: 'multiple',
                title: '递增量',
                width: 80,
                edit: 'text',
                style: 'background-color:white',
                templet: function (d) {
                    d.LAY_OLD_MULTIPLE = d.multiple; // 保存原始库存值
                    return d.multiple;
                }
            },
            {
                field: 'total_cost', title: '总成本', width: 120, templet: function (d) {
                    return d.total_cost;
                }
            },
            {
                field: 'purchase_coefficient',
                title: '京东采购价系数',
                width: 120,
                edit: 'text',
                style: 'background-color:white',
                templet: function (d) {
                    d.LAY_OLD_PURCHASE_COEFFICIENT = d.purchase_coefficient; // 保存原始库存值
                    return d.purchase_coefficient ? d.purchase_coefficient : d.shop.purchase_coefficient;
                }
            },
            {
                field: 'purchase_price',
                title: '京东采购价',
                width: 120,
                edit: 'text',
                style: 'background-color:white',
                templet: function (d) {
                    d.LAY_OLD_PURCHASE_PRICE = d.purchase_price; // 保存原始库存值
                    if (!(/^\d+(\.\d+)?$/.test(d.purchase_price))) {
                        layer.msg('请输入数字(可以是整数或小数)');
                        d.purchase_price = d.LAY_OLD_purchase_price;
                    }
                    return d.purchase_price;
                }
            },
            {
                field: 'sale_coefficient',
                title: '京东销售价系数',
                width: 120,
                edit: 'text',
                style: 'background-color:white',
                templet: function (d) {
                    d.LAY_OLD_SALE_COEFFICIENT = d.sale_coefficient; // 保存原始库存值
                    return d.sale_coefficient ? d.sale_coefficient : d.shop.sale_coefficient;
                }
            },
            {
                field: 'sale_price',
                title: '京东销售价',
                width: 120,
                edit: 'text',
                style: 'background-color:white',
                templet: function (d) {
                    d.LAY_OLD_SALE_PRICE = d.sale_price; // 保存原始库存值
                    if (!(/^\d+(\.\d+)?$/.test(d.sale_price))) {
                        layer.msg('请输入数字(可以是整数或小数)');
                        d.sale_price = d.LAY_OLD_SALE_PRICE;
                    }
                    return d.sale_price;
                }
            },
            { field: 'channel_name', title: '渠道', width: 110, templet: function (d) {
                return d.channel ? d.channel.supplier_name : '';
            } },
            { field: 'supplier_code', title: '供应商编码', width: 100, templet: function (d) {
                return d.supplier_channel ? d.supplier_channel.supplier_code : '';
            } },
            {
                field: 'goods_title',
                title: '商品标题',
                width: 200,
                edit: 'text',
                templet: function (d) {
                    d.LAY_OLD_GOODS_TITLE = d.goods_title; // 保存原始值
                    if (d.goods_title && d.goods_title.length > 50) {
                        return '<span style="color: red">' + d.goods_title + '</span>';
                    }
                    return d.goods_title || '';
                }
            },
            { field: 'sku_sn', title: '商品编码', width: 160 },
            { field: 'status_name', title: '状态', width: 70 },
            { field: 'push_fail_reason', title: '失败原因', width: 200 },
            { field: 'create_name', title: '创建人', width: 80 },
            { field: 'create_time', title: '创建时间', width: 150 },
            // {field: 'update_name', title: '修改人', width: 80},
            {field: 'update_time', title: '修改时间', width: 150},
            { field: 'operation', title: '操作', templet: '#operation', width: 60 }
        ]],
        id: 'shopDistributeSkuList',
        page: {},
        done: function (res, curr, count) {
            currentPage = curr;
        }
    });

    table.on('edit(shopDistributeSkuList)', function (obj) { //注：edit是固定事件名，test是table原始容器的属性 lay-filter="对应的值"
        console.log(obj.field);


        // if (!(/^\d+(\.\d+)?$/.test(obj.value))) {
        //     layer.msg('请输入数字(可以是整数或小数)');
        //     return false;
        // }
        let updateObj = {};
        if (obj.field == 'purchase_coefficient') {
            updateObj.purchase_coefficient = obj.value;
            updateObj.id = obj.data.id;
        }
        if (obj.field == 'sale_coefficient') {
            updateObj.sale_coefficient = obj.value;
            updateObj.id = obj.data.id;
        }
        if (obj.field == 'moq') {
            updateObj.moq = obj.value;
            updateObj.id = obj.data.id;
        }
        if (obj.field == 'multiple') {
            updateObj.multiple = obj.value;
            updateObj.id = obj.data.id;
        }
        if (obj.field == 'goods_title') {
            updateObj.goods_title = obj.value;
            updateObj.id = obj.data.id;
        }

        if (obj.field == 'sale_price') {
            updateObj.sale_price = obj.value;
            updateObj.id = obj.data.id;
        }

        if (obj.field == 'purchase_price') {
            updateObj.purchase_price = obj.value;
            updateObj.id = obj.data.id;
        }

        $.ajax({
            url: '/api/shopDistributeSku/updateShopDistributeSku',
            type: 'post',
            data: updateObj,
            dataType: 'json',
            timeout: 10000,
            success: function (res) {
                if (res.code !== 0) {
                    parent.layer.msg(res.msg, { icon: 5 });
                } else {
                    parent.layer.msg(res.msg, {
                        offset: ['50px'],
                        time: 1000,
                        icon: 6
                    });
                    // table.reload('shopSkuList', {
                    //     page: {
                    //         curr: currentPage //重新从第 1 页开始
                    //     },
                    //     where: data.field
                    // });
                    // layer.closeAll();
                }
            },
            error: function () {
                return layer.msg('网络错误，请重试', { icon: 5 });
            }
        });
    });

    form.on('submit(shopDistributeSkuList)', function (data) {
        console.log(data.field);
        //执行重载
        table.reload('shopDistributeSkuList', {
            page: {
                curr: 1 //重新从第 1 页开始
            },
            where: data.field
        });
        return false;
    });

    $('#viewImportShopDistributeSkuLog').click(function () {
        layer.open({
            title: "导入日志",
            area: ['80%', '80%'],
            type: 2,
            content: '/web/taskLog/taskLogList?window=fullWindow&type=' + 6,
        });
    });

    $('#batchSaveShopDistributeSku').on('click', function () {
        index.openTab({
            title: '新增推送选品',
            url: '/web/sku/skuList?window=fullWindow&is_distribute=1',
            end: function () {
                table.reload('shopDistributeSkuList', {});
            }
        });
    });

    // 导入SKU按钮点击事件
    $('#importShopDistributeSku').on('click', function () {
        layer.open({
            title: '导入SKU',
            area: ['70%', '70%'],
            type: 2,
            content: '/web/shopDistributeSku/importShopDistributeSku',
            end: function () {
                table.reload('shopDistributeSkuList', {});
            }
        });
    });

    $('#exportShopDistributeSku').on('click', function () {
        let map = form.val("sku_list_form");
        window.open('/api/shopDistributeSku/exportShopDistributeSku?params=' + JSON.stringify(map), '_blank');
    });

    form.on('submit(reset)', function (data) {
        location.reload();
    });

    // 状态切换
    form.on('switch(statusSwitch)', function (obj) {
        let id = this.value;
        let status = obj.elem.checked ? 1 : -1;

        $.ajax({
            url: '/api/shopDistributeSku/updateDistributeSkuStatus',
            type: 'post',
            data: {
                id: id,
                status: status
            },
            dataType: 'json',
            success: function (res) {
                if (res.code === 0) {
                    layer.msg('状态更新成功', { icon: 6 });
                } else {
                    layer.msg(res.msg, { icon: 5 });
                    // 恢复原状态
                    $(obj.elem).prop('checked', !obj.elem.checked);
                    form.render('checkbox');
                }
            },
            error: function () {
                layer.msg('网络错误，请重试', { icon: 5 });
                // 恢复原状态
                $(obj.elem).prop('checked', !obj.elem.checked);
                form.render('checkbox');
            }
        });
    });

    // 新增推送选品
    $('#saveShopDistributeSku').on('click', function () {
        layer.open({
            title: '新增推送选品',
            area: ['95%', '100%'],
            type: 2,
            content: '/web/sku/skuList?window=fullWindow&is_distribute=1',
            end: function () {
                table.reload('shopDistributeSkuList', {});
            }
        });
    });

    table.on('tool(shopDistributeSkuList)', function (obj) {
        let data = obj.data;
        if (obj.event === "update_sku") {
            let id = data.id;
            layer.open({
                title: '修改推送选品',
                area: ['50%', '80%'],
                type: 2,
                content: '/web/shopDistributeSku/saveShopDistributeSku?window=fullWindow&id=' + id,
                end: function () {
                    table.reload('shopDistributeSkuList', {});
                }
            });
        } else if (obj.event === "delete_sku") {
            layer.confirm('确定要删除该推送选品吗？', function (index) {
                $.ajax({
                    url: '/api/shopDistributeSku/deleteShopDistributeSku',
                    type: 'post',
                    data: {
                        id: data.id
                    },
                    dataType: 'json',
                    success: function (res) {
                        if (res.code === 0) {
                            layer.msg('删除成功', { icon: 6 });
                            table.reload('shopDistributeSkuList', {});
                        } else {
                            layer.msg(res.msg, { icon: 5 });
                        }
                    },
                    error: function () {
                        layer.msg('网络错误，请重试', { icon: 5 });
                    }
                });
                layer.close(index);
            });
        }
    });

      //划过显示成本价格
      let ladderPriceTipsVal = '';
      $(document).on('mouseenter', '.view_ladder_price', function () {
          let self = this;
          let rowIndex = $(this).parent().parent().parent().attr('data-index');
          let data = table.cache['shopDistributeSkuList'][rowIndex].ladder_price;
          let supplierId = table.cache['shopDistributeSkuList'][rowIndex].supplier_id;
          let moq = table.cache['shopDistributeSkuList'][rowIndex].moq;
          if (!data) {
              return false;
          }
          let htmlArr = [];
          let color = 'green';
          if (data.length > 0) {
              htmlArr.push('<table class="layui-table table-status"><thead>' +
                  '<tr><th style="text-align: center">数量</th>' +
                  '<th style="text-align: center">国内含税(￥)</th>' +
                  '<th style="text-align: center">香港交货($)</th>' +
                  '</tr></thead><tbody>')
              for (let i = 0; i < data.length; i++) {
                  if (supplierId == 17) {
                      if (data[i].price_cost_cn) {
                          htmlArr.push(
                              '<tr>' +
                              '  <td style="text-align: center">' + data[i].purchases + '</td>' +
                              '  <td style="text-align: center">' + (data[i].price_cost_cn ? data[i].price_cost_cn : '') + '</td>' +
                              '  <td style="text-align: center">' + (data[i].price_cost_us ? data[i].price_cost_us : '') + '</td>' +
                              '</tr>');
                      } else {
                          htmlArr.push(
                              '<tr>' +
                              '  <td style="text-align: center">' + (data[i].purchases != 0 ? data[i].purchases : moq) + '</td>' +
                              '  <td style="text-align: center">' + (data[i].price_cn ? data[i].price_cn : '') + '</td>' +
                              '  <td style="text-align: center">' + (data[i].price_us ? data[i].price_us : '') + '</td>' +
                              '</tr>');
                      }
                  } else {
                      htmlArr.push(
                          '<tr>' +
                          '  <td style="text-align: center">' + data[i].purchases + '</td>' +
                          '  <td style="text-align: center">' + (data[i].price_cn ? data[i].price_cn : '') + '</td>' +
                          '  <td style="text-align: center">' + (data[i].price_us ? data[i].price_us : '') + '</td>' +
                          '</tr>');
                  }

              }
              htmlArr.push('</tbody></table>')
              ladderPriceTipsVal = layer.tips(htmlArr.join(''), self, {
                  tips: [3, "#009688"],
                  time: 1000000,
                  area: ['400px', 'auto'],
                  skin: 'custom'
              });
          }
      }).on('mouseleave', '.view_ladder_price', function () {
          layer.close(ladderPriceTipsVal);
      });
});
