layui.use(['form', 'xmSelect', 'table', 'index', 'laydate'], function () {
    let table = layui.table;
    let form = layui.form;
    let index = layui.index;
    let xmSelect = layui.xmSelect;
    let laydate = layui.laydate;
    let layer = layui.layer;

    //渲染多选
    let brandSelect = xmSelect.render({
        el: '#brandSelect',
        name: 'brand_id',
        searchTips: '请输入要查找的制造商',
        paging: true,
        empty: '没有查找到数据',
        prop: {
            name: 'brand_name',
            value: 'brand_id'
        },
        height: "1300px",
        remoteSearch: true,
        autoRow: true,
        pageRemote: true,
        filterable: true,
        remoteMethod: function (val, cb, show, pageIndex) {
            //val: 搜索框的内容, 不开启搜索默认为空, cb: 回调函数, show: 当前下拉框是否展开, pageIndex: 当前第几页
            $.ajax({
                url: '/api/brand/searchBrand',
                type: 'post',
                data: {
                    brand_ids: $('#brand_ids').val(),
                    brand_name: val,
                    page: pageIndex
                },
                dataType: 'json',
                timeout: 10000,
                success: function (res) {
                    if (!res) return layer.msg('网络错误，请重试', { icon: 5 });
                    if (res.errcode === 0) {
                        cb(res.data, res.last_page);
                    } else {
                        layer.msg(res.errmsg, { icon: 6 });
                    }
                },
                error: function () {
                    return layer.msg('网络错误，请重试', { icon: 5 });
                }
            });
        },
        on: function (data) {
            let brandIds = '';
            for (let x in data.arr)  // x 为属性名
            {
                brandIds = brandIds + data.arr[x].brand_id + ',';
            }
            $("#brand_id_condition").val(brandIds);
        }
    });
    brandSelect.setValue(JSON.parse($('#brand_id_condition').attr('select_value')));

    laydate.render({
        elem: 'input[name=create_time]',
        type: 'datetime',
        trigger: 'click',
        range: '~', //或 range: '~' 来自定义分割字符
        value: ''
    });

    let cols = [
        { type: 'checkbox' },
        { field: 'spu_id', title: 'SPUID', align: 'center', width: 160 },
        { field: 'goods_id', title: 'SKUID', align: 'center', width: 160 },
        { field: 'old_goods_id', title: '原ID', align: 'center', width: 110 },
        // {
        //     field: 'is_buy', title: '可购买', align: 'center', width: 100, templet: function (d) {
        //         return d.is_buy == "1" ? "<a class='btn btn-xs btn-danger'>是</a>" : "<a class='btn btn-xs btn-primary'>否</a>";
        //     }
        // },
        { field: 'spu_name', title: 'SPU型号', align: 'center', width: 170 },
        { field: 'goods_name', title: '商品名称', align: 'center', width: 200 },
        { field: 'brand_name', title: '品牌', align: 'center', width: 150 },
        { field: 'standard_brand_name', title: '标准品牌', align: 'center', width: 150 },
        {
            field: 'spu_attrs', title: '参数', align: 'center', width: 80, templet: function (data) {
                return data.spu_attrs.attrs_extend ? '<span style="color: dodgerblue" class="view_spu_attrs">查看</span>' : '';
            }
        },
        { field: 'class_name', title: 'SKU分类', align: 'center', width: 200 },

        { field: 'series_name', title: '商品系列', align: 'center', width: 200 },
        { field: 'raw_goods_packing', title: '包装', align: 'center', width: 150 },
        { field: 'source_name', title: '来源', align: 'center', width: 80 },
        {
            field: 'canal', title: '供应商编码', align: 'center', width: 100, templet: function (data) {
                if (data.canal) {
                    let supplierId = parseInt(data.canal.replace(/\D/g, ''));
                    return "<a style='color: dodgerblue' href='" + getSupplierDomain() + "/?jump_url=/supplier/SupplierDetail&supplier_id=" + supplierId + "' target='_blank'>" + data.canal + "</a>";
                }
                return data.canal ? data.canal : '';
            }
        },
        { field: 'encoded_user_name', title: 'SKU采购', align: 'center', width: 120 },
        {
            field: 'stock', title: '库存', align: 'center', width: 80, edit: function (data) {
                //寄售商品不能修改库存
                if (data.source == 12) {
                    return false;
                }
                return 'text';
            }
        },

        //锁库数量
        {
            field: 'lock_stock', title: '锁库数量', align: 'center', width: 80, templet: function (data) {
                return data.lock_stock || 0;
            }
        },
        //未锁库数量
        {
            field: 'not_lock_stock', title: '未锁库数量', align: 'center', width: 120, templet: function (data) {
                if (data.stock < data.lock_stock) {
                    return 0;
                } else {
                    return (data.stock || 0) - (data.lock_stock || 0);
                }
            }
        },

        //是否寄售
        {
            field: 'source', title: '是否寄售', align: 'center', width: 80, templet: function (data) {
                return data.source == 12 ? "<span style='color: dodgerblue'>是</span>" : "<span style='color: dodgerblue'>否</span>";
            }
        },

        {
            field: 'ladder_price', title: '成本价', align: 'center', width: 80, templet: function (data) {
                return data.ladder_price ? '<span style="color: dodgerblue" class="view_ladder_price">移入查看</span>' : '';
            }
        },
        {
            field: 'show_price', title: '展示价格', align: 'center', width: 80, templet: function (data) {
                return data.show_price ? '<span style="color: dodgerblue" class="view_show_price">移入查看</span>' : '';
            }
        },
        { field: 'batch_sn', title: '批次', align: 'center', width: 80 },
        { field: 'encap', title: '封装', align: 'center', width: 80 },
        {
            field: 'ability_level_cn', title: '履约程度', align: 'center', width: 80, templet: function (d) {
                let htmlt = "";
                if (d.ability_level_cn) {
                    switch (d.ability_level) {
                        case 0:
                            htmlt = "<span style='color: '>" + d.ability_level_cn + "</span>";
                            break;
                        case 1:
                            htmlt = "<span style='color: orange'>" + d.ability_level_cn + "</span>";
                            break;
                        case 2:
                            htmlt = "<span style='color: red'>" + d.ability_level_cn + "</span>";
                            break;
                    }
                }
                return htmlt;
            }
        },
        { field: 'mpq', title: '标准包装量', align: 'center', width: 80 },
        { field: 'moq', title: '起订量', align: 'center', width: 80 },
        { field: 'multiple', title: '递增量', align: 'center', width: 80 },
        {
            field: 'status_name', title: '状态', align: 'center', width: 70, templet: function (d) {
                return d.status_name === "上架" ? "<span style='color: green'>上架</span>" : "<span style='color: red'>下架</span>";

            }
        },
        {
            field: 'is_expire', title: '过期', align: 'center', width: 60, templet: function (d) {
                if (d.supplier_id == 17) {
                    return '';
                } else {
                    return d.is_expire === "是" ? "<a class='btn btn-xs btn-danger'>是</a>" : "<a class='btn btn-xs btn-primary'>否</a>";
                }
            }
        },

        { field: 'cp_time', title: '上架有效期', align: 'center', width: 180 },
        { field: 'cn_delivery_time', title: '大陆交期', align: 'center', width: 100 },
        { field: 'hk_delivery_time', title: '香港交期', align: 'center', width: 100 },
        { field: 'goods_label_name', title: '渠道标签', align: 'center', width: 100 },
        { field: 'is_fulfill', title: '履约程度', align: 'center', width: 80 },
        { field: 'goods_tag_names', title: '标签', align: 'center', width: 150 },
        { field: '', title: '特殊规则', align: 'center', width: 100 },
        { field: 'eccn', title: 'ECCN', align: 'center', width: 100 },
        { field: '', title: '活动标签', align: 'center', width: 100 },
        { field: 'update_time', title: '更新时间', align: 'center', width: 170 },
        { field: 'create_time', title: '创建时间', align: 'center', width: 170 },

    ];

    if (!getQueryVariable('is_distribute')) {
        cols.push({ field: 'operate', title: '操作', fixed: 'right', width: 310, templet: '#operate' });
    }

    let currentPage = 0;
    //用户id由对应的blade模板去定义并且赋值
    //竞调用户不能查看渠道标签
    if (userId === 1442) {
        cols.splice(7, 1);
    }
    let where = {};
    if (getQueryVariable("spu_id/condition")) {
        where = {
            "spu_id/condition": getQueryVariable("spu_id/condition")
        };
    }
    if (getQueryVariable("brand_id/condition")) {
        where = {
            "brand_id/condition": getQueryVariable("brand_id/condition")
        };
    }

    if (getQueryVariable("class_id2/condition")) {
        where = {
            "class_id2/condition": getQueryVariable("class_id2/condition")
        };
    }

    //直接跳转过来
    if (getQueryVariable('jumpUrl')) {
        const queryString = window.location.search;
        const urlParams = new URLSearchParams(queryString);
        let entries = urlParams.entries();
        for (const entry of entries) {
            where[entry[0]] = entry[1];
        }
    }

    if (getQueryVariable('search')) {
        setTimeout(function () {
            $('#skuListSearch').click();
        }, 500);
    }

    table.render({
        elem: '#skuList',
        url: '/api/sku/getSkuList',
        method: 'post',
        cellMinWidth: 50, //全局定义常规单元格的最小宽度
        toolbar: '#toolbar',
        where: where,
        request: {
            limitName: 'offset',
            pageName: 'p'
        },
        limits: [10, 30, 50],
        loading: true,
        first: true, //不显示首页
        last: true, //不显示尾页
        cols: [cols],
        id: 'skuList',
        size: 'sm',
        limit: 10,
        page: {},
        done: function (res, curr, count) {
            currentPage = curr;
        },
    });

    form.on('submit(load)', function (data) {
        where = data.fi;
        form.render();
        //执行重载
        table.reload('skuList', {
            page: {
                curr: 1
            },
            where: data.field,
        });
        return false;
    });

    table.on('tool(skuList)', function (obj) {
        let data = obj.data;
        let layEvent = obj.event;
        if (layEvent === 'putAway' || layEvent === 'unShelve') {
            let skuIds = [];
            skuIds.push(data.goods_id);
            if (skuIds.length === 0) {
                layer.msg('请选择要操作的sku', { icon: 5 });
                return false;
            }
            const statusName = layEvent === 'putAway' ? 'passed' : 'offshelf';
            batchUpdateSkuStatus(skuIds, statusName);
        }
        if (layEvent === 'saveSpu') {
            layer.open({
                type: 2,
                area: ['90%', '90%'],
                fixed: false,
                offset: '50px',
                //不固定
                // maxmin: true,
                title: '编辑SPU ',
                content: '/web/spu/saveSpu?spu_id=' + data.spu_id,
                end: function () {
                    table.reload('skuList', {
                        page: {
                            curr: currentPage
                        },
                        where: where,
                    });
                }
            });
        }
        if (layEvent === 'saveSku') {
            layer.open({
                type: 2,
                area: ['90%', '90%'],
                fixed: false,
                offset: '50px',
                //不固定
                // maxmin: true,
                title: '编辑SKU ',
                content: '/web/sku/saveSku?goods_id=' + data.goods_id,
                end: function () {
                    table.reload('skuList', {
                        page: {
                            curr: currentPage
                        },
                        where: where,
                    });
                }
            });
        }
    });

    //spu参数导出
    $(document).on('click', '#exportSkuList', function () {
        $.ajax({
            type: 'post',
            url: '/api/sku/exportSkuList',
            timeout: 30000, //超时时间设置，单位毫秒
            data: form.val("sku_list_form"),
            dataType: 'json',
            success: function (resp) {
                if (!resp) {
                    layer.msg('网络连接失败', { icon: 5 });
                    return false;
                }
                if (resp.code === 0) {
                    window.open(resp.data, '_blank');
                } else {
                    parent.layer.msg(resp.msg, { icon: 5 });
                    return false;
                }
            }
        });
    });

    //批量绑定系列
    $(document).on('click', '#batchUpdateItemSeries', function () {
        let checkStatus = table.checkStatus('skuList');
        let data = checkStatus.data;
        let sku_ids = data.map(obj => obj.goods_id);
        if (sku_ids.length === 0) {
            parent.layer.msg('请选择要操作的sku', { icon: 5 });
            return false;
        }

        //先检测能否绑定
        let url = '/api/skuSeries/checkCanBatchUpdateItemSeries';
        $.get(url, { sku_ids: sku_ids.join(',') }, function (res) {
            if (res.code !== 0) {
                parent.layer.msg(res.msg);
                return false;
            } else {
                let hasInvalidOrgSku = false;
                data.forEach(function (item) {
                    if (item.org_id != 3 && item.org_id != 6) {
                        hasInvalidOrgSku = true;
                        return;
                    }
                });
                if (hasInvalidOrgSku) {
                    parent.layer.msg('只能针对华云的商品进行绑定系列', { icon: 5 });
                    return false;
                }

                //判断是否有不同的分类或者品牌
                let class_id1 = Array.from(new Set(data.map(obj => obj.class_id1)));
                let class_id2 = Array.from(new Set(data.map(obj => obj.class_id2)));
                let standard_brand_id = Array.from(new Set(data.map(obj => obj.standard_brand_id)));

                if (class_id1.length > 1 || class_id2.length > 1 || standard_brand_id.length > 1) {
                    parent.layer.msg('只允许同分类同品牌的商品一起绑定，请分批操作', { icon: 5 });
                    return false;
                }
                sku_ids = sku_ids.join(',');
                layer.open({
                    type: 2,
                    area: ['80%', '80%'],
                    fixed: false, //不固定
                    maxmin: true,
                    title: '批量绑定系列',
                    content: '/web/skuSeries/batchUpdateItemSeries?sku_ids=' + sku_ids,
                    end: function () {
                        table.reload('list');
                    }
                });
            }
        });


    });


    //批量新增分发商品
    $(document).on('click', '#batchSaveShopDistributeSku', function () {
        let checkStatus = table.checkStatus('skuList');
        let data = checkStatus.data;
        let sku_ids = data.map(obj => obj.goods_id);
        if (sku_ids.length === 0) {
            parent.layer.msg('请选择要操作的sku', { icon: 5 });
            return false;
        }
        layer.open({
            type: 2,
            area: ['75%', '95%'],
            fixed: false, //不固定
            title: '批量新增分发选品',
            content: '/web/shopDistributeSku/batchSaveShopDistributeSku?sku_ids=' + sku_ids.join(','),
            end: function () {
                table.reload('list');
            }
        });
    });


    //批量上下架
    $(document).on('click', '#selectAll', function () {
        $('.layui-table-header .laytable-cell-checkbox i').click();
    });
    //批量上下架
    $(document).on('click', '#batchPutAway', function () {
        let checkStatus = table.checkStatus('skuList');
        let data = checkStatus.data;
        let skuIds = [];
        $.each(data, function (index, value) {
            skuIds.push(value.goods_id);
        });
        if (skuIds.length === 0) {
            layer.msg('请选择要操作的sku', { icon: 5 });
            return false;
        }
        batchUpdateSkuStatus(skuIds, 'passed');
    });

    //批量下架
    $(document).on('click', '#batchUnShelve', function () {
        let checkStatus = table.checkStatus('skuList');
        let data = checkStatus.data;
        let skuIds = [];
        $.each(data, function (index, value) {
            skuIds.push(value.goods_id);
        });
        if (skuIds.length === 0) {
            layer.msg('请选择要操作的sku', { icon: 5 });
            return false;
        }
        parent.layer.confirm('确定要下架选中的商品吗', {
            offset: ['30%', '40%']
        }, function (index) {
            $.post('/api/sku/batchUpdateSkuStatus', {
                'sku_ids': skuIds.join(','),
                'operate_type': -1,
            }, function (res) {
                if (res.code === 0) {
                    parent.layer.msg(res.msg, { icon: 6 });
                } else {
                    parent.layer.msg(res.msg, { icon: 5 });
                    return false;
                }
            });
        });
    });



    //批量上下架操作
    function batchUpdateSkuStatus(skuIds, status) {
        skuIds = skuIds.join(',');
        let statusName = status === 'offshelf' ? '下架' : '上架';
        let operateType = status === 'offshelf' ? '-1' : '1';
        layer.open({
            type: 2,
            area: ['600px', '500px'],
            offset: '100px',
            fixed: false, //不固定
            // maxmin: true,
            content: '/web/sku/batchUpdateSkuStatus?window=true&sku_ids=' + skuIds + '&operate_type=' + operateType,
            title: statusName + '商品',
            end: function () {
                table.reload('skuList');
            }
        });
    }

    //批量上下架
    $(document).on('click', '#export_sku', function () {
        $.ajax({
            type: 'post',
            url: '/api/sku/exportSku',
            timeout: 30000, //超时时间设置，单位毫秒
            data: form.val("sku_list_form"),
            dataType: 'json',
            success: function (resp) {
                if (!resp) {
                    layer.msg('网络连接失败', { icon: 5 });
                    return false;
                }
                if (resp.code === 0) {
                    window.open(resp.data, '_blank');
                } else {
                    parent.layer.msg(resp.msg, { icon: 5 });
                    return false;
                }
            }
        });
    });

    //划过显示成本价格
    let ladderPriceTipsVal = '';
    $(document).on('mouseenter', '.view_ladder_price', function () {
        let self = this;
        let rowIndex = $(this).parent().parent().parent().attr('data-index');
        let data = table.cache['skuList'][rowIndex].ladder_price;
        let supplierId = table.cache['skuList'][rowIndex].supplier_id;
        let moq = table.cache['skuList'][rowIndex].moq;
        if (!data) {
            return false;
        }
        let htmlArr = [];
        let color = 'green';
        if (data.length > 0) {
            htmlArr.push('<table class="layui-table table-status"><thead>' +
                '<tr><th style="text-align: center">数量</th>' +
                '<th style="text-align: center">国内含税(￥)</th>' +
                '<th style="text-align: center">香港交货($)</th>' +
                '</tr></thead><tbody>')
            for (let i = 0; i < data.length; i++) {
                if (supplierId == 17) {
                    if (data[i].price_cost_cn) {
                        htmlArr.push(
                            '<tr>' +
                            '  <td style="text-align: center">' + data[i].purchases + '</td>' +
                            '  <td style="text-align: center">' + (data[i].price_cost_cn ? data[i].price_cost_cn : '') + '</td>' +
                            '  <td style="text-align: center">' + (data[i].price_cost_us ? data[i].price_cost_us : '') + '</td>' +
                            '</tr>');
                    } else {
                        htmlArr.push(
                            '<tr>' +
                            '  <td style="text-align: center">' + (data[i].purchases != 0 ? data[i].purchases : moq) + '</td>' +
                            '  <td style="text-align: center">' + (data[i].price_cn ? data[i].price_cn : '') + '</td>' +
                            '  <td style="text-align: center">' + (data[i].price_us ? data[i].price_us : '') + '</td>' +
                            '</tr>');
                    }
                } else {
                    htmlArr.push(
                        '<tr>' +
                        '  <td style="text-align: center">' + data[i].purchases + '</td>' +
                        '  <td style="text-align: center">' + (data[i].price_cn ? data[i].price_cn : '') + '</td>' +
                        '  <td style="text-align: center">' + (data[i].price_us ? data[i].price_us : '') + '</td>' +
                        '</tr>');
                }

            }
            htmlArr.push('</tbody></table>')
            ladderPriceTipsVal = layer.tips(htmlArr.join(''), self, {
                tips: [3, "#009688"],
                time: 1000000,
                area: ['400px', 'auto'],
                skin: 'custom'
            });
        }
    }).on('mouseleave', '.view_ladder_price', function () {
        layer.close(ladderPriceTipsVal);
    });

    //划过显示展示价格
    let showPriceTipsVal = '';
    $(document).on('mouseenter', '.view_show_price', function () {
        let self = this;
        let rowIndex = $(this).parent().parent().parent().attr('data-index');
        let data = table.cache['skuList'][rowIndex].show_price;
        if (!data) {
            return false;
        }
        let htmlArr = [];
        let color = 'green';
        if (data.length > 0) {
            htmlArr.push('<table class="layui-table table-status"><thead>' +
                '<tr><th style="text-align: center">数量</th>' +
                '<th style="text-align: center">国内含税(￥)</th>' +
                '<th style="text-align: center">香港交货($)</th>' +
                '<th style="text-align: center">折扣价格(￥)</th>' +
                '</tr></thead><tbody>')
            for (let i = 0; i < data.length; i++) {
                htmlArr.push(
                    '<tr>' +
                    '  <td style="text-align: center">' + data[i].purchases + '</td>' +
                    '  <td style="text-align: center">' + (data[i].price_cn ? data[i].price_cn : '') + '</td>' +
                    '  <td style="text-align: center">' + (data[i].price_us ? data[i].price_us : '') + '</td>' +
                    '  <td style="text-align: center">' + (data[i].price_ac ? data[i].price_ac : '') + '</td>' +
                    '</tr>');
            }
            htmlArr.push('</tbody></table>')
            showPriceTipsVal = layer.tips(htmlArr.join(''), self, {
                tips: [3, "#009688"],
                time: 1000000,
                area: ['400px', 'auto'],
                skin: 'custom'
            });
        }
    }).on('mouseleave', '.view_show_price', function () {
        layer.close(showPriceTipsVal);
    });


    //划过显示参数
    let spuAttrsTipsVal = '';
    $(document).on('mouseenter', '.view_spu_attrs', function () {
        let self = this;
        let rowIndex = $(this).parent().parent().parent().attr('data-index');
        let data = table.cache['skuList'][rowIndex].spu_attrs.attrs_extend;
        if (!data) {
            return false;
        }
        let htmlArr = [];
        let color = 'green';
        if (data.length > 0) {
            htmlArr.push('<table class="layui-table table-status"><thead>' +
                '<tr><th style="text-align: center">参数名</th>' +
                '<th style="text-align: center">参数值</th>' +
                // '<th style="text-align: center">录入方式</th>' +
                '</tr></thead><tbody>');
            for (let i = 0; i < data.length; i++) {
                htmlArr.push(
                    '<tr>' +
                    '  <td style="text-align: center">' + data[i].attr_name + '</td>' +
                    '  <td style="text-align: center">' + (data[i].attr_value) + '</td>' +
                    // '  <td style="text-align: center">' + (data[i].insert_type) + '</td>' +
                    '</tr>');

            }
            htmlArr.push('</tbody></table>');
            spuAttrsTipsVal = layer.tips(htmlArr.join(''), self, {
                tips: [3, "#009688"],
                time: 1000000,
                area: ['400px', 'auto'],
                skin: 'custom'
            });
        }
    }).on('mouseleave', '.view_spu_attrs', function () {
        layer.close(spuAttrsTipsVal);
    });

    $(document).on('click', '#batchGoodsNameSearch', function () {
        layer.open({
            type: 1,
            title: '批量搜索商品型号',
            area: ['500px', '450px'],
            content: '<div style="padding: 20px;">' +
                '<div class="layui-form-item">' +
                '<textarea id="batchModelInput" placeholder="请输入型号列表，只支持换行和逗号分隔,最多支持300个型号" class="layui-textarea" style="height: 300px;"></textarea>' +
                '</div>' +
                '<div class="layui-form-item">' +
                '<button class="layui-btn layui-btn-sm" id="submitBatchSearch">确认</button>' +
                '</div>' +
                '</div>',
            success: function (layero, index) {
                $(layero).find('#submitBatchSearch').on('click', function () {
                    let inputText = $('#batchModelInput').val().trim();
                    if (!inputText) {
                        layer.msg('请输入型号列表');
                        return;
                    }

                    // 处理输入，只支持换行和逗号分隔
                    let models = inputText.replace(/[\n,]+/g, ',').split(',').filter(item => item);

                    if (models.length === 0) {
                        layer.msg('请输入有效的型号列表');
                        return;
                    }
                    // 去除空白字符
                    models = models.map(item => item.trim()).filter(item => item);
                    // 去重并将处理后的型号用逗号连接
                    let uniqueModels = [...new Set(models)];
                    // 限制最多20个型号
                    if (uniqueModels.length > 300) {
                        layer.msg('最多支持300个型号，已自动截取前300个');
                        uniqueModels = uniqueModels.slice(0, 300);
                    }
                    let searchModels = uniqueModels.join(',');

                    // 执行搜索操作
                    // 这里可以根据实际需求调用搜索接口或者设置表单值
                    $('input[name="goods_name_origin/eqs"]').val(searchModels);

                    // 关闭弹窗
                    layer.close(index);
                });
            }
        });
    });

    table.on('edit(skuList)', function (obj) {

        let stock = obj.value;
        let goodsId = obj.data.goods_id;
        $.ajax({
            url: '/api/sku/updateSkuStock',
            type: 'post',
            data: {
                stock: stock,
                goods_id: goodsId,
            },
            dataType: 'json',
            timeout: 10000,
            success: function (res) {
                if (!res) return layer.msg('网络错误，请重试');
                if (res.code === 0) {
                    layer.msg(res.msg)
                } else {
                    layer.msg(res.msg);
                }
            },
            error: function () {
                return layer.msg('网络错误，请重试');
            }
        });
    });


    let classSelector = xmSelect.render({
        el: '#classSelector',
        autoRow: true,
        name: 'class_id2/eqs',
        filterable: true,
        direction: 'down',
        size: 'mini',
        toolbar: {
            show: true,
            list: ['CLEAR']
        },
        height: 'auto',
        // radio: true,
        paging: true,
        pageSize: 15,
        data: function () {
            //这个数据在对应的blade页面下面...
            return classListForXmSelect;
        }
    });

    // 保存原始的标准品牌列表数据
    let originalStandardBrandList = [...standardBrandListForXmSelect];

    let standardBrandSelector = xmSelect.render({
        el: '#standardBrandSelector',
        autoRow: true,
        name: 'standard_brand_ids',
        filterable: true,
        direction: 'down',
        size: 'mini',
        toolbar: {
            show: true,
            list: ['CLEAR']
        },
        height: 'auto',
        paging: true,
        pageSize: 15,
        data: function () {
            return standardBrandListForXmSelect;
        }
    });

    let standardBrandAreaSelector = xmSelect.render({
        el: '#standardBrandAreaSelector',
        autoRow: true,
        name: 'standard_brand_area_ids',
        filterable: true,
        direction: 'down',
        size: 'mini',
        toolbar: {
            show: true,
            list: ['CLEAR']
        },
        height: 'auto',
        data: function () {
            return standardBrandAreaListForXmSelect;
        },
        on: function(data) {
            // 监听任何选择变化事件
            // data.arr包含了当前所有选中的项目
            let selectedAreaIds = data.arr.map(item => item.value);
            filterStandardBrandsByAreas(selectedAreaIds);
        },
        // 添加清空按钮监听
        toolbar: {
            show: true,
            list: ['CLEAR'],
            click: function(id) {
                if(id === 'CLEAR') {
                    // 清空时重置为完整列表
                    standardBrandListForXmSelect = [...originalStandardBrandList];
                    standardBrandSelector.update({
                        data: standardBrandListForXmSelect,
                        autoRow: true
                    });
                }
            }
        }
    });

    /**
     * 根据选中的区域过滤标准品牌列表
     * @param {Array} selectedAreaIds 选中的区域ID数组
     */
    function filterStandardBrandsByAreas(selectedAreaIds) {
        // 如果没有选择任何区域，展示所有品牌
        if (!selectedAreaIds || selectedAreaIds.length === 0) {
            standardBrandListForXmSelect = [...originalStandardBrandList];
        } else {
            // 过滤符合选中区域的品牌
            standardBrandListForXmSelect = originalStandardBrandList.filter(brand => {
                // 如果品牌没有brand_area属性或为空，保留它
                if (!brand.brand_area) return true;

                // 检查品牌区域是否在选中的区域列表中
                return selectedAreaIds.includes(parseInt(brand.brand_area)) ||
                       selectedAreaIds.includes(brand.brand_area);
            });
        }
        // 更新标准品牌选择器数据
        standardBrandSelector.update({
            data: standardBrandListForXmSelect,
            autoRow: true
        });

        // 如果之前有选中的品牌不在新的列表中，需要清除它们
        let currentSelected = standardBrandSelector.getValue();
        if (currentSelected.length > 0) {
            let validSelectedValues = currentSelected
                .filter(item => standardBrandListForXmSelect.some(brand => brand.value === item.value))
                .map(item => item.value);

            standardBrandSelector.setValue(validSelectedValues);
        }
    }

    // 查看筛选条件
    $(document).on('click', '#viewSelectedFilters', function () {
        let formData = form.val("sku_list_form");
        let filterDataForApi = {};
        let contentHtml = '<div style="padding-top: 10px; padding-left: 20px; line-height: 2.5; font-size: 14px;">';
        let contentAdded = false;

        //还要加上店铺选择
        //默认会有店铺选择
        //shopList 在模板页面
        let shopListForSelect = '';
        for (let i = 0; i < shopList.length; i++) {
            shopListForSelect += `<option value="${shopList[i].shop_id}">${shopList[i].shop_name}</option>`;
        }
        contentHtml += `<div><strong>店铺选择:</strong>
        <select name="shop_id" lay-search class="layui-select" style="width: 200px; display: inline-block; height: 30px; line-height: 30px; border: 1px solid #e6e6e6; border-radius: 2px;">
            ${shopListForSelect}
        </select>
        </div>`;

        // Supplier
        if (formData.supplier_id) {
            filterDataForApi.supplier_id = parseInt(formData.supplier_id);
            let supplierName = $(`select[name="supplier_id"] option[value="${formData.supplier_id}"]`).text();
            contentHtml += `<div><strong>供应商:</strong> ${supplierName}</div>`;
            contentAdded = true;
        }

        // Supplier Code
        if (formData['canal/condition']) {
            filterDataForApi.supplier_code = formData['canal/condition'];
            contentHtml += `<div><strong>供应商编码:</strong> ${formData['canal/condition']}</div>`;
            contentAdded = true;
        }

        // Standard Brands,这个是通用组件multiSelectPresenter里面获取的前端实例
        let brandIds = $('input[name="standard_brand_ids"]').val();
        if (brandIds && brandIds.length > 0) {
            filterDataForApi.standard_brand_ids = brandIds;
            let standardBrands = $('#standard_brand_idsSelector .label-content').attr('title') || '';
            contentHtml += `<div><strong>标准品牌:</strong> ${standardBrands}</div>`;
            contentAdded = true;
        }

        // SKU Class
        let classIds = classSelector.getValue('value');
        if (classIds && classIds.length > 0) {
            filterDataForApi.class_id2_list = classIds.join(',');
            let classNames = classSelector.getValue('nameStr');
            contentHtml += `<div><strong>SKU分类:</strong> ${classNames}</div>`;
            contentAdded = true;
        }

        // Has Attr
        if (formData.has_attr) {
            filterDataForApi.has_attr = parseInt(formData.has_attr);
            let hasAttrText = $(`select[name="has_attr"] option[value="${formData.has_attr}"]`).text();
            contentHtml += `<div><strong>是否有参数:</strong> ${hasAttrText}</div>`;
            contentAdded = true;
        }
        // Has Stock
        if (formData.has_stock) {
            filterDataForApi.has_stock = parseInt(formData.has_stock);
            let hasStockText = $(`select[name="has_stock"] option[value="${formData.has_stock}"]`).text();
            contentHtml += `<div><strong>是否有库存:</strong> ${hasStockText}</div>`;
            contentAdded = true;
        }
        // Stock
        if (formData.stock_num && formData.stock_compare_type) {
            filterDataForApi.stock = [formData.stock_compare_type, parseInt(formData.stock_num)];
            let stockCompareText = $(`select[name="stock_compare_type"] option[value="${formData.stock_compare_type}"]`).text();
            contentHtml += `<div><strong>库存数量:</strong> ${stockCompareText.trim()} ${formData.stock_num}</div>`;
            contentAdded = true;
        }

        // Ability Level
        if (formData['ability_level/eq']) {
            filterDataForApi.ability_level = parseInt(formData['ability_level/eq']);
            let abilityLevelText = $(`select[name="ability_level/eq"] option[value="${formData['ability_level/eq']}"]`).text();
            contentHtml += `<div><strong>履约程度:</strong> ${abilityLevelText}</div>`;
            contentAdded = true;
        }

        // Is Expired
        if (formData.is_expire) {
            filterDataForApi.is_expired = parseInt(formData.is_expire);
            let isExpiredText = $(`select[name="is_expire"] option[value="${formData.is_expire}"]`).text();
            contentHtml += `<div><strong>是否过期:</strong> ${isExpiredText}</div>`;
            contentAdded = true;
        }

        if (!contentAdded) {
            contentHtml = '<div style="padding: 20px; text-align: center;">未选择任何筛选条件</div>';
        } else {
            contentHtml += '</div>';
        }

        var layer = parent.layer;
        layer.open({
            type: 1,
            title: '当前筛选条件',
            area: ['450px', 'auto'],
            content: contentHtml,
            btn: ['确定批量推送选品', '取消'],
            yes: function(index, layero){
                if (Object.keys(filterDataForApi).length === 0) {
                    layer.msg('没有选择任何筛选条件，无法推送', {icon: 5});
                    return false;
                }
                // 获取上面选择的店铺id
                filterDataForApi.shop_id = $(layero).find('select[name="shop_id"]').val();
                // Make AJAX call here
                $.ajax({
                    url: '/api/shopDistributeSku/batchSaveShopDistributeSkuByFilter', // NOTE: This is an assumed endpoint
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(filterDataForApi),
                    dataType: 'json',
                    success: function(res) {
                        if (res.code === 0) {
                            layer.msg('推送成功!', {icon: 1});
                            layer.close(index);
                        } else {
                            layer.msg(res.msg || '推送失败，请重试', {icon: 5});
                        }
                    },
                    error: function() {
                        layer.msg('网络错误，请重试', {icon: 5});
                    }
                });
            },
            btn2: function(index, layero){
                layer.close(index);
            }
        });
    });

});


$('#class_name').click(function () {
    layui.use('layer', function () {
        let layer = layui.layer;
        layer.open({
            type: 2,
            area: ['700px', '450px'],
            offset: '100px',
            fixed: false, //不固定
            // maxmin: true,
            content: '/web/sku/chooseSkuClass?window=true',
            title: '选择分类'
        });
    });
});


$('.empty').click(function () {
    let obj = $(this).attr('data');
    if (obj === 'brand') {
        $('#brand_name').val('');
        $('#brand_id').val('');
    }
    if (obj === 'class') {
        $('#class_name').val('');
        $('#class_id').val('');
    }
});


