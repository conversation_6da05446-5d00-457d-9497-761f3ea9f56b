layui.use(['form', 'layer', 'xmSelect', 'admin'], function () {
    var xmSelect = layui.xmSelect;
    var form = layui.form;
    var layer = layui.layer;
    var admin = layui.admin;

    let orgId = $('#org_id').val();

    //监听提交
    form.on('submit(load)', function (data) {
        var goods_type = $('select[name=goods_type]').val();
        if (goods_type != 1 && !data.field.hk_delivery_time && !data.field.cn_delivery_time && (orgId == 1 || orgId == 3)) {
            layer.msg('香港货期与大陆货期必须填写一个', {icon: 5, offset: 200});
            return false;
        }
        var reg = /^\d+(\.\d+)?(\-\d+(\.\d+)?)?$/;
        if (data.field.hk_delivery_time || data.field.cn_delivery_time) {
            if (!reg.test(data.field.hk_delivery_time) && !reg.test(data.field.cn_delivery_time)) {
                layer.msg('香港或大陆货期格式必须为 数字-数字（支持小数）', {icon: 5, offset: 200});
                return false;
            }
        }
        $.ajax({
            type: 'post',
            url: '/api/sku/saveSku',
            timeout: 30000, //超时时间设置，单位毫秒
            data: data.field,
            dataType: 'json',
            success: function (resp) {
                if (!resp) {
                    parent.layer.msg('网络异常，请重试');
                    return false;
                }
                if (resp.code === 0) {
                    layer.msg(resp.msg, {icon: 6});
                    setTimeout(function () {
                        admin.closeThisDialog();
                    }, 500);
                } else {
                    layer.msg(resp.msg);
                    return false;
                }
            },

            error: function (jqXHR, textStatus, errorThrown) {
                layer.closeAll();
                layer.msg("网络异常，请重试", {icon: 5});
            }

        });

        return false;
    });
    form.on('select(goods_type)', function (data) {
        $('select[name=supplier_id]').html('<option value="">加载中</option>');
        form.render('select'); //刷新select选择框渲染
        layer.load(1, {time: 100 * 1000});
        $.ajax({
            type: 'post',
            url: '/fsapi/Obtain_type_supplier',
            timeout: 10000, //超时时间设置，单位毫秒
            data: {
                type_id: data.value
            },
            dataType: 'json',
            success: function (resp) {
                layer.closeAll();
                if (!resp) {
                    layer.msg('网络异常，请重试', {icon: 5});
                    return false;
                }
                if (resp.errcode == 0) {
                    var html = '<option value="">请选择供应商</option>';
                    $.each(resp.data, function (k, v) {
                        html = html + '<option value="' + k + '">' + v + '</option>';
                    });
                    $('select[name=supplier_id]').html(html);
                    form.render('select'); //刷新select选择框渲染
                } else {
                    layer.msg(resp.msg, {icon: 5});
                    return false;
                }
            },

            error: function (jqXHR, textStatus, errorThrown) {
                layer.closeAll();
                layer.msg("网络异常，请重试", {icon: 5});
            }

        });
    });

    form.on('select(supplier_id)', function (data) {
        var supplier_id = data.value; //得到被选中的值
        if (supplier_id == $('#ti_supplier_id').val()) {
            $('.alike_goods_name').css('display', 'block');
        } else {
            $('.alike_goods_name').css('display', 'none');
        }

    });

    //添加一行价格
    $('#add_ladder_price').click(function () {
        var trHTML = '<tr align="center" class="tr_ladder_price">' +
            '<td><input style="text-align: center" type="number" name="purchases[]" value=""/></td> ' +
            '<td><input style="text-align: center" type="number" name="price_cn[]" value=""/></td> ' +
            '<td><input style="text-align: center" type="number" name="price_us[]" value=""/></td> ' +
            '<td><input style="text-align: center" type="number" name="cost_price[]" value=""/></td>' +
            '<td><button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete_price">删除</button> </td>'
        $('#tbody_ladder_price').append(trHTML);
    });


//删除一行价格   包含动态添加的
    $(document).on("click", ".delete_price", function () {
        $(this).parent().parent().remove();
    });

    //spu选择
    let spuSelector = xmSelect.render({
        el: '#spu_id_selector',
        name: 'spu_id',
        searchTips: '请输入查找内容',
        paging: true,
        empty: '没有查找到数据',
        radio: true,
        clickClose: true,
        remoteSearch: true,
        pageRemote: true,
        filterable: true,
        remoteMethod: function (val, cb, show, pageIndex) {
            //val: 搜索框的内容, 不开启搜索默认为空, cb: 回调函数, show: 当前下拉框是否展开, pageIndex: 当前第几页
            $.ajax({
                url: '/api/spu/getSpuListForXmSelect',
                type: 'post',
                data: {
                    // spu_id: $('#spu_id').val(),
                    spu_name: val,
                    page: pageIndex
                },
                dataType: 'json',
                timeout: 10000,
                success: function (res) {
                    if (!res) return layer.msg('网络错误，请重试', {icon: 5});
                    if (res.code === 0) {
                        cb(res.data, res.count)
                    } else {
                        layer.msg(res.msg, {icon: 6});
                    }
                },
                error: function () {
                    return layer.msg('网络错误，请重试', {icon: 5});
                }
            });
        }
    })

    let spuId = $('#spu_id').val();
    let spuName = $('#spu_name').val();
    let brandName = $('#brand_name').val();
    if (spuId && spuName) {
        spuSelector.setValue([
            {name: spuName + ' (' + brandName + ')', value: spuId},
        ]);
    }

});
