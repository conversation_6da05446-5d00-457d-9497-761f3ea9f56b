layui.use(['table', 'form', 'laydate', 'layer', 'upload', 'index', 'xmSelect'], function () {
    var table = layui.table;
    var laydate = layui.laydate;
    var form = layui.form;
    var index = layui.index;
    var upload = layui.upload;
    var xmSelect = layui.xmSelect;

    let currentPage = 1;
    table.render({
        elem: '#shopSkuList',
        url: '/api/shopSku/getShopSkuList',
        method: 'post',
        request: {
            pageName: 'page',//页码的参数名称，默认：page
            limitName: 'limit' //每页数据量的参数名，默认：limit
        },
        limit: 15,
        loading: true,
        size: 'sm',
        cols: [[
            { type: 'checkbox' },
            { field: 'id', title: '序号', width: 60 },
            { field: 'lx_sku_id', title: '猎芯SKUID', width: 150 },
            { field: 'shop_sku_id', title: '第三方店铺SKUID', width: 130 },
            { field: 'sku_sn', title: 'SKU编码', width: 160 },
            { field: 'shop_item_id', title: '第三方店铺其他ID', width: 130 },
            { field: 'goods_name', title: '型号', width: 180 },
            { field: 'brand_name', title: '品牌', width: 120 },
            { field: 'goods_title', title: '商品标题', width: 350 },
            { field: 'canal', title: '供应商编码', width: 100 },
            {
                field: 'stock',
                title: '库存(直接编辑)',
                width: 130,
                edit: 'text',
                style: 'background-color:white',
                templet: function (d) {
                    d.LAY_OLD_STOCK = d.stock; // 保存原始库存值
                    return d.stock;
                }
            },
            {
                field: 'init_status_name',
                title: '厂直',
                width: 60,
                templet: function (data) {
                    if (data.init_status_name === '成功') {
                        return '<span style="color: green;">' + data.init_status_name + '</span>';
                    } else if (data.init_status_name === '失败') {
                        return '<span style="color: red;">' + data.init_status_name + '</span>';
                    } else if (data.init_status_name === '未打') {
                        return '<span style="color: dodgerblue;">' + data.init_status_name + '</span>';
                    } else {
                        return '';
                    }
                }
            },
            { field: 'shop_price', title: '价格' },
            { field: 'rate', title: '价格系数', width: 100 },
            { field: 'cn_delivery_time_transfer', title: '交期(转换后)', width: 100 ,templet: function (data) {
                return data.cn_delivery_time_transfer ? '<span title="' + data.cn_delivery_time + '">' + data.cn_delivery_time_transfer + '</span>' : '';
            }},
            {
                field: 'shop_name', title: '店铺名称', templet: function (data) {
                    return data.shop ? data.shop.shop_name : '';
                }, width: 130
            },
            {
                field: 'is_on_sale', title: '上下架', templet: function (data) {
                    return data.is_on_sale === 1 ? '上架' : '下架';
                },
                width: 70
            },
            {
                field: 'platform_name', title: '外部平台', width: 80
            },
            { field: 'audit_status_name', title: '审核状态', width: 80 },
            { field: 'create_time', title: '创建时间', width: 150 },
            { field: 'update_time', title: '修改时间', width: 150 },
            // {field: 'operation', title: '操作', templet: '#operation', width: 120}
        ]],
        id: 'shopSkuList',
        page: {},
        done: function (res, curr, count) {
            currentPage = curr;
        }
    });

    xmSelect.render({
        el: '#brandSelector',
        autoRow: true,
        name: 'brand_name',
        filterable: true,
        direction: 'down',
        size: 'mini',
        toolbar: {
            show: true,
            list: ['CLEAR']
        },
        height: 'auto',
        paging: true,
        pageSize: 10,
        filterable: true,
        data: function () {
            //这个数据在对应的blade页面下面...
            return brandListForXmSelect;
        }
    });

    xmSelect.render({
        el: '#classSelector',
        autoRow: true,
        name: 'lx_class_id',
        filterable: true,
        direction: 'down',
        size: 'mini',
        toolbar: {
            show: true,
            list: ['CLEAR']
        },
        height: 'auto',
        paging: true,
        pageSize: 15,
        data: function () {
            //这个数据在对应的blade页面下面...
            return classListForXmSelect;
        }
    });

    // xmSelect.render({
    //     el: '#canalSelector',
    //     autoRow: true,
    //     name: 'canal',
    //     filterable: true,
    //     direction: 'down',
    //     size: 'mini',
    //     toolbar: {
    //         show: true,
    //         list: ['CLEAR']
    //     },
    //     height: 'auto',
    //     paging: true,
    //     pageSize: 10,
    //     filterable: true,

    //     data: function () {
    //         //这个数据在对应的blade页面下面...
    //         return canalListForXmSelect;
    //     }
    // });

    xmSelect.render({
        el: '#supplierSelector',
        autoRow: true,
        name: 'supplier_id',
        filterable: true,
        direction: 'down',
        size: 'mini',
        toolbar: {
            show: true,
            list: ['CLEAR']
        },
        filterable: true,
        paging: true,
        pageSize: 30,
        height: 'auto',
        data: function () {
            //这个数据在对应的blade页面下面...
            return supplierListForXmSelect;
        }
    });

    form.on('submit(shopSkuList)', function (data) {
        //执行重载
        table.reload('shopSkuList', {
            page: {
                curr: 1 //重新从第 1 页开始
            },
            where: data.field
        });
        return false;
    });

    form.on('submit(reset)', function (data) {
        location.reload();
    });

    $(document).on('click', '#exportSkuList', function () {
        let data = form.val("sku_list_form");
        if (data.shop_id == 0) {
            layer.msg('请先选择要导出的店铺', { icon: 5 });
            return;
        }
        $.ajax({
            type: 'post',
            url: '/api/shopSku/exportShopSku',
            timeout: 30000, //超时时间设置，单位毫秒
            data: data,
            dataType: 'json',
            success: function (resp) {
                if (!resp) {
                    layer.msg('网络连接失败', { icon: 5 });
                    return false;
                }
                if (resp.code === 0) {
                    window.open(resp.data, '_blank');
                } else {
                    parent.layer.msg(resp.msg, { icon: 5 });
                    return false;
                }
            }
        });
    });

    //拉取商店sku
    $(document).on('click', '#fetchShopSku', function () {
        let data = form.val("sku_list_form");
        console.log(data);
        if (data.shop_id == 0) {
            layer.msg('请先选择要导出的店铺', { icon: 5 });
            return;
        }
        let url = '/api/shopSku/fetchShopSku';
        $.ajax({
            url: url,
            type: 'post',
            data: {
                shop_id: data.shop_id
            },
            dataType: 'json',
            timeout: 10000,
            success: function (res) {
                if (res.code !== 0) {
                    layer.msg(res.msg, { icon: 5 });
                } else {
                    layer.msg(res.msg, { icon: 6 });
                    table.reload('shopSkuList', {
                        page: {
                            curr: currentPage //重新从第 1 页开始
                        },
                        where: data.field
                    });
                    // layer.closeAll();
                }
            },
            error: function () {
                return layer.msg('网络错误，请重试', { icon: 5 });
            }
        });
    });

    //批量删除
    $(document).on('click', '#batch_delete_shopSku', function () {
        let checkStatus = table.checkStatus('shopSkuList');
        let data = checkStatus.data;
        let ids = [];
        $.each(data, function (index, value) {
            ids.push(value.id);
        });
        if (ids.length === 0) {
            layer.msg('请选择要操作的数据', { icon: 5 });
            return false;
        }
        layer.confirm('确定要批量删除选中的商品吗？', {
            btn: ['确定', '取消'] //按钮
        }, function () {
            $.ajax({
                url: '/api/shopSku/deleteShopSku',
                type: 'post',
                data: {
                    ids: ids,
                    is_batch: 1
                },
                dataType: 'json',
                timeout: 10000,
                success: function (res) {
                    if (res.code !== 0) {
                        layer.msg(res.msg, { icon: 5 });
                    } else {
                        layer.msg(res.msg, { icon: 6 });
                        table.reload('shopSkuList', {
                            page: {
                                curr: currentPage //重新从第 1 页开始
                            },
                            where: data.field
                        });
                        layer.closeAll();
                    }
                },
                error: function () {
                    return layer.msg('网络错误，请重试', { icon: 5 });
                }
            });
        });

    });

    table.on('edit(shopSkuList)', function (obj) { //注：edit是固定事件名，test是table原始容器的属性 lay-filter="对应的值"
        if (!(/^\d+$/.test(obj.value))) {
            layer.msg('请输入纯数字');
            return false;
        }
        $.ajax({
            url: '/api/shopSku/updateShopSkuStock',
            type: 'post',
            data: {
                id: obj.data.id,
                stock: obj.value,
            },
            dataType: 'json',
            timeout: 10000,
            success: function (res) {
                if (res.code !== 0) {
                    layer.msg(res.msg, { icon: 5 });
                } else {
                    layer.msg(res.msg, {
                        offset: ['50px'],
                        time: 1000,
                        icon: 6
                    });
                    // table.reload('shopSkuList', {
                    //     page: {
                    //         curr: currentPage //重新从第 1 页开始
                    //     },
                    //     where: data.field
                    // });
                    // layer.closeAll();
                }
            },
            error: function () {
                return layer.msg('网络错误，请重试', { icon: 5 });
            }
        });
    });

    $(document).on('click', '#fetchSkuInfo', function () {
        layer.open({
            title: '拉取商品信息',
            area: ['50%', '80%'],
            type: 2,
            content: '/web/shopSku/fetchSkuInfo?window=fullWindow',
        });
    });

    $(document).on('click', '#fetchSkuInfo', function () {
        let checkStatus = table.checkStatus('shopSkuList');
        let data = checkStatus.data;
        let shopSkuIdList = [];
        $.each(data, function (index, value) {
            shopSkuIdList.push(value.shop_sku_id);
        });
        if (shopSkuIdList.length === 0) {
            layer.msg('请选择要操作的sku', { icon: 5 });
            return false;
        }
        $.post('/api/alikeSpu/deleteAlikeSpuSku', {
            'main_id': mainId,
            'shopSkuIdList': data.goods_id,
            'spu_name': spuName
        }, function (res) {
            if (res.code === 0) {
                layer.msg(res.msg, { icon: 6 });
            } else {
                layer.msg(res.msg, { icon: 5 });
                return false;
            }
        });
    });

    $(document).on('click', '#changeCzFlag', function () {
        let checkStatus = table.checkStatus('shopSkuList');
        let data = checkStatus.data;
        console.log(data);
        let params = JSON.stringify(data);
        let $btn = $(this);
        $btn.addClass('layui-btn-disabled').prop('disabled', true);
        $btn.html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 处理中...');

        $.ajax({
            url: '/api/shopSku/changeCzFlag',
            type: 'POST',
            data: { data: params },
            dataType: 'json',
            success: function (res) {
                if (res.code === 0) {
                    layer.msg(res.msg, { icon: 6 });
                    table.reload('shopSkuList');
                } else {
                    layer.msg(res.msg, { icon: 5 });
                }
            },
            error: function () {
                layer.msg('网络错误,请重试', { icon: 5 });
            },
            complete: function () {
                $btn.removeClass('layui-btn-disabled').prop('disabled', false);
                $btn.html('<i class="layui-icon">&#xe67c;</i>打厂直标');
            }
        });
    });

   //导入价格系数
    upload.render({
        elem: '#upload_rate', //绑定元素
        url: '/api/shopSku/batchUpdateSkuRate', //上传接口
        accept: 'file',
        auto: true,
        data: {
            type: function () {
                return $("input[name='type']:checked").val();
            }
        },
        before: function (obj) {

        },
        choose: function (obj) {
            $('#hasFile').val(1);
        },
        done: function (res) {
            //上传完毕回调
            if (res.code === 0) {
                layer.msg(res.msg, { icon: 6 });
                table.reload('shopSkuList');
            } else {
                layer.msg(res.msg, { icon: 5 });
            }
        }, error: function () {
            //请求异常回调
            layer.msg('请求异常', { icon: 5 });
        }
    });

    $(document).on('click', '#download_upload_rate_template', function () {
        window.open('/template/upload_shop_sku_rate.xlsx', '_blank');
    });

    //执行实例
    upload.render({
        elem: '#upload_button', //绑定元素
        url: '/api/shopSku/importShopSkuStock', //上传接口
        accept: 'file',
        // bindAction: '#confirmUpload',
        auto: true,
        data: {
            type: function () {
                return $("input[name='type']:checked").val();
            }
        },
        before: function (obj) {

        },
        choose: function (obj) {
            $('#hasFile').val(1);
        },
        done: function (res) {
            //上传完毕回调
            layer.closeAll();
            layer.msg(res.msg, { icon: 6 });
            table.reload('shopSkuList');
        }, error: function () {
            //请求异常回调
            layer.msg('请求异常', { icon: 5 });
            layer.closeAll();
        }
    });

    $('#shopSku_upload_log').click(function () {
        layer.open({
            title: '商品上传结果页',
            area: ['80%', '90%'],
            type: 2,
            content: '/web/shopSku/shopSkuUploadLog?window=fullWindow',
        });
    });

    table.on('tool(shopSkuList)', function (obj) {
        let data = obj.data;
        if (obj.event === "update_shopSku") {
            let id = data.id;
            layer.open({
                title: '修改商品',
                area: ['50%', '60%'],
                type: 2,
                content: '/web/shopSku/saveShopSku?window=fullWindow&id=' + id,
            });
        }
        if (obj.event === 'delete') {
            layer.confirm('确定删除这个商品吗？', {
                btn: ['确定', '取消'] //按钮
            }, function () {
                let id = $(this).attr('value');
                $.ajax({
                    url: '/api/shopSku/deleteShopSku',
                    type: 'post',
                    data: {
                        id: data.id
                    },
                    dataType: 'json',
                    timeout: 10000,
                    success: function (res) {
                        if (res.code !== 0) {
                            layer.msg(res.msg, { icon: 5 });
                        } else {
                            layer.msg(res.msg, { icon: 6 });
                            table.reload('shopSkuList', {
                                page: {
                                    curr: currentPage //重新从第 1 页开始
                                },
                                where: data.field
                            });
                            layer.closeAll();
                        }
                    },
                    error: function () {
                        return layer.msg('网络错误，请重试', { icon: 5 });
                    }
                });
            });
        }
    });
});
