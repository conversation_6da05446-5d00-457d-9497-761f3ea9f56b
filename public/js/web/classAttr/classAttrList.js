layui.use(['table', 'form', 'soulTable'], function () {
    var table = layui.table;
    var form = layui.form;
    var soulTable = layui.soulTable;

    table.render({
        elem: '#Reload',
        url: '/api/classAttr/classAttrList',
        method: 'post',
        toolbar: '#toolbarDemo',
        defaultToolbar: false,
        loading: true,
        cols: [[
            {field: 'attr_id', title: '属性ID', width: 70},
            {field: 'class_name', title: '分类名字', width: 270},
            {field: 'attr_name', title: '属性名称', width: 150},
            {field: 'show_name', title: '展示名称', width: 210, templet: '#show_name'},
            {field: 'is_main', title: '核心参数', width: 100, templet: '#is_main'},
            {field: 'insert_type_name', title: '录入方式', width: 150},
            {field: 'preference_name', title: '偏向', width: 100},
            {
                field: 'attr_unit', title: '默认单位', width: 100, templet: function (data) {
                    return data.standard_unit;
                }
            },
            {
                field: 'dgk_attr_id', title: '原始ID', width: 100, templet: function (data) {
                    return data.dgk_attr_id ? data.dgk_attr_id : '无';
                }
            },
            {field: 'class_id', title: '分类ID', width: 80},
            {field: 'status', title: '属性状态', width: 100, templet: '#status'},
            {field: 'remark', title: '备注', width: 200},
            {field: 'value_num_count', title: '数值数量', width: 100},
            {field: 'create_name', title: '创建人', width: 100},
            {field: 'add_time', title: '创建时间', width: 180},
            {field: 'experience', title: '操作', fixed: 'right', width: 120, templet: '#cz'},
        ]],
        id: 'Reload',
        page: {},
        done: function () {
            soulTable.render(this);
        },
        size: 'sm'
    });

    form.on('submit(load)', function (data) {
        //执行重载
        table.reload('Reload', {
            page: {
                curr: 1 //重新从第 1 页开始
            },
            where: data.field,
        });
        return false;
    });

    table.on('tool(Reload)', function (obj) {
        switch (obj.event) {
            case 'childHide':
                layer.confirm('确定隐藏这个参数吗？', {icon: 3}, function (index) {
                    obj.data.status = 2;
                    Hide(obj.data);
                });
                break;
            case 'childEdit':
                Edit(obj.data);
                break;
            case 'childShow':
                layer.confirm('确定显示这个参数吗？', {icon: 3}, function (index) {
                    obj.data.status = 1;
                    Hide(obj.data);
                });
                break;
        }
    });

    function Hide(data) {
        $.ajax({
            url: '/api/classAttr/hideClassAttr',
            type: 'post',
            data: {
                attr_id: data.attr_id,
                status: data.status
            },
            dataType: 'json',
            timeout: 10000,
            success: function (res) {
                if (!res) return parent.layer.msg('网络错误，请重试', {icon: 5});
                if (res.code === 0) {
                    layer.closeAll();
                    $('#loadList').click();
                }
                parent.layer.msg(res.msg, {icon: 6});
            },
            error: function () {
                return parent.layer.msg('网络错误，请重试', {icon: 5});
            }
        });
    }

    function Edit(data) {
        layer.open({
            type: 2,
            area: ['800px', '630px'],
            fixed: false, //不固定
            // maxmin: true,
            title: '编辑参数 ',
            content: '/web/classAttr/saveClassAttr?attr_id=' + data.attr_id + '&window=true'
        });
    }

    table.on('toolbar(Reload)', function (obj) {
        if (obj.event === 'add') {
            layer.open({
                type: 2,
                area: ['800px', '630px'],
                fixed: false, //不固定
                // maxmin: true,
                title: '新增参数 ',
                content: '/web/classAttr/saveClassAttr?window=true'
            });
        }
    });

    $(document).on('blur', ".update_show_name", function () {
        let classId = $(this).attr('data_id');
        let originShowName = $(this).attr('value');
        $(this).attr('value', $(this).val());
        let showName = $(this).val();
        console.log(originShowName);
        if (originShowName !== showName) {
            const url = '/api/classAttr/updateShowName?attr_id=' + classId + '&show_name=' + showName;
            $.get(url, function (res) {
                if (res.code === 0) {
                    parent.layer.msg("修改展示名称成功");
                }
            });
        }
    });
});
