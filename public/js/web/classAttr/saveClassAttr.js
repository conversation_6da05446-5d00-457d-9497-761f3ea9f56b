layui.use(['table', 'form', 'xmSelect'], function () {
    var table = layui.table;
    var form = layui.form;
    var xmSelect = layui.xmSelect;

    form.on('submit(load)', function (data) {
        $.ajax({
            url: '/api/classAttr/saveClassAttr',
            type: 'post',
            data: data.field,
            dataType: 'json',
            timeout: 10000,
            success: function (res) {
                if (!res) return layer.msg('网络错误，请重试', {icon: 5});
                if (res.code === 0) {
                    parent.layer.closeAll();
                    parent.layer.msg(res.msg, {icon: 6});
                    parent.$('#loadList').click();
                } else {
                    layer.msg(res.msg, {icon: 5});
                }
            },
            error: function () {
                return layer.msg('网络错误，请重试', {icon: 5});
            }
        });

        return false;
    });

    form.on('select(insert_type)', function (data) {
        let insertType = data.value;
        if (insertType == 5) {
            $('#unit_convert_id_div').hide();
            $('#preference_div').hide();
        }else{
            $('#unit_convert_id_div').show();
            $('#preference_div').show();
        }
    });

    form.on('select(parent_id)', function (data) {
        var val = data.value;
        $.ajax({
            url: '/api/class/getClassSubList',
            type: 'post',
            data: {
                parent_id: val
            },
            dataType: 'json',
            timeout: 10000,
            success: function (res) {
                if (!res) return layer.msg('网络错误，请重试', {icon: 5});

                if (res.code === 0) {
                    var html = '<option value="">请选择二级分类</option>';
                    $.each(res.data, function (k, v) {
                        html += '<option value="' + k + '">' + v + '</option>';
                    });
                    $('select[name=class_id]').html(html);
                    form.render('select');
                } else {
                    layer.msg(res.msg, {icon: 6});
                }
            },
            error: function () {
                return layer.msg('网络错误，请重试', {icon: 5});
            }
        });
    });
});
