layui.use(['table', 'form', 'laydate', 'layer', 'upload', 'admin'], function () {
    var table = layui.table;
    var laydate = layui.laydate;
    var form = layui.form;
    var admin = layui.admin;
    var upload = layui.upload;

    let currentPage = 1;
    table.render({
        elem: '#list',
        url: '/api/trainingData/getTrainingDataList',
        method: 'get',
        size: 'sm',
        defaultToolbar: ['filter', 'exports'],
        loading: true,
        where: {
            type: 2 // 只显示型号类型的数据
        },
        cols: [[
            {field: 'id', title: 'ID', width: 80, sort: true},
            {field: 'content', title: '型号内容', width: 300},
            {field: 'create_time', title: '创建时间', width: 160},
            {field: 'create_name', title: '创建人', width: 120},
            {field: 'update_time', title: '更新时间', width: 160},
            {field: 'update_name', title: '更新人', width: 120},
            {title: '操作', toolbar: '#operation', width: 150}
        ]],
        limit: 15,
        id: 'list',
        page: {},
        done: function (res, curr, count) {
            currentPage = curr;
        }
    });

    form.on('submit(list)', function (data) {
        // 添加类型过滤
        data.field.type = 2;
        //执行重载
        table.reload('list', {
            page: {
                curr: 1 //重新从第 1 页开始
            },
            where: data.field
        });
        return false;
    });

    //添加型号训练数据
    $('#save_model_training_data').click(function () {
        let title = "新增型号训练数据";
        layer.open({
            title: title,
            area: ['50%', '60%'],
            type: 2,
            content: '/web/trainingData/saveModelTrainingData?window=fullWindow',
            end: function () {
                table.reload('list', {
                    page: {
                        curr: currentPage
                    },
                });
            }
        });
    });

    //编辑型号训练数据
    $(document).on('click', '.edit_model_training_data', function () {
        let id = $(this).val();
        let title = "编辑型号训练数据";
        layer.open({
            title: title,
            area: ['50%', '60%'],
            type: 2,
            content: '/web/trainingData/saveModelTrainingData?window=fullWindow&id=' + id,
            end: function () {
                table.reload('list', {
                    page: {
                        curr: currentPage
                    },
                });
            }
        });
    });

    //删除型号训练数据
    $(document).on('click', '.delete_model_training_data', function () {
        let id = $(this).val();
        layer.confirm('确定要删除这条型号训练数据吗?', {icon: 3, title: '删除型号训练数据'}, function (index) {
            $.ajax({
                type: 'post',
                url: '/api/trainingData/deleteTrainingData?id=' + id,
                success: function (resp) {
                    if (resp.code > 0) {
                        layer.msg(resp.msg, {icon: 5});
                    } else {
                        table.reload('list', {
                            page: currentPage
                        });
                        layer.msg(resp.msg, {icon: 6});
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    Prompt("网络异常，请重试", 5);
                },
            });
        });
    });

    //导出型号训练数据
    $(document).on('click', '#export_model_training_data', function () {
        let data = form.val("model_training_data_filter_form");
        data.type = 2; // 只导出型号类型
        let params = JSON.stringify(data);
        let url = '/api/trainingData/exportTrainingData?params=' + params;
        window.open(url, '_blank');
    });

    //导入型号数据
    let uploadModelInst = upload.render({
        elem: '#import_model_data',
        url: '/api/trainingData/importModelData',
        field: 'upload',
        exts: 'csv',
        accept: 'file',
        data: {
            type: 2
        },
        before: function (obj) {
            layer.msg('加载中', {
                icon: 16,
                shade: 0.01
            });
        },
        done: function (res) {
            if (res.code === 0) {
                layer.msg(res.msg, {
                    icon: 6
                });
                table.reload('list');
                return false;
            } else {
                layer.msg(res.msg, {
                    icon: 5
                });
                return false;
            }
        },
        error: function (res) {
            layer.msg('上传失败', {
                icon: 5
            });
            return false;
        }
    });

});
