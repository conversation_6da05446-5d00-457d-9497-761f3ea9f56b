layui.use(['form', 'layer', 'admin'], function () {
    var form = layui.form;
    var layer = layui.layer;
    var admin = layui.admin;

    form.on('submit(SaveBrandTrainingData)', function (data) {
        admin.btnLoading('#SaveBrandTrainingData', '提交中');
        let url = '/api/trainingData/saveTrainingData';
        let result = $.post(url, data.field, function (result) {
            if (result.code === 0) {
                admin.closeThisDialog();
                parent.layer.msg('操作成功', {icon: 6});
            } else {
                admin.btnLoading('#SaveBrandTrainingData', false);
                layer.msg(result.msg, {icon: 5, offset: '150px'});
                return false;
            }
        });
    });

});
