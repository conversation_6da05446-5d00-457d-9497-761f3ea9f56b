layui.use(['table', 'admin', 'form', 'laydate'], function () {
    var table = layui.table;
    var form = layui.form;
    var admin = layui.admin;
    var laydate = layui.laydate;

    // 渲染日期范围选择器
    laydate.render({
        elem: '#create_time',
        range: '~',
        type: 'date'
    });

    // 渲染表格
    table.render({
        elem: '#dataTable',
        url: '/api/classManagement/getClassManagementList',
        page: true,
        limit: 10,
        size : 'sm',
        cols: [[
            {field: 'id', title: 'ID', width: 80},
            {title: '编辑', width: 80, templet: '<div><a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a></div>'},
            {field: 'create_time_text', title: '时间', width: 160},
            {field: 'goods_name', title: '型号',width: 200},
            {field: 'brand_name', title: '品牌', width: 120},
            {field: 'class_id', title: '分类', width: 120,templet: function (d) {
                return d.class_id?d.class_id:'';
            }},
            {field: 'class_name', title: '分类名称', width: 220,templet: function (d) {
                return d.class?d.class.class_name:'';
            }},
            {field: 'status_text', title: '状态', width: 100},
            {field: 'order_sn', title: '采购单单号', width: 150},
            {field: 'sales_name', title: '采购员', width: 90},
            {field: 'sales_id', title: '采购员ID', width: 90},
            {field: 'update_time_text', title: '更新时间', width: 160},
            {field: 'update_name', title: '更新人', width: 100},
            // {field: 'remark', title: '备注'},
            // {title: '操作', toolbar: '#toolbar', width: 280, fixed: 'right'}
        ]],
        response: {
            statusCode: 0
        },
        parseData: function (res) {
            return {
                "code": res.code,
                "msg": res.msg,
                "count": res.count,
                "data": res.data
            };
        }
    });

    // 表格工具条事件
    table.on('tool(dataTable)', function (obj) {
        var data = obj.data;
        var event = obj.event;

        if (event === 'edit') {
            // 编辑
            layer.open({
                type: 2,
                title: '编辑分类管理',
                area: ['600px', '620px'],
                content: '/web/classManagement/saveClassManagement?id=' + data.id,
                end: function() {
                    table.reload('dataTable');
                }
            });
        } else if (event === 'process') {
            // 标记为已处理
            layer.confirm('确定要将该记录标记为已处理吗？', function (index) {
                $.ajax({
                    url: '/api/classManagement/updateStatus',
                    type: 'post',
                    data: {
                        id: data.id,
                        status: 1
                    },
                    dataType: 'json',
                    success: function (res) {
                        if (res.code === 0) {
                            layer.msg(res.msg, {icon: 6});
                            table.reload('dataTable');
                        } else {
                            layer.msg(res.msg, {icon: 5});
                        }
                    },
                    error: function () {
                        layer.msg('网络错误，请重试', {icon: 5});
                    }
                });
                layer.close(index);
            });
        } else if (event === 'delete') {
            // 删除
            layer.confirm('确定要删除该记录吗？', function (index) {
                $.ajax({
                    url: '/api/classManagement/deleteClassManagement',
                    type: 'post',
                    data: {
                        id: data.id
                    },
                    dataType: 'json',
                    success: function (res) {
                        if (res.code === 0) {
                            layer.msg(res.msg, {icon: 6});
                            table.reload('dataTable');
                        } else {
                            layer.msg(res.msg, {icon: 5});
                        }
                    },
                    error: function () {
                        layer.msg('网络错误，请重试', {icon: 5});
                    }
                });
                layer.close(index);
            });
        }
    });

    // 搜索表单提交
    form.on('submit(searchForm)', function (data) {
        table.reload('dataTable', {
            where: data.field,
            page: {
                curr: 1
            }
        });
        return false;
    });

    // 重置按钮点击事件
    form.on('submit(resetBtn)', function () {
        window.location.reload();
    });
});
