layui.use(['table', 'form', 'layer'], function () {
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;

    // 已经在后端加载了分类数据，不需要再次加载
    // 只需要在选择主分类后加载子分类
    function loadSubClassOptions(parentId) {
        $.ajax({
            url: '/api/class/getSubClassList',
            type: 'get',
            data: {parent_id: parentId},
            dataType: 'json',
            success: function (res) {
                if (res.code === 0 && res.data) {
                    var options = '<option value="">请选择</option>';
                    $.each(res.data, function (index, item) {
                        options += '<option value="' + item.id + '">' + item.name + '</option>';
                    });
                    $('select[name=sub_class_id]').html(options);
                    form.render('select');
                } else {
                    // 如果没有子分类或请求失败，清空子分类选择框
                    $('select[name=sub_class_id]').html('<option value="">请选择</option>');
                    form.render('select');
                }
            },
            error: function() {
                $('select[name=sub_class_id]').html('<option value="">请选择</option>');
                form.render('select');
            }
        });
    }

    // 监听主分类选择，加载子分类
    form.on('select(class_id)', function(data){
        if (!data.value) {
            $('select[name=sub_class_id]').html('<option value="">请选择</option>');
            form.render('select');
            return;
        }

        // 加载子分类
        loadSubClassOptions(data.value);
    });

    // 表单提交
    form.on('submit(saveForm)', function (data) {
        // 验证表单
        if (!data.field.class_id) {
            layer.msg('请选择分类', {icon: 5});
            return false;
        }

        // 获取隐藏的ID字段
        var id = $('input[name=id]').val();
        if (!id) {
            layer.msg('缺失记录ID', {icon: 5});
            return false;
        }

        $.ajax({
            url: '/api/classManagement/saveClassManagement',
            type: 'post',
            data: {
                id: id,
                class_id: data.field.class_id,
                sub_class_id: data.field.sub_class_id || ''
            },
            dataType: 'json',
            success: function (res) {
                if (res.code === 0) {
                    layer.msg(res.msg, {icon: 1, time: 1000}, function () {
                        // 关闭当前弹出层
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                        // 刷新父页面表格
                        parent.layui.table.reload('classManagementTable');
                    });
                } else {
                    layer.msg(res.msg || '更新分类失败', {icon: 5});
                }
            },
            error: function () {
                layer.msg('网络错误，请重试', {icon: 5});
            }
        });

        return false;
    });

    // 取消按钮
    form.on('submit(closeForm)', function (data) {
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
        return false;
    });
});
