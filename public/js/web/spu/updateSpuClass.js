layui.use(['table', 'form', 'laydate', 'layer', 'upload', 'admin'], function () {
    var table = layui.table;
    var laydate = layui.laydate;
    var form = layui.form;
    var admin = layui.admin;
    var upload = layui.upload;

    //自定义格式
    laydate.render({
        elem: 'input[name=start_time]',
        type: 'date',
        format: 'yyyy-MM-dd'
    });
    laydate.render({
        elem: 'input[name=end_time]',
        type: 'date',
        format: 'yyyy-MM-dd'
    });
    let currentPage = 1;
    table.render({
        elem: '#list',
        url: '/api/taskLog/getTaskLogList',
        where: {
            type: 1
        },
        method: 'post',
        request: {
            pageName: 'page',//页码的参数名称，默认：page
            limitName: 'limit' //每页数据量的参数名，默认：limit
        },
        limit: 15,
        limits: [15, 30, 50, 100],
        loading: true,
        size: 'sm',
        cols: [[
            {field: 'id', title: 'ID', width: 60},
            {field: 'admin_name', title: '上传人', width: 120},
            {field: 'status_name', title: '任务状态', width: 120},
            {field: 'create_time', title: '上传时间', width: 180},
            {field: 'log', title: '说明'},
            {
                field: 'result_file_url', title: '结果下载', templet: function (data) {
                    if (data.result_file_url) {
                        return '<a style="color: blue" href="/api/spu/downloadSpuUpdateClassResult?id=' + data.id + '" target="_blank">下载结果</a>'
                    }
                    return '';
                }, width: 220
            }
        ]],
        id: 'Reload',
        page: {},
        done: function (res, curr, count) {
            currentPage = curr;
        }
    });

    form.on('submit(Reload)', function (data) {
        //执行重载
        table.reload('Reload', {
            where: data.field,
            page: {
                curr: 1 //重新从第 1 页开始
            },
        });
        return false;
    });

    $('#reset').click(function () {
        window.location.href = '/web/spu/updateSpuClass';
    });

    $('#download_template').click(function () {
        window.open('/template/更新SPU分类的模板_v2.xlsx', '_blank');
    });

    function sleep(time) {
        return new Promise((resolve) => setTimeout(resolve, time));
    }

    //执行实例
    upload.render({
        elem: '#upload_button', //绑定元素
        url: '/api/spu/updateSpuClass', //上传接口
        accept: 'file',
        bindAction: '#confirmUpload',
        auto: true,
        data: {
            type: function () {
                return $("input[name='type']:checked").val();
            }
        },
        before: function (obj) {
            layer.msg('上传开始,请耐心等待结果', {icon: 6});
        },
        choose: function (obj) {
            $('#hasFile').val(1);
        },
        done: function (res) {
            //上传完毕回调
            if (res.code === 0) {
                layer.msg(res.msg, {icon: 6});
            } else {
                layer.msg(res.msg, {icon: 5});
            }
            table.reload('Reload');
            setTimeout(function () {
                table.reload('Reload');
            }, 5000);
        }, error: function () {
            //请求异常回调
            layer.msg('请求异常', {icon: 5});
            layer.closeAll();
        }
    });

    //监听工具条
    table.on('tool(demo)', function (obj) {
        var data = obj.data;
        if (obj.event === 'detail') {
            layer.msg('ID：' + data.id + ' 的查看操作');
        } else if (obj.event === 'del') {
            layer.confirm('真的删除行么', function (index) {
                obj.del();
                layer.close(index);
            });
        } else if (obj.event === 'edit') {
            layer.alert('编辑行：<br>' + JSON.stringify(data));
        }
    });
});
