layui.use(['table', 'form', 'xmSelect', 'laydate', 'layer', 'admin'], function () {
    let table = layui.table;
    let form = layui.form;
    let admin = layui.admin;
    let layer = layui.layer;
    let $ = layui.$;
    let currentPage = 1;

    table.render({
        elem: '#list',
        url: '/api/distributeUserPrice/getDistributeUserPriceList',
        method: 'get',
        size: 'sm',
        loading: true,
        cols: [[
            { field: 'id', title: '序号', width: 60 },
            { field: 'user_name', title: '客户名称', width: 120 },
            { field: 'app_secret', title: 'user_name', width: 300 },
            { field: 'app_key', title: 'app_key', width: 300 },
            { field: 'price_type_name', title: '对接价格', width: 150 },
            { field: 'supplier_names', title: '猎芯渠道', width: 180 },
            { field: 'status_name', title: '状态', width: 80 },
            { field: 'create_name', title: '创建人', width: 100 },
            { field: 'create_time', title: '创建时间', width: 150 },
            { field: 'update_name', title: '更新人', width: 100 },
            { field: 'update_time', title: '更新时间', width: 150 },
            { field: 'cz', title: '操作', fixed: 'right', templet: '#operation', width: 210 },
        ]],
        limit: 15,
        id: 'list',
        page: {},
        done: function (res, curr, count) {
            currentPage = curr;
        }
    });

    form.on('submit(list)', function (data) {
        //执行重载
        table.reload('list', {
            page: {
                curr: 1 //重新从第 1 页开始
            },
            where: data.field
        });
        return false;
    });

    //重置按钮
    $('.reset-button').click(function () {
        $('input[name=user_name]').val('');
        $('select[name=status]').val('');
        $('input[name=supplier_id]').val('');
        $('input[name=create_time]').val('');
        form.render();
        return false;
    });

    //新增客户价格分发
    $('#add_distribute_user_price').click(function () {
        layer.open({
            title: '新增客户价格分发',
            area: ['80%', '80%'],
            type: 2,
            content: '/web/distributeUserPrice/saveDistributeUserPrice',
            end: function () {
                table.reload('list');
            }
        });
    });

    //监听工具条
    table.on('tool(list)', function (obj) {
        let data = obj.data;
        if (obj.event === 'edit_distribute_user_price') {
            layer.open({
                title: '编辑客户价格分发',
                area: ['80%', '80%'],
                type: 2,
                content: '/web/distributeUserPrice/saveDistributeUserPrice?id=' + data.id,
                end: function () {
                    table.reload('list');
                }
            });
        } else if (obj.event === 'delete_distribute_user_price') {
            layer.confirm('确定删除该客户价格分发规则吗？', function (index) {
                $.ajax({
                    url: '/api/distributeUserPrice/deleteDistributeUserPrice',
                    type: 'post',
                    data: {
                        id: data.id
                    },
                    dataType: "json",
                    timeout: 10000,
                    success: function (res) {
                        if (!res) return layer.msg('网络错误，请重试', { icon: 5 });
                        if (res.code == 0) {
                            layer.msg(res.msg, { icon: 6 });
                            table.reload('list');
                        } else {
                            layer.msg(res.msg, { icon: 5 });
                        }
                    },
                    error: function () {
                        return layer.msg('网络错误，请重试', { icon: 5 });
                    }
                });
            });
        } else if (obj.event === 'enable_distribute_user_price') {
            layer.confirm('确定将该条客户价格分发规则启用吗？', function (index) {
                $.ajax({
                    url: '/api/distributeUserPrice/updateStatus',
                    type: 'post',
                    data: {
                        id: data.id,
                        status: 1
                    },
                    dataType: "json",
                    timeout: 10000,
                    success: function (res) {
                        if (!res) return layer.msg('网络错误，请重试', { icon: 5 });
                        if (res.code == 0) {
                            layer.msg(res.msg, { icon: 6 });
                            table.reload('list');
                        } else {
                            layer.msg(res.msg, { icon: 5 });
                        }
                    },
                    error: function () {
                        return layer.msg('网络错误，请重试', { icon: 5 });
                    }
                });
            });
        } else if (obj.event === 'disable_distribute_user_price') {
            layer.confirm('确定将该条客户价格分发规则禁用吗？', function (index) {
                $.ajax({
                    url: '/api/distributeUserPrice/updateStatus',
                    type: 'post',
                    data: {
                        id: data.id,
                        status: -1
                    },
                    dataType: "json",
                    timeout: 10000,
                    success: function (res) {
                        if (!res) return layer.msg('网络错误，请重试', { icon: 5 });
                        if (res.code == 0) {
                            layer.msg(res.msg, { icon: 6 });
                            table.reload('list');
                        } else {
                            layer.msg(res.msg, { icon: 5 });
                        }
                    },
                    error: function () {
                        return layer.msg('网络错误，请重试', { icon: 5 });
                    }
                });
            });
        } else if (obj.event === 'copy_distribute_user_price') {
            layer.open({
                title: '复制客户价格分发',
                area: ['80%', '80%'],
                type: 2,
                content: '/web/distributeUserPrice/saveDistributeUserPrice?copy=1&id=' + data.id,

            });
        }
    });
});
