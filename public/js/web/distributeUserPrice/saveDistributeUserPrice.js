layui.use(['table', 'form', 'xmSelect', 'laydate', 'layer', 'admin'], function () {
    let form = layui.form;
    let table = layui.table;
    let admin = layui.admin;
    let element = layui.element;
    let layer = layui.layer;
    let $ = layui.$;
    let xmSelect = layui.xmSelect;

    // 初始化表单
    form.render();

    // 序号重新排序函数
    function reorderRowNumbers() {
        $('.row-number').each(function (index) {
            $(this).text(index + 1);
        });
    }

    //监听猎芯渠道选择
    form.on('select(supplier_id)', function (data) {
        let agreementPriceCoefficient = data.elem[data.elem.selectedIndex].attributes['agreement-value'].value;
        let guidePriceCoefficient = data.elem[data.elem.selectedIndex].attributes['guide-value'].value;
        $(this).closest('tr').find('input[name="agreement_price_coefficient[]"]').attr('placeholder', '不能少于' + agreementPriceCoefficient);
        $(this).closest('tr').find('input[name="guide_price_coefficient[]"]').attr('placeholder', '不能少于' + guidePriceCoefficient);
    });

    // 新增行
    $(document).on('click', '.btn-add', function () {
        let newRow = $(this).closest('tr').clone(true);
        newRow.find('input').val('');
        newRow.find('select').val('');
        $(this).closest('tr').after(newRow);
        reorderRowNumbers();
        form.render('select');
    });

    // 删除行
    $(document).on('click', '.btn-delete', function () {
        let rowCount = $('.rule-row').length;
        if (rowCount <= 1) {
            layer.msg('至少保留一行数据', { icon: 5 });
            return;
        }
        $(this).closest('tr').remove();
        reorderRowNumbers();
    });

    // 验证协议价和指导价系数
    form.verify({

    });

    // 取消按钮
    $('#cancelBtn').click(function () {
        admin.closeDialog();
    });

    // 提交表单
    form.on('submit(saveForm)', function (data) {
        let formData = data.field;
        let valid = true;
        let rules = [];

        // 遍历每一行获取数据
        $('.rule-row').each(function() {
            let $row = $(this);
            let rowIndex = $row.index();

            let rule = {
                supplier_id: $row.find(`select[name="supplier_id[${rowIndex}]"]`).val(),
                agreement_price_coefficient: $row.find(`input[name="agreement_price_coefficient[${rowIndex}]"]`).val(),
                guide_price_coefficient: $row.find(`input[name="guide_price_coefficient[${rowIndex}]"]`).val(),
                price_id: $row.find(`input[name="price_id[${rowIndex}]"]`).val(),
            };

            // 验证必填
            if (!rule.supplier_id) {
                layer.msg('请选择猎芯渠道', {icon: 5});
                valid = false;
                return false;
            }

            if (!rule.agreement_price_coefficient || !rule.guide_price_coefficient) {
                layer.msg('请填写价格系数', {icon: 5});
                valid = false;
                return false;
            }

            // 验证最小值
            let $agreementInput = $row.find(`input[name="agreement_price_coefficient[${rowIndex}]"]`);
            let $guideInput = $row.find(`input[name="guide_price_coefficient[${rowIndex}]"]`);

            let minAgreementValue = parseFloat($agreementInput.attr('placeholder').replace('不能少于', ''));
            let minGuideValue = parseFloat($guideInput.attr('placeholder').replace('不能少于', ''));

            if (parseFloat(rule.agreement_price_coefficient) < minAgreementValue) {
                layer.msg(`第${rowIndex + 1}行的协议价系数不能小于${minAgreementValue}`, {icon: 5});
                valid = false;
                return false;
            }

            if (parseFloat(rule.guide_price_coefficient) < minGuideValue) {
                layer.msg(`第${rowIndex + 1}行的指导价系数不能小于${minGuideValue}`, {icon: 5});
                valid = false;
                return false;
            }

            rules.push(rule);
        });

        if (!valid) {
            return false;
        }

        // 将规则数据添加到表单数据中
        formData.rules = JSON.stringify(rules);
        formData.copy = getQueryVariable('copy');
        let loading = layer.load(1, {shade: [0.1, '#fff']});
        $.ajax({
            url: '/api/distributeUserPrice/saveDistributeUserPrice',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function (res) {
                if (res.code === 0) {
                    layer.msg('保存成功', {icon: 6});
                    setTimeout(function () {
                        window.parent.location.reload();
                    }, 1000);
                } else {
                    layer.msg(res.msg, {icon: 5});
                }
            },
            error: function () {
                layer.msg('网络错误，请重试', {icon: 5});
            },
            complete: function () {
                layer.close(loading);
            }
        });

        return false;
    });
});
