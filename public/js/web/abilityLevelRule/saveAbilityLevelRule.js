layui.use(['table', 'admin', 'form', 'upload', 'xmSelect'], function () {
    var table = layui.table;
    var form = layui.form;
    var upload = layui.upload;
    var admin = layui.admin;
    var xmSelect = layui.xmSelect;

    let id = getQueryVariable('id')

    form.on('select(supplier_id)', function (data) {
        if (data.value == 17) {
            $('#supplierCodeSelector').show();
        } else {
            $('#supplierCodeSelector').hide();
        }
    });

    //渲染渠道多选
    let canalSelector = xmSelect.render({
        el: '#canal_selector',
        filterable: true,
        paging: true,
        direction: 'down',
        autoRow: true,
        radio: true,
        disabled: id ? true : false,
        prop: {
            name: 'supplier_name', value: 'supplier_code',
        },
        remoteSearch: true,
        pageRemote: true,
        pageSize: 30,
        remoteMethod: function (val, cb, show, pageIndex) {
            //val: 搜索框的内容, 不开启搜索默认为空, cb: 回调函数, show: 当前下拉框是否展开, pageIndex: 当前第几页
            $.ajax({
                url: '/api/common/getCanalListForXmSelect', type: 'get', data: {
                    supplier_name: val, page: pageIndex
                }, dataType: 'json', timeout: 10000, success: function (res) {
                    if (!res) return layer.msg('网络错误，请重试', { icon: 5 });
                    if (res.err_code === 0) {
                        cb(res.data, res.last_page);
                    } else {
                        layer.msg(res.err_msg, { icon: 6 });
                    }
                }, error: function () {
                    return layer.msg('网络错误，请重试', { icon: 5 });
                }
            });
        },
        on: function (canalList) {
            //arr:  当前多选已选中的数据
            let arr = canalList.arr;
            let canals = '';
            for (let i in arr) {
                canals += arr[i].supplier_code + ',';
            }
            $('#supplier_code').val(canals);
        },
    });

    let canalInitValue = $('#canal_init_value').val();
    canalInitValue = canalInitValue ? JSON.parse(canalInitValue) : [];
    canalSelector.setValue(canalInitValue);

    form.on('submit(saveForm)', function (data) {
        // 验证表单
        if (!data.field.supplier_id) {
            layer.msg('请选择猎芯渠道', { icon: 5 });
            return false;
        }

        if (!data.field.supplier_code && data.field.supplier_id == 17) {
            layer.msg('请选择供应商编码', { icon: 5 });
            return false;
        }

        if (!data.field.level) {
            layer.msg('请选择履约程度', { icon: 5 });
            return false;
        }

        // 当supplier_id不为17时，检查supplier_id是否存在
        if (data.field.supplier_id != 17) {
            // 这里不需要额外检查，后端会处理
        }

        $.ajax({
            url: '/api/abilityLevelRule/saveAbilityLevelRule',
            type: 'post',
            data: data.field,
            dataType: 'json',
            success: function (res) {
                if (res.code === 0) {
                    layer.msg(res.msg, { icon: 6 });
                    setTimeout(function () {
                        admin.closeThisDialog();
                    }, 1000);
                } else {
                    layer.msg(res.msg, { icon: 5 });
                }
            },
            error: function () {
                layer.msg('网络错误，请重试', { icon: 5 });
            }
        });

        return false;
    });

    form.on('submit(closeForm)', function (data) {
        admin.closeThisDialog();
        return false;
    });
});
