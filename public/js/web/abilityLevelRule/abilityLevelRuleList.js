layui.use(['table', 'form', 'laydate', 'layer', 'upload', 'index'], function () {
    var table = layui.table;
    var laydate = layui.laydate;
    var form = layui.form;
    var index = layui.index;
    var upload = layui.upload;

    table.render({
        elem: '#list',
        url: '/api/abilityLevelRule/getAbilityLevelRuleList',
        method: 'post',
        request: {
            pageName: 'page',//页码的参数名称，默认：page
            limitName: 'limit' //每页数据量的参数名，默认：limit
        },
        limit: 15,
        loading: true,
        size: 'sm',
        cols: [[
            { field: 'id', title: '序号', width: 60 },
            {
                field: 'supplier_name', title: '猎芯渠道', width: 120, templet: function (res) {
                    return res.supplier.supplier_name;
                }
            },
            {
                field: 'supplier_code', title: '供应商编码', width: 100
            },
            {
                field: 'supplier_name', title: '供应商名称', width: 180, templet: function (res) {
                    return res.supplier_channel ? res.supplier_channel.supplier_name : '';
                }
            },
            {
                field: 'level_name', title: '履约程度', align: 'center', width: 100, templet: function (d) {
                    let htmlt = "";
                    if (d.level_name) {
                        switch (d.level) {
                            case "0":
                                htmlt = "<span style='color: '>" + d.level_name + "</span>";
                                break;
                            case "1":
                                htmlt = "<span style='color: orange'>" + d.level_name + "</span>";
                                break;
                            case "2":
                                htmlt = "<span style='color: red'>" + d.level_name + "</span>";
                                break;
                        }
                    }
                    return htmlt;
                }
            },
            { field: 'source_name', title: '来源' },
            { field: 'status', title: '状态', width: 100, templet: '#statusTpl' },
            { field: 'create_name', title: '创建人', width: 100 },
            { field: 'create_time', title: '创建时间', width: 160 },
            { field: 'update_name', title: '更新人', width: 100 },
            { field: 'update_time', title: '更新时间', width: 160 },
            { field: 'operation', title: '操作', templet: '#operation', width: 200 }
        ]],
        id: 'list',
        page: {}
    });

    form.on('submit(search)', function (data) {
        //执行重载
        table.reload('list', {
            page: {
                curr: 1 //重新从第 1 页开始
            },
            where: data.field
        });
        return false;
    });

    form.on('submit(reset)', function (data) {
        location.reload();
    });

    // 状态切换
    form.on('switch(statusSwitch)', function (obj) {
        let id = this.value;
        let status = obj.elem.checked ? 1 : -1;

        $.ajax({
            url: '/api/abilityLevelRule/updateStatus',
            type: 'post',
            data: {
                id: id,
                status: status
            },
            dataType: 'json',
            success: function (res) {
                if (res.code === 0) {
                    layer.msg('状态更新成功', { icon: 6 });
                } else {
                    layer.msg(res.msg, { icon: 5 });
                    // 恢复原状态
                    $(obj.elem).prop('checked', !obj.elem.checked);
                    form.render('checkbox');
                }
            },
            error: function () {
                layer.msg('网络错误，请重试', { icon: 5 });
                // 恢复原状态
                $(obj.elem).prop('checked', !obj.elem.checked);
                form.render('checkbox');
            }
        });
    });

    // 新增履约能力规则
    $('#saveAbilityLevelRule').on('click', function () {
        layer.open({
            title: '新增履约能力规则',
            area: ['50%', '80%'],
            type: 2,
            content: '/web/abilityLevelRule/saveAbilityLevelRule?window=fullWindow',
            end: function () {
                table.reload('list', {});
            }
        });
    });

    table.on('tool(list)', function (obj) {
        let data = obj.data;
        if (obj.event === "update") {
            let id = data.id;
            layer.open({
                title: '修改履约能力规则',
                area: ['50%', '80%'],
                type: 2,
                content: '/web/abilityLevelRule/saveAbilityLevelRule?window=fullWindow&id=' + id,
                end: function () {
                    table.reload('list', {});
                }
            });
        } else if (obj.event === "delete") {
            layer.confirm('确定要删除该履约能力规则吗？', function (index) {
                $.ajax({
                    url: '/api/abilityLevelRule/deleteAbilityLevelRule',
                    type: 'post',
                    data: {
                        id: data.id
                    },
                    dataType: 'json',
                    success: function (res) {
                        if (res.code === 0) {
                            layer.msg('删除成功', { icon: 6 });
                            table.reload('list', {});
                        } else {
                            layer.msg(res.msg, { icon: 5 });
                        }
                    },
                    error: function () {
                        layer.msg('网络错误，请重试', { icon: 5 });
                    }
                });
                layer.close(index);
            });
        } else if (obj.event === "operationLog") {
            layer.open({
                title: '操作日志',
                area: ['50%', '80%'],
                type: 2,
                content: '/web/operationLog/operationLogList?window=fullWindow&obj_name=ability_level&obj_id=' + data.id,
                end: function () {
                    table.reload('list', {});
                }
            });
        }
    });
});
