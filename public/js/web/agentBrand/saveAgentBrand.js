document.addEventListener('alpine:init', () => {
    Alpine.data('agentBrandForm', () => ({
        id: null,
        standard_brand_id: null,
        brand_name: null,
        brand_name_cn: null,
        brand_name_en: null,
        brand_brief: null,
        certificate_effective_time: null,
        agent_certificate_name: null,
        show_brand_info: false,
        is_certificate_permanent: null,
        disableEffectTime: false,
        init() {
            let g = this;
            if (getQueryVariable('id')) {
                g.show_brand_info = true;
            }
            layui.use(['table', 'form', 'laydate', 'layer', 'upload', 'element', 'admin', 'xmSelect'], function () {
                let form = layui.form;
                let table = layui.table;
                let admin = layui.admin;
                let upload = layui.upload;
                let layer = layui.layer;
                let laydate = layui.laydate;
                let xmSelect = layui.xmSelect;
                let $ = layui.$;

                laydate.render({
                    elem: '#certificate_effective_time',
                    type: 'date', // 设置为 datetime 类型，可根据实际需求调整
                    trigger: 'click',
                });

                form.on('checkbox(is_certificate_permanent)', function (data) {
                    g.disableEffectTime = !!data.elem.checked;
                });

                if ($('#certificate_effective_time').val() === '') {
                    if (g.id === '0') {
                        g.disableEffectTime = false;
                    } else {
                        g.disableEffectTime = true;
                    }
                }

                //渲染多选
                let brandSelect = xmSelect.render({
                    el: '#brandSelect',
                    name: 'standard_brand_id',
                    searchTips: '请输入要查找的标准品牌',
                    paging: true,
                    empty: '没有查找到数据',
                    prop: {
                        name: 'brand_name',
                        value: 'standard_brand_id'
                    },
                    disabled: g.id !== '0',
                    radio: true,
                    height: "1300px",
                    remoteSearch: true,
                    autoRow: true,
                    pageRemote: true,
                    filterable: true,
                    ratio: true,
                    remoteMethod: function (val, cb, show, pageIndex) {
                        $.ajax({
                            url: '/api/standardBrand/searchStandardBrand',
                            type: 'post',
                            data: {
                                standard_brand_id: $('#standard_brand_id').val(),
                                brand_name: val,
                                page: pageIndex
                            },
                            dataType: 'json',
                            timeout: 10000,
                            success: function (res) {
                                if (!res) return layer.msg('网络错误，请重试', {icon: 5});
                                if (res.errcode === 0) {
                                    cb(res.data, res.last_page);
                                    g.show_brand_info = true;
                                } else {
                                    layer.msg(res.errmsg, {icon: 6});
                                }
                            },
                            error: function () {
                                return layer.msg('网络错误，请重试', {icon: 5});
                            }
                        });
                    },
                    on: function (data) {
                        g.show_brand_info = data.arr.length !== 0;
                        let brandId = '';
                        for (let x in data.arr)  // x 为属性名
                        {
                            brandId = data.arr[x].standard_brand_id;
                        }
                        //获取标准品牌信息
                        fetch('/api/standardBrand/getStandardBrandById?standard_brand_id=' + brandId, {
                            method: 'GET',
                        }).then(response => response.json())
                            .then(data => {
                                if (data.code === 0) {
                                    let standardBrand = data.data;
                                    console.log(standardBrand);
                                    g.brand_name_en = standardBrand.brand_name_en;
                                    g.brand_name_cn = standardBrand.brand_name_cn;
                                    g.brand_brief = standardBrand.brand_brief;
                                } else {
                                    layer.msg(data.msg, {icon: 5});
                                }
                            });

                        $("#standard_brand_id").val(brandId);
                    }
                });
                if (getQueryVariable('id')) {
                    brandSelect.setValue([{brand_name: g.brand_name, standard_brand_id: g.standard_brand_id}]);
                }

                table.render({
                    elem: '#spuList',
                    url: '/api/agentBrand/getAgentBrandSpuList?agent_brand_id=' + getQueryVariable('id'),
                    method: 'get',
                    size: 'sm',
                    defaultToolbar: ['filter', 'exports'],
                    loading: true,
                    toolbar: '#toolbar',
                    cols: [[
                        {field: 'id', title: '序号', width: 80, type: "checkbox"},
                        {field: 'spu_name', title: '商品型号', width: 150},
                        {field: 'type_name', title: '产品类型', width: 80},
                        {field: 'encap', title: '封装', width: 80,},
                        {field: 'bussiness_area', title: '应用领域', width: 150},
                        {field: 'application_level', title: '应用领域', width: 100},
                        {field: 'eccn', title: 'ECCN', width: 80},
                        {field: 'humistor', title: '湿敏等级', width: 80},
                        {field: 'series', title: '系列', width: 80},
                        {field: 'lifecycle', title: '生命周期', width: 80},
                        {field: 'brand_pack', title: '制造商包装', width: 100},
                        {field: 'mpq', title: '标准包装数量', width: 120},
                        {field: 'create_name', title: '创建人', width: 100},
                        {field: 'create_time', title: '创建时间', width: 150},
                        {field: 'update_name', title: '更新人', width: 100},
                        {field: 'update_time', title: '更新时间', width: 150},
                        {field: 'cz', title: '操作', fixed: 'right', width: 120, templet: '#operation'},
                    ]],
                    limit: 15,
                    id: 'spuList',
                    page: {},
                    done: function (res, curr, count) {
                        currentPage = curr;
                    }
                });


                //代理证
                let uploadAgentCertificateInst = upload.render({
                    elem: '#upload_agent_certificate',
                    url: UploadImgUrl,
                    field: 'upload',
                    exts: 'pdf|jpg|bmp|jpeg|png',
                    accept: 'file',
                    data: {
                        k1: k1,
                        k2: k2,
                        source: 1
                    },
                    choose: function (obj) {
                        let files = obj.pushFile(); // 获取选择的文件对象
                        var fileName = '';
                        obj.preview(function (index, file, result) {
                            fileName = files[index].name;
                            g.agent_certificate_name = fileName;
                            console.log('选中文件，文件名为：' + fileName);
                        });
                    },
                    before: function (obj) {
                        layer.msg('加载中', {
                            icon: 16,
                            shade: 0.01
                        });
                    },
                    done: function (res) {
                        layer.msg('上传成功');
                        $('#agent_certificate').val(res.data[0]);
                        let src = res.data[0];
                        if (res.data[0] !== '') {
                            if (res.data[0].includes('.pdf')) {
                                src = '/assets/images/pdf.png';
                            }
                        }
                        layui.$('#agent_certificate_ViewDiv').find('img').removeClass('layui-hide');
                        layui.$('#agent_certificate_ViewDiv').find('img').attr('src', src);
                        layui.$('#upload_agent_certificate').find('i').remove();
                        layui.$('#upload_agent_certificate').find('p').remove();
                    },
                    error: function (res) {
                        layer.msg('上传失败', {
                            icon: 5
                        });
                        return false;
                    }
                });

                //导入产品库
                let uploadInst = upload.render({
                    elem: '#import_agent_brand_spu',
                    url: '/api/agentBrand/importAgentBrandSpu?agent_brand_id=' + getQueryVariable('id'),
                    field: 'upload',
                    exts: 'xlsx',
                    accept: 'file',
                    data: {
                        k1: k1,
                        k2: k2,
                        source: 1
                    },
                    before: function (obj) {
                        layer.msg('加载中', {
                            icon: 16,
                            shade: 0.01
                        });
                    },
                    done: function (res) {
                        if (res.code === 0) {
                            layer.msg(res.msg, {
                                icon: 6
                            });
                            table.reload('spuList');
                            return false;
                        } else {
                            layer.msg(res.msg, {
                                icon: 5
                            });
                            return false;
                        }
                    },
                    error: function (res) {
                        layer.msg(res.msg, {
                            icon: 5
                        });
                        return false;
                    }
                });

                form.on('submit(spuList)', function (data) {
                    //执行重载
                    table.reload('spuList', {
                        page: {
                            curr: 1 //重新从第 1 页开始
                        },
                        where: data.field
                    });
                    return false;
                });

                table.on('tool(spuList)', function (obj) {
                    let id = obj.data.id;
                    switch (obj.event) {
                        case 'edit':
                            layer.open({
                                type: 2,
                                area: ['70%', '90%'],
                                fixed: false, //不固定
                                maxmin: true,
                                title: '编辑代理品牌产品',
                                content: '/web/agentBrand/saveAgentBrandSpu?standard_brand_id=' + g.standard_brand_id + '&agent_brand_id=' + getQueryVariable('id') + '&id=' + id,
                                end: function () {
                                    table.reload('spuList');
                                }
                            });
                            break;
                        case 'delete':
                            layer.confirm('确定删除这个产品吗？', {icon: 3}, function (index) {
                                let url = '/api/agentBrand/deleteAgentBrandSpu?id=' + id;
                                let res = ajaxCommon(url);
                                if (!res) {
                                    layer.msg('网络错误，请重试', {icon: 5});
                                } else {
                                    if (res.code === 0) {
                                        layer.msg(res.msg, {icon: 6});
                                        setTimeout(function () {
                                            table.reload('spuList');
                                        }, 300);
                                    } else {
                                        layer.msg('删除失败', {icon: 5});
                                    }
                                }
                            });
                            break;

                    }
                });


                table.on('toolbar(spuList)', function (obj) {
                    let checkStatus = table.checkStatus(obj.config.id);
                    switch (obj.event) {
                        case 'add_spu':
                            layer.open({
                                type: 2,
                                area: ['70%', '90%'],
                                fixed: false, //不固定
                                maxmin: true,
                                title: '添加代理品牌产品',
                                content: '/web/agentBrand/saveAgentBrandSpu?standard_brand_id=' + g.standard_brand_id + '&agent_brand_id=' + getQueryVariable('id'),
                                end: function () {
                                    table.reload('spuList');
                                }
                            });
                            break;
                        case 'batch_delete_spu':
                            console.log(checkStatus);
                            let data = checkStatus.data;
                            if (data.length === 0) {
                                layer.msg('请选择要操作的产品', {icon: 5});
                                return false;
                            }
                            layer.confirm('确定批量删除这些产品吗？', {icon: 3}, function (index) {
                                let ids = [];
                                data.forEach(function (item) {
                                    ids.push(item.id);
                                });
                                let url = '/api/agentBrand/deleteAgentBrandSpu';
                                let res = ajaxCommon(url, {ids: ids});
                                if (!res) {
                                    layer.msg('网络错误，请重试', {icon: 5});
                                } else {
                                    if (res.code === 0) {
                                        layer.msg(res.msg, {icon: 6});
                                        table.reload('spuList');
                                    } else {
                                        layer.msg('删除失败', {icon: 5});
                                    }
                                }
                            });

                            break;
                        case 'import_agent_brand_spu':
                            $('#import_agent_brand_spu').click();
                            break;
                    }
                });


                form.on('submit(saveAgentBrand)', function (data) {
                    let url = '/api/agentBrand/saveAgentBrand';
                    let result = $.post(url, data.field, function (result) {
                        if (result.code === 0) {
                            layer.msg('操作成功', {icon: 6});
                            setTimeout(function () {
                                admin.closeThisDialog();
                            }, 500);
                        } else {
                            layer.msg(result.msg, {icon: 5});
                            return false; //阻止表单跳转。如果需要表单跳转，去掉这段即可。
                        }
                    });
                });

                form.on('submit(cancel)', function (data) {
                    admin.closeThisDialog();
                });
            });
        }
    }));
});
