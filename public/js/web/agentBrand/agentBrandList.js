document.addEventListener('alpine:init', () => {
    Alpine.data('agentBrandList', () => ({
        init() {
            let g = this;
            layui.use(['table', 'form', 'laydate', 'layer', 'upload'], function () {
                let table = layui.table;
                let form = layui.form;
                let currentPage = 1;
                table.render({
                    elem: '#list',
                    url: '/api/agentBrand/getAgentBrandList',
                    method: 'get',
                    size: 'sm',
                    defaultToolbar: ['filter', 'exports'],
                    loading: true,
                    toolbar: '#toolbar',
                    cols: [[
                        { field: 'id', title: '序号' },
                        {
                            field: 'standard_brand_name', title: '代理品牌名称', width: 150, templet: function (data) {
                                return data.standard_brand ? data.standard_brand.brand_name : '';
                            }
                        },
                        {
                            field: 'brand_name_en', title: '英文全称', width: 200, templet: function (data) {
                                return data.standard_brand ? data.standard_brand.brand_name_en : '';
                            }
                        },
                        {
                            field: 'brand_name_cn', title: '中文全称', width: 200, templet: function (data) {
                                return data.standard_brand ? data.standard_brand.brand_name_cn : '';
                            }
                        },
                        {
                            field: 'brand_logo', title: '品牌logo', width: 80, templet: function (data) {
                                return data.standard_brand ? '<img src="' + data.standard_brand.brand_logo + '" style="width: 40px;height: 20px;">' : '';
                            }
                        },
                        {
                            field: 'brand_level_name', title: '品牌等级', width: 80, templet: function (data) {
                                return data.brand_level_name ? data.brand_level_name : '';
                            }
                        },
                        {
                            field: 'brand_area_name', title: '品牌区域', width: 150, templet: function (data) {
                                return data.brand_area_name ? data.brand_area_name : '';
                            }
                        },
                        {
                            field: 'main_product_class_name', title: '主营分类', width: 150, templet: function (data) {
                                return data.main_product_class_name ? data.main_product_class_name : '';
                            }
                        },
                        {
                            field: 'agent_class_name', title: '专题页所属分类', width: 250, templet: function (data) {
                                return data.agent_class_name ? data.agent_class_name : '';
                            }
                        },
                        {
                            field: 'supplier_code', title: '供应商编码', width: 100, templet: function (data) {
                                return data.supplier_code ? data.supplier_code : '';
                            }
                        },
                        {
                            field: 'brand_brief', title: '品牌官网链接', width: 220, templet: function (data) {
                                return data.standard_brand ? data.standard_brand.brand_brief : '';
                            }
                        },
                        { field: 'application_area_name', title: '应用领域', width: 350 },
                        {
                            field: 'agent_certificate', title: '代理证', width: 200, templet: function (data) {
                                let name = data.agent_certificate_name ? data.agent_certificate_name : '查看';
                                return '<span value="' + data.agent_certificate + '" class="certificate_link" style="color:rgb(34, 174, 255);">' + name + '</span>';
                            }
                        },
                        { field: 'certificate_effective_time', title: '代理证有效时间', width: 150 },
                        { field: 'pm_user_name', title: 'PM负责人', width: 90 },
                        { field: 'spu_count', title: '代理产品数量', width: 110 },
                        {
                            field: 'status_name', title: '状态', width: 60, templet: function (data) {
                                return data.status === 1 ? '启用' : '<span style="color: red">禁用</span>'
                            }
                        },
                        { field: 'create_name', title: '创建人', width: 100 },
                        { field: 'create_time', title: '创建时间', width: 150 },
                        { field: 'update_name', title: '更新人', width: 100 },
                        { field: 'update_time', title: '更新时间', width: 150 },
                        { field: 'cz', title: '操作', fixed: 'right', width: 120, templet: '#operation' },
                    ]],
                    limit: 15,
                    id: 'list',
                    page: {},
                    done: function (res, curr, count) {
                        currentPage = curr;
                    }
                });

                //监听supplier_code输入框变化,实时请求获取供应商编码,模糊查询,展示下拉可选择
                $('#supplier_code').on('input', function () {
                    let value = $(this).val().trim().toUpperCase(); // 转为大写并去空格
                    if (value) {
                        //请求获取供应商编码
                        //模板页面获取的supplierCodeList
                        let matchSupplierCodeList = [];

                        // 创建正则表达式用于模糊匹配
                        let regex = new RegExp(value, 'i'); // 'i'表示忽略大小写

                        // 匹配包含输入值的供应商编码
                        for (let i = 0; i < supplierCodeList.length; i++) {
                            if (regex.test(supplierCodeList[i])) {
                                matchSupplierCodeList.push(supplierCodeList[i]);
                            }
                        }

                        // 限制显示数量，避免太多结果
                        let maxResults = 10;
                        if (matchSupplierCodeList.length > maxResults) {
                            matchSupplierCodeList = matchSupplierCodeList.slice(0, maxResults);
                        }

                        //展示下拉可选择
                        let html = '';
                        if (matchSupplierCodeList.length > 0) {
                            for (let i = 0; i < matchSupplierCodeList.length; i++) {
                                // 高亮匹配部分
                                let highlightedCode = matchSupplierCodeList[i].replace(
                                    regex,
                                    '<span style="color: #FF5722; font-weight: bold;">$&</span>'
                                );
                                html += '<div class="layui-input-inline supplier-code-item">' + highlightedCode + '</div>';
                            }
                        } else {
                            html = '<div class="layui-input-inline">无匹配结果</div>';
                        }
                        $('#supplier_code_select').html(html).show();
                    } else {
                        // 输入框为空时隐藏下拉列表
                        $('#supplier_code_select').html('').hide();
                    }
                });

                $(document).on('click', '.certificate_link', function () {
                    let url = $(this).attr('value');
                    window.open(url, '_blank');
                });

                // 点击选择下拉项
                $(document).on('click', '.supplier-code-item', function () {
                    let code = $(this).text();
                    $('#supplier_code').val(code);
                    $('#supplier_code_select').html('').hide();
                });

                // 点击其他地方时隐藏下拉列表
                $(document).on('click', function (e) {
                    if (!$(e.target).closest('#supplier_code, #supplier_code_select').length) {
                        $('#supplier_code_select').html('').hide();
                    }
                });


                $(document).on('click', '#export_brand', function () {
                    console.log(form.val("agent_brand_filter_form"));
                    let map = form.val("agent_brand_filter_form");
                    let params = JSON.stringify(map);
                    let url = '/api/agentBrand/exportAgentBrand?params=' + params;
                    window.open(url, '_blank');
                });

                form.on('submit(list)', function (data) {
                    //执行重载
                    table.reload('list', {
                        page: {
                            curr: 1 //重新从第 1 页开始
                        },
                        where: data.field
                    });
                    return false;
                });

                table.on('tool(list)', function (obj) {
                    let id = obj.data.id;
                    switch (obj.event) {
                        case 'edit':
                            layer.open({
                                type: 2,
                                area: ['70%', '95%'],
                                fixed: false, //不固定
                                maxmin: true,
                                title: '编辑代理品牌',
                                content: '/web/agentBrand/saveAgentBrand?id=' + id,
                                end: function () {
                                    table.reload('list');
                                }
                            });
                            break;
                        case 'disable':
                            layer.confirm('确定禁用这个代理品牌吗？', { icon: 3 }, function (index) {
                                let url = '/api/agentBrand/changeAgentBrandStatus?status=-1&id=' + id;
                                let res = ajaxCommon(url);
                                if (!res) {
                                    layer.msg('网络错误，请重试', { icon: 5 });
                                } else {
                                    if (res.code === 0) {
                                        layer.msg(res.msg, { icon: 6 });
                                        setTimeout(function () {
                                            table.reload('list');
                                        }, 300);
                                    } else {
                                        layer.msg('禁用失败', { icon: 5 });
                                    }
                                }
                            });
                            break;
                        case 'enable':
                            layer.confirm('确定启用这个代理品牌吗？', { icon: 3 }, function (index) {
                                let url = '/api/agentBrand/changeAgentBrandStatus?status=1&id=' + id;
                                let res = ajaxCommon(url);
                                if (!res) {
                                    layer.msg('网络错误，请重试', { icon: 5 });
                                } else {
                                    if (res.code === 0) {
                                        layer.msg(res.msg, { icon: 6 });
                                        setTimeout(function () {
                                            table.reload('list');
                                        }, 300);
                                    } else {
                                        layer.msg('启用失败', { icon: 5 });
                                    }
                                }
                            });
                            break;
                    }
                });


                table.on('toolbar(list)', function (obj) {
                    let checkStatus = table.checkStatus(obj.config.id);
                    switch (obj.event) {
                        case 'add_brand':
                            layer.open({
                                type: 2,
                                area: ['70%', '95%'],
                                fixed: false, //不固定
                                maxmin: true,
                                title: '新增代理品牌',
                                content: '/web/agentBrand/saveAgentBrand',
                                end: function () {
                                    table.reload('list');
                                }
                            });
                            break;
                    }
                });
            });
        }
    }))
})
