layui.use(['table', 'form', 'laydate', 'layer', 'upload', 'admin'], function () {
    var table = layui.table;
    var laydate = layui.laydate;
    var form = layui.form;
    var admin = layui.admin;
    var upload = layui.upload;

    //自定义格式
    laydate.render({
        elem: 'input[name=start_time]',
        type: 'date',
        format: 'yyyy-MM-dd'
    });
    laydate.render({
        elem: 'input[name=end_time]',
        type: 'date',
        format: 'yyyy-MM-dd'
    });
    let currentPage = 1;
    table.render({
        elem: '#list',
        url: '/api/taskLog/getTaskLogList',
        where: {
            type: getQueryVariable("type")
        },
        method: 'post',
        request: {
            pageName: 'page',//页码的参数名称，默认：page
            limitName: 'limit' //每页数据量的参数名，默认：limit
        },
        limit: 15,
        limits: [15, 30, 50, 100],
        loading: true,
        size: 'sm',
        cols: [[
            {field: 'id', title: 'ID', width: 60},
            {field: 'admin_name', title: '上传人', width: 120},
            {field: 'status_name', title: '任务状态', width: 120},
            {field: 'create_time', title: '上传时间', width: 180},
            {field: 'log', title: '说明'},
            {
                field: 'result_file_url', title: '结果下载', templet: function (data) {
                    if (data.result_file_url) {
                        //如果包含http字符串,那就直接用result_file_url
                        if (data.result_file_url.includes('http')) {
                            return '<a style="color: blue" href="' + data.result_file_url + '" target="_blank">下载结果</a>'
                        } else {
                            return '<a style="color: blue" href="/api/taskLog/downloadTaskLogResult?id=' + data.id + '" target="_blank">下载结果</a>'
                        }
                    }
                    return '';
                }, width: 220
            }
        ]],
        id: 'Reload',
        page: {},
        done: function (res, curr, count) {
            currentPage = curr;
        }
    });

    form.on('submit(Reload)', function (data) {
        //执行重载
        table.reload('Reload', {
            where: data.field,
            page: {
                curr: 1 //重新从第 1 页开始
            },
        });
        return false;
    });

    $('#reset').click(function () {
        window.location.href = '/web/taskLog/taskLogList?type=' + getQueryVariable("type");
    });
});
