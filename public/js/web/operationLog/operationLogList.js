layui.use(['table', 'form', 'laydate', 'layer', 'upload', 'admin'], function () {
    var table = layui.table;
    var laydate = layui.laydate;
    var form = layui.form;
    var admin = layui.admin;
    var upload = layui.upload;

    //自定义格式
    laydate.render({
        elem: 'input[name=start_time]',
        type: 'date',
        format: 'yyyy-MM-dd'
    });
    laydate.render({
        elem: 'input[name=end_time]',
        type: 'date',
        format: 'yyyy-MM-dd'
    });
    let currentPage = 1;
    table.render({
        elem: '#list',
        url: '/api/operationLog/getOperationLogList',
        where: {
            obj_name: getQueryVariable("obj_name"),
            obj_id: getQueryVariable("obj_id")
        },
        method: 'post',
        request: {
            pageName: 'page',//页码的参数名称，默认：page
            limitName: 'limit' //每页数据量的参数名，默认：limit
        },
        limit: 15,
        limits: [15, 30, 50, 100],
        loading: true,
        size: 'sm',
        cols: [[
            {field: 'id', title: 'ID', width: 60},
            // {field: 'obj_id', title: '对象ID', width: 120},
            // {field: 'obj_name', title: '操作对象', width: 120},
            {field: 'create_time', title: '操作时间', width: 180},
            {field: 'create_name', title: '操作人', width: 120},
            {field: 'content', title: '操作内容'},
        ]],
        id: 'Reload',
        page: {},
        done: function (res, curr, count) {
            currentPage = curr;
        }
    });

    form.on('submit(Reload)', function (data) {
        //执行重载
        table.reload('Reload', {
            where: data.field,
            page: {
                curr: 1 //重新从第 1 页开始
            },
        });
        return false;
    });

    $('#reset').click(function () {
        window.location.href = '/web/operationLog/operationLogList?type=' + getQueryVariable("type");
    });
});
