<?php

return [

    'default' => "default",

    'connections' => [
        'default' => [
            'host' => get_resource_config_section('rabbit', 'rabbit')['host'],
            'port' => get_resource_config_section('rabbit', 'rabbit')['port'],
            'login' => get_resource_config_section('rabbit', 'rabbit')['user'],
            'password' => get_resource_config_section('rabbit', 'rabbit')['passwd'],
            'vhost' => get_resource_config_section('rabbit', 'rabbit')['vhost']
        ],
        'dgk' => [
            'host' => get_resource_config_section('rabbit', 'dgk')['host'],
            'port' => get_resource_config_section('rabbit', 'dgk')['port'],
            'login' => get_resource_config_section('rabbit', 'dgk')['user'],
            'password' => get_resource_config_section('rabbit', 'dgk')['passwd'],
            'vhost' => get_resource_config_section('rabbit', 'dgk')['vhost']
        ],
        'trading' => [
            'host' => get_resource_config_section('rabbit', 'trading_rabbit')['host'],
            'port' => get_resource_config_section('rabbit', 'trading_rabbit')['port'],
            'login' => get_resource_config_section('rabbit', 'trading_rabbit')['user'],
            'password' => get_resource_config_section('rabbit', 'trading_rabbit')['passwd'],
            'vhost' => get_resource_config_section('rabbit', 'trading_rabbit')['vhost']
        ],
        'sku' => [
            'host' => env('RABBITMQ_HOST'),
            'port' => env('RABBITMQ_PORT'),
            'login' => env('RABBITMQ_USER'),
            'password' => env('RABBITMQ_PASSWORD'),
            'vhost' => env('RABBITMQ_VHOST'),
        ],
    ]
];
