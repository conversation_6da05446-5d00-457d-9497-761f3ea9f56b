<?php

use Illuminate\Support\Str;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for all database work. Of course
    | you may use many connections at once using the Database library.
    |
    */

    'default' => env('DB_CONNECTION', 'mysql'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | Of course, examples of configuring each database platform that is
    | supported by Laravel is shown below to make development simple.
    |
    |
    | All database work in Laravel is done through the PHP PDO facilities
    | so make sure you have the driver for your particular database of
    | choice installed on your machine before you begin development.
    |
    */

    'connections' => [


        'cms' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_cms')['host'],
            'database' => get_resource_config_section('db', 'db_cms')['db'],
            'username' => get_resource_config_section('db', 'db_cms')['user'],
            'password' => get_resource_config_section('db', 'db_cms')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => '',
            'strict' => false,
            'engine' => null,
        ],
        'spu' => [ //spu数据库
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_spu')['host'],
            'database' => get_resource_config_section('db', 'db_spu')['db'],
            'username' => get_resource_config_section('db', 'db_spu')['user'],
            'password' => get_resource_config_section('db', 'db_spu')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => false,
        ],
        'liexin_data' => [ //自营数据库
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_data')['host'],
            'database' => get_resource_config_section('db', 'db_data')['db'],
            'username' => get_resource_config_section('db', 'db_data')['user'],
            'password' => get_resource_config_section('db', 'db_data')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => false,
        ],

        'order' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_order')['host'],
            'database' => get_resource_config_section('db', 'db_order')['db'],
            'username' => get_resource_config_section('db', 'db_order')['user'],
            'password' => get_resource_config_section('db', 'db_order')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => 'lie_',
            'strict' => false,
            'engine' => null,
        ],

        'order_v2' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_order_v2')['host'],
            'database' => get_resource_config_section('db', 'db_order_v2')['db'],
            'username' => get_resource_config_section('db', 'db_order_v2')['user'],
            'password' => get_resource_config_section('db', 'db_order_v2')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => 'lie_',
            'strict' => false,
            'engine' => null,
        ],

        'mysql' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_purchase')['host'],
            'database' => get_resource_config_section('db', 'db_purchase')['db'],
            'username' => get_resource_config_section('db', 'db_purchase')['user'],
            'password' => get_resource_config_section('db', 'db_purchase')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => '',
            'strict' => false,
            'engine' => null,
        ],

        'ichunt' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_liexin')['host'],
            'database' => get_resource_config_section('db', 'db_liexin')['db'],
            'username' => get_resource_config_section('db', 'db_liexin')['user'],
            'password' => get_resource_config_section('db', 'db_liexin')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => '',
            'strict' => false,
            'engine' => null,
        ],

        'os_log' => [ //actionlog数据库
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_os_log')['host'],
            'database' => get_resource_config_section('db', 'db_os_log')['db'],
            'username' => get_resource_config_section('db', 'db_os_log')['user'],
            'password' => get_resource_config_section('db', 'db_os_log')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => false,
        ],

        'supplier' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_supp')['host'],
            'database' => get_resource_config_section('db', 'db_supp')['db'],
            'username' => get_resource_config_section('db', 'db_supp')['user'],
            'password' => get_resource_config_section('db', 'db_supp')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => 'lie_',
            'strict' => false,
            'engine' => null,
        ],
        'flow' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_flow')['host'],
            'database' => get_resource_config_section('db', 'db_flow')['db'],
            'username' => get_resource_config_section('db', 'db_flow')['user'],
            'password' => get_resource_config_section('db', 'db_flow')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => '',
            'strict' => false,
            'engine' => null,
        ],
        'class' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_class')['host'],
            'database' => get_resource_config_section('db', 'db_class')['db'],
            'username' => get_resource_config_section('db', 'db_class')['user'],
            'password' => get_resource_config_section('db', 'db_class')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => 'lie_',
            'strict' => false,
            'engine' => null,
        ],
        'mongodb' => [
            'driver' => 'mongodb',
            'host' => get_resource_config_section('mongodb', 'mongo')['host'],
            'port' => get_resource_config_section('mongodb', 'mongo')['port'],
            'database' => get_resource_config_section('mongodb', 'mongo')['db'],
            'username' => get_resource_config_section('mongodb', 'mongo')['user'],
            'password' => get_resource_config_section('mongodb', 'mongo')['passwd'],
            'options' => [
                'database' => 'ichunt',
                'readPreference' => 'primary'
            ]
        ],

        'rabbitmq' => [
            'driver' => 'rabbitmq',
            'host' => get_resource_config_section('rabbit', 'user')['host'],
            'port' => get_resource_config_section('rabbit', 'user')['port'],
            'vhost' => env('RABBITMQ_VHOST', '/'),
            'login' => get_resource_config_section('rabbit', 'user')['user'],
            'password' => get_resource_config_section('rabbit', 'user')['passwd'],
            'queue' => env('RABBITMQ_QUEUE'), // name of the default queue,
        ],

        'sku_0' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_spu')['host'],
            'database' => "liexin_sku_0",
            'username' => get_resource_config_section('db', 'db_spu')['user'],
            'password' => get_resource_config_section('db', 'db_spu')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => 'lie_',
            'strict' => false,
            'engine' => null,
        ],
        'sku_1' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_spu')['host'],
            'database' => "liexin_sku_1",
            'username' => get_resource_config_section('db', 'db_spu')['user'],
            'password' => get_resource_config_section('db', 'db_spu')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => 'lie_',
            'strict' => false,
            'engine' => null,
        ],
        'sku_2' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_spu')['host'],
            'database' => "liexin_sku_2",
            'username' => get_resource_config_section('db', 'db_spu')['user'],
            'password' => get_resource_config_section('db', 'db_spu')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => 'lie_',
            'strict' => false,
            'engine' => null,
        ],
        'sku_3' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_spu')['host'],
            'database' => "liexin_sku_3",
            'username' => get_resource_config_section('db', 'db_spu')['user'],
            'password' => get_resource_config_section('db', 'db_spu')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => 'lie_',
            'strict' => false,
            'engine' => null,
        ],
        'sku_4' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_spu')['host'],
            'database' => "liexin_sku_4",
            'username' => get_resource_config_section('db', 'db_spu')['user'],
            'password' => get_resource_config_section('db', 'db_spu')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => 'lie_',
            'strict' => false,
            'engine' => null,
        ],
        'sku_5' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_spu')['host'],
            'database' => "liexin_sku_5",
            'username' => get_resource_config_section('db', 'db_spu')['user'],
            'password' => get_resource_config_section('db', 'db_spu')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => 'lie_',
            'strict' => false,
            'engine' => null,
        ],
        'sku_6' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_spu')['host'],
            'database' => "liexin_sku_6",
            'username' => get_resource_config_section('db', 'db_spu')['user'],
            'password' => get_resource_config_section('db', 'db_spu')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => 'lie_',
            'strict' => false,
            'engine' => null,
        ],
        'sku_7' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_spu')['host'],
            'database' => "liexin_sku_7",
            'username' => get_resource_config_section('db', 'db_spu')['user'],
            'password' => get_resource_config_section('db', 'db_spu')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => 'lie_',
            'strict' => false,
            'engine' => null,
        ],
        'sku_8' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_spu')['host'],
            'database' => "liexin_sku_8",
            'username' => get_resource_config_section('db', 'db_spu')['user'],
            'password' => get_resource_config_section('db', 'db_spu')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => 'lie_',
            'strict' => false,
            'engine' => null,
        ],
        'sku_9' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_spu')['host'],
            'database' => "liexin_sku_9",
            'username' => get_resource_config_section('db', 'db_spu')['user'],
            'password' => get_resource_config_section('db', 'db_spu')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => 'lie_',
            'strict' => false,
            'engine' => null,
        ],
        'bigdata' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_bigdata')['host'],
            'database' => get_resource_config_section('db', 'db_bigdata')['db'],
            'username' => get_resource_config_section('db', 'db_bigdata')['user'],
            'password' => get_resource_config_section('db', 'db_bigdata')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => '',
            'strict' => false,
            'engine' => null,
        ],
        'distribution' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_distribution')['host'],
            'database' => get_resource_config_section('db', 'db_distribution')['db'],
            'username' => get_resource_config_section('db', 'db_distribution')['user'],
            'password' => get_resource_config_section('db', 'db_distribution')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => '',
            'strict' => false,
            'engine' => null,
        ],
        'frq' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_frq')['host'],
            'database' => get_resource_config_section('db', 'db_frq')['db'],
            'username' => get_resource_config_section('db', 'db_frq')['user'],
            'password' => get_resource_config_section('db', 'db_frq')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => '',
            'strict' => false,
            'engine' => null,
        ],
        //'digikey' => [
        //    'driver' => 'mysql',
        //    'host' => get_resource_config_section('db', 'db_digikey')['host'],
        //    'database' => "digikey",
        //    'username' => get_resource_config_section('db', 'db_digikey')['user'],
        //    'password' => get_resource_config_section('db', 'db_digikey')['passwd'],
        //    'port' => 3306,
        //    'charset' => 'utf8',
        //    'collation' => 'utf8_general_ci',
        //    'prefix' => 'lie_',
        //    'strict' => false,
        //    'engine' => null,
        //]

    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run in the database.
    |
    */

    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer body of commands than a typical key-value system
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    */

    'redis' => [
        'cluster' => false,
        'frq' => [
            'host' => get_resource_config_section('redis', 'frq')['host'],
            'password' => get_resource_config_section('redis', 'frq')['passwd'],
            'port' => get_resource_config_section('redis', 'frq')['port'],
            'database' => 0,
            'prefix' => env('PREFIX', '')
        ],
        'sku' => [
            'host' => get_resource_config_section('redis', 'sku')['host'],
            'password' => get_resource_config_section('redis', 'sku')['passwd'],
            'port' => get_resource_config_section('redis', 'sku')['port'],
            'database' => 0,
            'prefix' => env('PREFIX', '')
        ],
        'spu' => [
            'host' => get_resource_config_section('redis', 'spu')['host'],
            'password' => get_resource_config_section('redis', 'spu')['passwd'],
            'port' => get_resource_config_section('redis', 'spu')['port'],
            'database' => 0,
            'prefix' => env('PREFIX', '')
        ],
    ],


];
