<?php
return [
    //关税
    'Tax' => 1.13,
    'SpuField' => [
        'class_id1' => 1,
        'class_id2' => 1,
        'class_id3' => 0,
        'spu_name' => 1,
        'brand_id' => 1,
        'pdf' => 1,
        'images_l' => 1,
        'spu_brief' => 1,
        'status' => 1,
        'update_time' => 1,
        'encap' => 1,
        'application_level' => 1,
        'eccn' => 1,
        'has_rohs' => 1,
        'series' => 1,
        'lifecycle' => 1,
        'brand_pack' => 1,
        'mpq' => 1,
        's_brand_id' => 1,
        'spu_title' => 1,
    ],
    'SkuField' => [
        'spu_id' => 1,
        'encoded' => 1,
        'goods_name' => 1,
        'moq' => 1,
        'mpq' => 1,
        'old_goods_id' => 1,
        'goods_type' => 1,
        'goods_status' => 1,
        'batch_sn' => 1,
        'stock' => 1,
        'hk_delivery_time' => 1,
        'cn_delivery_time' => 1,
        'ladder_price' => 1,
        'update_time' => 1,
        'goods_images' => 1,
        'canal' => 1,
        'supplier_id' => 1,
        'cp_time' => 1,
        'goods_sn' => 1,
        'source' => 1,
        'goods_label' => 1,
        'org_id' => 1,
        'coo' => 1,
        'multiple' => 1,
    ],
    //（1-欧美，2-日韩，3-台湾，4-中国内地）
    'StandardBrandArea' => [
        0 => '其他亚洲区域',
        1 => '欧美',
        2 => '日韩',
        3 => '中国台湾',
        4 => '中国内地'
    ],
    //标准品牌状态
    //0：待审核 1：正常 2：审核不通过 3：禁用
    'StandardBrandStatus' => [
        0 => '待审核',
        1 => '正常',
        2 => '审核不通过',
        3 => '禁用',
    ],

    'BrandInsertType' => [
        0 => '人工录入',
        1 => 'SKU',
        2 => '订单',
        3 => '询报价',
        4 => '订单',
        10 => '系统后台'
    ],

    //产品类型(1优势产品，2主打产品，3火爆产品以及4新产品)
    'StandardBrandSpuType' => [
        0 => '其他',
        1 => '优势产品',
        2 => '主打产品',
        3 => '火爆产品',
        4 => '新产品'
    ],

    'BrandArea' => [
        1 => '欧美',
        2 => '日韩',
        3 => '台湾',
        4 => '中国内地'
    ],


    //品牌状态
    //0：待审核 1：正常 2：审核不通过 3：禁用
    'BrandStatus' => [
        0 => '待审核',
        1 => '正常',
        2 => '审核不通过',
        3 => '禁用',
    ],

    'SkuStatus' => [
        0 => '待审核',
        1 => '上架',
        2 => '审核不通过',
        3 => '下架',
        4 => '删除'
    ],

    'SkuType' => [
        0 => '自营',
        1 => '联营',
        2 => '专卖',
        3 => '寄售',
        4 => '云仓'
    ],

    'SpuStatus' => [
        0 => '待审核',
        1 => '上架',
        2 => '审核不通过',
        3 => '下架',
        4 => '删除'
    ],

    'SpuApplicationArea' => [
        1 => '消费电子',
        2 => '照明电子',
        3 => '医疗电子',
        4 => '仪器仪表',
        5 => '通讯/网络',
        6 => '交通/汽车',
        7 => '安防/监控',
        8 => '工业/自动化',
        9 => '军事/国防',
        10 => '便携设备',
        11 => '能源/勘测',
        12 => '船舶/航天'
    ],

    'AgentBrandApplicationArea' => [
        1 => '消费电子',
        2 => '照明电子',
        3 => '医疗电子',
        4 => '仪器仪表',
    ],


    'AgentBrandSpuTypeName' => [
        '主打产品' => '主打产品',
        '替代料' => '替代料',
        '其它' => '其它',
    ],

    'AgentBrandClass' => [
        1 => '半导体/分立器件',
        2 => '传感器',
        3 => '连接器/开关/继电器',
        4 => '晶体/晶振',
        5 => '微处理器/存储器',
        6 => '电子元件/模块',
        7 => '物联网技术',
        8 => '光电/LED',
        9 => '电源/电子电力',
        10 => '射频微波',
        11 => 'MCU/开发板',
        12 => '电阻/电容/电感',
        13 => '音频器件',
    ],

    'AgentBrandClassMap' => [
        '半导体/分立器件' => 1,
        '传感器' => 2,
        '连接器/开关/继电器' => 3,
        '晶体/晶振' => 4,
        '微处理器/存储器' => 5,
        '电子元件/模块' => 6,
        '物联网技术' => 7,
        '光电/LED' => 8,
        '电源/电子电力' => 9,
        '射频微波' => 10,
        'MCU/开发板' => 11,
        '电阻/电容/电感' => 12,
        '音频器件' => 13,
    ],

    'ScmBrandStatus' => [
        0 => '未核准',
        1 => '正常',
        2 => '禁用',
        3 => '作废',
    ],

    'SupplierTypeID' => [
        0 => '自营',
        1 => '联营',
        2 => '专卖',
        3 => '寄售'
    ],
    'SupplierStatus' => [
        0 => '待审核',
        1 => '启用',
        2 => '禁用',
        3 => '删除'
    ],
    'PinToPinMap' => [
        -1 => '否',
        0 => '未知',
        1 => '是',
    ],
    'AlikeSpuUploadStatus' => [
        -1 => '处理失败',
        0 => '待处理',
        1 => '处理中',
        2 => '处理完成'
    ],
    'TaskLogStatus' => [
        -1 => '处理失败',
        0 => '待处理',
        1 => '处理中',
        2 => '处理完成'
    ],
    'SampleStatus' => [
        1 => '启用',
        2 => '禁用',
    ],
    'SampleClassStatus' => [
        1 => '启用',
        2 => '禁用',
    ],
    'SampleType' => [
        1 => '自营',
        2 => '联营',
    ],
    'SampleUploadStatus' => [
        -1 => '处理失败',
        0 => '待处理',
        1 => '处理中',
        2 => '处理完成'
    ],
    'Letters' => [
        '#' => '#',
        'A' => 'A',
        'B' => 'B',
        'C' => 'C',
        'D' => 'D',
        'E' => 'E',
        'F' => 'F',
        'G' => 'G',
        'H' => 'H',
        'I' => 'I',
        'J' => 'J',
        'K' => 'K',
        'L' => 'L',
        'M' => 'M',
        'N' => 'N',
        'O' => 'O',
        'P' => 'P',
        'Q' => 'Q',
        'R' => 'R',
        'S' => 'S',
        'T' => 'T',
        'U' => 'U',
        'V' => 'V',
        'W' => 'W',
        'X' => 'X',
        'Y' => 'Y',
        'Z' => 'Z',
    ],
    'GoodsLabel' => [
        1 => '国内现货',
        2 => '国际现货',
        3 => '猎芯期货',
        4 => '询价现货',
        5 => '原厂直售',
        6 => '猎芯精选',
        -1 => '代购现货'
    ],
    'GoodsLabelStringMap' => [
        '国内现货' => 1,
        '国际现货' => 2,
        '猎芯期货' => 3,
        '询价现货' => 4,
        '原厂直售' => 5,
        '猎芯精选' => 6,
        '代购现货' => -1,
    ],
    'GoodsLabelForIedge' => [
        1 => '华云现货',
        2 => '国际现货',
        3 => '华云期货',
        4 => '询价现货',
        5 => '原厂直售',
        6 => '猎芯精选',
        -1 => '代购现货'
    ],
    'GoodsLabelForIedgeStringMap' => [
        '华云现货' => 1,
        '国际现货' => 2,
        '华云期货' => 3,
        '询价现货' => 4,
        '原厂直售' => 5,
        '猎芯精选' => 6,
        '代购现货' => -1,
    ],
    'SkuSource' => [
        1 => '代购采集',
        2 => '代购API',
        3 => '代购数据包',
        4 => '专营API',
        5 => '专营数据包',
        6 => '专营后台上传',
        7 => '专营芯链上传',
        8 => '专营采集',
        9 => '芯链人工上传',
        12 => '芯链寄售上传'
    ],

    //sku最低价系数
    'SkuRatio' => 6.9,

    'AttrInsertType' => [
        1 => '单数值',
        2 => '范围值',
        6 => '多个单数值',
        7 => '多个范围值',
        3 => '单数值+文本',
        4 => '范围值+文本',
        8 => '多个单数值+文本',
        9 => '多个范围值+文本',
        5 => '文本',
    ],

    'LieSpuColumnAttr' => [
        -12 => 'SPU品牌',
        -11 => 'SPU海关编码',
        -10 => 'SPU-RoHS',
        -9 => 'SPU封装',
        -8 => 'SKU-批次',
        -7 => 'SKU-ECCN',
        -6 => 'SPU-ECCN',
        -5 => 'SPU应用领域',
        -4 => 'SKU包装',
        -3 => 'SPU制造商包装',
        -2 => 'SPU标准包装量',
        -1 => 'SPU型号',
    ],

    //参数偏向
    'AttrPreference' => [
        0 => '无',
        1 => '偏大',
        2 => '偏小',
        3 => '偏宽',
        4 => '偏窄',
    ],

    //1：京东自营店铺;2：京东POP店铺;3：京东国际店铺,4：淘宝店铺;5:天猫店铺,6:亚马逊店铺
    'ShopType' => [
        1 => '京东自营店铺',
        2 => '京东POP店铺',
        3 => '京东国际店铺',
        4 => '淘宝店铺',
        5 => '天猫店铺',
        6 => '亚马逊店铺',
        7 => '电子交易中心店铺',
    ],

    //平台
    'ShopPlatform' => [
        1 => '京东VC',
        2 => '墨卡托',
        3 => '京东POP',
        4 => '电子交易中心',
    ],

    'ShopDistributeRuleStatus' => [
        1 => '启用',
        -1 => '禁用',
    ],

    'ShopDistributeRuleSpuStatus' => [
        1 => '启用',
        -1 => '禁用',
    ],


    'ShopSkuAuditStatus' => [
        0 => '未审核',
        -1 => '审核失败',
        1 => '审核中',
        2 => '审核通过'
    ],

    'ShopSkuInitStatus' => [
        -1 => '失败',
        0 => '未打',
        1 => '成功'
    ],

    'ClassType' => [
        1 => '猎芯',
        2 => 'MRO',
        3 => '爱智',
    ],

    'SkuSeriesStatus' => [
        -1 => '禁用',
        1 => '启用'
    ],

    'SkuOrgList' => [
        1 => '猎芯',
        2 => '深贸',
        3 => '华云',
        6 => '华云香港'
    ],

    'Preference' => [
        1 => 'max',
        2 => 'min',
        3 => '',
        4 => ''
    ],

    //标准封装状态
    //0：待审核 1：正常 2：审核不通过 3：禁用
    'StandardEncapStatus' => [
        0 => '待审核',
        1 => '正常',
        2 => '审核不通过',
        3 => '禁用',
    ],

    //输入方式映射
    'ShopAttrInputType' => [
        1 => '(单选枚举)',
        2 => '(多选枚举)',
        3 => '文本',
        7 => '数值',
        10 => '数值+单位'
    ],

    //外部sku推送状态
    'ShopSkuPushStatus' => [
        -1 => '失败',
        1 => '成功',
    ],

    'ShopSkuPushAuditStatus' => [
        -1 => '审核失败',
        0 => '无',
        1 => '审核中',
        2 => '审核通过',
    ],

    //外部spu推送状态
    'ShopSpuPushStatus' => [
        -1 => '没推送',
        0 => '推送失败',
        1 => '推送成功',
    ],

    //外部spu校验状态
    'ShopSpuVerifyStatus' => [
        1 => '校验成功',
        2 => '校验失败'
    ],

    'DistributePriceStatus' => [
        1 => '启用',
        -1 => '禁用',
    ],

    'DistributeUserPriceStatus' => [
        1 => '启用',
        -1 => '禁用',
    ],

    //推送sku池子的状态
    'ShopDistributeSkuStatus' => [
        0 => '待推送',
        1 => '成功',
        -1 => '失败',
    ],

    'AbilityLevelRuleLevel' => [
        -1 => '',
        0 => '弱履约',
        1 => '强履约B',
        2 => '强履约A',
    ],

    'AgentBrandLevel' => [
        1 => '一线品牌',
        2 => '二线品牌',
        3 => '三线品牌',
    ],

    'SkuOptionalBatch' => [
        -1 => '无法指定',
        1 => '任意批次',
        2 => '1年内',
        3 => '2年内',
        4 => '3年内',
        5 => '4年内',
        6 => '5年内',
    ],
];
