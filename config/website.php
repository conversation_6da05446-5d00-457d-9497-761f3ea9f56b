<?php

$app_config = get_resource_config('app');
$domain_config = get_resource_config('domain');
$allow_origin_str = $domain_config['domain']['allow_origin'];
$allow_origin_list = explode(",", $allow_origin_str);
$current_domain = $domain_config['domain']['footstone_domain'];
return [
    'adminId' => 1000,
    'admin' => ['<EMAIL>'],
    'login' => [
        'login' => $domain_config['domain']['login_domain'] . '/login',
        'logout' => $domain_config['domain']['login_domain'] . '/logout?redirect=' . $current_domain,
        'check' => $domain_config['domain']['login_domain'] . '/api/checklogin',
        'search' => $domain_config['domain']['login_domain'] . '/api/search',
        'dashboard' => $domain_config['domain']['login_domain'] . '/dashboard',
    ],
    'dashboard_domain' => $domain_config['domain']['login_domain'].'/dashboard',
    //允许跨域站点
    'ALLOW_ORIGIN' => $allow_origin_list,


    'MainDomain' => $domain_config['domain']['www_domain'],
    'ten_floor' => $app_config['order']["joint_supplier_code"], //10楼备货供应商编码
    'hk_floor' => $app_config['order']['hk_supplier_code'],  //香港备货编码

    "cookieDomain" => $domain_config['domain']['cookieDomain'], //cookie 域

    "domain" => $domain_config['domain']['domain'],

    'api_domain' => $domain_config['domain']['api_domain'], // API

    'search_domain' => $domain_config['domain']['search_url'],
    'search_domain_new' => $domain_config['domain']['search_url_new'],


    "order_domain" => $domain_config['domain']['new_order_domain'], // 销售系统域名

    "pur_domain" => $domain_config['domain']['pur_domain'], // 采购系统域名
    "cube_domain" => $domain_config['domain']['cube_domain'], // 魔方域名

    "footstone_domain" => $domain_config['domain']['footstone_domain'], // 采购系统域名

    // 权限系统
    'perm_url' => $domain_config['domain']['perm_url'],
    // 获取用户权限接口
    'perm_api' => $domain_config['domain']['perm_api'],

    'perm_id' => env('PERM_ID', 50),

    // 获取用户许可权限接口
    'check_access_api' => $domain_config['domain']['check_access_api'],

    //上传图片接口地址
    'UploadUrl' => $domain_config['upload']['upload_url'],
    'UploadKey' => $domain_config['upload']['upload_key'],

    //供应商系统接口地址
    'SupplierUrl' => $domain_config['domain']['supplier_domain'],

    'MessageKey' => 'fh6y5t4rr351d2c3bryi',

    'MessageUrl' =>  'https://api.ichunt.com/msg/sendMessageByAuto',
    'ShopApi' => env('SHOP_API'),
];
