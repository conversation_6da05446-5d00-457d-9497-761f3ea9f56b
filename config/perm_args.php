<?php
return [
    // 菜单key
    'perm_menus_data' => 'frq_menus_href_data',
    'perm_menus_data_expire' => 7200,

    // 用户角色
    'roles' => [
        '管理员' => 1,
        '销售查看下级' => 2,
        '销售查看自己' => 3,
        '采购查看下级' => 4,
        '采购查看自己' => 5,
        '运营' => 6,
    ],

    //忽略校验权限的方法
    'ignore_perm_check_functions' => [
        'getSpuListForXmSelect',
        'checkStandardBrandNameList',
        'searchStandardBrand',
        'getStandardBrandById',
        'getSpuBySpuNameAndStandardBrandId',
        'getSpuInfo',
        'extractAttr',
        'getEncapByClassId',
        'getCanalListForXmSelect',
        'getTaskLogList',
        'taskLogList',
        'changeCzFlag',
        'downloadTaskLogResult',
    ]

];
