<?php

// 判断是否是命令模式
if (is_cli()){
    $default_env_dir = dirname(__DIR__);
} else {
    $default_env_dir = dirname($_SERVER['DOCUMENT_ROOT']);
}

$dotenv = Dotenv\Dotenv::createImmutable($default_env_dir);
$dotenv->safeLoad();
$RESOURCE_CONFIG_DIR = $APP_STORAGE_PATH = '';
if (!empty(env('RESOURCE_CONFIG_DIR'))){
    $RESOURCE_CONFIG_DIR = env('RESOURCE_CONFIG_DIR');
}

if (!empty(env('APP_STORAGE_PATH'))){
    $APP_STORAGE_PATH = env('APP_STORAGE_PATH');
}

// 定义资源配置文件目录
define('RESOURCE_CONFIG_DIR', (is_dir($RESOURCE_CONFIG_DIR)) ? $RESOURCE_CONFIG_DIR : '/data/liexin_config');

// 定义日志目录
define('APP_STORAGE_PATH', (is_dir($APP_STORAGE_PATH)) ? $APP_STORAGE_PATH : '/data2/liexin_logs/footstone');

function is_cli(){
    return preg_match("/cli/i", php_sapi_name()) ? true : false;
}
// 自动创建应用必须目录
$auto_create_dir = [
    APP_STORAGE_PATH . "/framework/sessions",
    APP_STORAGE_PATH . "/framework/caches",
    APP_STORAGE_PATH . "/framework/views",
    APP_STORAGE_PATH . "/app/public",
];

foreach ($auto_create_dir as $create_dir)
{
    if (!is_dir($create_dir)){
        mkdir($create_dir, 0755, true);
    }
}


function get_resource_config($type){
    if(!isset($GLOBALS['_lx_resource_config'][$type])){
        $path = RESOURCE_CONFIG_DIR.'/'.$type.'.ini';
        $config = parse_ini_file($path , true);
        $GLOBALS['_lx_resource_config'][$type] = $config;
    }else{
        $config = $GLOBALS['_lx_resource_config'][$type];
    }
    return $config;
}

function get_resource_config_section($type, $section){
    if(!isset($GLOBALS['_lx_resource_config'][$type])){
        $path = RESOURCE_CONFIG_DIR.'/'.$type.'.ini';
        $type_config = parse_ini_file($path , true);
        $GLOBALS['_lx_resource_config'][$type] = $type_config;
        $config = isset($type_config[$section]) ? $type_config[$section] : [];
    }else{
        $config = isset($GLOBALS['_lx_resource_config'][$type][$section]) ? $GLOBALS['_lx_resource_config'][$type][$section] : [];
    }
    return $config;
}

