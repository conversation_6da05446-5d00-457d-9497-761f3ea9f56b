<?php

// 判断是否是命令模式
if (is_cli()){
    $default_env_dir = dirname(__DIR__);
} else {
    $default_env_dir = dirname($_SERVER['DOCUMENT_ROOT']);
}

$dotenv = Dotenv\Dotenv::createImmutable($default_env_dir);
$dotenv->safeLoad();
$RESOURCE_CONFIG_DIR = $APP_STORAGE_PATH = '';
if (!empty(env('RESOURCE_CONFIG_DIR'))){
    $RESOURCE_CONFIG_DIR = env('RESOURCE_CONFIG_DIR');
}

if (!empty(env('APP_STORAGE_PATH'))){
    $APP_STORAGE_PATH = env('APP_STORAGE_PATH');
}

// 定义资源配置文件目录
define('RESOURCE_CONFIG_DIR', (is_dir($RESOURCE_CONFIG_DIR)) ? $RESOURCE_CONFIG_DIR : '/data/liexin_config');

// 定义日志目录
define('APP_STORAGE_PATH', (is_dir($APP_STORAGE_PATH)) ? $APP_STORAGE_PATH : '/data2/liexin_logs/footstone');

function is_cli(){
    return preg_match("/cli/i", php_sapi_name()) ? true : false;
}
// 自动创建应用必须目录
$auto_create_dir = [
    APP_STORAGE_PATH . "/framework/sessions",
    APP_STORAGE_PATH . "/framework/caches",
    APP_STORAGE_PATH . "/framework/views",
    APP_STORAGE_PATH . "/app/public",
];

foreach ($auto_create_dir as $create_dir)
{
    if (!is_dir($create_dir)){
        mkdir($create_dir, 0755, true);
    }
}


function get_resource_config($type){
    if(!isset($GLOBALS['_lx_resource_config'][$type])){
        $path = RESOURCE_CONFIG_DIR.'/'.$type.'.ini';
        $config = parse_ini_file($path , true);
        $GLOBALS['_lx_resource_config'][$type] = $config;
    }else{
        $config = $GLOBALS['_lx_resource_config'][$type];
    }
    return $config;
}

function get_resource_config_section($type, $section){
    if(!isset($GLOBALS['_lx_resource_config'][$type])){
        $path = RESOURCE_CONFIG_DIR.'/'.$type.'.ini';
        $type_config = parse_ini_file($path , true);
        $GLOBALS['_lx_resource_config'][$type] = $type_config;
        $config = isset($type_config[$section]) ? $type_config[$section] : [];
    }else{
        $config = isset($GLOBALS['_lx_resource_config'][$type][$section]) ? $GLOBALS['_lx_resource_config'][$type][$section] : [];
    }
    return $config;
}



/*
|--------------------------------------------------------------------------
| Create The Application
|--------------------------------------------------------------------------
|
| The first thing we will do is create a new Laravel application instance
| which serves as the "glue" for all the components of Laravel, and is
| the IoC container for the system binding all of the various parts.
|
*/

$app = new Illuminate\Foundation\Application(
    $_ENV['APP_BASE_PATH'] ?? dirname(__DIR__)
);

/*
|--------------------------------------------------------------------------
| Bind Important Interfaces
|--------------------------------------------------------------------------
|
| Next, we need to bind some important interfaces into the container so
| we will be able to resolve them when needed. The kernels serve the
| incoming requests to this application from both the web and CLI.
|
*/

$app->singleton(
    Illuminate\Contracts\Http\Kernel::class,
    App\Http\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);

$app->useStoragePath(APP_STORAGE_PATH) ;


/*
|--------------------------------------------------------------------------
| Return The Application
|--------------------------------------------------------------------------
|
| This script returns the application instance. The instance is given to
| the calling script so we can separate the building of the instances
| from the actual running of the application and sending responses.
|
*/

return $app;
