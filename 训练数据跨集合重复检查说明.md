# 训练数据跨集合重复检查功能说明

## 概述

为了确保训练数据的唯一性，系统现在会在新增训练数据前检查该内容是否已在其他类型的训练数据集合中存在。如果存在重复，将阻止新增操作并提示用户。

## 检查范围

系统包含4个MongoDB训练数据集合：

| 集合名称 | 字段名称 | 数据类型 | 说明 |
|---------|---------|---------|------|
| `bom_brand_train_data` | `brand` | 品牌 | 品牌训练数据 |
| `bom_gn_train_data` | `gn` | 型号 | 型号训练数据 |
| `bom_cate_train_data` | `cate` | 分类 | 分类训练数据 |
| `bom_params_train_data` | `param` | 参数 | 参数判断数据（新增） |

## 检查逻辑

### 新增品牌数据时
检查以下集合是否已存在相同内容：
- ✅ `bom_gn_train_data` (型号)
- ✅ `bom_cate_train_data` (分类)  
- ✅ `bom_params_train_data` (参数)

### 新增型号数据时
检查以下集合是否已存在相同内容：
- ✅ `bom_brand_train_data` (品牌)
- ✅ `bom_cate_train_data` (分类)
- ✅ `bom_params_train_data` (参数)

### 新增分类数据时
检查以下集合是否已存在相同内容：
- ✅ `bom_brand_train_data` (品牌)
- ✅ `bom_gn_train_data` (型号)
- ✅ `bom_params_train_data` (参数)

## 触发场景

### 1. 单个新增
**触发位置**: `TrainingDataService::saveTrainingData()` (新增操作)

**检查时机**: 在保存到MySQL数据库之前

**错误提示**: `"该内容已在{类型}训练数据中存在，不能重复添加"`

### 2. 单个修改
**触发位置**: `TrainingDataService::saveTrainingData()` (更新操作)

**检查时机**: 仅当内容发生变化时检查

**错误提示**: `"该内容已在{类型}训练数据中存在，不能重复添加"`

### 3. 批量导入
**触发位置**: `TrainingDataImport::array()`

**检查时机**: 在每条记录插入MySQL数据库之前

**错误提示**: `"内容「{具体内容}」已在{类型}训练数据中存在，不能重复添加"`

## 技术实现

### 核心检查方法
```php
private function checkContentExistsInOtherCollections($content, $currentType)
{
    $mongo = DB::connection('mongodb');
    $otherCollections = $this->getOtherMongoCollections($currentType);
    
    foreach ($otherCollections as $config) {
        $exists = $mongo->table($config['collection'])
            ->where($config['field'], $content)
            ->exists();
        
        if ($exists) {
            return $collectionNames[$config['collection']] ?? '其他';
        }
    }
    
    return false;
}
```

### 集合配置
```php
private function getAllMongoCollections()
{
    return [
        ['collection' => 'bom_brand_train_data', 'field' => 'brand'],
        ['collection' => 'bom_gn_train_data', 'field' => 'gn'],
        ['collection' => 'bom_cate_train_data', 'field' => 'cate'],
        ['collection' => 'bom_params_train_data', 'field' => 'param'],
    ];
}
```

## 用户体验

### 前端错误提示
当检测到重复内容时，系统会：
1. 阻止保存操作
2. 显示明确的错误信息，指出在哪个类型的数据中已存在
3. 用户需要修改内容后才能继续操作

### 批量导入处理
- 如果导入文件中有重复内容，会抛出异常
- 整个导入操作会回滚，不会部分成功
- 用户需要修改导入文件后重新导入

## 示例场景

### 场景1：新增品牌时发现重复
```
用户尝试新增品牌："iPhone"
系统检查发现"iPhone"已在型号训练数据中存在
提示：该内容已在型号训练数据中存在，不能重复添加
操作被阻止
```

### 场景2：批量导入时发现重复
```
用户导入品牌文件，包含内容："连接器"
系统检查发现"连接器"已在分类训练数据中存在
提示：内容「连接器」已在分类训练数据中存在，不能重复添加
整个导入操作回滚
```

### 场景3：修改内容时发现重复
```
用户将品牌"Apple"修改为"MacBook"
系统检查发现"MacBook"已在型号训练数据中存在
提示：该内容已在型号训练数据中存在，不能重复添加
修改操作被阻止
```

## 注意事项

1. **检查范围**: 只检查其他类型的集合，不检查当前类型的集合
2. **大小写敏感**: 内容比较是大小写敏感的
3. **空格处理**: 导入时会自动trim空格，但比较时以实际存储内容为准
4. **事务安全**: 检查在数据库事务开始前进行，确保数据一致性
5. **性能考虑**: 每次检查需要查询多个MongoDB集合，建议为相关字段建立索引

## 扩展性

如果需要添加新的训练数据类型：
1. 在 `getAllMongoCollections()` 方法中添加新的集合配置
2. 在 `$collectionNames` 数组中添加对应的中文名称
3. 新类型会自动参与跨集合重复检查

这种设计确保了训练数据在不同类型间的唯一性，提高了数据质量和训练效果。
